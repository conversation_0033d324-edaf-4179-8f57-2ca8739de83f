FROM <CI_REGISTRY>/<PROJECT_ID>/<CI_APPLICATION_REPOSITORY>/dependency:latest as BUILDER
WORKDIR /opt/app
COPY pom.xml .
COPY src/main/resources /opt/app/src/main/resources
COPY src/main/java /opt/app/src/main/java
RUN mvn -B -e clean package

FROM azul/zulu-openjdk:11
ENV TZ=Asia/Hong_Kong
ENV JAVA_OPTS="-server -XX:+UseG1GC -verbose:gc -Xlog:gc:stdout -XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=70 -XX:MinRAMPercentage=50"
COPY --from=BUILDER /opt/app/target/*.jar app.jar
COPY extra-directories /extra-directories
ENTRYPOINT exec java $JAVA_OPTS -javaagent:/extra-directories/glowroot/glowroot.jar -Dglowroot.collector.address=$mms_product_configmap_glowroot_collector_address -Dglowroot.agent.id=mms-product-api:: -jar /app.jar
