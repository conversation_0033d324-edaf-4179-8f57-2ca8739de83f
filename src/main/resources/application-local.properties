# Database
spring.datasource.url=**************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

mybatis.configuration.map-underscore-to-camel-case=true

# JWT
jwt.app.secret.key=cebrzhSTzWlylwk7EgJPQnSTvMYRLKeykNZNH5m79OIK8ryyiIexqEo09jGhSrhs/jO+ec3EHXLul/WkdSZERmXY9wm2RuMgXCH463stcGdNE1UQXXezO1ZGhasdhtmGpB3ORtSCcu42iVRC9fdPz2Ox476+qRv5mXE1JQCY6Z8=

# Product Master
product.master.api.url=https://shoalter-see-product-master-dev.hkmpcl.com.hk
product.master.rabbitmq.addresses=127.0.0.1:5673
product.master.rabbitmq.port=5673
product.master.rabbitmq.username=guest
product.master.rabbitmq.password=guest
product.master.rabbitmq.product.result.exchange=mms_product_topic
product.master.rabbitmq.product.result.routing.key=mms_product.product-result
product.master.ew.update.binding.endpoint=/v1/api/products/extended_warranty_mapping
product.master.ew.search.binding.endpoint=/v1/api/products/search_extended_warranty
product.master.size.update.visibility=5

# Inventory
inventory.api.url=https://mms-inventory-dev.hkmpcl.com.hk/inventory
product.inventory.service.name=/api/s2s/product-inventory
product.inventory.batch.history=/api/s2s/product-inventory/batch/history
product.inventory.bundle.update=/api/v2/s2s/product-inventory/bundle
product.inventory.bundle.selling.qty=/api/v2/s2s/product-inventory/findBundleInventoryList
product.inventory.child.sku.qty=/api/v2/s2s/product-inventory/findChildSkuQtyList

# Mms Store Api
mms.store.api.url=http://localhost:8081/store

# Mpps Rabbit MQ
mpps.rabbitmq.sku.info.routing.key=mms.sku_info
mpps.rabbitmq.addresses=127.0.0.1:5672
mpps.rabbitmq.port=5672
mpps.rabbitmq.username=guest
mpps.rabbitmq.password=guest
mpps.rabbitmq.queue.name=mms_product_product-info-hktvmall_queue
mpps.rabbitmq.single.queue.name=mms_product_single-product-info-hktvmall_queue
mpps.rabbitmq.batch.mq.queue.name=mms_product_product-info-hktvmall_mq_queue

# image
hktv.image.url=https://hktv-img.hkmpcl.com.hk/images
image.upload.url=https://eese-dyn-image-upload-dev.hkmpcl.com.hk/oapi/v1/images/mms/upload
image.status.url=https://eese-dyn-image-upload-dev.hkmpcl.com.hk/oapi/v1/images/status
image.delete.url=https://eese-dyn-image-upload-dev.hkmpcl.com.hk/oapi/v1/images/
image.batch.delete.url=https://eese-dyn-image-upload-dev.hkmpcl.com.hk/oapi/v1/images/delete
image.domain=images.hktvmall.com,images.hktv-img.com,images.hokobuy.com,mmstest-images.hkmpcl.com.hk,dyn-image-server-dev.hkmpcl.com.hk,dyn-image-server-dev2.hkmpcl.com.hk,eese-dyn-image-server-dev.hkmpcl.com.hk,mms-dyn-image-server-dev.hkmpcl.com.hk
image.domain.modify=images.hktvmall.com
upload.file.imagePath=/images

# video
video.upload.url=https://vid-upload-dev.hkmpcl.com.hk/oapi/v1/videos
video.status.url=https://vid-upload-dev.hkmpcl.com.hk/oapi/v1/videos/status
upload.file.videoPath=/video
mms.video.private.key=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDnkOa8uiRyxJDdDYNnM28v90hHtPyLo4Wm+qLz+eGvBd39cYa1HbLPni1Reh6MM8Um/XKhWounVurnOSNX/T/3Uib19kuW5in2WDBHmC45rpZpXXNvec/Z436TwpsKLJaYj4Jvu23wsNDYYgXoWtqOnags/phYO4F8dwV01S05qIEN9E2ZK7I5/dkyqsuyO6JGnxirXsj0W/U0ECUwMRwVSltUQ58a7IN1BWs8v9Zg0njcKzLWMRFQK8mDUGZcTJfnO70e9sB2ClNq7RboRrHYLQtHKWJREaBAyW90VMF/k3el+904PDgHe87KxVMRtAz+XMwRe9nXG4AdNS6Kf/mBAgMBAAECggEBALrJp1WU0NHCvReSTuxwEMAgN62f0BbTUHvgsJEcf28uzGIjsQVnb1UJwoR2fS2alO1eg5ij8QKKmfDpphjTr0VQfxzoDxEpl6+ec1NM7fyZV7qmJvdEyuR3HSMTVvxd+xwUpqJ6DbSY692fNCS5JJLlmUKyGzEQzuIXH8+eJVzbhkxpl+OFDNXrSaPULjBDuMC3xlqC1geEVqavtdSYE44IB0G9Ak0ldP7zGECwPj/Q3W+D+5weP6Eo7P9NYereyXAOfTdWpxvJuFIu9mxhNHB2OJrBSz5USUFqRY7TPUAacuAdtJppFDrAMfWj6+K2NlQ89O0dnbabComGpfBFugECgYEA+kICuDo81UEk9bjaYe4/Zt2yMjswBMraizM1sP0lTyoZ4YE/rAjxcKnbbsigBtwjS7YScZIx+mXztvZrYX7mVJ4mv44yX+fF8RGt5u0MWO4YprZqeKhnIDYp6462fR7ykwFvOCGe95qbgw5zZiw3y9D1z2J8StyQUm9Rrr8ip38CgYEA7OEYxLnpXDi6+5O0H1yJq5UCj5Hrlj4kmYYXXxp+ls6K3x+L/PBQ6bT3C7L/lVRDLhOmLtMOwu1ujRY4OoURFuTcDfajEUetWifHNDAcb3QZxczmtZfG+gw7r1H5BHBR7Lop/NYBcThmRyLMzd8fJ7ZzplHuJm9r3sfW30vF3v8CgYALCCfoiSCvYezILDrsQD1pQnKdhkBqpaqWok5GnBIt+DQl3bW51rcV+Vbj/lIXm1Ku/rq3aPNtD1t8AEbhiF+M8V/R5+nOkoAGTXo2CNMXhH9vsfDlmr7ZC6ozaRogT+HvmwuVFN5WGemrEFMD+QUIphB/8N57yldOWvvl0SUs7wKBgQC/9nLSCi9Y8whklAAmYiGXEPywSHp4mQej+oy6H4hywhK8Wp5hbobDvkluC7Mf2tdOYdIMpDGUOkGayGTs16VPvkuubS8c889B/nZqjoqcr6OrLi/Q/ZzcSkmb/Hwm4rchdoRMR0mH7ULFZyg9thFTZ4I1SLQMJcNvGNfCwW8rFQKBgB0FTNOfsnArW/jxh4wJuzTxOGK4KTBYAkTCD55G/aZzESrbIck9O83orprsdc7wT0OPBR3v1c0KGEeJxVkUwIzk5VHMSeWfqRRb1BqSkOC0OwUZ3mz7NXCfJW6lm/i6hnumHvCOiwDflTdlUDMnaR5mFdTQTG+i6ajin4GFKHi9

# Little Mall
little-mall.base-url=http://localhost:8080

# 3PL
third-party.api.url=https://tpl-mms-dev.hkmpcl.com.hk/hktv3plmms
third-party.api.sku.validate.url=/internal/sku/validate

# mms-third-party-sku
mms-third-party-sku.api.url=https://mms-third-party-sku-dev.hkmpcl.com.hk
mms-third-party-sku.one-bound.detail.endpoint=/third-party-sku/api/s2s/one-bound/{storeId}/{productId}/detail
mms-third-party-sku.toonies.detail.endpoint=/third-party-sku/api/s2s/toonies/{productId}/detail

# Feature Toggle
feature.toggle.send.mpps.queue=true

# Hybris
hybris.api-url=https://ecomtest01.hkmpcl.com.hk/hktvwebservices

hybris.endpoint.create-bundle=/v1/hktv/s2s/mms/import_bundle_sets
hybris.endpoint.update-bundle=/v1/hktv/s2s/mms/update_bundle_sets
hybris.endpoint.update-ew-binding=/v1/hktv/s2s/mms/set_dynamicProduct_relation
hybris.endpoint.update-product-mainland-same-price=/v1/hktv/s2s/mms/updateProductMainlandSamePrice
hybris.endpoint.upsert-everuts-buyer=/v1/hktv/s2s/mms/createUpdateEverutsBuyer
hybris.endpoint.create-update-option-value=/v1/hktv/s2s/mms/createUpdateOptionValue
hybris.endpoint.create-product=/v1/hktv/s2s/mms/createProduct
hybris.endpoint.update-product=/v1/hktv/s2s/mms/updateProduct
hybris.endpoint.create-discount-rule=/v1/hktv/s2s/mms/createDiscountRule

hybris.rabbitmq.product.update.routing.key=mms_product.update_product_from_mms_product_to_hktvmall_hybris
hybris.rabbitmq.product.update.result.queue.name=mms_product_update_product_result_from_hktvmall_hybris_to_mms_product_queue
hybris.api.secret.key=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCaPW0HAvtOMlOYGrv98gYwQKjb5rIH5mgRFuwPNeSfyDvE7sSBtsnr3RcZ+6Bo+Ti67Ik91FH97vnM5041gKwCg8rtmfZ0gfS7fEt60eOPCyPF6sX6a4LfYSO4u7yh7GwvUcn0i5D+XLX5gRhW32JuqmTxNFi96WVgVOuN1kLoMFMIu7BgBtKRdkPkqqYC6d/wl3NFDxGKq9GG0GMR32gMssO9dEtpaP78wmNksWErTrQvKmPDqBE+TDQ79FvEDo6zH1AeaZ0aAyzS5I6wdY3wxQKtmppsbH/okkA6ISHeJH3nnilY8mqYMt4bC+KMAjA8DO5AdgQH74BKh6G1r9R7AgMBAAECggEALJcWhoDnqyckgKQ19gf6nweKaaxripojOtGQJCOV+VV3ZgsLF18DSCNuMs5KhR6Ltop8cLoJjCqC4Ch8tT4CPGXijEdG9068tFkOLy8vpOJ5EJY7bxaWysJ4A8eOlJdXfWSdSIvyun6OA1g9c5v4BntPXGYgQ6MOckeKo8IuLJac4mzI69gs7kKStOrIJ865xKQia6JlZf/KWh7Fjirgwge/+Ayn3kOng8C7sDuGW0V+IfalNOFkD1m58GYY4+Pu0YJOI6VbivK8SQ/95gpB+p+OVD5tV9QepiVINS8XnauWS100nJ1H1v1MJn21e0ZN9dQm1iwZr/9rNJB//OJPyQKBgQDPekuY1D79N+QhZ9tP/CDSrRZo1gkwFVxgUtVeszboYXrbT89gclQo1gt/VrXljaax7F+ND4kDQ/yRKRWW9GRNAyj4Iz2axhJqvDCdqP3k6jxFB7KKLPQoOm6mBwOWAQtrrSsgsQxE3Ev5CHkAKtkWeMXaIqf+ylRmXWZVwUbXKQKBgQC+T8gcBf4uPNhIBDmA5CnEi5xY3Zr+5tcPuwrIa0W1bS3eZEyWBJs4bhfyRrOjyfoPY12IgsgrELWsdnHECNC4vysGHvimAT6yihbeQRKUKIQDKXCSQRy9cm6BsJn/s/O0elN4giYQtMJWQaTO13/J6davjsixJildEPlSUEW3AwKBgHUdsYKpfIzvecUpnPC/f7+smNPAzhroU+bDm1IDk+EE9ZbKrGLi5oLOoFrXFYJovUn+BcnYlRzUycMfD+LQPbYDLKM1MVcmq8q4RDEa7YbEehT1TWEWExSKN19c4jdQi6PScl/X0Pcl63S9xZ/EkBpRPk3t9iqrB41o95U8rsJRAoGAb5fesjf9ZOV+uTPo+CBSMZLf2egQA9oeT3YRC5aDaLAdjmNn2SvYTM2ZsxIsMiW0QKO2a96sOzxwGzHU4et9YGeQdeVhTg460nxQjoC/wSrExgndnZUaoifgjqGcm6jMhY6ZNXezlGY58lRJrJQGtcIthgIGwoGrDYxvdgnl5zsCgYBTPbtDcMkF6l/cw9aVERlD+1qpOSSLkidYFf5s1bzEeV4bMlevk3b2pj3USgwpOdNcKfwyEs8sI6oGyoplNs6pU81wNNQsNlZc59xgjrJx5OASHtcVmxzkL8FT9b/l7kfbrShOWbU0ulXz+w9MV+lFIRNWvYR5mhlVi8/Ee+cmmA==
hybris.api.batch.size=100
hybris.api.update.mainland.same.price.size=5

# grafana endpoint
management.endpoint.prometheus.enabled=false
management.endpoints.web.exposure.include=prometheus,health

# HttpClient config
httpclient.pool.connection.default.max.route=20

# hktvmall external affiliate url
hktvmall.external.base.url=https://www.hktvmall.com
hktvmall.external.affiliate.endpoint=/external_affiliate?targetURL=

# Promotion
promotion.api.url=https://mms-promotion-dev.hkmpcl.com.hk/promotion
promotion.membership.pricing.unexpiredEvents.check=https://mms-promotion-dev.hkmpcl.com.hk/promotion/membershipPricing/internal/validateEventSet

# Notification
notification.url=https://merchant-notification-system-shoalter-dev.hkmpcl.com.hk

# Approval deal daily report
approval.daily.report.receiver=<EMAIL>

# Product price alert
product.price.alert.internal.user.receiver=<EMAIL>
product.price.alert.onebound.threads=4

# Toonies sku report
toonies.report.receiver=<EMAIL>
partner.product.price.threads=6
partner.product.price.check.timeout.minutes=360
partner.product.price.report.timeout.minutes=1200

# Mms Setting api
mms.setting.api.url=https://mms-setting-dev.hkmpcl.com.hk/setting
mms.setting.exchange-rate.endpoint=/api/s2s/exchange-rate/

# Cron job
mms.save-product-record.expired.days=90
mms.housekeeping.limit.counts=5000

# MMS Product
mms.db.price.alert.data.size=5

# mock listener
mock.listener.enabled=true

# Tmall variant option
tmall.cronjob.processingVariantLimit=${mms_product_configmap_tmall_cronjob_processing_variant_limit:2}

# async pool size
product.async.ecom-engine.core-pool-size=4
product.async.ecom-engine.max-pool-size=4
