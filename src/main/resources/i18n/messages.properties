message1=File: ''{0}'' is not supported for batch upload
message2={0} input wrong format
message3={0} input wrong format
message6=SKU ID: ''{0}''  already exists in the store.
message7=All variant SKU must under same brand with primary SKU.
message8="Brand Name (EN)" is required to fill in.
message9=Failed to adjust inventory.  Please try again later.  System code: {0}
message10=Product server is busy, try again later. System code: {0}
message11=Warehouse is not found in the system.  Please check and try again.
message12=Cannot find any matches contract terms.
message13=Can't find need Offline product.
message14=Contract doesn't exist.
message15=E-Voucher sku can only select {0} as Packing Box Type.
message16=Exceed maximum records of {0} at a time.
message17=Weight cannot be over {0} kg for {1}.
message18=Fail to sync price rule. Store_SKU {0}.
message20=File cannot be null
message21=Server busy, please try again later. System code:{0}
message22=Invalid warehouse for product ready method.
message23=Please enter Inventory  information.
message24="Is Primary SKU" cannot be modified
message25=Mall Dollar should be between {0} and {1}.
message26=Mall Dollar should be an Integer.
message27=merchant id is required.
message28=No Permission, role code {0}.
message29=One category in {0} must be selected
message30=Only enable to select the category code with '01' as the last number.
message31=Packing box type:''{0}'' can't match "storage temperature" or "fragile item"
message32=Parameter exception
message33=Physical store can only consist of uppercase, lowercase, number and special characters (,)
message34=Please create primary SKU before create variant SKU.
message36=Please enter physical store.
message37=Please enter RM code.
message38=Please enter storage type.
message39=Please make sure the Warehouse data is fully set up in the store settings.
message45=Pre-sale fruits are only available for non-standard delivery method.
message46=Packing box type doesn't match primary category
message47=Primary category code cannot be used as SKU primary category.
message48=Primary SKU already exists in the product.
message49=Primary category: ''{0}'' doesn't exist.
message51=Product doesn't exist.
message52=Product id: ''{0}'' cannot include ''_S_''.
message53=Product id: ''{0}'' contains not allow character, system only allow A-z, 0-9 , hyphen and underscore
message54=Please enter Product id.
message55=Product: ''{0}'' is not approved.
message56=Product type code: ''{0}''(HKTVmall) is inactive , please use other product type code
message59=Resource not found.
message60=RM code can only consist of uppercase, lowercase, number and special characters (|).
message62=Server busy, try again later.
message63=Shelf life should be digits only.
message64=SKU ID cannot include '_S_'.
message65=SKU ID contains not allow character!, system only allow A-z, 0-9 , hyphen and underscore.
message66=SKU ID: ''{0}'' already exists.
message67=Standard delivery (merchant deliver to warehouse) or standard delivery (pickup by Third Party) can only select am/pm in pickup timeslot.
message68=Please enter "Storage temperature" or "fragile item"
message69=Store doesn't exist.
message70={0} can''t modify during the promotion period.
message71=The following store is not allowed to choose following category: ''{0}''.
message72=The status of the brand has not been approved.
message73=This packing box type can not match merchant delivery.
message75=Upload type undefined.
message76=Urgent cannot be changed.
message77=Can't modify user max.
message79=Video link should be entered by starting with a '=' with YouTube video id.
message80=Video link text(in Chinese) can not contain '|'.
message81=Video link text(in English) can not contain '|'.
message82=VIP Mall Dollar should be between ''{0}'' and ''{1}''.
message83=VIP Mall Dollar should be an Integer.
message84=Please enter packing height.
message85=Please enter packing depth.
message86=Please enter packing length.
message87=Weight cannot be equal to 0.
message88=Packing height cannot be equal to 0.
message89=Packing depth cannot be equal to 0.
message90=Packing length cannot be equal to 0.
message91=No inventory data detected, please enter.
message92=Please enter weight .
message93=Please enter {0} .
message94=Please enter Option{0} field name for [{1}]
message95=Please enter Option{0}  [{1}] value
message96=Please enter Size value for Size System [{0}]
message97=Please enter Size System
message98=Please enter Color(in English) for Colour Families [{0}]
message99=Please enter Colour Families
message100=Pickup Days isn't in store setting.
message101=Store warehouse doesn't exist
message102=Cannot find the corresponding merchant.
message103=Save product failed: fail to commit video to video server.
message104=Product create/edit successfully yet failed to update Inventory.  Please try again later. System code: {0}
message105=Old product data error.
message106=All field is required.
message107=Please check this sku field.
message110=Please enter Original price
message111=Selling Price can't more than Original Price
message112=BarCode only allow number, A-z and special characters (!@#$%^&*():??<>?-_=+~';|\,.)
message113=This sku no inventory data, please contact system administrator.
message114=Shelf life cannot smaller than 1.
message115=Removal Service only can apply to Categories under Producer Responsibility Schemes.
message116=Barcode cannot exceed 100 characters.
message117=There are some products in process, please try it again later.
message118=Barcode cannot be duplicated in merchant when product ready method contains 3PL.
message119=Packing info or barcode can not be edited.
message120={0} can not ship to {1}.
message121=Category:{0} can''t ship to {1}.
message122=Please check {0} that is selected in store setting.
message123=Please input right format in oversea delivery setting column.
message124=E-Voucher SKU can be only applied to Service Deals contracts.
message125=Please enter terms name.
message126=1 Product ID should only have 1 SKU with [is primary sku] = Y.
message127=Product not found in Product Master, please contact your system administrator.
message128=Not HKTV Product
message129=Server busy, please try again later. System code:{0} Tracking Code:{1}
message130=Merchant not found in virtual store list.
message131=You can not use this function please check with system admin
message132=You can not change store:{0}'s SKU Oversea distinct
message133={0} is required please check again
message134=This store ID isn't exist please check again
message135=Upload file is empty please check again
message136=One upload file can only contain 10000 record please check it again
message137=This SKU ID was duplicate in this file, please check again
message138=Group not exist.
message139=Please enter main barcode.
message140=Fill one of carton length height depth that all carton dimension should be fill.
message141=All carton dimension cannot be equal to 0.
message142=Please enter packing box type.
message143=Please enter packing dimension unit.
message144=Please enter weight unit.
message145=No warehouse id is mapping with this storage type: {0}
message146=Record not found
message147=User max must be a number greater than 0
message148=Consignment or 3PL can only select am/pm/ev in pickup timeslot.
message149=Storage Type cannot be amended
message150=RM Code cannot be amended
message151=Emoji is not allowed to input for Product Information fields
message152=Pickup time slot need to be AM/PM/EV for standard delivery SKU
message153=Same day in hub can only select 0 in product ready day.
message154=child sku id {0} not exist.
message155=only 100 types of child sku allow.
message156=child sku {0} is already bound over 10 online-bundles.
message157=Save product failed: fail to commit image to image server.
message158=child sku {0} is offline, bundle product cannot online.
message159=Business or platform doesn't exist.
message160=Bundle product must be the primary sku.
message163=The selling quantity of offline bundle product can only be 0.
message164=Can't find need Offline bundle product {0}.
message165=Can't find bundle product {0}.
message166=child sku {0} not match contract prod terms.
message167=image fail to upload. image url : {0}, image type: {1}, error message: {2}.
message168=Record {0} with upload type {1} and status {2} not found.
message169=sku name {0} contain 4 bytes emoji.
message170="SKU ID" is required to fill in
message171="Product ID" is required to fill in
message172="SKU Name (Chi)" is required to fill in
message173="SKU Long Description (Chi)" is required to fill in
message174="Main Photo" is required
message175="Original Price" is required to fill in
message176="Visibility" is required to fill in
message177="Online Status" is required to fill in
message180="Is Primary SKU" is required to fill in
message181=Invalid code for HKTVmall Category
message182=HKTVmall category code {0} is not able to use now. Please select another similar category code instead.
message183=there is still an import record processing.
message184=store {0} is invalid
message185=no sku under the store {0}
message186=no sku need to import
message187="Store ID" is required to fill in
message188="Product type code" is required to fill in
message189="Primary category code" is required to fill in
message190="Product Ready Method" is required to fill in
message191="SKU Status" is required to fill in
message192="Warehouse" is required to fill in
message193="SKU Name" is required to fill in
message194="SKU Short Description (Eng)" is required to fill in
message195="SKU Short Description (Chi)" is required to fill in
message196="Manufactured Country" is required to fill in
message197="Currency" is required to fill in
message198="Invisible flag" is required to fill in
message199="Return Days" is required to fill in
message200="Product Ready Days" is required to fill in
message201="Pickup Days" is required to fill in
message202="Pickup Timeslot" is required to fill in
message203="User Max" is required to fill in
message204="Feature Start Date/Time" is required to fill in
message205="Feature End Date/Time" is required to fill in
message206="Voucher Type" is required to fill in
message207="Voucher Display Type" is required to fill in
message208="Voucher Template Type" is required to fill in
message209="Expiry Type" is required to fill in
message210="Redeem Start Date" is required to fill in
message211="Fixed Redemption Date" is required to fill in
message212="Upon Purchase Date" is required to fill in
message213="Warranty Period Unit" is required to fill in
message214="Warranty Period" is required to fill in
message215="Affiliate Url" is required to fill in
message216="need_removal_services" is required to fill in
message217="Goods Type" is required to fill in
message218="Carton Height(mm)" is required to fill in
message219="Carton Length(mm)" is required to fill in
message220="Carton Depth(mm)" is required to fill in
message221=shopId doesn''t exist.
message222="Product ID" cannot be modified
message223=Tmall products not found
message224=Error occurred while processing excel or zip
message225=sku uuid {0} not found in Product Master
message226=file contain products with different merchants
message227=Hktv data not found.
message228="Delivery Method" is required to fill in Please check input product ready method is exist or ask system admin for help
message229="Physical Store" is required to fill in
message230="Cost" is required to fill in
message231=Input Product Ready Method isn''t available to this store
message232=Main Video name length is out of range(maximum is {0} characters)
message233=Can''t input cost when Product ready Method isn''t oversea delivery
message234=User Max can''t more than {0}
message235={0} can''t enter decimal when unit is mm
message236={0} can''t enter more than two decimal when unit is cm
message237={0} can''t enter more than three decimal when unit is m
message238=Weight can''t enter decimal when unit is g
message239=Weight can''t enter more than three decimal when unit is kg
message240=Can''t use this Packing Box Type for this SKU
message242=Can''t change Mall Dollar (% of selling price) values
message243=Can''t change VIP Mall Dollar (% of selling price) values
message246=Cannot choose more than three HKTV categories.
message247=Input Primary category code must in Product type code
message248=input Product Ready Method doesn''t exist
message258=Packing box type isn't match please check with system admin
message260=Main photo Unable to upload more than {0} picture
message262={0} can''t less than 0
message263={0} can''t more than {1}.
message264=input {0} doesn''t exist
message265=other photo Unable to upload more than {0} picture
message266=Other product photo Unable to upload more than {0} picture
message267=Video Link Text{0} ({1}) length is out of range(maximum is {2} characters)
message268=Can''t input cost when Product ready Method isn''t oversea delivery
message269=User Max can''t less than {0}
message270={0} length is out of range(maximum is {1} characters)
message271={0} should be positive integer
message272=Product Ready Method = E-Voucher doesn't allow to modify Invisible flag when not approve.
message273={0} format is {1}
message274={0} should be later than the {1}
message275={0} should be earlier than the {1}
message276=Service Centre Email input Invalid Email format
message277=Product type code {0} can''t be used for product ready method {1}
message278=Product ready days can''t use in this product ready method {0}
message279={0} is out of range(maximum is {1} characters)
message280=Emoji is not allowed to input for {0}
message281=Can''t input original price when Product ready Method is oversea delivery
message282=Product type code {0} can''t be used for contract type {1}.
message283=Advertising photo Unable to upload more than {0} picture
message284=Currency only allow HKD
message285=Can''t input {0} when {1}
message287=All column can't cross store update
message288=Merchant is not in the whitelist
message289=Product Save Timeout
message290={0} contain 4 bytes emoji.
message291=E-Voucher SKU can be only applied to Service Deals contracts
message292=Insurance SKU can be only applied to Annual Insurance Contract, Fixed cost contract - Insurance
message293=Can't change Primary Category Code/Brand while modifying the Product Ready Method
message294=Affiliate Url should start with "https:" or "http:"
message295=Affiliate network no data can export.
message296=EW Percentage Setting must be within 0-{0}, can include up to two decimal places, but cannot be 0.
message297=Can''t fill in EW Percentage Setting
message298=For Extended Warranty, category must only have {0}
message299=Cannot modified status, original status: {0}, modified status: {1}
message300=approval type {0} is invalid
message301={0} doesn''t exist
message302=This {0} doesn't exist
message303={0} is not {1} Product
message304=Each electronic product should only be linked with one extended warranty product.
message305=Can't link offline Extended Warranty Product
message306={0} cannot be amended
message307="{0}" is required to fill in
message310=Cannot modify the variant SKU's primary category code before the primary SKU's approval deal is approved.
message311=StorefrontStoreCode not found, storeCode is {0}.
message312=duplicated data
message313=This uuid {0} no inventory data, please contact system administrator.
message314=The user does not have permission to access this storeFrontStoreCode {0}.
message315=storeId size should be between 1 and 5
message316={0}:''{1}'' contains not allow character, system only allow A-z, 0-9, hyphen and underscore
message317=SKU Name (Chi) can't contain emoji
message318=Create commission rate approval deal, some fields cannot be null.
message319=process commission rate approval deal error
message320=Buyer ID can only input positive number
message321="{0}" cannot be modified
message322=invalid action
message323=skuCode not match storeCode
message324=non-Everuts store is not allow
message325=Everuts contract is unable to create SKUs through the UI. Please use the Open API to create SKUs instead.
message326=Original Price needs to be greater than the Plus Price by at least 5%.
message327=Selling Price needs to be greater than the Plus Price by at least 5%.
message328=Partner Platform and Partner Product ID must be filled together. Other fields cannot be filled without Partner Platform.
message329=No Sku to Flatten.
message330=Video link text(in Simplified) can not contain '|'.
message332=Can''t choose {0} when bundle SKU have plus promotion.
message334=Can''t choose {0} when SKU have plus promotion.
message335=Media-related fields can't contain unacceptable content.
message336=Product ready method {0} is not included in the available product ready method: {1}
message337=variant sku update failed.
message338=sku id {0} is duplicated.
message339={0} - All Variant SKU must under same data with Primary SKU.
message340=cannot find record row id {0}.
message343=Can''t add {0}, please contact by "MCS Form"
message344=Can''t ship to {0}. Delivery District for Bundle SKU are based on the category of the Child SKU.
message345=Product field of SKU ID {0} is duplicated.
message346=Product field Option of SKU ID {0} is duplicated.
message347={0} must not be duplicated.
message348=The SKU ID of the primary SKU is different from the exist primary SKU.
message349=The product field does not match with Primary SKU. Please follow the product field format of Primary SKU.
message350=The Variant SKU cannot be created, because the Primary SKU duplicated in this file.
message351=Cannot create variant SKU because the primary SKU has not enabled variants.
message352=primary sku PRODUCT ID {0} is duplicated.
message353=Relation Error,Please upload again.
message354=Editing the Primary SKU's common column will sync with Variant SKUs. Batch updates can't exceed 10,000 SKUs, including Variants SKUs update. Please verify this.
message355=Switch primary sku Timeout
message356=Product ready method {0} can''t select Delivery District
message357=Packing Length/Packing Width/Packing Height limits exceeded: Each side must be <= 130cm and the sum of all three dimensions must be <= 180cm
message358=Weight limit exceeded: Package weight must be less than 20kg.
message359=Mainland Merchant Currency only allow RMB
message360=Exchange rate empty.
message361=Fail to update price related field, please contact your administrator.
message362=Cannot edit the relation for PRODUCT ID {0}: this product is being created or updated. Please wait and try again later.
message363=Invalid relation for PRODUCT ID {0}: product field options are unsynchronized. Please wait and try again later.
message364={0} can only input positive number
message365=Return Days do not apply to this type of product.
message366=STORE SKU ID: {0} already exists in the store.
message367=API validation error, field:{0}, reject value:{1}, message:{2}
message368=Invalid system name, system name: {0}.
message369=Cannot find product by uuid in MMS db.
message370=Update invisible error, message: {0}"
message371=No SKU was added successfully, please cancel adding or updating SKU
message372=Product Temporarily offline: We have identified some issues with the product and have temporarily offline. For more details, please refer to the related offline notification email we sent.
message373=Case Number length is out of range (maximum is 50 characters)
