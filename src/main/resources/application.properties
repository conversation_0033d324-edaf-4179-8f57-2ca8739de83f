spring.application.name=mms-product-api

spring.profiles.active=${mms_product_configmap_active:local}
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER

server.servlet.context-path=/product
server.contextPath=/product
server.port=8080

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.tomcat.init-s-q-l=SET NAMES utf8mb4

spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.open-in-view=false

log4j2.formatMsgNolookups=true

spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=105MB

spring.messages.basename=i18n/messages
