package com.shoalter.mms_product_api.schedule;

import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@Component
public class CheckTimeSchedule {

	private static final ConcurrentHashMap<Long, LocalDateTime> PRODUCT_RECORD_CHECK_TIME_MAP = new ConcurrentHashMap<>();
	private static final ConcurrentHashMap<Integer, LocalDateTime> EXPORT_HISTORY_CHECK_TIME_MAP = new ConcurrentHashMap<>();

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final BuExportHistoryRepository buExportHistoryRepository;

	@Scheduled(fixedRate = 25000)
	public void updateCheckTime() {
		LocalDateTime now = LocalDateTime.now();

		//Mistake Proofing
		PRODUCT_RECORD_CHECK_TIME_MAP.entrySet().removeIf(entry -> entry.getValue().plusHours(1).isBefore(now));
		EXPORT_HISTORY_CHECK_TIME_MAP.entrySet().removeIf(entry -> entry.getValue().plusHours(1).isBefore(now));

		if (!PRODUCT_RECORD_CHECK_TIME_MAP.isEmpty()) {
			log.info("[Start] update check time for product record ids : {}", PRODUCT_RECORD_CHECK_TIME_MAP.keySet());
			saveProductRecordRepository.updateCheckTimeByRecordIds(PRODUCT_RECORD_CHECK_TIME_MAP.keySet());
			log.info("[End] update check time for product record ids : {}", PRODUCT_RECORD_CHECK_TIME_MAP.keySet());
		}

		if (!EXPORT_HISTORY_CHECK_TIME_MAP.isEmpty()) {
			log.info("[Start] update check time for export history ids : {}", EXPORT_HISTORY_CHECK_TIME_MAP.keySet());
			buExportHistoryRepository.updateCheckTimeByIds(EXPORT_HISTORY_CHECK_TIME_MAP.keySet());
			log.info("[End] update check time for export history ids : {}", EXPORT_HISTORY_CHECK_TIME_MAP.keySet());
		}
	}

	public static void addProductRecordId(Long recordId) {
		PRODUCT_RECORD_CHECK_TIME_MAP.put(recordId, LocalDateTime.now());
	}

	public static void removeProductRecordId(Long recordId) {
		PRODUCT_RECORD_CHECK_TIME_MAP.remove(recordId);
	}

	public static void addExportHistoryId(Integer recordId) {
		EXPORT_HISTORY_CHECK_TIME_MAP.put(recordId, LocalDateTime.now());
	}

	public static void removeExportHistoryId(Integer recordId) {
		EXPORT_HISTORY_CHECK_TIME_MAP.remove(recordId);
	}
}
