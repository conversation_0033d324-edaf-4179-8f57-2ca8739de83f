package com.shoalter.mms_product_api.schedule;

import com.shoalter.mms_product_api.asynctask.HktvExportProductTask;
import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.config.product.BuExportHistoryEnum;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.HktvExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.BuExportHistoryDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.HktvExportInfoDo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class HktvExportSchedule {

	private final Executor productExportExecutor;

	private final BuExportHistoryRepository buExportHistoryRepository;
	private final HktvExportInfoRepository hktvExportInfoRepository;

	private final HktvExportProductTask hktvExportProductTask;

	private final static int CHECK_TIME_SECOND = 120;

	@Scheduled(initialDelay = 1000, fixedRate = 3000)
	public void checkHktvProductExportStatus() {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = (ThreadPoolTaskExecutor) productExportExecutor;
		int queryTaskCount = (threadPoolTaskExecutor.getCorePoolSize() - threadPoolTaskExecutor.getActiveCount()) * 4;
		if (queryTaskCount <= 0) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		List<BuExportHistoryDo> buExportHistoryDos = buExportHistoryRepository.queryCheckId(BuExportHistoryEnum.HKTV.getExportHistoryBu(), ExportStatusEnum.PROCESSING.getExportStatus(), CHECK_TIME_SECOND, queryTaskCount);
		List<Integer> filterExportList = buExportHistoryDos.stream()
			.filter(data -> data.getCheckTime() == null || new Date().getTime() - data.getCheckTime().getTime() > 1000 * CHECK_TIME_SECOND)
			.map(BuExportHistoryDo::getId)
			.collect(Collectors.toList());
		if (filterExportList.isEmpty()) {
			return;
		}

		buExportHistoryRepository.updateCheckId(BuExportHistoryEnum.HKTV.getExportHistoryBu(), checkId, ExportStatusEnum.PROCESSING.getExportStatus(), CHECK_TIME_SECOND, queryTaskCount, filterExportList);

		List<HktvExportInfoDo> waitingForExportList = hktvExportInfoRepository.findByCheckId(checkId);

		if (waitingForExportList.isEmpty()) {
			return;
		}

		waitingForExportList.forEach(hktvExportProductTask::start);
	}
}
