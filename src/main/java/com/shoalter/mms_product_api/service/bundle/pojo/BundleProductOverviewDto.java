package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.CategoryInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class BundleProductOverviewDto {
	private String uuid;

	@JsonProperty("inventory_qty")
	@SerializedName("inventory_qty")
	private Integer inventoryQty;

	@JsonProperty("match_contract")
	@SerializedName("match_contract")
	private Boolean matchContract;

	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;

	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;

	private List<ProductBarcodeDto> barcodes;

	@JsonProperty("main_photo")
	@SerializedName("main_photo")
	private String mainPhoto;

	@JsonProperty("warehouse_type")
	@SerializedName("warehouse_type")
	private String warehouseType;

	@JsonProperty("product_type_info")
	@SerializedName("product_type_info")
	private List<CategoryInfoDto> productTypeInfo;

	@JsonProperty("primary_category_info")
	@SerializedName("primary_category_info")
	private CategoryInfoDto primaryCategoryInfo;

	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	private String skuNameEn;

	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	private String skuNameCh;

	@JsonProperty("sku_name_sc")
	@SerializedName("sku_name_sc")
	private String skuNameSc;

	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;

	@JsonProperty("online_status")
	@SerializedName("online_status")
	private String onlineStatus;

	@JsonProperty("storage_type")
	@SerializedName("storage_type")
	private String storageType;

	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storefrontStoreCode;

	@JsonProperty("merchant_name")
	@SerializedName("merchant_name")
	private String merchantName;

	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;

	@JsonProperty("mms_create_time")
	@SerializedName("mms_create_time")
	private Date mmsCreateTime;

	@JsonProperty("mms_create_user")
	@SerializedName("mms_create_user")
	private String mmsCreateUser;

	@JsonProperty("mms_modified_time")
	@SerializedName("mms_modified_time")
	private Date mmsModifiedTime;

	@JsonProperty("mms_modified_user")
	@SerializedName("mms_modified_user")
	private String mmsModifiedUser;

	@JsonProperty("brand_id")
	@SerializedName("brand_id")
	private Integer brandId;

	@JsonProperty("brand_name")
	@SerializedName("brand_name")
	private String brandName;

	private String currency;

	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;

	@JsonProperty("sku_short_description_en")
	@SerializedName("sku_short_description_en")
	private String skuShortDescriptionEn;

	@JsonProperty("sku_short_description_ch")
	@SerializedName("sku_short_description_ch")
	private String skuShortDescriptionCh;

	@JsonProperty("sku_short_description_sc")
	@SerializedName("sku_short_description_sc")
	private String skuShortDescriptionSc;

	@JsonProperty("manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;

	@JsonProperty("return_days")
	@SerializedName("return_days")
	private Integer returnDays;

	@JsonProperty("pickup_days")
	@SerializedName("pickup_days")
	private String pickupDays;

	@JsonProperty("pickup_timeslot")
	@SerializedName("pickup_timeslot")
	private String pickupTimeslot;

	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;

	@JsonProperty("is_primary_sku")
	@SerializedName("is_primary_sku")
	private String isPrimarySku;

	private String visibility;

	@JsonProperty("discount_text_en")
	@SerializedName("discount_text_en")
	private String discountTextEn;

	@JsonProperty("discount_text_ch")
	@SerializedName("discount_text_ch")
	private String discountTextCh;

	@JsonProperty("discount_text_sc")
	@SerializedName("discount_text_sc")
	private String discountTextSc;

	@JsonProperty("packing_spec_en")
	@SerializedName("packing_spec_en")
	private String packingSpecEn;

	@JsonProperty("packing_spec_ch")
	@SerializedName("packing_spec_ch")
	private String packingSpecCh;

	@JsonProperty("packing_spec_sc")
	@SerializedName("packing_spec_sc")
	private String packingSpecSc;

	@JsonProperty("virtual_store")
	@SerializedName("virtual_store")
	private String virtualStore;

	@JsonProperty("bundle_setting")
	@SerializedName("bundle_setting")
	private BundleSettingResponseDto bundleSetting;

	public static BundleProductOverviewDto generateBundleProductInventoryDto(ProductMasterResultDto productMasterResultDto) {
		return BundleProductOverviewDto.builder()
			.uuid(productMasterResultDto.getUuid())
			.skuId(productMasterResultDto.getSkuId())
			.barcodes(productMasterResultDto.getBarcodes())
			.productId(productMasterResultDto.getProductId())
			.mainPhoto(productMasterResultDto.getAdditional().getHktv().getMainPhoto())
			.skuNameEn(productMasterResultDto.getSkuNameEn())
			.skuNameCh(productMasterResultDto.getSkuNameCh())
			.skuNameSc(productMasterResultDto.getSkuNameSc())
			.storageType(productMasterResultDto.getStorageTemperature())
			.sellingPrice(productMasterResultDto.getAdditional().getHktv().getSellingPrice())
			.onlineStatus(productMasterResultDto.getAdditional().getHktv().getOnlineStatus().name())
			.storefrontStoreCode(productMasterResultDto.getAdditional().getHktv().getStoreSkuId().split(StringUtil.PRODUCT_SEPARATOR)[0])
			.merchantName(productMasterResultDto.getMerchantName())
			.merchantId(productMasterResultDto.getMerchantId())
			.mmsCreateTime(productMasterResultDto.getMmsCreateTime() == null ? null : Date.from(productMasterResultDto.getMmsCreateTime().atZone(ZoneId.systemDefault()).toInstant()))
			.mmsCreateUser(productMasterResultDto.getMmsCreateUser())
			.mmsModifiedTime(productMasterResultDto.getMmsModifiedTime() == null ? null : Date.from(productMasterResultDto.getMmsModifiedTime().atZone(ZoneId.systemDefault()).toInstant()))
			.mmsModifiedUser(productMasterResultDto.getMmsModifiedUser())
			.brandId(productMasterResultDto.getBrandId())
			.currency(productMasterResultDto.getAdditional().getHktv().getCurrency())
			.originalPrice(productMasterResultDto.getOriginalPrice())
			.skuShortDescriptionEn(productMasterResultDto.getAdditional().getHktv().getSkuShortDescriptionEn())
			.skuShortDescriptionCh(productMasterResultDto.getAdditional().getHktv().getSkuShortDescriptionCh())
			.skuShortDescriptionSc(productMasterResultDto.getAdditional().getHktv().getSkuShortDescriptionSc())
			.manufacturedCountry(productMasterResultDto.getManufacturedCountry())
			.returnDays(productMasterResultDto.getAdditional().getHktv().getReturnDays())
			.pickupDays(productMasterResultDto.getAdditional().getHktv().getPickupDays())
			.pickupTimeslot(productMasterResultDto.getAdditional().getHktv().getPickupTimeslot())
			.packingBoxType(productMasterResultDto.getPackingBoxType())
			.isPrimarySku(productMasterResultDto.getAdditional().getHktv().getIsPrimarySku())
			.visibility(productMasterResultDto.getAdditional().getHktv().getVisibility())
			.discountTextEn(productMasterResultDto.getAdditional().getHktv().getDiscountTextEn())
			.discountTextCh(productMasterResultDto.getAdditional().getHktv().getDiscountTextCh())
			.discountTextSc(productMasterResultDto.getAdditional().getHktv().getDiscountTextSc())
			.packingSpecEn(productMasterResultDto.getAdditional().getHktv().getPackingSpecEn())
			.packingSpecCh(productMasterResultDto.getAdditional().getHktv().getPackingSpecCh())
			.packingSpecSc(productMasterResultDto.getAdditional().getHktv().getPackingSpecSc())
			.virtualStore(productMasterResultDto.getAdditional().getHktv().getVirtualStore())
			.bundleSetting(BundleSettingResponseDto.generateBundleSettingResponseDto(productMasterResultDto.getBundleSetting()))
			.build();
	}
}
