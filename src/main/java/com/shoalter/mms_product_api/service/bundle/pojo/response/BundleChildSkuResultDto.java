package com.shoalter.mms_product_api.service.bundle.pojo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BundleChildSkuResultDto extends ProductMasterResultDto {

	@JsonProperty(value = "setting_quantity")
	private int settingQty;

	@JsonProperty(value = "ceiling_quantity")
	private int ceilingQty;

	@JsonProperty(value = "is_loop")
	private boolean isLoop;

}
