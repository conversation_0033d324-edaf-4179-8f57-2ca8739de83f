package com.shoalter.mms_product_api.service.bundle.pojo;

import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BundleValidationData {
	private BusinessPlatformDo businessPlatformDo;
	private ContractDo contractDo;
	private StoreDo storeDo;
	private List<String> unmatchedChildSkuIds;
}
