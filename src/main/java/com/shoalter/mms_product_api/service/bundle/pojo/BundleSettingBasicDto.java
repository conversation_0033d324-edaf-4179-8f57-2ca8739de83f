package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BundleSettingBasicDto {

	@JsonProperty("is_reserve_inventory")
	@SerializedName("is_reserve_inventory")
	private Boolean isReserveInventory;

	@JsonProperty("is_active")
	@SerializedName("is_active")
	private Boolean isActive;

	private Integer priority;

	@JsonProperty("child_sku_info")
	@SerializedName("child_sku_info")
	private List<BundleChildSkuDto> childSkuInfo;
}
