package com.shoalter.mms_product_api.service.bundle.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.PackageBoxTypeCodeAndDescEnum;
import com.shoalter.mms_product_api.config.product.ProductContractMatchStatusEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.config.type.PickupDaysType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.config.type.StorageTemperatureType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandDo;
import com.shoalter.mms_product_api.dao.repository.bundle.BundleProductRepository;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductDo;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductProductInfoData;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryOverseaRepository;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractProdTermsRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractTypeRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractTypeDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantStoreDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreOverseaDeliveryRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreOverseaDeliveryDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildSkuDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleInventoryInfoRequestDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.helper.BuProductCategoryHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.MerchantHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.PromotionHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.StoreOverseaDeliveryHelper;
import com.shoalter.mms_product_api.service.product.helper.SysParamHelper;
import com.shoalter.mms_product_api.service.product.helper.ThirdPartyHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckContractProdTermResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByHktvStoreProductMasterRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductCheckDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateResponseDto;
import com.shoalter.mms_product_api.util.BigDecimalUtil;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@SuppressWarnings("ALL")
@RequiredArgsConstructor
@Service
@Slf4j
public class CheckBundleHelper {

	private static final String YOUTUBE_LINK_ID_REGEX = "[a-zA-Z0-9-_]+";
	private static final String SHELF_LIFE_REGEX = "^\\d+$";
	private static final String RM_CODE_REGEX = "^[A-Za-z0-9-|]+$";
	private static final String PHYSICAL_STORE_REGEX = "^[A-Za-z0-9-,]+$";
	private static final Pattern IMAGE_PATTERN_1 = Pattern.compile("<img.*src=(.*?)[^>]*?>");
	private static final Pattern IMAGE_PATTERN_2 = Pattern.compile("(http(s?):)([/|.\\w\\s-])*\\.(?:jpg|gif|png)");
	private static final Pattern TABLE_REGX_PATTERN = Pattern.compile("(</?tr[^>]*+>)|(</?td[^>]*+>)|(</?table[^>]*+>)");
	private static final Pattern BARCODE_PATTERN = Pattern.compile("[\\w!@#$%^&*:”<>?|=()+~\\-';\\\\,.]+");
	private static final Pattern FOUR_BYTE_EMOJI_PATTERN = Pattern.compile("[\\x{10000}-\\x{10FFFF}]");
	private final ProductStorePromotionMapper productStorePromotionMapper;
	private final ContractRepository contractRepository;
	private final ContractTypeRepository contractTypeRepository;
	private final ContractProdTermsRepository contractProdTermsRepository;
	private final SysParmRepository sysParmRepository;
	private final BuProductCategoryRepository buProductCategoryRepository;
	private final BuProductCategoryOverseaRepository buProductCategoryOverseaRepository;
	private final BrandRepository brandRepository;
	private final StoreRepository storeRepository;
	private final ProductRepository productRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final MerchantStoreRepository merchantStoreRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreOverseaDeliveryRepository storeOverseaDeliveryRepository;
	private final BundleProductRepository bundleProductRepository;

	private final ProductMasterHelper productMasterHelper;
	private final ThirdPartyHelper thirdPartyHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final MerchantHelper merchantHelper;
	private final SysParamHelper sysParamHelper;
	private final PromotionHelper promotionHelper;
	private final StoreOverseaDeliveryHelper storeOverseaDeliveryHelper;
	private final BuProductCategoryHelper buProductCategoryHelper;
	private final MessageSource messageSource;
	private final CheckProductHelper checkProductHelper;
	private final ExchangeRateHelper exchangeRateHelper;

	private final Gson gson;

	public ResponseDto<Void> checkEditBundleHandler(UserDto userDto, SaveProductRecordRowDo row, ProductMasterResultDto beforeProduct,
													SaveProductRecordDo productRecord, MembershipPricingEventSetDto checkPricingResult) {
		SingleEditProductDto bundleSingleEditDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = bundleSingleEditDto.getProduct();

		if (productMasterDto.getAdditional().getHktv() == null) {
			return ResponseDto.<Void>builder().status(1).build();
		}

		Optional<BundleProductDo> existProduct = bundleProductRepository.findByUuid(productMasterDto.getUuid());

		if (existProduct.isPresent()) {
			BundleProductProductInfoData productInfoData = existProduct.get().getProductInfo();
			if (!(productInfoData.getSkuId().equals(productMasterDto.getSkuId()) && productInfoData.getMerchantId().equals(productMasterDto.getMerchantId()))) {
				log.error("skuCode or merchantId is not the same with exist product. exist sku code: {}, edit sku id: {}; " +
								"exist sku merchant id: {}, edit sku id: {}",
						productInfoData.getSkuId(), productMasterDto.getSkuId(), productInfoData.getMerchantId(), productMasterDto.getMerchantId());
				return ResponseDto.<Void>builder().status(-1).errorMessageList(
						List.of(messageSource.getMessage("message105", null, Locale.getDefault()))).build();
			}
		} else {
			return ResponseDto.<Void>builder().status(-1).errorMessageList(
					List.of(messageSource.getMessage("message51", null, Locale.getDefault()))).build();
		}

		StoreDo store = storeRepository.findHktvStoreByStoreCode(productMasterDto.getAdditional().getHktv().getStores()).orElse(null);
		if (store == null) {
			return ResponseDto.<Void>builder().status(-1).errorMessageList(
					List.of(messageSource.getMessage("message69", null, Locale.getDefault()))).build();
		}
		Optional<BuProductCategoryDo> primaryCategoryOpt = buProductCategoryHelper.getBuProductCategoryDo(productMasterDto, beforeProduct.getAdditional().getHktv());
		if (primaryCategoryOpt.isEmpty()) {
			return ResponseDto.<Void>builder().status(-1).errorMessageList(
					List.of(messageSource.getMessage("message49", new String[]{productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode()}, null))).build();
		}

		boolean isOfflineDueToRollback = productMasterDto.getAdditional().getHktv().getOfflineDueToRollback() != null
				&& productMasterDto.getAdditional().getHktv().getOfflineDueToRollback();
		ProductCheckDto productCheckDo = ProductCheckDto.generateProductCheckDo(productMasterDto, store, primaryCategoryOpt.get(), false,
			isOfflineDueToRollback, beforeProduct.getAdditional().getHktv(), null, checkPricingResult);
		CheckProductResultDto checkProductResultDto = checkEditBundle(userDto, productCheckDo, beforeProduct, productRecord);

		return ResponseDto.<Void>builder()
				.status(checkProductResultDto.getErrorMessageList().isEmpty() ? 1 : -1)
				.errorMessageList(checkProductResultDto.getErrorMessageList()).build();
	}

	public ResponseDto<Void> checkCreateBundleHandler(UserDto userDto, SaveProductRecordRowDo row, SaveProductRecordDo productRecord) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = singleEditProductDto.getProduct();
		StoreDo store = storeRepository.findHktvStoreByStoreCode(productMasterDto.getAdditional().getHktv().getStores()).orElse(null);
		if (store == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message69", null, null)));
		}

		Optional<BuProductCategoryDo> primaryCategory = buProductCategoryHelper.getBuProductCategoryDo(productMasterDto, null);
		if (primaryCategory.isEmpty()) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message49", new String[]{productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode()}, null)));
		}

		boolean isOfflineDueToRollback = productMasterDto.getAdditional().getHktv().getOfflineDueToRollback() != null
				&& productMasterDto.getAdditional().getHktv().getOfflineDueToRollback();
		ProductCheckDto productCheckDo = ProductCheckDto.generateProductCheckDo(productMasterDto, store, primaryCategory.get(), true, isOfflineDueToRollback, null, null, null);
		CheckProductResultDto checkProductResultDto = checkCreateBundle(userDto, productCheckDo, productRecord);
		return ResponseDto.<Void>builder()
				.status(checkProductResultDto.getErrorMessageList().isEmpty() ? 1 : -1)
				.errorMessageList(checkProductResultDto.getErrorMessageList()).build();
	}


	private CheckProductResultDto checkCreateBundle(UserDto userDto, ProductCheckDto productCheckDo, SaveProductRecordDo productRecord) {

		CheckProductResultDto checkProductResultDto = checkAllBundle(userDto, productCheckDo, productRecord, null);

		List<String> errorMessageList = checkProductResultDto.getErrorMessageList();
		List<BundleChildSkuDto> childSkus = productCheckDo.getChildSkuInfo();
		List<String> childSkuUuids = childSkus.stream().map(data -> data.getUuid()).collect(Collectors.toList());
		List<ProductMasterResultDto> childSkusResponseData = productMasterHelper.requestProductsByUuid(userDto, ProductMasterSearchRequestDto.builder().uuids(childSkuUuids).build());
		errorMessageList.addAll(checkBundleSkuIds(userDto, productCheckDo.getStore().getStoreCode(), childSkus, productCheckDo.getSkuId()).getErrorMessageList());
		errorMessageList.addAll(checkChildSku(userDto, productCheckDo, childSkus, childSkusResponseData, childSkuUuids));
		errorMessageList.addAll(checkBundlePackingBoxType(productCheckDo, childSkusResponseData));
		errorMessageList.addAll(checkDeliveryDistrictAndCategoryOverseaShippable(productCheckDo.getDeliveryDistrictList(), productCheckDo, childSkusResponseData));

		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public List<String> checkBundleQty(OnlineStatusEnum onlineStatusEnum, List<BundleInventoryInfoRequestDto> mallInventoryInfo) {
		List<String> errorMessageList = new ArrayList<>();
		if(onlineStatusEnum == OnlineStatusEnum.OFFLINE && mallInventoryInfo.stream()
				.anyMatch(Predicate.not(data -> data.getSettingQuantity() == 0))){
			errorMessageList.add(messageSource.getMessage("message163", null, null));
		}
		return errorMessageList;
	}

	private Pair<Set<String>, Set<String>> findHighestPriceCategory(List<ProductMasterResultDto> childSkusResponseData) {
		Set<String> passPrimaryCategories = new HashSet<>();
		Set<String> passProductTypeCodes = new HashSet<>();

		Set<String> highestPriceSkuUuids = findHighestPriceSkusUuid(childSkusResponseData);

		childSkusResponseData.stream()
				.filter(sku -> highestPriceSkuUuids.contains(sku.getUuid()))
				.forEach(sku -> {
					passPrimaryCategories.add(sku.getAdditional().getHktv().getPrimaryCategoryCode());
					passProductTypeCodes.addAll(sku.getAdditional().getHktv().getProductTypeCode());
				});

		return Pair.of(passPrimaryCategories, passProductTypeCodes);
	}

	private List<String> checkChildSku(UserDto userDto, ProductCheckDto productCheckDo, List<BundleChildSkuDto> bundleChildSkuDtos, List<ProductMasterResultDto> childSkusResponseData, List<String> childSkuUuids) {
		List<String> errorMessageList = new ArrayList<>();

		//only 100 types of child sku allow
		if (bundleChildSkuDtos.size() > 100) {
			errorMessageList.add(messageSource.getMessage("message155", null, null));
		}

		if (productCheckDo.getOnlineStatus() == OnlineStatusEnum.ONLINE) {

			//if bundle is online, all child skus need to online
			List<String> offlineChildSkus = childSkusResponseData.stream()
					.filter(childSku -> childSku.getAdditional().getHktv().getOnlineStatus() == OnlineStatusEnum.OFFLINE)
					.map(childSku -> childSku.getSkuId())
					.collect(Collectors.toList());
			if (!offlineChildSkus.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message158", new String[]{offlineChildSkus.toString()}, null));
			}

			//if bundle is online, all child skus need to match contract prod terms
			List<String> unmatchedChildSkus = productStorePromotionMapper.findProductStatusByUuids(childSkuUuids).stream()
					.filter(Predicate.not(skuStatus -> ProductContractMatchStatusEnum.MATCH.getCode().equals(skuStatus.getStatus())))
					.map(skuStatus -> skuStatus.getSkuCode())
					.collect(Collectors.toList());
			if (!unmatchedChildSkus.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message166", new String[]{unmatchedChildSkus.toString()}, null));
			}

			//a child sku can only bind to 10 online parent sku
			List<ProductMasterResultDto> parentBundles = productMasterHelper.requestPmForGetBundlesByChildUuids(userDto, childSkuUuids.toString(), childSkuUuids);
			Map<String, Set<String>> childSkuToParentSkus = new HashMap<>();
			parentBundles.stream()
					.filter(parentSku -> parentSku.getAdditional().getHktv().getOnlineStatus() == OnlineStatusEnum.ONLINE)
					.forEach(parentBundle -> {
						parentBundle.getBundleSetting().getChildSkuInfo().forEach(childSku -> {
							childSkuToParentSkus.putIfAbsent(childSku.getChildSkuId(), new HashSet<>());
							childSkuToParentSkus.get(childSku.getChildSkuId()).add(parentBundle.getUuid());
						});
					});
			List<String> overBindChildSkuIds = childSkuToParentSkus.entrySet().stream()
					.filter(entry -> entry.getValue().size() >= 10)
					.map(Map.Entry::getKey)
					.collect(Collectors.toList());
			if (!overBindChildSkuIds.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message156", new String[]{overBindChildSkuIds.toString()}, null));
			}
		}

		return errorMessageList;
	}

	private CheckProductResultDto checkAllBundle(UserDto userDto, ProductCheckDto productCheckDto, SaveProductRecordDo productRecord, ProductMasterResultDto beforeProduct) {
		String contractType = contractRepository.findMainContractTypeInContract(productCheckDto.getContractId());

		CheckProductResultDto checkSkuIdAndProductIdResult = checkProductIdAndSkuId(productCheckDto.getSkuId(), productCheckDto.getProductId());
		List<String> errorMessageList = new ArrayList<>(checkSkuIdAndProductIdResult.getErrorMessageList());
		CheckProductResultDto checkCurrencyResult = checkCurrencyByContractType(contractType, productCheckDto.getCurrency());
		errorMessageList.addAll(checkCurrencyResult.getErrorMessageList());
		CheckProductResultDto checkBrandResult = checkBrandApproved(productCheckDto.getBrandId());
		errorMessageList.addAll(checkBrandResult.getErrorMessageList());
		CheckProductResultDto checkSkuNameResult = checkSkuName(productCheckDto.getSkuNameEn(), productCheckDto.getSkuNameCh(), productCheckDto.getSkuNameSc());
		errorMessageList.addAll(checkSkuNameResult.getErrorMessageList());
		CheckProductResultDto checkDisscountTextResult = checkDiscountText(productCheckDto.getDiscountTextEn(), productCheckDto.getDiscountTextCh(), productCheckDto.getDiscountTextSc());
		errorMessageList.addAll(checkDisscountTextResult.getErrorMessageList());
		CheckProductResultDto checkMallDollarResult = checkMallDollar(userDto, productCheckDto.getBuCode(), productCheckDto.getMallDollar(), productCheckDto.getVipMallDollar());
		errorMessageList.addAll(checkMallDollarResult.getErrorMessageList());
		CheckProductResultDto checkWarehouseResult = checkWarehouse(productCheckDto.getBuCode(), productCheckDto.getReadyMethodCode(), productCheckDto.getWarehouseId());
		errorMessageList.addAll(checkWarehouseResult.getErrorMessageList());
		CheckProductResultDto checkPackingSpecResult = checkPackingSpec(productCheckDto.getPackingSpecEn(), productCheckDto.getPackingSpecCh(), productCheckDto.getPackingSpecSc());
		errorMessageList.addAll(checkPackingSpecResult.getErrorMessageList());
		CheckProductResultDto checkVideoLinkResult = checkVideoLink(productCheckDto.getVideoLink());
		errorMessageList.addAll(checkVideoLinkResult.getErrorMessageList());
		CheckProductResultDto checkVideoLinkTextResult = checkVideoLinkText(productCheckDto.getVideoLinkTextEn(), productCheckDto.getVideoLinkTextCh(), productCheckDto.getVideoLinkTextSc(), 1);
		errorMessageList.addAll(checkVideoLinkTextResult.getErrorMessageList());
		CheckProductResultDto checkVideoLink2Result = checkVideoLink(productCheckDto.getVideoLink2());
		errorMessageList.addAll(checkVideoLink2Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText2Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn2(), productCheckDto.getVideoLinkTextCh2(), productCheckDto.getVideoLinkTextSc2(), 2);
		errorMessageList.addAll(checkVideoLinkText2Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink3Result = checkVideoLink(productCheckDto.getVideoLink3());
		errorMessageList.addAll(checkVideoLink3Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText3Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn3(), productCheckDto.getVideoLinkTextCh3(), productCheckDto.getVideoLinkTextSc3(), 3);
		errorMessageList.addAll(checkVideoLinkText3Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink4Result = checkVideoLink(productCheckDto.getVideoLink4());
		errorMessageList.addAll(checkVideoLink4Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText4Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn4(), productCheckDto.getVideoLinkTextCh4(), productCheckDto.getVideoLinkTextSc4(), 4);
		errorMessageList.addAll(checkVideoLinkText4Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink5Result = checkVideoLink(productCheckDto.getVideoLink5());
		errorMessageList.addAll(checkVideoLink5Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText5Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn5(), productCheckDto.getVideoLinkTextCh5(), productCheckDto.getVideoLinkTextSc5(), 5);
		errorMessageList.addAll(checkVideoLinkText5Result.getErrorMessageList());
		if (!productCheckDto.isSkuOfflineOrWillBeOffline()){
			CheckProductResultDto checkPrimaryCategoryResult = checkPrimaryCategory(productCheckDto.getBuCode(), productCheckDto.getPrimaryCategory().getId());
			errorMessageList.addAll(checkPrimaryCategoryResult.getErrorMessageList());
		}
		CheckProductResultDto checkBatchBrandResult = checkBatchBrand(productCheckDto.isNewProduct(), productCheckDto.getMerchantId(), productCheckDto.getStore().getId(), productCheckDto.getProductId(), productCheckDto.getBrandId(), productCheckDto.isOfflineDueToRollback());
		errorMessageList.addAll(checkBatchBrandResult.getErrorMessageList());
		CheckProductResultDto checkUrgentFlagResult = checkUrgentFlagUpdatedByMerchant(userDto, productCheckDto.getBeforeUrgent(), productCheckDto.getUrgent());
		errorMessageList.addAll(checkUrgentFlagResult.getErrorMessageList());
		CheckProductResultDto checkReadyMethodAndPackingBoxTypeResult = checkReadyMethodAndPackingBoxType(productCheckDto.getReadyMethodCode(), productCheckDto.getPackingBoxTypeCode());
		errorMessageList.addAll(checkReadyMethodAndPackingBoxTypeResult.getErrorMessageList());
		CheckProductResultDto checkLandMarkCategoryResult = checkProductLandMarkCat(productCheckDto.getBuCode(), productCheckDto.getStore(), productCheckDto.getProductTypeCodeList());
		errorMessageList.addAll(checkLandMarkCategoryResult.getErrorMessageList());
		CheckProductResultDto checkWeightLimitByDeliveryResult = checkWeightLimitByDeliveryMethod(productCheckDto.getBuCode(), productCheckDto.getDeliveryMethodCode(), productCheckDto.getWeight(), productCheckDto.getWeightUnit());
		errorMessageList.addAll(checkWeightLimitByDeliveryResult.getErrorMessageList());
		CheckProductResultDto checkTimeSlotResult = checkTimeSlot(productCheckDto.getReadyMethodCode(), productCheckDto.getPickupTimeslot());
		errorMessageList.addAll(checkTimeSlotResult.getErrorMessageList());
		CheckProductResultDto checkBuySellStoreResult = checkBuySellStore(productCheckDto.getMerchantId(), productCheckDto.getReadyMethodCode(), productCheckDto.getMinimumShelfLife(), productCheckDto.getRmCode(), productCheckDto.getPreSellFruit(), productCheckDto.getPhysicalStore(), productCheckDto.isNewProduct(), productCheckDto.getBeforeRmcode());
		errorMessageList.addAll(checkBuySellStoreResult.getErrorMessageList());
		CheckProductResultDto checkDescriptionResult = checkDescription(productCheckDto.getSkuSDescHktvEn(), productCheckDto.getSkuSDescHktvCh(), productCheckDto.getSkuSDescHktvSc(), productCheckDto.getSkuLDescHktvEn(), productCheckDto.getSkuLDescHktvCh(), productCheckDto.getSkuLDescHktvSc());
		errorMessageList.addAll(checkDescriptionResult.getErrorMessageList());
		CheckProductResultDto checkProductDataForUploadResult = checkProductDataForUpload(productCheckDto.getProductId(), productCheckDto.getSkuId(), productCheckDto.getReadyMethodCode(), productCheckDto.getMainPhoto(), productCheckDto.getOtherPhoto(), productCheckDto.getVariantProductPhoto(), productCheckDto.getAdvertisePhoto());
		errorMessageList.addAll(checkProductDataForUploadResult.getErrorMessageList());
		CheckProductResultDto checkPickupDayResult = checkPickupDay(productCheckDto.getPickupDays(), productCheckDto.getStore().getId(), productCheckDto.getReadyMethodCode());
		errorMessageList.addAll(checkPickupDayResult.getErrorMessageList());
		CheckProductResultDto checkOptionFieldAndValueResult = checkOptionFieldAndValue(productCheckDto.getField1(), productCheckDto.getValue1(), productCheckDto.getField2(), productCheckDto.getValue2(), productCheckDto.getField3(), productCheckDto.getValue3());
		errorMessageList.addAll(checkOptionFieldAndValueResult.getErrorMessageList());
		CheckProductResultDto checkSizeSystemResult = checkSizeSystem(productCheckDto.getSizeSystem(), productCheckDto.getSize());
		errorMessageList.addAll(checkSizeSystemResult.getErrorMessageList());
		CheckProductResultDto checkColorFamilyResult = checkColorFamily(productCheckDto.getColorFamiliar(), productCheckDto.getColorEn());
		errorMessageList.addAll(checkColorFamilyResult.getErrorMessageList());
		CheckProductResultDto checkPricesResult = checkOriginalPrice(productCheckDto.getOriginalPrice(), productCheckDto.getSellingPrice());
		errorMessageList.addAll(checkPricesResult.getErrorMessageList());
		CheckProductResultDto checkMinimumShelfLifeResult = checkMinimumShelfLife(productCheckDto.getMinimumShelfLife());
		errorMessageList.addAll(checkMinimumShelfLifeResult.getErrorMessageList());
		CheckProductResultDto checkRemovalServiceResult = checkRemovalService(productCheckDto.getRemovalService(), productCheckDto.getProductTypeCodeList(), productCheckDto.getPrimaryCategory());
		errorMessageList.addAll(checkRemovalServiceResult.getErrorMessageList());
		CheckProductResultDto checkOverseaDeliveryDistrictFormatResult = checkOverseaDeliveryDistrictFormat(productCheckDto.getDeliveryDistrictList());
		errorMessageList.addAll(checkOverseaDeliveryDistrictFormatResult.getErrorMessageList());
		CheckProductResultDto checkOverseaDeliveryDistrictMatchedStoreSettingResult = checkOverseaDeliveryDistrictMatchedStoreSetting(productCheckDto.getDeliveryDistrictList(), productCheckDto.getStore());
		errorMessageList.addAll(checkOverseaDeliveryDistrictMatchedStoreSettingResult.getErrorMessageList());
		CheckProductResultDto checkOverseaDeliveryDistrictAndDeliveryMethodResult = checkOverseaDeliveryDistrictAndDeliveryMethod(productCheckDto.getDeliveryDistrictList(), productCheckDto.getDeliveryMethodCode());
		errorMessageList.addAll(checkOverseaDeliveryDistrictAndDeliveryMethodResult.getErrorMessageList());
		List<String> originalOverseaDeliveryDistrictList = beforeProduct == null ? null : beforeProduct.getAdditional().getHktv().getDeliveryDistrict();
		CheckProductResultDto checkOverseaDeliveryDistrictRestrictionResultDto = checkOverseaDeliveryDistrictRestriction(userDto, productCheckDto.getDeliveryDistrictList(), originalOverseaDeliveryDistrictList);
		errorMessageList.addAll(checkOverseaDeliveryDistrictRestrictionResultDto.getErrorMessageList());
		CheckProductResultDto checkVirtualStoreResult = checkVirtualStore(productCheckDto.getMerchantId(), productCheckDto.getVirtualStore());
		errorMessageList.addAll(checkVirtualStoreResult.getErrorMessageList());
		CheckProductResultDto checkUserMaxResult = checkUserMax(contractType, productCheckDto.getUserMax());
		errorMessageList.addAll(checkUserMaxResult.getErrorMessageList());
		CheckProductResultDto checkStorageTypeResult = checkStorageType(productCheckDto.isNewProduct(), productCheckDto.getReadyMethodCode(), productCheckDto.getStorageType(), productCheckDto.getBeforeStorageType());
		errorMessageList.addAll(checkStorageTypeResult.getErrorMessageList());
		CheckProductResultDto checkProductTypeResult = checkProductType(productCheckDto.getBuCode(), productCheckDto.getProductTypeCodeList(), productCheckDto.isSkuOfflineOrWillBeOffline());
		errorMessageList.addAll(checkProductTypeResult.getErrorMessageList());
		CheckProductResultDto checkProductReadyDaysResult = checkProductReadyDays(productCheckDto.getReadyMethodCode(), productCheckDto.getProductReadyDays());
		errorMessageList.addAll(checkProductReadyDaysResult.getErrorMessageList());
		CheckProductResultDto checkReturnDayResult = checkProductHelper.checkReturnDay(productCheckDto.getReturnDays(), contractType);
		errorMessageList.addAll(checkReturnDayResult.getErrorMessageList());
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public CheckProductResultDto checkProductPackingAndBarcode(
			String buCode, String readyMethodCode, BuProductCategoryDo primaryCategory,
			String packingBoxTypeCode, BigDecimal weight, BigDecimal height, BigDecimal depth, BigDecimal length,
			String storageTemperature, List<ProductBarcodeDto> barcodeDtoList) {
		List<String> errorMessageList = new ArrayList<>();
		errorMessageList.addAll(checkCategoryCodeAndPackingBoxType(buCode, primaryCategory, packingBoxTypeCode).getErrorMessageList());
		errorMessageList.addAll(checkPackingInformation(readyMethodCode, weight, height, depth, length).getErrorMessageList());
		errorMessageList.addAll(checkPackingBoxType(packingBoxTypeCode, storageTemperature).getErrorMessageList());
		errorMessageList.addAll(checkBarCode(barcodeDtoList, readyMethodCode).getErrorMessageList());
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public CheckProductResultDto checkProductCommonlyUsed(String uuid, BigDecimal oldPrice, BigDecimal newPrice) {
		CheckProductResultDto checkProductResult = checkProductPriceUpdateDuringPromotion(uuid, oldPrice, newPrice);
		List<String> errorMessageList = new ArrayList<>(checkProductResult.getErrorMessageList());
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public CheckProductResultDto checkProductIdAndSkuId(String skuId, String productId) {
		List<String> errorMessageList = new ArrayList<>();
		if (!skuId.matches(ConstantType.REGEX_CONDITION_PRODUCT_CODE)) {
			errorMessageList.add(messageSource.getMessage("message65", null, null));
		}
		if (skuId.contains("_S_")) {
			errorMessageList.add(messageSource.getMessage("message64", null, null));
		}
		if (!productId.matches(ConstantType.REGEX_CONDITION_PRODUCT_CODE)) {
			errorMessageList.add(messageSource.getMessage("message53", new String[]{productId}, null));
		}
		if (productId.contains("_S_")) {
			errorMessageList.add(messageSource.getMessage("message52", new String[]{productId}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkProductType(String buCode, List<String> productTypeCodeList, boolean skuOfflineOrWillBeOffline) {
		List<String> errorMessageList = new ArrayList<>();

		if (!skuOfflineOrWillBeOffline) {
			for (String productTypeCode : productTypeCodeList) {
				BuProductCategoryDo buProductCategoryDo = buProductCategoryRepository.findByBuCodeAndProductCatCode(buCode, productTypeCode);
				if (buProductCategoryDo != null) {
					String subCode = productTypeCode.substring(productTypeCode.length() - 1);
					if (!"1".equalsIgnoreCase(subCode)) {
						errorMessageList.add(messageSource.getMessage("message30", null, null));
					}
				} else {
					errorMessageList.add(messageSource.getMessage("message56", new String[]{String.valueOf(productTypeCode)}, null));
				}
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkPrimaryCategory(String buCode, Integer primaryCategoryId) {
		List<String> errorMessageList = new ArrayList<>();
		BuProductCategoryDo buProductCategoryDo = buProductCategoryRepository.findByBuCodeAndIndex(buCode, primaryCategoryId);
		if (buProductCategoryDo == null) {
			errorMessageList.add(messageSource.getMessage("message47", null, null));
		} else {
			String primaryCategoryCode = buProductCategoryDo.getProductCatCode();
			int count = sysParmRepository.countBySegmentAndBuCodeAndParmValue("PRIMARY_CAT_LIST", buCode, primaryCategoryCode);
			if (count > 0) {
				errorMessageList.add(messageSource.getMessage("message47", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckContractProdTermResultDto checkContractProdTerm(
			String insuranceContractProdTermName, Integer contractId, Integer storeId, String productReadyMethodCode, String skuId,
			BuProductCategoryDo primaryCategory, Integer brandId) {
		List<String> errorMessageList = new ArrayList<>();


		ContractProdTermsDo contractProdTermsDo = null;
		//If select contract type is “Fixed cost contract”
		//Need to bypass the lookup terms logic, direct return the user selected terms to screen
		ContractTypeDo contractTypeDo = contractTypeRepository.findByContractId(contractId);
		boolean fixedCostType = (contractTypeDo != null && "FC".equalsIgnoreCase(contractTypeDo.getCode()));
		Integer lookupContractId = contractId;
		List<ContractDo> supplementaryContractList =
				contractRepository.findSupplementaryContractList(contractId, new Date());
		if (supplementaryContractList != null && !supplementaryContractList.isEmpty()) {
			lookupContractId = supplementaryContractList.get(0).getId();
		}
		if (fixedCostType) {
			if (StringUtil.isEmpty(insuranceContractProdTermName)) {
				errorMessageList.add(messageSource.getMessage("message125", null, null));
			}

			String termName = insuranceContractProdTermName.split("\\(")[0];
			List<ContractProdTermsDo> contractProdTermsDoList = contractProdTermsRepository.findByTermsNameAndContractIdAndStoreId(termName, lookupContractId, storeId, Sort.by(Sort.Direction.DESC, "id"));
			if (CollectionUtil.isNotEmpty(contractProdTermsDoList)) {
				// 前端傳過來的termName是由termName和fixedCost組合而成的 ex：termName(12)
				String fixedCostValue = insuranceContractProdTermName.split("\\(")[1].split("\\)")[0];
				if (StringUtil.isNotEmpty(fixedCostValue)) {
					BigDecimal fixedCost = new BigDecimal(fixedCostValue);
					ContractProdTermsDo fixedCostContractProdTerm = contractProdTermsDoList.stream()
							.filter(contractProdTerms -> contractProdTerms.getFixedCost().compareTo(fixedCost) == 0)
							.findFirst()
							.orElse(null);
					if (fixedCostContractProdTerm != null) {
						contractProdTermsDo = fixedCostContractProdTerm;
					} else {
						contractProdTermsDo = contractProdTermsDoList.get(0);
					}
				} else {
					contractProdTermsDo = contractProdTermsDoList.get(0);
				}
			}
		} else {
			String productCatCode = primaryCategory.getProductCatCode();
			String productPrimaryCategoryPrefix = productCatCode.substring(0, 4);
			log.info(String.format("contractId = %d, storeId = %d, skuCode = %s, brandId = %d, productCatCode = %s, productPrimaryCategoryPrefix = %s, productReadyMethod = %s",
					lookupContractId, storeId, skuId, brandId, productCatCode, productPrimaryCategoryPrefix, productReadyMethodCode));
			List<ContractProdTermsDo> contractProdTermsDoList = contractProdTermsRepository.findMatchStore(lookupContractId, storeId, skuId, brandId, productReadyMethodCode, productCatCode);
			if (CollectionUtil.isNotEmpty(contractProdTermsDoList)) {
				contractProdTermsDo = contractProdTermsDoList.get(0);
			}
		}

		if (contractProdTermsDo == null) {
			errorMessageList.add(messageSource.getMessage("message12", null, null));
		}

		return CheckContractProdTermResultDto.builder().contractProdTerms(contractProdTermsDo)
				.errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkBundleSkuIds(UserDto userDto, String store, List<BundleChildSkuDto> bundleChildSkuDtos, String parentSkuId) {
		List<String> errorMessageList = new ArrayList<>();

		List<String> requestSkuIds = new ArrayList<>(bundleChildSkuDtos.stream()
				.map(BundleChildSkuDto::getChildSkuId)
				.collect(Collectors.toList()));
		requestSkuIds.add(parentSkuId);
		CheckSkuIdByHktvStoreProductMasterRequestDto checkSkuIdDto = new CheckSkuIdByHktvStoreProductMasterRequestDto(store, requestSkuIds);
		CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByHktvStore(userDto, checkSkuIdDto);

		if (checkSkuIdResultDto == null) {
			log.error("Unable to request product master check sku.");
			errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR}, null));
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}

		//parent sku id should not exist
		if (checkSkuIdResultDto.getExistsSkus().contains(parentSkuId)) {
			errorMessageList.add(messageSource.getMessage("message6", new String[]{parentSkuId}, null));
		}

		//child sku ids should exist
		requestSkuIds.remove(parentSkuId);
		requestSkuIds.removeAll(checkSkuIdResultDto.getExistsSkus());
		if (!requestSkuIds.isEmpty()) {
			errorMessageList.add(messageSource.getMessage("message154", new String[]{String.join(", ", requestSkuIds)}, null));
		}

		return CheckProductResultDto.generate(errorMessageList);
	}

	public CheckProductResultDto checkCategoryCodeAndPackingBoxType(
			String buCode, BuProductCategoryDo primaryCategory, String packingBoxTypeCode) {
		List<String> errorMessageList = new ArrayList<>();

		boolean checkPass = false;
		List<SysParmDo> categoryRestrictionList =
				sysParmRepository.findBySegmentAndBuCodeAndCode("CATEGORY_RESTRICTION", buCode, packingBoxTypeCode);
		String primaryCategoryCode = primaryCategory.getProductCatCode();

		if (CollectionUtil.isEmpty(categoryRestrictionList)) {
			checkPass = true;
		} else {
			for (SysParmDo categoryRestriction : categoryRestrictionList) {
				String[] checkCategoryCodes = categoryRestriction.getParmValue().split(",");
				for (String checkCategoryCode : checkCategoryCodes) {
					if (primaryCategoryCode.startsWith(checkCategoryCode)) {
						checkPass = true;
						break;
					}
				}
			}
		}
		if (!checkPass) {
			errorMessageList.add(messageSource.getMessage("message46", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkCurrencyByContractType(String contractType, String currency) {
		List<String> errorMessageList = new ArrayList<>();
		Set<String> currenctCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.CURRENCY.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (currency != null && !currenctCodes.contains(currency)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Currency"}, null));
			// mainland contract currency must be RMB
		} else if (currency != null && contractType != null && ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)) {
			if (!CurrencyEnum.RMB.name().equals(currency)) {
				errorMessageList.add(messageSource.getMessage("message359", null, null));
			}
			BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);
			if (rmbRate == null) {
				errorMessageList.add(messageSource.getMessage("message361", null, null));
			}
		} else if (currency != null && !CurrencyEnum.HKD.name().equals(currency)) {
			errorMessageList.add(messageSource.getMessage("message284", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkBrandApproved(Integer brandId) {
		List<String> errorMessageList = new ArrayList<>();
		BrandDo brand = brandRepository.findById(brandId).orElseThrow(() -> new SystemException("Brand doesn't exist"));
		if (!"A".equals(brand.getStatus())) {
			errorMessageList.add(messageSource.getMessage("message72", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkProductPriceUpdateDuringPromotion(String uuid, BigDecimal oldPrice, BigDecimal newPrice) {
		List<String> errorMessageList = new ArrayList<>();
		Integer count = productStorePromotionMapper.countProductStorePromotionByUuId(uuid);
		if (count != null && count > 0 && !BigDecimalUtil.isEqual(oldPrice, newPrice)) {
			errorMessageList.add(messageSource.getMessage("message70", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkMallDollar(
			UserDto userDto, String buCode, BigDecimal mallDollar, BigDecimal vipMallDollar) {
		List<String> errorMessageList = new ArrayList<>();
		String roleCode = userDto.getRoleCode();

		//Update Product Detail, Check MallDollar
		List<String> changeMalldollarRolecodeList = List.of("RM", "RML", "RMO");
		if (changeMalldollarRolecodeList.contains(roleCode)) {
			if (mallDollar != null) {
				if (mallDollar.compareTo(BigDecimal.valueOf(mallDollar.intValue())) != 0) {
					errorMessageList.add(messageSource.getMessage("message26", null, null));
				}

				String[] mallDollarList = findMallDollar(buCode);
				if (!checkMallDollerInRange(mallDollarList, mallDollar)) {
					errorMessageList.add(messageSource.getMessage("message25", mallDollarList, null));
				}

			}
			if (vipMallDollar != null) {
				if (vipMallDollar.compareTo(BigDecimal.valueOf(vipMallDollar.intValue())) != 0) {
					errorMessageList.add(messageSource.getMessage("message83", null, null));
				}

				String[] vipMallDollarList = findVipMallDollars(buCode);
				if (!checkMallDollerInRange(vipMallDollarList, vipMallDollar)) {
					errorMessageList.add(messageSource.getMessage("message82", vipMallDollarList, null));
				}
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private String[] findMallDollar(String buCode) {
		String[] strings = null;
		List<SysParmDo> sysParmDoList = sysParmRepository.
				findBySegmentAndBuCodeAndCode(SysParmSegment.MALL_DOLLAR, buCode, "MALL_DOLLAR");
		if (CollectionUtil.isNotEmpty(sysParmDoList)) {
			SysParmDo parmVo = sysParmDoList.get(0);
			String parmValue = parmVo.getParmValue();
			if (StringUtil.isNotEmpty(parmValue)) {
				strings = parmValue.split("-");
			}
		}
		return strings;
	}

	private boolean checkMallDollerInRange(String[] range,BigDecimal mallDollar){
		boolean result=false;
		if(range!=null && range.length==2){
			String min = range[0];
			String max = range[1];
			try {
				if (mallDollar.doubleValue() >= Double.parseDouble(min) && mallDollar.doubleValue() <= Double.parseDouble(max)) {
					result=true;
				}
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
		}
		return result;

	}

	private String[] findVipMallDollars(String buCode) {
		String[] vipMallDollars = null;
		List<SysParmDo> parList = sysParmRepository.
				findBySegmentAndBuCodeAndCode(SysParmSegment.MALL_DOLLAR, buCode, "VIP_MALL_DOLLAR");
		if (CollectionUtil.isNotEmpty(parList)) {
			SysParmDo parmVo = parList.get(0);
			String parmValue = parmVo.getParmValue();
			if (StringUtil.isNotEmpty(parmValue))
				vipMallDollars = parmValue.split("-");
		}
		return vipMallDollars;
	}

	public CheckProductResultDto checkWarehouse(
			String buCode, String readyMethodCode, Integer storeWarehouseId) {

		List<String> errorMessageList = new ArrayList<>();
		if (storeWarehouseId == null){
			errorMessageList.add(messageSource.getMessage("message101", null, null));
		}else {
			try{
				StoreWarehouseDo storeWarehouseDo = storeWarehouseRepository.findById(storeWarehouseId).
						orElseThrow(() -> new SystemException(messageSource.getMessage("message101", null, null)));

				//Search merchant id to check if it is buy sell merchant
				MerchantStoreDo merchantStoreDo = merchantStoreRepository.findByStoreId(storeWarehouseDo.getStoreId()).
						orElseThrow(() -> new SystemException(messageSource.getMessage("message102", null, null))
						);

				Integer merchantId = merchantStoreDo.getMerchantId();

				if (storeWarehouseDo.getSeqNo() == null) {
					errorMessageList.add(messageSource.getMessage("message22", null, null));
				} else if (!checkMatchWarehouseSeqNoForReadyMethod(buCode, String.valueOf(storeWarehouseDo.getSeqNo()), readyMethodCode, merchantId)) {
					errorMessageList.add(messageSource.getMessage("message22", null, null));
				} else if (!checkStoreWarehouseEntity(storeWarehouseDo)) {
					errorMessageList.add(messageSource.getMessage("message39", null, null));
				} else {
					if (storeWarehouseDo.getLastSynchronizeDate() == null) {
						//3 .check syn OIX
						errorMessageList.add(messageSource.getMessage("message39", null, null));
					}
				}
			}catch (SystemException e){
				errorMessageList.add(e.getMessage());
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkPackingSpec(String packingSpecEn, String packingSpecCh, String packingSpecSc) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(packingSpecEn)) {
			if (packingSpecEn.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (Eng)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecEn).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (Eng)"}, null));
			}
		}
		if (StringUtil.isNotEmpty(packingSpecCh)) {
			if (packingSpecCh.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (Chi)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecCh).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (Chi)"}, null));
			}
		}
		if (StringUtil.isNotEmpty(packingSpecSc)) {
			if (packingSpecSc.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (in Simplified)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecSc).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (in Simplified)"}, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();

	}

	public boolean checkMatchWarehouseSeqNoForReadyMethod(
			String buCode, String seqNo, String readyMethodCode, Integer merchantId) {
		List<String> readyMethodSeqNoList = findReadyMethodSeqNo(buCode, readyMethodCode, merchantId);
		if (CollectionUtil.isEmpty(readyMethodSeqNoList)) {
			return false;
		}

		log.debug(String.format("seqNo = %s, seqNoList = %s", seqNo, readyMethodSeqNoList));
		boolean match = false;
		if (CollectionUtil.isNotEmpty(readyMethodSeqNoList)) {
			for (String readyMethodSeqNoStr : readyMethodSeqNoList) {
				if (readyMethodSeqNoStr.equals(seqNo)) {
					match = true;
					break;
				}
			}
		}

		return match;
	}

	private List<String> findReadyMethodSeqNo(String buCode, String readyMethodCode, Integer merchantId) throws SystemException {
		List<String> list = new ArrayList<>();

		if (StringUtil.isEmpty(readyMethodCode) || merchantId == null) {
			return list;
		}

		List<SysParmDo> parmVoList;
		if (ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(readyMethodCode)) {
			parmVoList = sysParmRepository.findBySegmentAndBuCodeAndCodeAndShortDesc(SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE, buCode, readyMethodCode, "11");
		} else {
			parmVoList = sysParmRepository.findBySegmentAndBuCodeAndCode(SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE, buCode, readyMethodCode);
		}

		if (CollectionUtil.isEmpty(parmVoList)) {
			return list;
		}
		SysParmDo parmVo = parmVoList.get(0);
		String longDesc = parmVo.getLongDesc();
		if (StringUtil.isEmpty(longDesc)) {
			return list;
		}
		String[] descArray = longDesc.split(",");
		Set<String> set = new HashSet<>();
		for (String seqStr : descArray) {
			if (set.add(seqStr) && StringUtil.isNotEmpty(seqStr))
				list.add(seqStr);
		}

		return list;
	}

	private boolean isBuySellMerchant(Integer merchantId) {
		List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndCode("BUYSELL_MERCHANT", "BUYSELL_MERCHANT");
		HashSet<String> buySellMerchantSet = new HashSet<>();
		for (SysParmDo sysParmDo : sysParmDoList) {
			String[] merchantIdArray = sysParmDo.getParmValue().split(",");
			buySellMerchantSet.addAll(Arrays.asList(merchantIdArray));
		}
		return buySellMerchantSet.contains(String.valueOf(merchantId));
	}

	public boolean checkStoreWarehouseEntity(StoreWarehouseDo storeWarehouseDo) {
		return !StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress1()) &&
				!StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress2()) &&
				!StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress3()) &&
				!StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress4()) &&
				!StringUtil.isEmpty(storeWarehouseDo.getWarehouseContactName()) &&
				!StringUtil.isEmpty(storeWarehouseDo.getWarehouseContactPhoneNo());
	}

	public CheckProductResultDto checkVideoLink(String videoLink) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(videoLink)) {
			String youtubeLinkId = videoLink.trim().substring(1);
			if (!videoLink.trim().startsWith("=") || !youtubeLinkId.matches(YOUTUBE_LINK_ID_REGEX)) {
				errorMessageList.add(messageSource.getMessage("message79", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkVideoLinkText(String videoLinkTextEn, String videoLinkTextCh, String videoLinkTextSc, int index) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotNullOrBlank(videoLinkTextEn)) {
			if (videoLinkTextEn.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message81", null, null));
			}
			if (videoLinkTextEn.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "in English", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}
		if (StringUtil.isNotNullOrBlank(videoLinkTextCh)) {
			if (videoLinkTextCh.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message80", null, null));
			}
			if (videoLinkTextCh.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "in Chinese", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}
		if (StringUtil.isNotNullOrBlank(videoLinkTextSc)) {
			if (videoLinkTextSc.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message330", null, null));
			}
			if (videoLinkTextSc.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "in Simplified", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkPrimarySku(ProductDo productDo, String isPrimarySku, boolean isBatchUpload) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(isPrimarySku) &&
				StringUtil.isNotEmpty(productDo.getIsPrimarySku()) &&
				!productDo.getIsPrimarySku().equals(isPrimarySku)
				&& isBatchUpload
		) {
			errorMessageList.add(messageSource.getMessage("message24", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkBatchBrand(
			boolean isNewProduct, Integer merchantId, Integer storeId,
			String productId, Integer brandId, boolean offlineDueToRollback) {
		List<String> errorMessageList = new ArrayList<>();
		List<ProductDo> productList = productRepository.findByStoreIdAndMerchantIdAndIsPrimarySkuAndProductCode(storeId, merchantId, "Y", productId);

		if (CollectionUtil.isNotEmpty(productList) && Boolean.FALSE.equals(offlineDueToRollback)) {
			ProductDo product = productList.get(0);
			if (isNewProduct &&
					!brandId.equals(product.getBrandId())) {
				errorMessageList.add(messageSource.getMessage("message7", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkUrgentFlagUpdatedByMerchant(
			UserDto userDto, String beforeUrgent, String currentUrgent) {
		List<String> errorMessageList = new ArrayList<>();
		if (RoleCode.MERCHANT_ADMIN.equalsIgnoreCase(userDto.getRoleCode()) ||
				RoleCode.MERCHANT.equalsIgnoreCase(userDto.getRoleCode())) {
			if (currentUrgent == null) {
				currentUrgent = "";
			}
			if (beforeUrgent == null) {
				beforeUrgent = "";
			}
			if (!beforeUrgent.equalsIgnoreCase(currentUrgent)) {
				errorMessageList.add(messageSource.getMessage("message76", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkReadyMethodAndPackingBoxType(String readyMethodCode, String packingBoxTypeCode) {
		List<String> errorMessageList = new ArrayList<>();
		String merchantDelivery = "M";
		String eVoucher = "E";

		if (merchantDelivery.equals(readyMethodCode) && "U".equalsIgnoreCase(packingBoxTypeCode)) {
			errorMessageList.add(messageSource.getMessage("message73", null, null));
		}
		if (eVoucher.equals(readyMethodCode) && !"L".equalsIgnoreCase(packingBoxTypeCode)) {
			String packingBoxTypeDesc = findPackBoxTypeDesc("L");
			errorMessageList.add(messageSource.getMessage("message15", new String[]{packingBoxTypeDesc}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public String findPackBoxTypeDesc(String packingBoxTypeCode) {
		List<SysParmDo> list = sysParmRepository.findBySegmentAndCode("PACK_BOX_TYPE", packingBoxTypeCode);
		if (CollectionUtil.isNotEmpty(list)) {
			String desc = list.get(0).getShortDesc();
			if (StringUtil.isNotEmpty(desc)) {
				return desc;
			}
		}
		return null;
	}

	public CheckProductResultDto checkProductLandMarkCat(String buCode, StoreDo storeDo, List<String> productTypeCodeList) {
		List<String> errorMessageList = new ArrayList<>();
		List<String> productCategoryCodeList = new ArrayList<>();
		for (String productTypeCode : productTypeCodeList) {
			buProductCategoryRepository.findByProductCatCode(ConstantType.PLATFORM_CODE_HKTV, productTypeCode).ifPresent(buProductCategoryDo -> productCategoryCodeList.add(buProductCategoryDo.getProductCatCode()));
		}
		if (CollectionUtil.isNotEmpty(productCategoryCodeList)) {
			List<SysParmDo> landmarkCatCodeSysParmList = sysParmRepository.findBySegmentAndBuCode("LANDMARK_CAT_CODE", buCode);

			if (!"Y".equals(storeDo.getStoreLandmarkFlag()) && CollectionUtil.isNotEmpty(landmarkCatCodeSysParmList)
					&& isLandMarkStoreProduct(landmarkCatCodeSysParmList.get(0).getParmValue(), productCategoryCodeList)) {
				errorMessageList.add(messageSource.getMessage("message71", new String[]{landmarkCatCodeSysParmList.get(0).getParmValue()}, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private boolean isLandMarkStoreProduct(String landMarkCatCode, List<String> categoryCodeList) {
		for (String categoryCode : categoryCodeList) {
			if (categoryCode.startsWith(landMarkCatCode)) {
				return true;
			}
		}
		return false;
	}

	public CheckProductResultDto checkPackingInformation(
			String readyMethodCode, BigDecimal weight, BigDecimal height, BigDecimal depth, BigDecimal length) {
		List<String> errorMessageList = new ArrayList<>();
		// If product ready method is "Merchant Delivery" or "E-Voucher" or "DISPLAY_STORE", no need to check packing information.
		String[] unnecessaryCheckingArray = {ProductReadyMethodType.MERCHANT_DELIVERY, ProductReadyMethodType.E_VOUCHER, ProductReadyMethodType.DISPLAY_STORE};
		if (Arrays.stream(unnecessaryCheckingArray).noneMatch(e -> e.equals(readyMethodCode))) {

			if (weight == null)
				errorMessageList.add(messageSource.getMessage("message92", null, null));
			else if (weight.compareTo(BigDecimal.valueOf(0)) == 0)
				errorMessageList.add(messageSource.getMessage("message87", null, null));

			if (height == null)
				errorMessageList.add(messageSource.getMessage("message84", null, null));
			else if (height.compareTo(BigDecimal.valueOf(0)) == 0)
				errorMessageList.add(messageSource.getMessage("message88", null, null));

			if (depth == null)
				errorMessageList.add(messageSource.getMessage("message85", null, null));
			else if (depth.compareTo(BigDecimal.valueOf(0)) == 0)
				errorMessageList.add(messageSource.getMessage("message89", null, null));

			if (length == null)
				errorMessageList.add(messageSource.getMessage("message86", null, null));
			else if (length.compareTo(BigDecimal.valueOf(0)) == 0)
				errorMessageList.add(messageSource.getMessage("message90", null, null));

		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkWeightLimitByDeliveryMethod(
			String buCode, String deliveryMethodCode, BigDecimal weight, String weightUnit) {

		List<String> errorMessageList = new ArrayList<>();
		List<SysParmDo> deliveryMethodList = sysParmRepository.findBySegmentAndBuCodeAndCode("DELIVERY_METHOD", buCode, deliveryMethodCode);
		if (!deliveryMethodList.isEmpty() && StringUtil.isNotEmpty(deliveryMethodList.get(0).getParmValue())) {
			String deliveryMethodValue = deliveryMethodList.get(0).getParmValue();
			List<SysParmDo> weightLimitList = sysParmRepository.findBySegmentAndBuCodeAndCode("WEIGHT_LIMIT_FOR_PACKAGE", buCode, deliveryMethodValue);

			if (!weightLimitList.isEmpty() && StringUtil.isNotEmpty(weightLimitList.get(0).getParmValue())) {
				BigDecimal weightLimitForPackage;
				try {
					// According to Delivery Method, get the weight limit of this package. The default weight unit is kilogram.
					weightLimitForPackage = new BigDecimal(weightLimitList.get(0).getParmValue());
				} catch (Exception e) {
					weightLimitForPackage = null;
				}
				BigDecimal originPackageWeight = formatPackageWeightToKilogram(weight, weightUnit);
				// The weight limit should be greater than 0, if not, it will not check the package weight.
				if (weightLimitForPackage != null &&
						originPackageWeight != null &&
						weightLimitForPackage.compareTo(new BigDecimal(0)) > 0 &&
						originPackageWeight.compareTo(weightLimitForPackage) > 0) {
					// The package weight cannot over the weight limit of this package
					errorMessageList.add(messageSource.getMessage("message17", new String[]{weightLimitList.get(0).getParmValue(), deliveryMethodList.get(0).getShortDesc()}, null));
				}
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private BigDecimal formatPackageWeightToKilogram(BigDecimal weight, String weightUnit) {
		if (weight != null && weightUnit != null) {
			BigDecimal convertWeight;
			if ("g".equals(weightUnit)) {
				convertWeight = weight.divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_DOWN);
			} else {
				convertWeight = weight;
			}
			return convertWeight;
		} else {
			return null;
		}
	}

	public CheckProductResultDto checkTimeSlot(String readyMethodCode, String pickupTimeslot) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(readyMethodCode) && StringUtil.isNotEmpty(pickupTimeslot)) {
			if ((readyMethodCode.equals(ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE) || readyMethodCode.equals(ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY))
					&& !pickupTimeslot.equals("AM/PM")) {
				errorMessageList.add(messageSource.getMessage("message67", null, null));
			}
			if ((readyMethodCode.equals(ProductReadyMethodType.CONSIGNMENT) || readyMethodCode.equals(ProductReadyMethodType.THIRD_PARTY))
					&& !pickupTimeslot.equals("AM/PM/EV")) {
				errorMessageList.add(messageSource.getMessage("message148", null, null));
			}
			if(ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB.equals(readyMethodCode)
					&& !pickupTimeslot.equals("AM/PM/EV")){
				errorMessageList.add(messageSource.getMessage("message152", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkBuySellStore(
			Integer merchantId, String productReadyMethod,
			Integer minimumShelfLife, String rmCode, String preSellFruit, String physicalStore, boolean isNewProduct, String beforeRmCode) {
		List<String> errorMessageList = new ArrayList<>();
		if (isBuySellMerchant(merchantId)) {
			CheckProductResultDto checkMinimumShelfLifeResult = checkMinimumShelfLife(minimumShelfLife);
			errorMessageList.addAll(checkMinimumShelfLifeResult.getErrorMessageList());
			CheckProductResultDto checkRmCodeResult = checkRmCode(productReadyMethod, isNewProduct, rmCode, beforeRmCode);
			errorMessageList.addAll(checkRmCodeResult.getErrorMessageList());
			CheckProductResultDto checkPreSellFruitResult = checkPreSellFruit(preSellFruit, productReadyMethod, physicalStore);
			errorMessageList.addAll(checkPreSellFruitResult.getErrorMessageList());
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkMinimumShelfLife(Integer minimumShelfLife) {
		List<String> errorMessageList = new ArrayList<>();
		if (null != minimumShelfLife && !String.valueOf(minimumShelfLife).matches(SHELF_LIFE_REGEX)) {
			errorMessageList.add(messageSource.getMessage("message63", null, null));
		}
		if (null != minimumShelfLife && minimumShelfLife < 1) {
			errorMessageList.add(messageSource.getMessage("message114", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkRmCode(String productReadyMethod, boolean isNewProduct, String currentRmCode, String beforeRmCode) {
		List<String> errorMessageList = new ArrayList<>();

		if (!ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productReadyMethod)) {
			return CheckProductResultDto.generate(errorMessageList);
		}

		if (isNewProduct) {
			if (StringUtil.isEmpty(currentRmCode)) {
				errorMessageList.add(messageSource.getMessage("message37", null, null));
			}
			if (StringUtil.isNotEmpty(currentRmCode) && !currentRmCode.matches(RM_CODE_REGEX)) {
				errorMessageList.add(messageSource.getMessage("message60", null, null));
			}
		} else {
			if (!StringUtil.generateDefaultStringValue(beforeRmCode).equalsIgnoreCase(StringUtil.generateDefaultStringValue(currentRmCode))) {
				errorMessageList.add(messageSource.getMessage("message150", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkPreSellFruit(String preSellFruit, String productReadyMethod, String physicalStore) {
		List<String> errorMessageList = new ArrayList<>();
		if ("Y".equalsIgnoreCase(preSellFruit)) {
			if ("NS".equalsIgnoreCase(productReadyMethod)) {
				if (StringUtil.isEmpty(physicalStore)) {
					errorMessageList.add(messageSource.getMessage("message36", null, null));
				} else {
					if (!physicalStore.matches(PHYSICAL_STORE_REGEX)) {
						errorMessageList.add(messageSource.getMessage("message33", null, null));
					}
				}
			} else {
				errorMessageList.add(messageSource.getMessage("message45", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkDescription(
			String skuSDescHktvEn, String skuSDescHktvCh, String skuSDescHktvSc, String skuLDescHktvEn, String skuLDescHktvCh, String skuLDescHktvSc) {
		List<String> errorMessageList = new ArrayList<>();
		Map<String, String> descriptionMap = new HashMap<>();
		descriptionMap.put("SKU Short Description (in English)", skuSDescHktvEn);
		descriptionMap.put("SKU Short Description (in Chinese)", skuSDescHktvCh);
		descriptionMap.put("SKU Short Description (in Simplified)", skuSDescHktvSc);
		descriptionMap.put("SKU Long Description (in English)", skuLDescHktvEn);
		descriptionMap.put("SKU Long Description (in Chinese)", skuLDescHktvCh);
		descriptionMap.put("SKU Long Description (in Simplified)", skuLDescHktvSc);

		for (Map.Entry<String, String> description : descriptionMap.entrySet()) {
			if(StringUtil.isNotEmpty(description.getValue())){
				if (IMAGE_PATTERN_1.matcher(description.getValue()).find() || IMAGE_PATTERN_2.matcher(description.getValue()).find()) {
					errorMessageList.add(messageSource.getMessage("message2", new String[]{description.getKey()}, null));
				}

				if (TABLE_REGX_PATTERN.matcher(description.getValue()).find()) {
					errorMessageList.add(messageSource.getMessage("message3", new String[]{description.getKey()}, null));
				}
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkPackingBoxType(String packingBoxTypeCode, String storageTemperature) {
		List<String> errorMessageList = new ArrayList<>();
		boolean errorFlag = false;
		if (StringUtil.isNotNullOrBlank(storageTemperature)) {
			switch (packingBoxTypeCode) {
				case "E":
				case "T":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.FROZEN));
					break;
				case "M":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.CHILLED));
					break;
				case "J":
				case "W":
				case "N":
				case "K":
				case "U":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.CHILLED));
					break;
				case "O":
				case "V":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.AIRCON));
					break;
				case "P":
				case "S":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.AIRCON));
					break;
				case "G":
				case "F":
				case "Y":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.ROOM_TEMPERATURE));
					break;
				case "R":
				case "Q":
				case "H":
				case "L":
				case "X":
					errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.ROOM_TEMPERATURE));
					break;
				default:
			}
		} else {
			errorMessageList.add(messageSource.getMessage("message68", null, null));
		}

		if (errorFlag) {
			errorMessageList.add(messageSource.getMessage("message31", new String[]{packingBoxTypeCode}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkProductDataForUpload(String productCode, String skuCode, String productReadyMethod, String mainPhoto,
	                                                        List<String> otherPhoto, List<String> variantProductPhoto, String advertisePhoto) {
		List<String> errorMessageList = new ArrayList<>();
		checkRequired(productCode, "Product ID", errorMessageList);
		checkRequired(skuCode, "Sku ID", errorMessageList);
		checkRequired(productReadyMethod, "Product Ready Method", errorMessageList);
		checkRequired(mainPhoto, "Main Photo", errorMessageList);

		boolean emptyContain = false;
		if (CollectionUtil.isNotEmpty(otherPhoto)) {
			if (otherPhoto.size() > ConstantType.OTHER_PHOTO_MAX) {
				errorMessageList.add(messageSource.getMessage("message265", new String[]{String.valueOf(ConstantType.OTHER_PHOTO_MAX)}, null));
			}
			if (otherPhoto.stream().anyMatch(String::isBlank)) {
				emptyContain = true;
			}
		}
		if (CollectionUtil.isNotEmpty(variantProductPhoto)) {
			if (variantProductPhoto.size() > ConstantType.VARIANT_PRODUCT_PHOTO_MAX) {
				errorMessageList.add(messageSource.getMessage("message266", new String[]{String.valueOf(ConstantType.VARIANT_PRODUCT_PHOTO_MAX)}, null));
			}
			if (variantProductPhoto.stream().anyMatch(String::isBlank)) {
				emptyContain = true;
			}
		}
		if (advertisePhoto != null && advertisePhoto.isBlank()) {
			emptyContain = true;
		} else if (StringUtil.isNotEmpty(advertisePhoto) && advertisePhoto.split(",").length > ConstantType.ADVERTISE_PHOTO_MAX) {
			errorMessageList.add(messageSource.getMessage("message283", new String[]{String.valueOf(ConstantType.ADVERTISE_PHOTO_MAX)}, null));
		}

		if (emptyContain) {
			errorMessageList.add(messageSource.getMessage("message335", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private void checkRequired(String str, String parameterName, List<String> errorMessageList) {
		if (StringUtils.isBlank(str)) {
			errorMessageList.add(messageSource.getMessage("message93", new String[]{parameterName}, null));
		}
	}

	private CheckProductResultDto checkPickupDay(String pickupDays, Integer storeId, String readyMethodCode) {
		List<String> errorMessageList = new ArrayList<>();

		List<String> necessaryMSUProductReadyMethodList = List.of(ProductReadyMethodType.CONSIGNMENT, ProductReadyMethodType.THIRD_PARTY
				,ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB);
		if (necessaryMSUProductReadyMethodList.contains(readyMethodCode)
				&& !PickupDaysType.MON_SUN.equalsIgnoreCase(pickupDays)) {
			errorMessageList.add(messageSource.getMessage("message100", null, null));
		}
		if (!necessaryMSUProductReadyMethodList.contains(readyMethodCode)
				&& sysParmRepository.countStorePickupDay(pickupDays, storeId, ConstantType.SYS_PARAM_SEGMENT_PICKUP_DAYS) == 0) {
			errorMessageList.add(messageSource.getMessage("message100", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkOptionFieldAndValue(String field1, String value1,
														   String field2, String value2,
														   String field3, String value3) {
		List<String> errorMessageList = new ArrayList<>();

		checkFieldAndValue(field1, value1, 1, errorMessageList);
		checkFieldAndValue(field2, value2, 2, errorMessageList);
		checkFieldAndValue(field3, value3, 3, errorMessageList);

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private void checkFieldAndValue(String field, String value, int index, List<String> errorMessageList) {
		if (StringUtils.isEmpty(field) && StringUtils.isNotEmpty(value)) {
			errorMessageList.add(messageSource.getMessage("message94", new Object[]{index, value}, null));
		}

		if (StringUtils.isNotEmpty(field) && StringUtils.isEmpty(value)) {
			//noinspection ConstantConditions
			errorMessageList.add(messageSource.getMessage("message95", new Object[]{index, field}, null));
		}
	}

	private CheckProductResultDto checkSizeSystem(String sizeSystem, String size) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtils.isNotEmpty(sizeSystem) && StringUtils.isEmpty(size)) {
			errorMessageList.add(messageSource.getMessage("message96", new Object[]{sizeSystem}, null));
		}

		if (StringUtils.isEmpty(sizeSystem) && StringUtils.isNotEmpty(size)) {

			errorMessageList.add(messageSource.getMessage("message97", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkColorFamily(String colorFamiliar, String colorEn) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtils.isNotEmpty(colorFamiliar) && StringUtils.isEmpty(colorEn)) {
			errorMessageList.add(messageSource.getMessage("message98", new Object[]{colorFamiliar}, null));
		}

		if (StringUtils.isEmpty(colorFamiliar) && StringUtils.isNotEmpty(colorEn)) {
			errorMessageList.add(messageSource.getMessage("message99", new Object[]{colorFamiliar}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkOriginalPrice(BigDecimal originalPrice, BigDecimal sellingPrice) {
		List<String> errorMessageList = new ArrayList<>();
		if (originalPrice == null ) {
			errorMessageList.add(messageSource.getMessage("message110", null, null));
		} else {
			checkOriginalPriceAndSellingPrice(originalPrice, sellingPrice, errorMessageList);
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private void checkOriginalPriceAndSellingPrice(BigDecimal originalPrice, BigDecimal sellingPrice, List<String> errorMessageList) {
		if (originalPrice !=null && sellingPrice != null && (originalPrice.compareTo(sellingPrice) < 0))
		{errorMessageList.add(messageSource.getMessage("message111", null, null));
		}
	}

	private CheckProductResultDto checkBarCode(List<ProductBarcodeDto> barcodeDtoList, String readyMethodCode) {
		List<String> errorMessageList = new ArrayList<>();
		barcodeDtoList.forEach(productBarcodeDto -> {
			String barcode = productBarcodeDto.getEan();
			if (StringUtil.isNotEmpty(barcode)) {
				if (!BARCODE_PATTERN.matcher(barcode).matches()) {
					errorMessageList.add(messageSource.getMessage("message112", null, null));
				}
				if (barcode.length() > 100) {
					errorMessageList.add(messageSource.getMessage("message116", null, null));
				}
			}
		});

		if (ProductReadyMethodType.THIRD_PARTY.equalsIgnoreCase(readyMethodCode) && !is3PLProductReadyMethodContainMainBarcode(barcodeDtoList)) {
			errorMessageList.add(messageSource.getMessage("message139", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private boolean is3PLProductReadyMethodContainMainBarcode(List<ProductBarcodeDto> barcodeDtoList) {
		boolean is3PLProductReadyMethodContainMainBarcode = false;
		for (ProductBarcodeDto productBarcodeDto : barcodeDtoList) {
			if (Objects.equals(1, productBarcodeDto.getSequenceNo()) && StringUtil.isNotEmpty(productBarcodeDto.getEan())) {
				is3PLProductReadyMethodContainMainBarcode = true;
			}
		}
		return is3PLProductReadyMethodContainMainBarcode;
	}

	private CheckProductResultDto checkRemovalService(String removalService, List<String> productTypeCodeList, BuProductCategoryDo primaryCategory) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(removalService) && removalService.equalsIgnoreCase(ConstantType.Y)) {
			List<SysParmDo> removalServiceList = sysParmRepository.findBySegmentAndBuCode("CATEGORY_FOR_REMOVAL_SERVICE", ConstantType.PLATFORM_CODE_HKTV);

			List<String> removalServiceCategoryList = new ArrayList<>();
			removalServiceList.forEach(removalServiceElement -> {
				String[] removalServiceArray = removalServiceElement.getParmValue().split(",");
				removalServiceCategoryList.addAll(Arrays.asList(removalServiceArray));
			});

			boolean isProductTypeCodeError = true;
			// check product type code exist in removalServiceCategoryList or not
			for (String productTypeCode : productTypeCodeList) {
				for (String removalServiceCategory : removalServiceCategoryList) {
					if (productTypeCode.startsWith(removalServiceCategory)) {
						isProductTypeCodeError = false;
					}
				}
			}
			boolean isPrimaryCategoryError = true;
			// check priamry code exist in removalServiceCategoryList or not
			for (String removalServiceCategory : removalServiceCategoryList) {
				if (primaryCategory.getProductCatCode().startsWith(removalServiceCategory)) {
					isPrimaryCategoryError = false;
				}
			}

			if (isProductTypeCodeError || isPrimaryCategoryError) {
				errorMessageList.add(messageSource.getMessage("message115", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public BigDecimal calculateJapanAndKoreaContractOriginalPrice(BigDecimal cost, BigDecimal markupRate, String currency) {
		// if contract is J or K : original price = (cost + cost * markUpRate / 100) * exchangeRate
		List<String> exchangeRateList = sysParmRepository.findParmValueBySegmentAndCodeAndPlatformId("EXCHANGE_RATE", currency, ConstantType.PLATFORM_CODE_HKTV);
		BigDecimal exchangeRate = new BigDecimal(exchangeRateList.get(0));
		markupRate = markupRate == null ? new BigDecimal("0") : markupRate;
		BigDecimal productOfMarkUpRateAndCost = (cost.multiply(markupRate)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
		BigDecimal orginalPrice = (cost.add(productOfMarkUpRateAndCost)).multiply(exchangeRate).setScale(0, RoundingMode.HALF_UP);
		log.info("JK contract after calculate： exchange_rate = {}, markup_rate = {}, cost = {}, original_price = {}", exchangeRate, markupRate, cost, orginalPrice);
		return orginalPrice;
	}

	public void checkBarcodeEnable(List<SingleEditProductDto> productList) {
		Set<String> tplBarcodeSet = new HashSet<>();
		Set<String> nonTplBarcodeSet = new HashSet<>();
		Set<String> failedBarcodeSet = new HashSet<>();
		Set<String> nonSyncToTplBarcodeSet = new HashSet<>();

		productList.stream()
				.filter(product -> CollectionUtil.isNotEmpty(product.getProduct().getBarcodes()))
				.forEach(product -> {
					HktvProductDto hktvProductDto = product.getProduct().getAdditional().getHktv();
					List<ProductBarcodeDto> barcodeDtoList = product.getProduct().getBarcodes();

					if (hktvProductDto != null) {
						boolean is3pl = hktvProductDto.getProductReadyMethod().equalsIgnoreCase(ProductReadyMethodType.THIRD_PARTY);
						barcodeDtoList.forEach( barcodeDto->{
							String barcode =  barcodeDto.getEan();
							if (is3pl) {
								if (!tplBarcodeSet.add(barcode)) {
									failedBarcodeSet.add(barcode);
								}
							} else {
								if (!nonTplBarcodeSet.add(barcode)) {
									nonSyncToTplBarcodeSet.add(barcode);
								}
							}
						});

					}
				});
		tplBarcodeSet.forEach(tplBarcode -> {
			if (nonTplBarcodeSet.contains(tplBarcode)) {
				failedBarcodeSet.add(tplBarcode);
			}
		});

		for (SingleEditProductDto product : productList) {
			if (CollectionUtil.isNotEmpty(product.getProduct().getBarcodes())) {
				AtomicBoolean hasDuplicateBarcode = new AtomicBoolean(false);
				AtomicBoolean isSyncTo3PL = new AtomicBoolean(true);
				product.getProduct().getBarcodes().forEach( barcodeDto->{
					String barcode =  barcodeDto.getEan();
					if(!hasDuplicateBarcode.get()){
						hasDuplicateBarcode.set(failedBarcodeSet.contains(barcode));
					}

					if(isSyncTo3PL.get()){
						isSyncTo3PL.set(nonSyncToTplBarcodeSet.contains(barcode));
					}
				});

				if (hasDuplicateBarcode.get()) {
					saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getProduct().getRecordRowId(), messageSource.getMessage("message118", null, null));
					continue;
				}
				product.getProduct().setDisableTo3PL(isSyncTo3PL.get());
				saveProductRecordRowRepository.updateRowContentByRecordRowId(product.getProduct().getRecordRowId(), gson.toJson(product));
			}
		}
	}

	public CheckProductResultDto checkPackInfoBy3PL(UserDto userDto, List<SkuValidateRequestDto> skuValidateRequestDtoList, String identifier) {
		List<String> errorMessageList = new ArrayList<>();

		SkuValidateResponseDto skuValidateResponseDto = thirdPartyHelper.skuValidate(userDto, skuValidateRequestDtoList, identifier);

		if (skuValidateResponseDto == null) {
			String errorMessage = "TPL: " + messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.THIRD_PARTY_SKU_VALIDATE_ERROR}, null);
			errorMessageList.add(errorMessage);
		} else if (Boolean.FALSE.equals(skuValidateResponseDto.getSuccess())) {
			errorMessageList = skuValidateResponseDto.getReason();
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkPackageConfirmed(ProductMasterResultDto beforeProduct, BigDecimal packingDepth, BigDecimal packingHeight, BigDecimal packingLength,
														BigDecimal weight, String packingDimensionUnit, String weightUnit) {
		List<String> errorMessageList = new ArrayList<>();

		if (beforeProduct.getAdditional().getThirdParty() != null && beforeProduct.getAdditional().getThirdParty().isPackageConfirmed()) {
			boolean result = Stream.of(packingDepth, packingHeight, packingLength, weight, packingDimensionUnit, weightUnit,
							beforeProduct.getPackingDepth(), beforeProduct.getPackingHeight(), beforeProduct.getPackingLength(),
							beforeProduct.getWeight(), beforeProduct.getPackingDimensionUnit(), beforeProduct.getWeightUnit())
					.allMatch(Objects::nonNull);
			result = result && packingDepth.compareTo(beforeProduct.getPackingDepth()) == 0;
			result = result && packingHeight.compareTo(beforeProduct.getPackingHeight()) == 0;
			result = result && packingLength.compareTo(beforeProduct.getPackingLength()) == 0;
			result = result && weight.compareTo(beforeProduct.getWeight()) == 0;
			result = result && StringUtils.equals(packingDimensionUnit, beforeProduct.getPackingDimensionUnit());
			result = result && StringUtils.equals(weightUnit, beforeProduct.getWeightUnit());

			if (!result) {
				errorMessageList.add(messageSource.getMessage("message119", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkBarcodeLock(ProductMasterResultDto beforeProduct, List<ProductBarcodeDto> currentBarcodeDtoList) {
		List<String> errorMessageList = new ArrayList<>();

		List<ProductBarcodeDto> beforeBarcodeDtoList = beforeProduct.getBarcodes();
		if(CollectionUtil.isNotEmpty(beforeBarcodeDtoList)){
			AtomicBoolean isSucess = new AtomicBoolean(true);
			beforeBarcodeDtoList.forEach( beforeBarcodeDto->{
				if(isSucess.get() && beforeBarcodeDto.isLock()){
					String beforeBarcode = beforeBarcodeDto.getEan();
					String currentBarcode = currentBarcodeDtoList.stream()
							.filter(currentBarcodeDto-> currentBarcodeDto.getSequenceNo().equals(beforeBarcodeDto.getSequenceNo()))
							.map(ProductBarcodeDto::getEan)
							.findFirst()
							.orElse(null);

					if(!beforeBarcode.equals(currentBarcode)){
						isSucess.set(false);
					}

				}
			});

			if (!isSucess.get()) {
				errorMessageList.add(messageSource.getMessage("message119", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private List<String> checkDeliveryDistrictAndCategoryOverseaShippable(List<String> regionsUnderTest, ProductCheckDto productCheckDo, List<ProductMasterResultDto> childSkusData) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isNotEmpty(regionsUnderTest)) {
			String buCode = productCheckDo.getBuCode();
			Integer storeId = productCheckDo.getStore().getId();

			List<String> childSkusPrimaryCategoryCodeList = childSkusData.stream()
				.map(ProductMasterResultDto::getAdditional)
				.map(BuProductDto::getHktv)
				.map(HktvProductDto::getPrimaryCategoryCode)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.toList());

			Set<Integer> childSkusCategoryIds = buProductCategoryRepository.findActiveCatogoriesByProductCatCodeList(buCode, childSkusPrimaryCategoryCodeList)
				.stream()
				.map(BuProductCategoryDo::getId)
				.collect(Collectors.toSet());

			if (CollectionUtil.isNotEmpty(childSkusCategoryIds)) {
				Set<String> validRegions = storeOverseaDeliveryHelper.getValidRegions(buCode, storeId, childSkusCategoryIds);
				Set<String> invalidRegions = storeOverseaDeliveryHelper.validateAndReturnInvalidRegions(validRegions, new HashSet<>(regionsUnderTest));

				if (CollectionUtil.isNotEmpty(invalidRegions)) {
					errorMessageList.add(messageSource.getMessage("message344", invalidRegions.toArray(String[]::new), null));
				}
			}
		}

		return errorMessageList;
	}

	public CheckProductResultDto checkOverseaDeliveryDistrictMatchedStoreSetting(List<String> deliveryDistrictList, StoreDo store) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isNotEmpty(deliveryDistrictList)) {
			List<StoreOverseaDeliveryDo> storeOverseaDeliveryDoList = storeOverseaDeliveryRepository.findByStoreId(store.getId());
			List<String> overseaDeliveryCountryList = storeOverseaDeliveryDoList.stream().map(StoreOverseaDeliveryDo::getRegion).collect(Collectors.toList());

			List<String> deliveryDistrictNonMatchedList = new ArrayList<>();
			if (CollectionUtil.isNotEmpty(overseaDeliveryCountryList)) {
				deliveryDistrictNonMatchedList = deliveryDistrictList.stream().filter(country -> !overseaDeliveryCountryList.contains(country)).collect(Collectors.toList());
			} else {
				deliveryDistrictNonMatchedList.addAll(deliveryDistrictList);
			}

			if (CollectionUtil.isNotEmpty(deliveryDistrictNonMatchedList)) {
				errorMessageList.add(messageSource.getMessage("message122", deliveryDistrictNonMatchedList.toArray(String[]::new), null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkOverseaDeliveryDistrictFormat(List<String> deliveryDistrictList) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isNotEmpty(deliveryDistrictList)) {
			List<SysParmDo> overseaDeliveryList = sysParmRepository.findBySegmentAndPlatformId(SysParmSegment.OVERSEA_DELIVERY, 1);
			List<String> overseaDeliveryRegionCodeList = overseaDeliveryList.stream().map(SysParmDo::getCode).collect(Collectors.toList());

			boolean isWrongOverseaDeliveryFormat = false;
			for (String deliveryDistrict : deliveryDistrictList) {
				if (CollectionUtil.isNotEmpty(overseaDeliveryRegionCodeList) && !overseaDeliveryRegionCodeList.contains(deliveryDistrict)) {
					isWrongOverseaDeliveryFormat = true;
				}
			}

			if (isWrongOverseaDeliveryFormat) {
				errorMessageList.add(messageSource.getMessage("message123", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkOverseaDeliveryDistrictAndDeliveryMethod(List<String> deliveryDistrictList, String deliveryMethod) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isNotEmpty(deliveryDistrictList)) {
			List<SysParmDo> overseaMethodList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.OVERSEA_METHOD, deliveryMethod, 1);
			List<String> overseaMethodCodeList = new ArrayList<>();
			for (SysParmDo overseaMethod : overseaMethodList) {
				if (StringUtil.isNotEmpty(overseaMethod.getParmValue())) {
					String[] overseaMethodCode = overseaMethod.getParmValue().split(",");
					overseaMethodCodeList = Arrays.asList(overseaMethodCode);
				}
			}

			List<String> nonMatchDeliveryMethodList = new ArrayList<>();
			for (String deliveryDistrict : deliveryDistrictList) {
				if (!overseaMethodCodeList.contains(deliveryDistrict)) {
					nonMatchDeliveryMethodList.add(deliveryDistrict);
				}
			}

			if (CollectionUtil.isNotEmpty(nonMatchDeliveryMethodList)) {
				errorMessageList.add(messageSource.getMessage("message120", new Object[]{deliveryMethod, nonMatchDeliveryMethodList}, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkOverseaDeliveryDistrictRestriction(UserDto userDto, List<String> deliveryDistrictList, List<String> originalDeliveryDistrictList) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isEmpty(deliveryDistrictList)) {
			return CheckProductResultDto.generate(errorMessageList);
		}

		//check Oversea Delivery Region Restriction (MS-5851)
		if (!RoleCode.ALLOW_ADD_OVERSEA_DELIVERY_ROLES.contains(userDto.getRoleCode())) {
			Set<String> restrictedRegions = sysParamHelper.getSplitSystemParamsBySegmentAndBuCode(SysParmSegmentEnum.ADMIN_ALLOW_OVERSEA, BuCodeEnum.HKTV.name()).stream().collect(Collectors.toSet());
			List<String> notAllowedRegions = deliveryDistrictList.stream()
				.filter(district -> (originalDeliveryDistrictList == null || !originalDeliveryDistrictList.contains(district)) && restrictedRegions.contains(district))
				.collect(Collectors.toList());

			if (!notAllowedRegions.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message343", new String[]{notAllowedRegions.toString()}, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	/**
	 * Check if the product/bundle is under membership pricing event set from PROMOTION_SERVICE
	 */
	private List<String> checkMembershipPricingEventSet(UserDto userDto, List<String> deliveryDistrictList, String storeCode, String skuId, MembershipPricingEventSetDto checkPricingResultDto) {
		List<String> errorMessageList = new ArrayList<>();

		// Filter out the delivery district that is not in the oversea region
		List<String> deliveryRestrictList = Optional.ofNullable(deliveryDistrictList)
			.orElse(Collections.emptyList())
			.stream()
			.filter(PromotionHelper.CHECK_OVERSEA_REGION::contains)
			.collect(Collectors.toList());

		// No need to check if no delivery district is provided
		if (deliveryRestrictList.isEmpty()) {
			return errorMessageList;
		}

		// Check if the product is under membership pricing event set from PROMOTION_SERVICE
		// If the product/bundle is under membership pricing event set, it cannot be sold to oversea regions
		if (checkPricingResultDto == null) {
			log.error("Unable to request promotion check membership pricing event set.");
			errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PROMOTION_CHECK_MEMBERSHIP_PRICING_ONGOING}, null));
		} else if (checkPricingResultDto.getIsEventSet()) {
			errorMessageList.add(messageSource.getMessage("message332", new String[]{String.join(", ", deliveryRestrictList)}, null));
		}

		return errorMessageList;
	}

	private List<String> checkPlusPrice(BigDecimal originalPrice, BigDecimal sellingPrice, MembershipPricingEventSetDto checkPricingResultDto) {
		List<String> errorMessageList = new ArrayList<>();
		BigDecimal adjustedOriginalPrice = originalPrice.multiply(promotionHelper.PRICE_THRESHOLD);
		BigDecimal adjustedSellingPrice = sellingPrice != null && sellingPrice.compareTo(BigDecimal.ZERO) > 0  ? sellingPrice.multiply(promotionHelper.PRICE_THRESHOLD) : null;

		if (checkPricingResultDto == null) {
			log.error("Unable to request promotion check membership pricing event set.");
			errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PROMOTION_CHECK_MEMBERSHIP_PRICING_ONGOING}, null));
		} else if (checkPricingResultDto.getIsEventSet()) {
			if (adjustedOriginalPrice.compareTo(checkPricingResultDto.getPlusPrice()) < 0) {
				log.info("Adjusted original price {} is less than plus price {}", adjustedOriginalPrice, checkPricingResultDto.getPlusPrice());
				errorMessageList.add(messageSource.getMessage("message326", null, null));
			}

			if (adjustedSellingPrice != null && adjustedSellingPrice.compareTo(checkPricingResultDto.getPlusPrice()) < 0) {
				log.info("Adjusted selling price {} is less than plus price {}", adjustedSellingPrice, checkPricingResultDto.getPlusPrice());
				errorMessageList.add(messageSource.getMessage("message327", null, null));
			}
		}

		return errorMessageList;
	}

	public void checkPrimarySkuWithSameProductCode(List<ProductMasterDto> variantProductFromProductMasterList, List<ProductMasterDto> userUploadedProductList) {
		List<ProductMasterDto> primaryProductList = new ArrayList<>();
		List<ProductMasterDto> allSameProductCodeList = new ArrayList<>();
		allSameProductCodeList.addAll(userUploadedProductList);
		allSameProductCodeList.addAll(variantProductFromProductMasterList);

		if (CollectionUtil.isNotEmpty(allSameProductCodeList)) {
			primaryProductList = allSameProductCodeList.stream()
					.filter(product -> product.getAdditional().getHktv() != null && "Y".equalsIgnoreCase(product.getAdditional().getHktv().getIsPrimarySku()))
					.collect(Collectors.toList());
		}

		if (primaryProductList.size() > 1) {
			boolean isAllCurrentProductsInPrimaryProducts = primaryProductList.containsAll(userUploadedProductList);
			if (isAllCurrentProductsInPrimaryProducts) {
				allSameProductCodeList.forEach(product ->
						saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getRecordRowId(), messageSource.getMessage("message126", null, null))
				);
			} else {
				userUploadedProductList.stream()
						.filter(product -> "Y".equalsIgnoreCase(product.getAdditional().getHktv().getIsPrimarySku()))
						.forEach(product ->
								saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getRecordRowId(), messageSource.getMessage("message126", null, null))
						);
			}
		} else if (primaryProductList.isEmpty()) {
			allSameProductCodeList.forEach(product ->
					saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getRecordRowId(), messageSource.getMessage("message34", null, null))
			);
		}
	}

	private CheckProductResultDto checkVirtualStore(Integer merchantId, String virtualStore) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(virtualStore)) {
			if (!merchantHelper.isVirtualStoreMerchant(merchantId, virtualStore)) {
				errorMessageList.add(messageSource.getMessage("message130", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkProductReadyDays(String readyMethodCode, String productReadyDays) {
		List<String> errorMessageList = new ArrayList<>();
		if (ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB.equals(readyMethodCode)
				&& !productReadyDays.equals("0")) {
			errorMessageList.add(messageSource.getMessage("message153", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkCartonPackingInformation(Integer cartonHeight, Integer cartonLength, Integer cartonDepth) {
		List<String> errorMessageList = new ArrayList<>();
		if(cartonHeight == null && cartonLength == null && cartonDepth == null){
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}

		if (cartonHeight == null || cartonLength == null || cartonDepth == null){
			errorMessageList.add(messageSource.getMessage("message140", null, null));
		}

		if(cartonHeight == 0 || cartonLength == 0 || cartonDepth == 0){
			errorMessageList.add(messageSource.getMessage("message141", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkUserMax(String contractType, Long userMax) {
		List<String> errorMessageList = new ArrayList<>();
		boolean isEvoucherContractType = contractType != null && ContractType.SERVICE_DEAL.equalsIgnoreCase(contractType);
		if (isEvoucherContractType && (userMax == null || userMax.equals(0L))) {
			errorMessageList.add(messageSource.getMessage("message147", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkStorageType(boolean isNewProduct, String productReadyMethod, String currentStorageType, String beforeStorageType) {
		List<String> errorMessageList = new ArrayList<>();
		if (ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productReadyMethod)) {
			if (isNewProduct && StringUtil.isEmpty(currentStorageType)) {
				errorMessageList.add(messageSource.getMessage("message38", null, null));
			}
			boolean isStorageTypeUpdated = StringUtil.generateDefaultStringValue(beforeStorageType).equalsIgnoreCase(StringUtil.generateDefaultStringValue(currentStorageType));
			if (!isNewProduct && !isStorageTypeUpdated) {
				errorMessageList.add(messageSource.getMessage("message149", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkSkuName(String skuNameEn, String skuNameCh, String skuNameSc) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(skuNameEn)) {
			if (FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameEn).find()) {
				errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameEn}, null));
			}
			if (skuNameEn.length() > ConstantType.MAX_CHARACTERS_100) {
				errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
			}
		}
		if (StringUtil.isNotEmpty(skuNameCh)) {
			if (FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameCh).find()) {
				errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameCh}, null));
			}
			if (skuNameCh.length() > ConstantType.MAX_CHARACTERS_100) {
				errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name Chi length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
			}
		}
		if (StringUtil.isNotEmpty(skuNameSc)) {
			if (FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameSc).find()) {
				errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameSc}, null));
			}
			if (skuNameSc.length() > ConstantType.MAX_CHARACTERS_100) {
				errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name Sc length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
			}
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkDiscountText(String discountTextEn, String discountTextCh, String discountTextSc) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(discountTextEn) && discountTextEn.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (Eng)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		if (StringUtil.isNotEmpty(discountTextCh) && discountTextCh.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (Chi)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		if (StringUtil.isNotEmpty(discountTextSc) && discountTextSc.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (Sc)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkEditBundle(UserDto userDto, ProductCheckDto productCheckDo, ProductMasterResultDto beforeProduct, SaveProductRecordDo productRecord) {
		long startTime = System.currentTimeMillis();

		CheckProductResultDto checkProductResultDto = checkAllBundle(userDto, productCheckDo, productRecord, beforeProduct);

		List<String> errorMessageList = new ArrayList<>(checkProductResultDto.getErrorMessageList());

		CheckProductResultDto checkPackageConfirmedResult = checkPackageConfirmed(beforeProduct, productCheckDo.getDepth(),
				productCheckDo.getHeight(), productCheckDo.getLength(), productCheckDo.getWeight(),
				productCheckDo.getPackingDimensionUnit(), productCheckDo.getWeightUnit());
		errorMessageList.addAll(checkPackageConfirmedResult.getErrorMessageList());

		CheckProductResultDto checkBarcodeLockResult = checkBarcodeLock(beforeProduct, productCheckDo.getBarcodeDtoList());
		errorMessageList.addAll(checkBarcodeLockResult.getErrorMessageList());

		//bundle check
		List<BundleChildSkuDto> childSkus = productCheckDo.getChildSkuInfo();
		List<String> childSkuUuids = childSkus.stream().map(BundleChildSkuDto::getUuid).collect(Collectors.toList());
		List<ProductMasterResultDto> childSkusResponseData = productMasterHelper.requestProductsByUuid(userDto, ProductMasterSearchRequestDto.builder().uuids(childSkuUuids).build());
		errorMessageList.addAll(checkChildSku(userDto, productCheckDo, childSkus, childSkusResponseData, childSkuUuids));
		errorMessageList.addAll(checkMembershipPricingEventSet(userDto, productCheckDo.getDeliveryDistrictList(), productCheckDo.getStore().getStorefrontStoreCode(), productCheckDo.getSkuId(), productCheckDo.getCheckPricingResult()));
		errorMessageList.addAll(checkPlusPrice(productCheckDo.getOriginalPrice(), productCheckDo.getSellingPrice(), productCheckDo.getCheckPricingResult()));

		// if update delivery_district is null, still need to check the origin delivery_district from before product
		List<String> deliveryRegionsUnderTest = Objects.isNull(productCheckDo.getDeliveryDistrictList()) ? beforeProduct.getAdditional().getHktv().getDeliveryDistrict() : productCheckDo.getDeliveryDistrictList();
		errorMessageList.addAll(checkDeliveryDistrictAndCategoryOverseaShippable(deliveryRegionsUnderTest, productCheckDo, childSkusResponseData));

		long endTime = System.currentTimeMillis();
		log.info("Time taken by checkEditBundle method : {} milliseconds", (endTime - startTime));
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	private List<String> checkBundlePackingBoxType(ProductCheckDto productCheckDo, List<ProductMasterResultDto> childSkusResponseData) {
		List<String> errorMessageList = new ArrayList<>();

		//primary category and product type code of parent bundle is
		//the child sku's primary category and product type code which has the highest selling/original price
		ProductMasterResultDto filterFragileAndHighestPriceSku = filterFragileAndHighestPriceSku(childSkusResponseData, productCheckDo.getChildSkuInfo());

		if (!productCheckDo.getPackingBoxTypeCode().equals(filterFragileAndHighestPriceSku.getPackingBoxType())) {
			errorMessageList.add(messageSource.getMessage("message258", null, null));
		}

		return errorMessageList;
	}

	private ProductMasterResultDto filterFragileAndHighestPriceSku(List<ProductMasterResultDto> childSkusResponseData, List<BundleChildSkuDto> childSkuInfo) {

		List<ProductMasterResultDto> childSkusFilterByFragile = childSkusResponseData.stream()
				.filter(childSku -> PackageBoxTypeCodeAndDescEnum.isFragile(childSku.getPackingBoxType()))
				.collect(Collectors.toList());

		// Determine whether it is fragile and confirm with a high price
		Set<String> highestPriceSkuUuids = childSkusFilterByFragile.isEmpty() ?
				findHighestPriceSkusUuid(childSkusResponseData) : findHighestPriceSkusUuid(childSkusFilterByFragile);

		// if get more than one highestPriceSkuUuids then get first set into bundle sku
		ProductMasterResultDto filterFragileAndHighestPriceSku = null;
		for(BundleChildSkuDto bundleChildSku : childSkuInfo) {
			if (highestPriceSkuUuids.contains(bundleChildSku.getUuid())) {
				filterFragileAndHighestPriceSku = childSkusResponseData.stream().filter(childSkuResponseInfo -> childSkuResponseInfo.getUuid().equals(bundleChildSku.getUuid()))
						.findFirst().orElse(null);
				break;
			}
		}
		if(filterFragileAndHighestPriceSku == null){
			log.error("When check bundle packingBoxType, but product master response uuid not match with user expect create bundle child sku uuid");
			throw new SystemException("When check bundle packingBoxType, but product master response uuid not match with user expect create bundle child sku uuid");
		}

		return filterFragileAndHighestPriceSku;
	}

	private Set<String> findHighestPriceSkusUuid(List<ProductMasterResultDto> childSkusResponseData){
		Map<String, Double> skuUuidToPriceMap = convertToPriceMap(childSkusResponseData);

		double highestSellingPrice = skuUuidToPriceMap.values().stream()
				.mapToDouble(Double::doubleValue)
				.max().orElseThrow(NoSuchElementException::new);

		return skuUuidToPriceMap.entrySet().stream()
				.filter(entry -> entry.getValue() == highestSellingPrice)
				.map(entry -> entry.getKey())
				.collect(Collectors.toSet());
	}

	private Map<String, Double> convertToPriceMap(List<ProductMasterResultDto> childSkusResponseData){
		return childSkusResponseData.stream()
				.collect(Collectors.toMap(
						ProductMasterResultDto::getUuid,
						sku -> {
							BigDecimal sellingPrice = sku.getAdditional().getHktv().getSellingPrice();
							if (sellingPrice != null && sellingPrice.doubleValue() > 0) {
								return sellingPrice.doubleValue();
							} else {
								return sku.getOriginalPrice().doubleValue();
							}
						}
				));
	}
}
