package com.shoalter.mms_product_api.service.bundle.pojo.request;

import com.shoalter.mms_product_api.service.base.pojo.BundleProductQueryBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class BundleChildProductQueryDto extends BundleProductQueryBaseDto {

	private Integer page;
	private Integer size;
	private List<String> orderBy;
	private String skuCode;
	private String skuName;

}
