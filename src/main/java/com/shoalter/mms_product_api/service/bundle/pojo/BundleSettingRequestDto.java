package com.shoalter.mms_product_api.service.bundle.pojo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BundleSettingRequestDto extends BundleSettingBasicDto {

	@JsonProperty("mall_inventory_info")
	@SerializedName("mall_inventory_info")
	private List<BundleInventoryInfoRequestDto> mallInventoryInfo;
}
