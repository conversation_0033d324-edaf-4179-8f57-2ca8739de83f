package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewPageableResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewSortResultDto;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BundleChildDetailDto {

	private int totalPages;
	private int totalElements;
	private int size;
	private int numberOfElement;
	private boolean first;
	private boolean last;
	private boolean empty;
	private int number;

	@JsonProperty(value = "child_sku_info")
	@SerializedName("child_sku_info")
	private List<ChildSkuDetailDataDto> childSkuInfo;

	private ProductOverviewPageableResultDto pageable;
}
