package com.shoalter.mms_product_api.service.approval_deal;

import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.approval_deal.CommissionApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.SkuCommissionRateMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.SkuCommissionRateMainResponseData;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindSkuCommissionRateService {

	private final CommissionApprovalDealRepository commissionApprovalDealRepository;

	public ResponseDto<SkuCommissionRateMainResponseData> start(UserDto userDto, SkuCommissionRateMainRequestData requestData) {

		List<CommissionApprovalDealDo> commissionApprovalDealDoList = commissionApprovalDealRepository.findByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalStatusIn(
			requestData.getBu(),
			requestData.getStorefrontStoreCode(),
			List.of(ApprovalDealStatusEnum.MERCHANT_SUBMITTED.name(), ApprovalDealStatusEnum.RM_REVIEWED.name()),
			requestData.getSkuCode()
		);

		if (commissionApprovalDealDoList.isEmpty()) {
			return ResponseDto.success(null);
		}

		return ResponseDto.success(generateSkuCommissionRateMainResponseData(commissionApprovalDealDoList));
	}

	private static SkuCommissionRateMainResponseData generateSkuCommissionRateMainResponseData(List<CommissionApprovalDealDo> commissionApprovalDealDoList) {
		CommissionApprovalDealDo commissionApprovalDealDo = commissionApprovalDealDoList.get(0);
		SkuCommissionRateMainResponseData responseData = new SkuCommissionRateMainResponseData();
		responseData.setBu(commissionApprovalDealDo.getBu().name());
		responseData.setStorefrontStoreCode(commissionApprovalDealDo.getStorefrontStoreCode());
		responseData.setSkuCode(commissionApprovalDealDo.getSkuCode());
		responseData.setOriginalCommissionRate(commissionApprovalDealDo.getProductHistoryContent().getStandardCommissionRate());
		responseData.setModifiedCommissionRate(commissionApprovalDealDo.getCommissionRate());
		return responseData;
	}
}
