package com.shoalter.mms_product_api.service.approval_deal;


import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.approval_deal.CommissionApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.helper.CommissionApprovalDealTemplateHelper;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ExportApprovalDealMainRequestData;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;


@RequiredArgsConstructor
@Service
@Slf4j
public class ExportApprovalDealReportService extends AbstractReport {
	private final CommissionApprovalDealTemplateHelper commissionApprovalDealTemplateHelper;
	private final CommissionApprovalDealRepository commissionApprovalDealRepository;

	public HttpEntity<ByteArrayResource> start(UserDto userDto, ExportApprovalDealMainRequestData requestData) {

		// check permission
		if (!RoleCode.ALLOW_QUERY_APPROVAL_DEAL_ROLES.contains(userDto.getRoleCode())) {
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}

		// produce file
		long startTime = System.currentTimeMillis();

		List<CommissionApprovalDealDo> commissionApprovalDealDoList = commissionApprovalDealRepository.findByBuAndStoreAndUpdatedDate(
			userDto,
			BuCodeEnum.HKTV.name(),
			requestData.getStorefrontStoreCodes(),
			DateUtil.timestampToDate(requestData.getStartTimestamp()),
			DateUtil.timestampToDate(requestData.getEndTimestamp()));

		ByteArrayResource byteArrayResource = commissionApprovalDealTemplateHelper.generateCommissionApprovalDealFile(commissionApprovalDealDoList);
		HttpHeaders header = commissionApprovalDealTemplateHelper.generateHeader();
		log.info("Time taken to ApprovalDealReport: {} milliseconds", (System.currentTimeMillis() - startTime));

		return new ResponseEntity<>(byteArrayResource, header, HttpStatus.OK);
	}
}
