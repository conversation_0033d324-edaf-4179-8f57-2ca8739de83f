package com.shoalter.mms_product_api.service.approval_deal.helper;

import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.merchant.UserMerchantRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.dao.repository.system.SysUserRepository;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealSendMessageDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CheckApprovalDealStatusDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.notification.enums.NotificationCodeEnum;
import com.shoalter.mms_product_api.service.notification.helper.NotificationHelper;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationRequest;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationResponseData;
import com.shoalter.mms_product_api.service.notification.pojo.dto.ButtonDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.TemplateDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Slf4j
public class ApprovalDealStatusHelper {

	private final MessageSource messageSource;
	private final NotificationHelper notificationHelper;
	private final UserMerchantRepository userMerchantRepository;
	private final SysUserRepository sysUserRepository;
	private static final String TEMPLATE_SKU_CODE = "SKU_CODE";
	private static final String TEMPLATE_STOREFRONT_STORE_CODE = "StorefrontStoreCode";
	private static final String TEMPLATE_YEAR = "year";
	private static final String TEMPLATE_MONTH = "month";
	private static final String TEMPLATE_DAY = "day";
	private static final String NOTIFICATION_REDIRECT_URL = "/product-management/product-list/edit-product/%s";

	public ResponseDto<Void> checkApprovalStatusChange(CheckApprovalDealStatusDto checkApprovalDealStatusDto) {
		final List<String> error = List.of(messageSource.getMessage("message299", new Object[]{checkApprovalDealStatusDto.getOriginalStatus(), checkApprovalDealStatusDto.getModifiedStatus()}, null));

		if (checkApprovalDealStatusDto.getApprovalDealType() == null ||
			checkApprovalDealStatusDto.getOriginalStatus() == null ||
			checkApprovalDealStatusDto.getModifiedStatus() == null) {
			return ResponseDto.fail(error);
		}
		ApprovalDealStatusEnum originalStatus = checkApprovalDealStatusDto.getOriginalStatus();

		boolean isCanTransition = originalStatus.isCanTransitionTo(
			checkApprovalDealStatusDto.getApprovalDealType(),
			checkApprovalDealStatusDto.getModifiedStatus()
		);
		return isCanTransition ? ResponseDto.success(null) : ResponseDto.fail(error);
	}

	public void sendMessagesForApprovalStatusChange(UserDto userDto, List<ApprovalDealSendMessageDto> sendMessages) {
		log.info("ApprovalDealStatus Change SendMessages, sendMessages: {}", sendMessages);
		List<NotificationRequest> requests = new ArrayList<>();
		for (ApprovalDealSendMessageDto sendMessage : sendMessages) {
			NotificationCodeEnum notificationCode = findNotificationCodeByApprovalStatusChange(sendMessage.getApprovalDealStatusChange());
			String redirectUrl = String.format(NOTIFICATION_REDIRECT_URL, sendMessage.getUuid());
			NotificationRequest request = NotificationRequest.generate(notificationCode, ButtonDto.getClickNowButton(), generateTemplate(sendMessage), null, sendMessage.getReceiverUsers(), redirectUrl);
			requests.add(request);
		}
		ResponseDto<NotificationResponseData> result = notificationHelper.requestSendMessage(userDto, requests);
		if (result.getStatus() == StatusCodeEnum.FAIL.getCode()) {
			log.error("sendMessagesForApprovalStatusChange fail :{}", result.getErrorMessageList());
		}
	}

	private NotificationCodeEnum findNotificationCodeByApprovalStatusChange(ApprovalDealStatusEnum approvalDealStatusChange) {
		switch (approvalDealStatusChange) {
			case MERCHANT_SUBMITTED:
				return NotificationCodeEnum.APPROVE_COMMISSION_MERCHANT_SUBMITTED;
			case APPROVED:
				return NotificationCodeEnum.APPROVE_COMMISSION_APPROVED;
			case REJECTED:
				return NotificationCodeEnum.APPROVE_COMMISSION_REJECTED;
			default:
				return null;
		}
	}

	private TemplateDto generateTemplate(ApprovalDealSendMessageDto sendMessage) {
		if (sendMessage == null) return null;
		LocalDateTime now = LocalDateTime.now();
		return TemplateDto.generateTemplate(
			Map.of(
				TEMPLATE_SKU_CODE, sendMessage.getSkuCode(),
				TEMPLATE_STOREFRONT_STORE_CODE, sendMessage.getStorefrontStoreCode(),
				TEMPLATE_YEAR, String.valueOf(now.getYear()),
				TEMPLATE_MONTH, String.valueOf(now.getMonthValue()),
				TEMPLATE_DAY, String.valueOf(now.getDayOfMonth())
			)
		);
	}

	public List<UserNameAndEmailViewDo> findApprovalDealCreatorAndContractRms(Integer merchantId,
		String storefrontStoreCode, String userCode) {
		List<UserNameAndEmailViewDo> createUserAndMerchantRmUsers = new ArrayList<>();
		Optional<UserNameAndEmailViewDo> createApprovalDealUser =
			sysUserRepository.findUserNameAndEmailViewByUserCode(userCode);
		List<UserNameAndEmailViewDo> contractRms =
			userMerchantRepository.findContractUsersByMerchantIdAndStorefrontCode(merchantId,
				storefrontStoreCode);
		if (createApprovalDealUser.isPresent()) {
			createUserAndMerchantRmUsers.add(createApprovalDealUser.get());
		} else {
			log.info("create ApprovalDeal User not found, userCode={}", userCode);
		}
		createUserAndMerchantRmUsers.addAll(contractRms);
		return createUserAndMerchantRmUsers;
	}
}
