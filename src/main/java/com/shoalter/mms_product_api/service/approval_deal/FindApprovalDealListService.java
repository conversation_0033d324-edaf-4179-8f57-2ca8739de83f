package com.shoalter.mms_product_api.service.approval_deal;

import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.approval_deal.ApprovalDealRepository;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListMainRequestData;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindApprovalDealListService {
	private final ApprovalDealRepository approvalDealRepository;

	public ResponseDto<PageDto<ApprovalDealListDto>> start(UserDto userDto, ApprovalDealListMainRequestData request) {
		// check permission
		if (!RoleCode.ALLOW_QUERY_APPROVAL_DEAL_ROLES.contains(userDto.getRoleCode())) {
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}

		String[] orderBy = request.getOrderBy().split(" ");
		Pageable pageable = PageUtil.createPageable(request.getSize(), request.getPage(), orderBy[0], Sort.Direction.valueOf(orderBy[1].toUpperCase()));
		Page<ApprovalDealListDto> pageList =  approvalDealRepository.findApprovalDealList(userDto, request, pageable);
		PageDto<ApprovalDealListDto> pageDto = PageDto.<ApprovalDealListDto>builder()
			.list(pageList.getContent())
			.pageCount(pageList.getTotalPages())
			.totalCount(pageList.getTotalElements())
			.build();
		return ResponseDto.success(pageDto);
	}
}
