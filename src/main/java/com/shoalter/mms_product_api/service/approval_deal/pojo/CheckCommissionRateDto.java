package com.shoalter.mms_product_api.service.approval_deal.pojo;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Builder
public class CheckCommissionRateDto {

	private boolean isSmallThanOriginal;
	private ProductStoreStatusDo originalProductStoreStatus;
	private BigDecimal newCommissionRate;
	private Integer newContractProdTermsId;
	private List<String> errorMessage;
}
