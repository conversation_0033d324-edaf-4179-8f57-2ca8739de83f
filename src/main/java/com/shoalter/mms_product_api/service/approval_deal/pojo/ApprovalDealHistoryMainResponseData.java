package com.shoalter.mms_product_api.service.approval_deal.pojo;

import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ApprovalDealHistoryMainResponseData {
	private Long id;
	private List<ApprovalDealHistoryResponseData> list;

	@Data
	public static class ApprovalDealHistoryResponseData {
		private ApprovalDealStatusEnum originalStatus;
		private ApprovalDealStatusEnum changeStatus;
		private Date statusChangeDate;
		private String statusChangeBy;
	}

}
