package com.shoalter.mms_product_api.service.approval_deal.pojo;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class ApprovalDealSendMessageDto {

	private ApprovalDealStatusEnum approvalDealStatusChange;
	private List<UserNameAndEmailViewDo> receiverUsers;
	private String uuid;
	private String skuCode;
	private String storefrontStoreCode;

}
