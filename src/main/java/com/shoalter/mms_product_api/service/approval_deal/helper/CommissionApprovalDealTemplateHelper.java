package com.shoalter.mms_product_api.service.approval_deal.helper;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.template.CommissionApprovalDealColumnEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.approval_deal.CommissionApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameEnViewDo;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameTcViewDo;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ProductHistoryContentDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ThresholdCommissionSettingDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCol;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class CommissionApprovalDealTemplateHelper extends AbstractReport {
	private final BuProductCategoryRepository buProductCategoryRepository;
	private final BrandRepository brandRepository;
	private final String SHEET_NAME = "Commission_Rate_Change";
	private final String FILE_NAME = "Export_CommissionRate_Report_%s.%s";

	public ByteArrayResource generateCommissionApprovalDealFile(List<CommissionApprovalDealDo> commissionApprovalDealDoList) {
		try (ByteArrayOutputStream os = new ByteArrayOutputStream();
			 Workbook tempWorkbook = setBodyColumn(setHeaderColumn(), commissionApprovalDealDoList)) {

			tempWorkbook.write(os);
			return new ByteArrayResource(os.toByteArray());
		} catch (IOException e) {
			log.error("Error occurred while creating commission approval deal excel file: {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}
	}

	public Workbook setHeaderColumn() {
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		XSSFSheet dataSheet = workbook.createSheet(SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(true);
		CellStyle notLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		notLockStyle.setLocked(false);

		CTCol col = dataSheet.getCTWorksheet().getColsArray(0).addNewCol();
		col.setMin(1);
		col.setMax(16384);
		col.setWidth(9.15);
		col.setStyle(notLockStyle.getIndex());

		int rowNum = 0;
		int colNum = 0;
		Row row = CellUtil.getRow(rowNum, dataSheet);
		row.setHeight((short) (30 * 20));

		for (CommissionApprovalDealColumnEnum columnEnum : CommissionApprovalDealColumnEnum.class.getEnumConstants()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), columnEnum.getColumnName().length() * 400);
			cell.setCellStyle(headerStyle);
			colNum++;
		}
		return workbook;
	}

	public Workbook setBodyColumn(Workbook workbook, List<CommissionApprovalDealDo> commissionApprovalDealDoList) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet sheet = tempWorkbook.getSheet(SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY, tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_SIMPLE);
		AtomicInteger rowIndex = new AtomicInteger(1);


		Set<Integer> brandIdList = new HashSet<>();
		Set<Integer> contractProdTermsIdList = new HashSet<>();
		Set<String> categoryCodeSet = new HashSet<>();
		commissionApprovalDealDoList.forEach(commissionApprovalDeal -> {
				brandIdList.add(commissionApprovalDeal.getBrandId());
				brandIdList.add(commissionApprovalDeal.getProductHistoryContent().getBrandId());
				contractProdTermsIdList.add(commissionApprovalDeal.getContractProdTermsId());
				contractProdTermsIdList.add(commissionApprovalDeal.getProductHistoryContent().getContractProdTermsId());
				categoryCodeSet.add(commissionApprovalDeal.getPrimaryCategoryCode());
				categoryCodeSet.addAll(commissionApprovalDeal.getProductTypeCode());
				categoryCodeSet.add(commissionApprovalDeal.getProductHistoryContent().getPrimaryCategoryCode());
				if (CollectionUtil.isNotEmpty(commissionApprovalDeal.getProductHistoryContent().getProductTypeCode())) {
					categoryCodeSet.addAll(commissionApprovalDeal.getProductHistoryContent().getProductTypeCode());
				}
			}
		);
		Map<Integer, String> brandNameMap = brandRepository.findBrandNameTcByIdIn(new ArrayList<>(brandIdList)).stream().collect(Collectors.toMap(BrandNameTcViewDo::getId, BrandNameTcViewDo::getBrandNameTc));
		Map<Integer, ThresholdCommissionSettingDto> thresholdCommissionSettingDtoMap = contractProdTermsRepository.findThresholdCommissionSettingById(new ArrayList<>(contractProdTermsIdList)).stream().collect(Collectors.toMap(ThresholdCommissionSettingDto::getContractProdTermsId, Function.identity()));
		Map<String, BuProductCategoryDo> buProductCategoryMap = buProductCategoryRepository.findByProductCatCodeList(BuCodeEnum.HKTV.name(), categoryCodeSet).stream().collect(Collectors.toMap(BuProductCategoryDo::getProductCatCode, Function.identity()));

		commissionApprovalDealDoList.forEach(commissionApprovalDeal -> {
			ProductHistoryContentDto productHistoryContentDto = commissionApprovalDeal.getProductHistoryContent();

			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_ID.getColumnNumber(), commissionApprovalDeal.getId());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.MERCHANT_NAME.getColumnNumber(), commissionApprovalDeal.getMerchantName());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.STORE_CODE.getColumnNumber(), commissionApprovalDeal.getStorefrontStoreCode());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.SKU_ID.getColumnNumber(), commissionApprovalDeal.getSkuCode());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.SKU_NAME_CH.getColumnNumber(), commissionApprovalDeal.getSkuNameTchi());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.SKU_NAME_EN.getColumnNumber(), commissionApprovalDeal.getSkuName());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_TYPE.getColumnNumber(), commissionApprovalDeal.getApprovalType().name());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.PRODUCT_TYPE_CODE.getColumnNumber(), generateProductTypeCode(productHistoryContentDto.getProductTypeCode(), buProductCategoryMap));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.PRIMARY_CATEGORY_CODE.getColumnNumber(), generatePrimaryCategoryCode(productHistoryContentDto.getPrimaryCategoryCode(), buProductCategoryMap));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.BRAND_NAME.getColumnNumber(), brandNameMap.get(productHistoryContentDto.getBrandId()));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.STANDARD_COMMISSION_RATE.getColumnNumber(), productHistoryContentDto.getStandardCommissionRate());

			ThresholdCommissionSettingDto thresholdCommissionSettingDto = thresholdCommissionSettingDtoMap.get(productHistoryContentDto.getContractProdTermsId());
			if (thresholdCommissionSettingDto != null) {
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.TIER_ONE_THRESHOLD.getColumnNumber(), thresholdCommissionSettingDto.getTier1Threshold());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.TIER_ONE_COMMISSION_RATE.getColumnNumber(), thresholdCommissionSettingDto.getTier1CommissionRate());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.TIER_TWO_THRESHOLD.getColumnNumber(), thresholdCommissionSettingDto.getTier2Threshold());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.TIER_TWO_COMMISSION_RATE.getColumnNumber(), thresholdCommissionSettingDto.getTier2CommissionRate());
			}

			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.PACKING_BOX_TYPE.getColumnNumber(), productHistoryContentDto.getPackingBoxType());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_PRODUCT_TYPE_CODE.getColumnNumber(), generateProductTypeCode(commissionApprovalDeal.getProductTypeCode(), buProductCategoryMap));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_PRIMARY_CATEGORY_CODE.getColumnNumber(), generatePrimaryCategoryCode(commissionApprovalDeal.getPrimaryCategoryCode(), buProductCategoryMap));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_BRAND_NAME.getColumnNumber(), brandNameMap.get(commissionApprovalDeal.getBrandId()));
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_STANDARD_COMMISSION_RATE.getColumnNumber(), commissionApprovalDeal.getCommissionRate());

			ThresholdCommissionSettingDto approveThresholdCommissionSettingDto = thresholdCommissionSettingDtoMap.get(commissionApprovalDeal.getContractProdTermsId());
			if (approveThresholdCommissionSettingDto != null) {
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_TIER_ONE_THRESHOLD.getColumnNumber(), approveThresholdCommissionSettingDto.getTier1Threshold());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_TIER_ONE_COMMISSION_RATE.getColumnNumber(), approveThresholdCommissionSettingDto.getTier1CommissionRate());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_TIER_TWO_THRESHOLD.getColumnNumber(), approveThresholdCommissionSettingDto.getTier2Threshold());
				setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_TIER_TWO_COMMISSION_RATE.getColumnNumber(), approveThresholdCommissionSettingDto.getTier2CommissionRate());
			}

			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_PACKING_BOX_TYPE.getColumnNumber(), commissionApprovalDeal.getPackingBoxType());
			setCellValue(sheet, bodyStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.APPROVE_STATUS.getColumnNumber(), commissionApprovalDeal.getApprovalStatus().name());
			setCellValue(sheet, dateSimpleStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.CREATE_TIME.getColumnNumber(), commissionApprovalDeal.getCreatedDate().format(formatter));
			setCellValue(sheet, dateSimpleStyle, rowIndex.get(), CommissionApprovalDealColumnEnum.LAST_UPDATE_TIME.getColumnNumber(), commissionApprovalDeal.getLastUpdatedDate().format(formatter));
			rowIndex.addAndGet(1);
		});

		return tempWorkbook;
	}

	private String generateProductTypeCode(List<String> productTypeCode, Map<String, BuProductCategoryDo> buProductCategoryMap) {
		return productTypeCode == null ? "" :
			productTypeCode.stream()
				.map(buProductCategoryMap::get)
				.filter(Objects::nonNull)
				.map(buProductCategoryDo -> String.format("%s-%s", buProductCategoryDo.getProductCatCode(), buProductCategoryDo.getProductCatNameTchi()))
				.collect(Collectors.joining(StringUtil.COMMA + System.lineSeparator()));
	}

	private String generatePrimaryCategoryCode(String primaryCategoryCode, Map<String, BuProductCategoryDo> buProductCategoryMap) {
		if (primaryCategoryCode == null) {
			return "";
		}
		String result = "";
		BuProductCategoryDo primaryCategoryInfo = buProductCategoryMap.get(primaryCategoryCode);
		if (primaryCategoryInfo != null) {
			result = String.format("%s-%s", primaryCategoryInfo.getProductCatCode(), primaryCategoryInfo.getProductCatNameTchi());
		}
		return result;
	}

	public HttpHeaders generateHeader() {
		String fileName = String.format(FILE_NAME, new SimpleDateFormat(ConstantType.DATE_FORMAT_YMD).format(new Date()), StringUtil.FILE_EXTENSION_EXCEL);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		header.setAccessControlExposeHeaders(Collections.singletonList(HttpHeaders.CONTENT_DISPOSITION));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}
}
