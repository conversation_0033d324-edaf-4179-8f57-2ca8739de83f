package com.shoalter.mms_product_api.service.base.pojo;

import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewPageableResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewSortResultDto;
import lombok.Data;

@Data
public class ProductMasterBasePageResultDto {
	private Integer totalPages;
	private Integer totalElements;
	private Boolean last;
	private Integer size;
	private Integer number;
	private Integer numberOfElements;
	private Boolean first;
	private Boolean empty;
	private ProductOverviewPageableResultDto pageable;
	private ProductOverviewSortResultDto sort;

	protected void defaultEmptyResult(Integer inputSize) {
		this.totalPages = 0;
		this.totalElements = 0;
		this.last = true;
		this.size = inputSize;
		this.number = 0;
		this.numberOfElements = 0;
		this.first = true;
		this.empty = true;
		this.pageable = ProductOverviewPageableResultDto.builder()
			.sort(new ProductOverviewSortResultDto(true, false, true))
			.offset(0)
			.pageNumber(0)
			.pageSize(inputSize)
			.unpaged(false)
			.paged(true)
			.build();
		this.sort = new ProductOverviewSortResultDto(true, false, true);
	}
}
