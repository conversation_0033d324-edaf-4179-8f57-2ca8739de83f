package com.shoalter.mms_product_api.service.notification.pojo.dto;

import com.shoalter.mms_product_api.config.type.ConstantType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ButtonDto {

	private List<TranslationDto> translations;
	private String url;


	public static ButtonDto getClickNowButton() {
		return ButtonDto.builder()
			.translations(List.of(
				TranslationDto.builder().language(ConstantType.LANGUAGE_EN).value(ConstantType.BUTTON_CLICK_NOW_EN).build(),
				TranslationDto.builder().language(ConstantType.LANGUAGE_ZH_HK).value(ConstantType.BUTTON_CLICK_NOW_ZH).build(),
				TranslationDto.builder().language(ConstantType.LANGUAGE_ZH_CN).value(ConstantType.BUTTON_CLICK_NOW_CN).build()
			))
			.url(null)
			.build();
	}
}
