package com.shoalter.mms_product_api.service.notification.pojo.dto;

import com.shoalter.mms_product_api.config.type.ConstantType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageLanguageDto {
	private String language;
	private TemplateDetailDto templateDetail;
	// 不需要轉導頁面,整個不用給
	private ButtonDetailDto buttonDetail;


	public static List<MessageLanguageDto> generateAllLanguageTemplate(TemplateDetailDto templateDetail, ButtonDetailDto buttonDetail) {
		MessageLanguageDto messageEn = new MessageLanguageDto();
		messageEn.setLanguage(ConstantType.LANGUAGE_EN);
		messageEn.setTemplateDetail(templateDetail);
		messageEn.setButtonDetail(buttonDetail);

		MessageLanguageDto messageHk = new MessageLanguageDto();
		messageHk.setLanguage(ConstantType.LANGUAGE_ZH_HK);
		messageHk.setTemplateDetail(templateDetail);
		messageHk.setButtonDetail(buttonDetail);

		MessageLanguageDto messageCn = new MessageLanguageDto();
		messageCn.setLanguage(ConstantType.LANGUAGE_ZH_CN);
		messageCn.setTemplateDetail(templateDetail);
		messageCn.setButtonDetail(buttonDetail);

		return List.of(messageEn, messageHk, messageCn);
	}
}
