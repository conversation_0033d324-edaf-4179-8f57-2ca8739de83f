package com.shoalter.mms_product_api.service.notification.enums;

import com.shoalter.mms_product_api.config.type.ConstantType;
import lombok.Getter;

@Getter
public enum NotificationCodeEnum {
	APPROVE_COMMISSION_MERCHANT_SUBMITTED(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "ApproveCommission_MerchantSubmitted", ""),
	APPROVE_COMMISSION_APPROVED(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "ApproveCommission_Approved", ""),
	APPROVE_COMMISSION_REJECTED(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "ApproveCommission_Rejected", ""),
	COMMISSION_RATE_DAILY_REPORT(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "CommissionRate_Daily_Report", ""),
	PRICE_ALERT_MERCHANT(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "PriceAlert", "Merchant1"),
	PRICE_ALERT_INTERNAL_USER(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "PriceAlert", "InternalUser1"),
	TOONIES_CHARGE_DAILY_REPORT(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "Toonies", "Charge_Daily_Report"),
	TOONIES_SKU_NOT_FOUND(ConstantType.HKTVMALL, ConstantType.PRODUCT_INVENTORY, "Toonies", "SKU_Not_Found_Report"),
	;

	private final String serviceCode;
	private final String typeCode;
	private final String subtypeCode;
	// default ""
	private final String label;

	NotificationCodeEnum(String serviceCode, String typeCode, String subtypeCode, String label) {
		this.serviceCode = serviceCode;
		this.typeCode = typeCode;
		this.subtypeCode = subtypeCode;
		this.label = label;
	}

}
