package com.shoalter.mms_product_api.service.notification.helper;

import com.google.gson.reflect.TypeToken;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.notification.enums.NotificationCodeEnum;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationAttachmentRequest;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationAttachmentResponse;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationRequest;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationResponse;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationResponseData;
import com.shoalter.mms_product_api.service.notification.pojo.dto.EmailDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.FailResponseDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MailHeadersDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MessageContentDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MessageDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MessageLanguageDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class NotificationHelper {

	private static final String SERVICE_NAME = "Notification";
	private static final String X_JWT_AUTH = "x-jwt-auth";
	private static final String SEND_MESSAGE = "/v2/messages";
	private static final String SEND_MESSAGE_ATTACHMENT = "/s2s/messages/attachment";
	private static final String MESSAGE_CONTENT = "messageContent";
	private static final String ATTACHMENT = "attachment";

	@Value("${notification.url}")
	private String NOTIFICATION_URL;

	private final TokenHelper tokenHelper;
	private final HttpRequestHelper httpRequestHelper;

	public ResponseDto<NotificationResponseData> requestSendMessage(UserDto userDto, List<NotificationRequest> requests) {
		NotificationResponse<NotificationResponseData> result = httpRequestHelper.requestForBody(HttpRequestDto.<List<NotificationRequest>, NotificationResponse<NotificationResponseData>>builder()
			.serviceName(SERVICE_NAME)
			.url(NOTIFICATION_URL + SEND_MESSAGE)
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requests)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<NotificationResponse<NotificationResponseData>>() {
				}.getType()))
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.NOTIFICATION_API_ERROR)
			.build());

		int status = (result != null && result.getData() != null && CollectionUtil.isEmpty(result.getData().getFail()))
			? StatusCodeEnum.SUCCESS.getCode()
			: StatusCodeEnum.FAIL.getCode();

		List<String> errorMessage = null;
		if (result != null && result.getData() != null && CollectionUtil.isNotEmpty(result.getData().getFail())) {
			errorMessage = result.getData().getFail().stream().map(FailResponseDto::getMessage).collect(Collectors.toList());
		}

		return ResponseDto.<NotificationResponseData>builder()
			.status(status)
			.data(result != null ? result.getData() : null)
			.errorMessageList(errorMessage)
			.build();
	}

	public ResponseDto<NotificationAttachmentResponse> requestSendMessageWithAttachment(NotificationAttachmentRequest request) {
		MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
		multiValueMap.add(MESSAGE_CONTENT, request.getMessageContentDto());
		multiValueMap.add(ATTACHMENT, request.getAttachment());

		NotificationAttachmentResponse result = httpRequestHelper.requestForBody(HttpRequestDto.<MultiValueMap<String, Object>, NotificationAttachmentResponse>builder()
			.serviceName(SERVICE_NAME)
			.url(NOTIFICATION_URL + SEND_MESSAGE_ATTACHMENT)
			.method(HttpMethod.POST)
			.customHeaders(generateMediaTypeHeaders())
			.body(multiValueMap)
			.user(null)
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.systemErrorCode(ErrorMessageTypeCode.NOTIFICATION_API_ERROR)
			.build());

		return ResponseDto.<NotificationAttachmentResponse>builder()
			.status(
				result != null && result.getCode() != null && Objects.equals(result.getCode(), StatusCodeEnum.SUCCESS.name())
					? StatusCodeEnum.SUCCESS.getCode()
					: StatusCodeEnum.FAIL.getCode()
			)
			.data(null)
			.errorMessageList(null)
			.build();
	}


	private HttpHeaders generateHeaders(UserDto userDto) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set(X_JWT_AUTH, tokenHelper.generateToken(userDto));
		return headers;
	}

	private HttpHeaders generateMediaTypeHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		return headers;
	}

	public NotificationAttachmentRequest generateSystemNotificationAttachmentRequest(FileSystemResource fileResource,
																					 List<UserNameAndEmailViewDo> receiverUsers,
																					 NotificationCodeEnum notificationCodeEnum,
																					 List<MessageLanguageDto> messageLanguageList,
																					 boolean sendMessageCenter
	) {
		List<Integer> recipientIds = new ArrayList<>();
		List<MailHeadersDto> mailHeadersList = new ArrayList<>();
		for (UserNameAndEmailViewDo receiverUser : receiverUsers) {
			if (StringUtils.isEmpty(receiverUser.getEmail())) {
				continue;
			}
			MailHeadersDto mailHeadersDto = new MailHeadersDto();
			mailHeadersDto.setFromAddress(ConstantType.NOTIFICATION_MAIL_FROM_ADDRESS);
			mailHeadersDto.setFromName(ConstantType.NOTIFICATION_MAIL_FROM_NAME);
			mailHeadersDto.setToName(receiverUser.getUserName());
			mailHeadersDto.setToAddress(receiverUser.getEmail());
			mailHeadersDto.setLanguage(ConstantType.LANGUAGE_EN);
			mailHeadersList.add(mailHeadersDto);

			recipientIds.add(receiverUser.getUserId());
		}

		EmailDto emailDto = new EmailDto();
		emailDto.setMailHeaderList(mailHeadersList);

		MessageDto messageDto = MessageDto.builder()
			.senderId(0)
			.recipientIdList(recipientIds)
			.serviceCode(notificationCodeEnum.getServiceCode())
			.typeCode(notificationCodeEnum.getTypeCode())
			.subtypeCode(notificationCodeEnum.getSubtypeCode())
			.label(notificationCodeEnum.getLabel())
			.messageLanguageList(messageLanguageList)
			.sendMessageCenter(sendMessageCenter)
			.build();

		MessageContentDto messageContentDto = MessageContentDto.builder()
			.message(messageDto)
			.email(emailDto)
			.build();

		return NotificationAttachmentRequest.builder()
			.messageContentDto(messageContentDto)
			.attachment(fileResource)
			.build();
	}

	public NotificationAttachmentRequest generateSystemNotificationAttachmentRequestForGroupReceiver(
		FileSystemResource fileResource,
		NotificationCodeEnum notificationCodeEnum,
		List<MessageLanguageDto> messageLanguageList,
		boolean sendMessageCenter,
		String toName,
		String toAddress
	) {
		MessageDto messageDto = MessageDto.builder()
			.senderId(0)
			.serviceCode(notificationCodeEnum.getServiceCode())
			.typeCode(notificationCodeEnum.getTypeCode())
			.subtypeCode(notificationCodeEnum.getSubtypeCode())
			.label(notificationCodeEnum.getLabel())
			.sendMessageCenter(sendMessageCenter)
			.messageLanguageList(messageLanguageList)
			.recipientIdList(List.of(0))
			.build();

		EmailDto emailDto = new EmailDto();
		emailDto.setMailHeaderList(List.of(
			MailHeadersDto.builder()
				.fromName(ConstantType.NOTIFICATION_MAIL_FROM_NAME)
				.fromAddress(ConstantType.NOTIFICATION_MAIL_FROM_ADDRESS)
				.toName(toName)
				.toAddress(toAddress)
				.language(ConstantType.LANGUAGE_EN)
				.build()
		));

		MessageContentDto messageContentDto = MessageContentDto.builder()
			.message(messageDto)
			.email(emailDto)
			.build();

		return NotificationAttachmentRequest.builder()
			.messageContentDto(messageContentDto)
			.attachment(fileResource)
			.build();
	}

	public File createTempExcelFile(byte[] data, String excelFileName, String fileDateFormat) throws IOException {
		String fileName = String.format(excelFileName, new SimpleDateFormat(fileDateFormat).format(new Date()), StringUtil.FILE_EXTENSION_EXCEL);
		File tempFile = new File(System.getProperty(ConstantType.FILE_TEMP_PROPERTY), fileName);
		try (FileOutputStream fos = new FileOutputStream(tempFile)) {
			fos.write(data);
		}
		return tempFile;
	}

}
