package com.shoalter.mms_product_api.service.bu_export_history;

import com.shoalter.mms_product_api.config.product.BuExportHistoryEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.BuPartyHistoryOverviewProjection;
import com.shoalter.mms_product_api.service.product.BuExportHistoryOverviewRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ViewBuHistoryDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.PageUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ViewBuExportHistoryService {

	private final BuExportHistoryRepository buExportHistoryRepository;

	public ResponseDto<PageDto<ViewBuHistoryDto>> start(UserDto userDto, BuExportHistoryOverviewRequestDto requestDto) {
		Date searchStartDate = null;
		Date searchEndDate = null;
		if (!StringUtil.isEmpty(requestDto.getStartDate()) && !StringUtil.isEmpty(requestDto.getEndDate())) {
			searchStartDate = DateUtil.timestampToDateStartTime(Long.valueOf(requestDto.getStartDate()));
			searchEndDate = DateUtil.timestampToDateEndTime(Long.valueOf(requestDto.getEndDate()));
		}

		Pageable pageable = PageUtil.createPageable(requestDto.getSize(), requestDto.getPage(), requestDto.getOrderBy());

		Page<BuPartyHistoryOverviewProjection> pageList;
		if (RoleCode.THIRD_PARTY_ROLES.contains(userDto.getRoleCode())) {
			Pair<List<String>, List<String>> queryBuCodes = getRmRoleQueryBuCodesByBuRequest(requestDto.getBuCodes());
			pageList = buExportHistoryRepository.findRmRoleBuHistoryOverviewByUserCodeOrBuOrCreateTime(userDto.getUserCode(), queryBuCodes.getLeft(), queryBuCodes.getRight(), searchStartDate, searchEndDate, pageable);
		} else {
			List<String> queryCreateByBuCodes = getDefaultRoleQueryBuCodesByBuRequest(requestDto.getBuCodes());
			pageList = buExportHistoryRepository.findBuHistoryOverviewByUserCodeOrBuOrCreateTime(userDto.getUserCode(), queryCreateByBuCodes, searchStartDate, searchEndDate, pageable);
		}
		long totalCount = pageList.getTotalElements();
		long pageCount = pageList.getTotalPages();

		List<ViewBuHistoryDto> viewBuHistoryDtoList = convertToViewBuHistoryDto(pageList.getContent());
		PageDto<ViewBuHistoryDto> page = PageDto.<ViewBuHistoryDto>builder().list(viewBuHistoryDtoList).pageCount(pageCount).totalCount(totalCount).build();
		return ResponseDto.<PageDto<ViewBuHistoryDto>>builder().data(page).status(1).build();
	}

	/**
	 * @param requestBuCodes
	 * @return Left : queryCreateByBuCodes <br>
	 * Right : queryRmBuCodes
	 */
	private Pair<List<String>, List<String>> getRmRoleQueryBuCodesByBuRequest(List<BuExportHistoryEnum> requestBuCodes) {
		if (CollectionUtil.isEmpty(requestBuCodes)) {
			return Pair.of(BuExportHistoryEnum.DEFAULT_BU_LIST, BuExportHistoryEnum.THIRD_PARTY_BU_LIST);
		} else {
			List<String> queryDefaultBuCodes = new ArrayList<>();
			List<String> queryThirdPartyCodes = new ArrayList<>();
			requestBuCodes.forEach(buCode -> {
				if (BuExportHistoryEnum.DEFAULT_BU_LIST.contains(buCode.getExportHistoryBu())) {
					queryDefaultBuCodes.add(buCode.getExportHistoryBu());
				} else if (BuExportHistoryEnum.THIRD_PARTY_BU_LIST.contains(buCode.getExportHistoryBu())) {
					queryThirdPartyCodes.add(buCode.getExportHistoryBu());
				}
			});
			return Pair.of(queryDefaultBuCodes, queryThirdPartyCodes);
		}
	}

	private List<String> getDefaultRoleQueryBuCodesByBuRequest(List<BuExportHistoryEnum> requestBuCodes) {
		if (CollectionUtil.isEmpty(requestBuCodes)) {
			return BuExportHistoryEnum.ALL_BU_LIST;
		} else {
			return requestBuCodes.stream().map(BuExportHistoryEnum::getExportHistoryBu).collect(Collectors.toList());
		}
	}

	private List<ViewBuHistoryDto> convertToViewBuHistoryDto(List<BuPartyHistoryOverviewProjection> buHistoryOverviewProjection) {
		List<ViewBuHistoryDto> viewThirdPartyHistoryDtoList = new ArrayList<>();

		for (BuPartyHistoryOverviewProjection buHistoryData : buHistoryOverviewProjection) {
			viewThirdPartyHistoryDtoList.add(ViewBuHistoryDto.builder()
				.id(buHistoryData.getHistoryRecordId())
				.buCode(buHistoryData.getBuCode())
				.exportType(buHistoryData.getExportType())
				.status(buHistoryData.getRecordStatus())
				.createTime(buHistoryData.getCreateTime())
				.createBy(buHistoryData.getCreateBy())
				.productCounts(buHistoryData.getProductCounts())
				.isLatestData(buHistoryData.getIsLatestData())
				.isExpiredData(checkIsExpiredByBuCreateTime(buHistoryData.getBuCode(), buHistoryData.getCreateTime()))
				.failReasons(convertErrorReasonStrToList(buHistoryData.getFailReason()))
				.informProductToSyncProgress(buHistoryData.getInformProductToSyncProgress())
				.build());
		}
		return viewThirdPartyHistoryDtoList;
	}

	private List<String> convertErrorReasonStrToList(String errorReasonStr) {
		if (!StringUtil.isEmpty(errorReasonStr)) {
			return List.of(errorReasonStr.split(","));
		}
		return null;
	}

	private boolean checkIsExpiredByBuCreateTime(String buCode, Date createTime) {
		if (StringUtils.isBlank(buCode) || createTime == null) {
			return false;
		}
		// check HKTV createDate, expired: three days ago
		if (buCode.equalsIgnoreCase(BuExportHistoryEnum.HKTV.getExportHistoryBu())) {
			LocalDateTime hktvExpiredDate = LocalDateTime.now().minusDays(BuExportHistoryEnum.HKTV.getExpiredDay()).toLocalDate().atStartOfDay();
			LocalDateTime buHistoryCreateTime = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
			return buHistoryCreateTime.isBefore(hktvExpiredDate);
		}

		// check LittleMall createDate, expired: three days ago
		if (buCode.equalsIgnoreCase(BuExportHistoryEnum.LITTLE_MALL.getExportHistoryBu())) {
			LocalDateTime littleMallExpiredDate = LocalDateTime.now().minusDays(BuExportHistoryEnum.LITTLE_MALL.getExpiredDay()).toLocalDate().atStartOfDay();
			LocalDateTime buHistoryCreateTime = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
			return buHistoryCreateTime.isBefore(littleMallExpiredDate);
		}

		return false;
	}
}
