package com.shoalter.mms_product_api.service.bu_export_history;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.AffiliateExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.HktvExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.HktvMallSkuRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.LittleMallExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.LittleMallSkuRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.AffiliateExportInfoDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.BuExportHistoryDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.HktvExportInfoDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.HktvMallSkuDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.LittleMallExportInfoDo;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.LittleMallSkuDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.AffiliateProductDataProjection;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallProductDataProjection;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.helper.CreateProductExcelHelper;
import com.shoalter.mms_product_api.service.product.helper.EditLittleMallProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.EditProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.MerchantHelper;
import com.shoalter.mms_product_api.service.product.helper.TmallProductExcelHelper;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallExportDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
@AllArgsConstructor
public class DownloadBuDataInExcelService {

	private final BuExportHistoryRepository buExportHistoryRepository;
	private final TmallProductExcelHelper tmallProductExcelHelper;
	private final MerchantHelper merchantHelper;
	private final MerchantStoreRepository merchantStoreRepository;
	private final SysParmRepository sysParmRepository;
	private final ContractRepository contractRepository;
	private final Gson gson;
	private final HktvMallSkuRepository hktvMallSkuRepository;
	private final EditProductTemplateHelper editProductTemplateHelper;
	private final HktvExportInfoRepository hktvExportInfoRepository;
	private final LittleMallExportInfoRepository littleMallExportInfoRepository;
	private final LittleMallSkuRepository littleMallSkuRepository;
	private final EditLittleMallProductTemplateHelper editLittleMallProductTemplateHelper;
	private final MessageSource messageSource;
	private final AffiliateExportInfoRepository affiliateExportInfoRepository;
	private final CreateProductExcelHelper createProductExcelHelper;

	public HttpEntity<ByteArrayResource> start(UserDto userDto, Integer id, Integer storeId) {
		BuExportHistoryDo buExportHistoryDo = buExportHistoryRepository.findById(id)
			.orElseThrow(() -> new BadRequestException(messageSource.getMessage("message146", null, null)));

		switch (buExportHistoryDo.getBu()) {
			case TMALL:
				return produceTmallFile(userDto, id, storeId);
			case HKTV:
				return produceHktvFile(userDto, id);
			case AFFILIATE:
				return produceAffiliateFile(userDto, id);
			case LITTLE_MALL:
				return produceLittleMallFile(userDto, id);
			default:
				throw new BadRequestException(messageSource.getMessage("message146", null, null));
		}
	}

	private HttpEntity<ByteArrayResource> produceTmallFile(UserDto userDto, Integer id, Integer storeId) {
		if (storeId == null) {
			throw new NoDataException();
		}
		checkUserRole(userDto);
		//query for check user permission
		List<Integer> userMerchantList = merchantHelper.findMerchantIdByRole(userDto);
		Integer storeMerchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);
		//check user permission
		if (CollectionUtil.isEmpty(userMerchantList) || !userMerchantList.contains(storeMerchantId)) {
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}

		// get data
		List<TmallProductDataProjection> tmallProductDataList =
			buExportHistoryRepository.findTmallProductDataByIdAndStatus(id, ExportStatusEnum.SUCCESS.getExportStatus());

		if (tmallProductDataList == null || tmallProductDataList.isEmpty()) {
			throw new SystemI18nException("message223");
		}

		List<HktvProductFieldDto> hktvProductFieldDtoList =
			tmallProductDataList.stream().map(tmallProductData -> {
				HktvProductFieldDto hktvProductFieldDto = gson.fromJson(tmallProductData.getSkuData(), HktvProductFieldDto.class);
				hktvProductFieldDto.setStorefrontStoreCode(tmallProductData.getStorefrontStoreCode());
				return hktvProductFieldDto;
			}).collect(Collectors.toList());

		String fileName = tmallProductDataList.get(0).getExportType();
		String fileExtension;
		Integer contractId = contractRepository.findContractIdByStoreId(storeId);
		List<SysParmDo> sysParmList = sysParmRepository.findBySegments(AbstractReport.SEGMENT_PARENT_LIST);
		ByteArrayOutputStream os = new ByteArrayOutputStream();

		try {
			log.info("start create file,total data size {}", hktvProductFieldDtoList.size());
			if (hktvProductFieldDtoList.size() > ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
				fileExtension = StringUtil.PERIOD + StringUtil.FILE_EXTENSION_ZIP;
				List<Workbook> workbooks = new ArrayList<>();

				for (List<HktvProductFieldDto> hktvProductFieldDtoPartitionList : ListUtils.partition(hktvProductFieldDtoList, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
					workbooks.add(tmallProductExcelHelper.generateFromTmallData(userDto, storeId, contractId, null,
						hktvProductFieldDtoPartitionList, sysParmList, false));
				}

				workbooksToZipFile(os, workbooks, fileName);
			} else {
				fileExtension = StringUtil.PERIOD + StringUtil.FILE_EXTENSION_EXCEL;
				Workbook workbook = tmallProductExcelHelper.generateFromTmallData(userDto, storeId, contractId, new ArrayList<>(),
					hktvProductFieldDtoList, sysParmList, false);
				writeWorkbookToOutputStream(workbook, os);
			}
		} catch (IOException e) {
			log.error("Error occurred while processing excel or zip : {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}


		return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(fileName, fileExtension), HttpStatus.OK);
	}

	public HttpHeaders getResponseHeader(String fileName, String fileExtension) {
		String fileNameWithExtension = fileName + fileExtension;
		HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("application", "octet-stream"));
		header.setAccessControlExposeHeaders(Collections.singletonList(HttpHeaders.CONTENT_DISPOSITION));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileNameWithExtension);
		return header;
	}

	private void writeWorkbookToOutputStream(Workbook workbook, ByteArrayOutputStream os) throws IOException {
		workbook.write(os);
	}

	private void workbooksToZipFile(ByteArrayOutputStream byteArrayOutputStream, List<Workbook> workbooks, String baseFileName) {
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
			int index = 1;
			for (Workbook workbook : workbooks) {
				String splitFileName = baseFileName + StringUtil.UNDERLINE + index++ + StringUtil.PERIOD + StringUtil.FILE_EXTENSION_EXCEL;
				try (ByteArrayOutputStream tempByteArrayOutputStream = new ByteArrayOutputStream()) {
					writeWorkbookToOutputStream(workbook, tempByteArrayOutputStream);
					zipOutputStream.putNextEntry(new ZipEntry(splitFileName));
					zipOutputStream.write(tempByteArrayOutputStream.toByteArray());
				} finally {
					zipOutputStream.closeEntry();
				}
			}
		} catch (IOException e) {
			log.error("Error occurred while creating ZIP file: {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}
	}

	/**
	 * for third party bu
	 */
	private void checkUserRole(UserDto userDto) {
		if (RoleCode.THIRD_PARTY_ROLES.contains(userDto.getRoleCode())) {
			return;
		}
		throw new SystemI18nException("message28", userDto.getRoleCode());
	}

	private void checkUserRoleFromCreatedBy(UserDto userDto, String createdBy) {
		if (StringUtils.equals(userDto.getUserCode(), createdBy)) {
			return;
		}
		throw new SystemI18nException("message28", userDto.getRoleCode());
	}

	public HttpEntity<ByteArrayResource> produceHktvFile(UserDto userDto, Integer historyId) {
		HktvExportInfoDo hktvExportInfoDo = hktvExportInfoRepository.findById(historyId).orElseThrow(NoDataException::new);
		List<HktvMallSkuDo> hktvMallSkuDoList = hktvMallSkuRepository.findByHktvExportInfoId(historyId);
		if (hktvExportInfoDo.getStatus() != ExportStatusEnum.SUCCESS || CollectionUtil.isEmpty(hktvMallSkuDoList)) {
			throw new NoDataException();
		}

		checkUserRoleFromCreatedBy(userDto, hktvExportInfoDo.getCreatedBy());
		long startTime = System.currentTimeMillis();

		String filePostfix = DateTimeFormatter.ofPattern(ConstantType.DATE_TIME_FORMAT_ONLY_NUMBER)
			.format(hktvExportInfoDo.getCreatedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
		List<SingleEditProductDto> singleEditProductDtoList = hktvMallSkuDoList.stream().map(HktvMallSkuDo::getSkuData).collect(Collectors.toList());

		//produce file
		Pair<String, ByteArrayOutputStream> fileResult = editProductTemplateHelper.generateEditProductFile(hktvExportInfoDo.getTemplateType(), singleEditProductDtoList, filePostfix);
		if (StringUtil.FILE_EXTENSION_EXCEL.equals(fileResult.getLeft()) &&
			hktvExportInfoDo.getTemplateType() == TemplateTypeEnum.ALL_COLUMN &&
			CollectionUtil.isNotEmpty(singleEditProductDtoList)) {
			String oneStoreCode = singleEditProductDtoList.get(0).getProduct().getAdditional().getHktv().getStores();
			filePostfix = oneStoreCode + StringUtil.UNDERLINE + filePostfix;
		}
		log.info("Time taken to create {} product for hktv upload template: {} milliseconds", singleEditProductDtoList.size(), (System.currentTimeMillis() - startTime));

		ByteArrayResource byteArrayResource = new ByteArrayResource(fileResult.getRight().toByteArray());
		HttpHeaders httpHeaders = generateResponseHeader(hktvExportInfoDo.getTemplateType(), filePostfix, fileResult.getLeft());
		return new ResponseEntity<>(byteArrayResource, httpHeaders, HttpStatus.OK);
	}

	public HttpEntity<ByteArrayResource> produceLittleMallFile(UserDto userDto, Integer historyId) {
		LittleMallExportInfoDo littleMallExportInfoDo = littleMallExportInfoRepository.findById(historyId).orElseThrow(NoDataException::new);
		List<LittleMallSkuDo> littleMallSkuDoList = littleMallSkuRepository.findByLittleMallExportInfoId(historyId);
		if (littleMallExportInfoDo.getStatus() != ExportStatusEnum.SUCCESS || CollectionUtil.isEmpty(littleMallSkuDoList)) {
			throw new NoDataException();
		}
		checkUserRoleFromCreatedBy(userDto, littleMallExportInfoDo.getCreatedBy());
		long startTime = System.currentTimeMillis();

		String filePostfix = DateTimeFormatter.ofPattern(ConstantType.DATE_TIME_FORMAT_ONLY_NUMBER)
			.format(littleMallExportInfoDo.getCreatedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
		List<LittleMallExportDto> littleMallExportDtoList = littleMallSkuDoList.stream().map(LittleMallSkuDo::getSkuData).collect(Collectors.toList());
		Pair<String, ByteArrayOutputStream> fileResult = editLittleMallProductTemplateHelper.generateEditProductFile(userDto, littleMallExportDtoList, filePostfix);

		// only one store and size <= 10000, file name : Product_export_all_column_{{storeFrontStoreCode}}_yyyyMMddhhmmss.xlsx
		if (StringUtil.FILE_EXTENSION_EXCEL.equals(fileResult.getLeft())) {
			String oneStoreCode = littleMallExportDtoList.get(0).getStoreFrontStoreCode();
			filePostfix = oneStoreCode + StringUtil.UNDERLINE + filePostfix;
		}
		log.info("Time taken to create {} product for little mall upload template: {} milliseconds", littleMallExportDtoList.size(), (System.currentTimeMillis() - startTime));

		ByteArrayResource byteArrayResource = new ByteArrayResource(fileResult.getRight().toByteArray());
		HttpHeaders httpHeaders = generateResponseHeader(TemplateTypeEnum.ALL_COLUMN, filePostfix, fileResult.getLeft());
		return new ResponseEntity<>(byteArrayResource, httpHeaders, HttpStatus.OK);
	}

	public HttpHeaders generateResponseHeader(TemplateTypeEnum templateType, String postfix, String fileExtension) {
		String fileName = editProductTemplateHelper.generateEditProductFileName(templateType, postfix, fileExtension);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		header.setAccessControlExposeHeaders(Collections.singletonList(HttpHeaders.CONTENT_DISPOSITION));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}

	public HttpEntity<ByteArrayResource> produceAffiliateFile(UserDto userDto, Integer historyId) {
		checkUserRole(userDto);

		AffiliateExportInfoDo affiliateExportInfoDo = affiliateExportInfoRepository.findById(historyId).orElseThrow(NoDataException::new);
		if (affiliateExportInfoDo.getStatus() != ExportStatusEnum.SUCCESS) {
			throw new NoDataException();
		}

		checkPermission(userDto, affiliateExportInfoDo.getStoreId());

		List<AffiliateProductDataProjection> skuDataList =
			buExportHistoryRepository.findAffiliateProductDataByIdAndStatus(historyId, ExportStatusEnum.SUCCESS.name());
		if (CollectionUtils.isEmpty(skuDataList)) {
			log.info("skuDataList is empty.");
			throw new SystemI18nException("message295");
		}

		List<HktvProductFieldDto> productExcelList =
			skuDataList.stream().map(skuData -> gson.fromJson(skuData.getSkuData(), HktvProductFieldDto.class)).collect(Collectors.toList());

		Integer contractId = contractRepository.findContractIdByStoreId(affiliateExportInfoDo.getStoreId());
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		String fileExtension;
		String fileName = skuDataList.get(0).getExportType() + StringUtil.UNDERLINE + DateTimeFormatter.ofPattern(ConstantType.DATE_TIME_FORMAT_FILE_NAME).format(LocalDateTime.now());

		try {
			log.info("start create affiliate file,total data size {}", productExcelList.size());
			if (productExcelList.size() > ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
				fileExtension = StringUtil.PERIOD + StringUtil.FILE_EXTENSION_ZIP;
				List<Workbook> workbooks = new ArrayList<>();

				for (List<HktvProductFieldDto> batchProductExcelDtoList : ListUtils.partition(productExcelList, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
					Workbook workbook = createProductExcelHelper.generateExcel(userDto, batchProductExcelDtoList, affiliateExportInfoDo.getStoreId(), contractId, false);
					workbooks.add(workbook);
				}

				workbooksToZipFile(os, workbooks, fileName);
			} else {
				fileExtension = StringUtil.PERIOD + StringUtil.FILE_EXTENSION_EXCEL;
				Workbook workbook = createProductExcelHelper.generateExcel(userDto, productExcelList, affiliateExportInfoDo.getStoreId(), contractId, false);
				writeWorkbookToOutputStream(workbook, os);
			}
		} catch (IOException e) {
			log.error("Error occurred while creating ZIP file: {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}

		return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(fileName, fileExtension), HttpStatus.OK);

	}

	private void checkPermission(UserDto userDto, Integer storeId) {
		List<Integer> userMerchantList = merchantHelper.findMerchantIdByRole(userDto);
		Integer storeMerchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);

		if (CollectionUtil.isEmpty(userMerchantList) || !userMerchantList.contains(storeMerchantId)) {
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}
	}
}
