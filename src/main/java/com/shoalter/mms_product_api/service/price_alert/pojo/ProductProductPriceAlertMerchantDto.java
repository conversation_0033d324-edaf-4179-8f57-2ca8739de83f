package com.shoalter.mms_product_api.service.price_alert.pojo;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductProductPriceAlertMerchantDto {

	private List<UserNameAndEmailViewDo> receiverUsers;
	private List<ProductPriceAlertDto> productPriceAlerts;

}
