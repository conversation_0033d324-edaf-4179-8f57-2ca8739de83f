package com.shoalter.mms_product_api.service.price_alert.enums;

import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum ProductPriceAlertMerchantEnum implements TemplateInterface<ProductPriceAlertMerchantEnum> {

	SKU_ID(0, "SKU ID", null, null, null),
	HKTV_ORIGINAL_PRICE(1, "HKTVmall price (Original price)", null, null, null),
	HKTV_SELLING_PRICE(2, "HKTVmall price (Selling price)", null, null, null),
	TMALL_ORIGINAL_PRICE(3, "Tmall price (Original price)", null, null, null),
	TMALL_SELLING_PRICE(4, "Tmall price (Selling price)", null, null, null),
	TMALL_URL(5, "Tmall URL", null, null, null),

	;


	private final Integer columnNumber;
	private final String columnName;
	private final String validationName;
	private final ProductPriceAlertMerchantEnum parent;
	private final String parentSegment;

	ProductPriceAlertMerchantEnum(Integer columnNumber, String columnName, String validationName, ProductPriceAlertMerchantEnum parent, String parentSegment) {
		this.columnNumber = columnNumber;
		this.columnName = columnName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}

}
