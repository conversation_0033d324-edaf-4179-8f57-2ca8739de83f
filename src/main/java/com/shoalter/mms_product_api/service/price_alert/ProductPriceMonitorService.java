package com.shoalter.mms_product_api.service.price_alert;

import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.product.helper.BusUnitHelper;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailSkuDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * Service for monitoring and updating product prices from third-party platforms
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductPriceMonitorService {

	public static final int DEFAULT_CHECKING_PRICE_DAYS = 7;
	private final ProductPriceMonitorProductRepository productPriceMonitorProductRepository;
	private final MmsThirdPartySkuHelper mmsThirdPartySkuHelper;
	private final BusUnitHelper busUnitHelper;
	private final SysParmRepository sysParmRepository;
	private final Executor oneboundTaskExecutor;

	/**
	 * Update monitor product prices by fetching data from third-party platform
	 */
	@Async
	public void updateMonitorProductPrice() {
		long startTime = System.currentTimeMillis();
		log.info("[cron-job][price-monitor/update-price] Starting update product price monitor product price process");

		// Find business unit ID
		Integer busUnitId = busUnitHelper.findBusinessId(ConstantType.HKTV);

		SysParmDo sysParmDo = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRICE_MONITOR, ConstantType.PRICE_ALERT_CODE_PRICE_UPDATE_THRESHOLD_DAY, 1).stream()
			.findFirst()
			.orElseThrow(() -> new SystemException("PRICE_ALERT_CODE_PRICE_UPDATE_THRESHOLD_DAY not found for code"));
		int checkingPriceDayThreshold = sysParmDo.getParmValue() != null ? Integer.parseInt(sysParmDo.getParmValue()) : DEFAULT_CHECKING_PRICE_DAYS;

		// 1. Fetch all ENABLE records for TMALL platform using repository query
		List<ProductPriceMonitorProductDo> enabledProducts = productPriceMonitorProductRepository
			.findByActiveIndAndTargetPlatformAndBusUnitIdAndTargetPriceUpdatedDateAfter(ActiveInd.ENABLE.getValue(), ThirdPartySourceEnum.TMALL.name(), busUnitId, checkingPriceDayThreshold);

		if (CollectionUtils.isEmpty(enabledProducts)) {
			log.info("[cron-job][price-monitor/update-price] No enabled product price monitor products found");
			return;
		} else {
			log.info("[cron-job][price-monitor/update-price] Found {} enabled product price monitor products", enabledProducts.size());
		}

		// 2. Group results by targetProductCode
		Map<String, List<ProductPriceMonitorProductDo>> productGroups = enabledProducts.stream()
			.filter(p -> p.getTargetProductCode() != null && !p.getTargetProductCode().isBlank())
			.collect(Collectors.groupingBy(ProductPriceMonitorProductDo::getTargetProductCode));
		log.info("[cron-job][price-monitor/update-price] Found {} product groups to process", productGroups.size());

		// 3. Process each product group
		// to list for parallel processing
		List<Map.Entry<String, List<ProductPriceMonitorProductDo>>> productGroupList = new ArrayList<>(productGroups.entrySet());
		productGroupList.stream().map(entry -> CompletableFuture.supplyAsync(
				() -> processProductGroup(entry.getKey(), entry.getValue()),
				oneboundTaskExecutor
			)).collect(Collectors.toList())
			.forEach(CompletableFuture::join);

		long endTime = System.currentTimeMillis();
		log.info("[cron-job][price-monitor/update-price] Completed updating monitor product prices for business unit HKTV, execution time: {} ms", (endTime - startTime));
	}

	/**
	 * Process a group of products with the same targetProductCode
	 *
	 * @param targetProductCode The product code in the third-party platform
	 * @param products          List of products with the same targetProductCode
	 * @return Count of records processed
	 */
	private int processProductGroup(String targetProductCode, List<ProductPriceMonitorProductDo> products) {
		Integer storeId = products.get(0).getStoreId();
		log.info("Processing product price monitor product group with targetProductCode: {}, storeId: {}, productCount: {}", targetProductCode, storeId, products.size());

		try {
			// 3. Call MmsThirdPartySkuHelper to fetch third-party details
			OneBoundDetailRequestDto requestDto = OneBoundDetailRequestDto.builder()
				.storeId(storeId)
				.targetProductCode(targetProductCode)
				.build();

			OneBoundDetailResponseDto responseDto = mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(requestDto);

			// 4. Handle response, similar to PriceAlertService
			if (responseDto == null) {
				log.error("Error fetching one-bound detail for targetProductCode {}: responseDto is null", targetProductCode);
				updateProductsWithError(products, MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR, "One-bound response is null");
				return products.size();
			} else if (StatusCodeEnum.SUCCESS.getCode() != responseDto.getStatus() || responseDto.getData() == null) {
				log.error("Error fetching one-bound detail for targetProductCode {}: {}", targetProductCode, StringUtil.generateErrorMessage(responseDto.getErrorMessageList()));
				updateProductsWithError(products, MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR, "One-bound response is not success or data is null: " + StringUtil.generateErrorMessage(responseDto.getErrorMessageList()));
				return products.size();
			}

			// Extract SKUs and group by SKU code
			List<OneBoundDetailSkuDto> oneBoundSkus = responseDto.getData().getSkus();
			Map<String, OneBoundDetailSkuDto> skuCodeMap = oneBoundSkus.stream()
				.collect(Collectors.toMap(OneBoundDetailSkuDto::getSkuCode, sku -> sku, (a, b) -> a));

			// 5. Update product data
			String detailUrl = responseDto.getData().getDetailUrl();
			LocalDateTime now = LocalDateTime.now();

			for (ProductPriceMonitorProductDo product : products) {
				String targetSkuCode = product.getTargetSkuCode();
				if (targetSkuCode != null && skuCodeMap.containsKey(targetSkuCode)) {
					// SKU found, update price and URL
					OneBoundDetailSkuDto skuDetail = skuCodeMap.get(targetSkuCode);
					product.setTargetOriginalPrice(skuDetail.getOriginalPrice());
					product.setTargetSellingPrice(skuDetail.getPrice());
					product.setTargetUrl(detailUrl);
					product.setPriceStatus(MonitorProductPriceStatus.PRICE_UPDATED.getValue());
				} else if (StringUtil.isEmpty(targetSkuCode)) {
					product.setTargetOriginalPrice(responseDto.getData().getOriginalPrice());
					product.setTargetSellingPrice(responseDto.getData().getPrice());
					product.setTargetUrl(detailUrl);
					product.setPriceStatus(MonitorProductPriceStatus.PRICE_UPDATED.getValue());
				}else {
					// SKU not found
					product.setTargetOriginalPrice(null);
					product.setTargetSellingPrice(null);
					product.setPriceStatus(MonitorProductPriceStatus.PRICE_NOT_FOUND.getValue());
				}

				// 6. Update common fields
				product.setTargetPriceUpdatedDate(now);
				product.setLastUpdatedDate(now);
				product.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
			}

			// Save all products in one batch operation instead of individual saves
			productPriceMonitorProductRepository.saveAll(products);

			return products.size();

		} catch (Exception e) {
			log.error("Error processing product price monitor product group with targetProductCode: {}", targetProductCode, e);
			updateProductsWithError(products, MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR, "Server error: " + e.getMessage());
			return products.size();
		}
	}

	/**
	 * Update a list of products with error status
	 *
	 * @param products     List of products to update
	 * @param status       The status to set
	 * @param errorMessage Error message to log
	 */
	private void updateProductsWithError(List<ProductPriceMonitorProductDo> products, MonitorProductPriceStatus status, String errorMessage) {
		if (CollectionUtils.isEmpty(products)) {
			return;
		}

		LocalDateTime now = LocalDateTime.now();
		for (ProductPriceMonitorProductDo product : products) {
			product.setPriceStatus(status.getValue());
			product.setLastUpdatedDate(now);
			product.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
		}

		// Save all products in one batch operation instead of individual saves
		productPriceMonitorProductRepository.saveAll(products);

		log.error("Updated {} product price monitor products with error status {}: {}", products.size(), status, errorMessage);
	}
}
