package com.shoalter.mms_product_api.service.price_alert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriceAlertsProcessDto {
	String cronJobUuid;
	ProcessStatus processStatus;

	List<String> errorMessageList;

	public enum ProcessStatus {
		ERROR,
		NO_RECORD,
		PRICE_CHECKED,
		OFFLINE,
		NOTIFY,
		OFFLINE_CHECKED,
	}
}
