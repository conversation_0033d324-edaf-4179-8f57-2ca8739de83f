package com.shoalter.mms_product_api.service.little_mall_product.pojo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class LittleMallFindRelationSettingResponseData {
    @JsonProperty("storefront_store_code")
    @SerializedName("storefront_store_code")
    private String storefrontStoreCode;

    @JsonProperty("product_id")
    @SerializedName("product_id")
    private String productId;

    private Boolean enable;

    @JsonProperty("product_field")
    @SerializedName("product_field")
    private List<ProductFieldResponseData> productField;

    @JsonProperty("skus_path")
    @SerializedName("skus_path")
    private List<SkusPathResponseData> skusPath;
}
