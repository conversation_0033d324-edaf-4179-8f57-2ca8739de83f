package com.shoalter.mms_product_api.service.little_mall_product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.mapper.SingleEditProductDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallRelationHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallVariantSkuRequestData;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class LittleMallVariantSaveService {
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final PermissionHelper permissionHelper;
	private final SingleEditProductDtoMapper singleEditProductDtoMapper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final LittleMallRelationHelper littleMallRelationHelper;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final MessageSource messageSource;
	private final Gson gson;

	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, LittleMallVariantSkuRequestData requestData) {
		Integer merchantId = (requestData.getMerchantId() == null) ? userDto.getMerchantId() : requestData.getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);

		if (CollectionUtils.isEmpty(requestData.getVariantSkuList())) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message371", null, null)));
		}

		// check relation duplicated
		List<String> errorMessages = new ArrayList<>();
		checkLittleMallProductHelper.checkRelation(requestData.getRelation(), errorMessages);
		if (!errorMessages.isEmpty()) {
			return ResponseDto.fail(errorMessages);
		}

		ProductMasterResultDto primaryFromProductMaster = findPrimarySku(userDto, requestData);

		// check relation setting
		Pair<Boolean, String> checkRelationResult = littleMallRelationHelper.queryAndCheckRelationSetting(
			userDto,
			primaryFromProductMaster.getProductId(),
			primaryFromProductMaster.getAdditional().getLittleMall().getStoreCode(),
			requestData.getRelation()
		);
		if (Boolean.FALSE.equals(checkRelationResult.getLeft())) {
			return ResponseDto.fail(List.of(checkRelationResult.getRight()));
		}

		List<SingleEditProductDto> variantList = generateVariantList(primaryFromProductMaster, requestData);

		checkLittleMallProductHelper.checkCreateLittleMallProduct(userDto, variantList, primaryFromProductMaster.getAdditional().getLittleMall(), errorMessages);
		if (!errorMessages.isEmpty()) {
			return ResponseDto.fail(errorMessages);
		}

		updateRelation(userDto, requestData);

		Long recordId = SpringBeanProvider.getBean(LittleMallVariantSaveService.class).processDataAndSaveProductRecord(userDto, merchantId, variantList);
		SpringBeanProvider.getBean(LittleMallVariantSaveService.class).callProductMasterAndUpdateRecord(userDto, recordId);

		return ResponseDto.success(ProductRecordResponseDto.builder().recordId(recordId).build());
	}

	private ProductMasterResultDto findPrimarySku(UserDto userDto, LittleMallVariantSkuRequestData requestData) {
		ProductMasterResultDto primaryFromProductMaster = checkLittleMallProductHelper.findPrimarySku(userDto, requestData.getRelation().getProductId(), requestData.getRelation().getStorefrontStoreCode());
		if (primaryFromProductMaster == null) {
			throw new SystemException(messageSource.getMessage("message34", null, null));
		}
		return primaryFromProductMaster;
	}

	private List<SingleEditProductDto> generateVariantList(ProductMasterResultDto primaryFromProductMaster, LittleMallVariantSkuRequestData requestData) {
		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(ProductMasterDto.convertFromProductMasterResultDto(primaryFromProductMaster));
		return singleEditProductDtoMapper.toSingleEditProductDto(singleEditProductDto, requestData.getVariantSkuList());
	}

	private void updateRelation(UserDto userDto, LittleMallVariantSkuRequestData requestData) {
		List<String> putVariantError = littleMallRelationHelper.requestProductMasterPutRelation(userDto, Optional.of(requestData.getRelation()).map(Collections::singletonList).orElseGet(Collections::emptyList));
		if (!putVariantError.isEmpty()) {
			throw new SystemException(putVariantError.get(0));
		}
	}

	@Transactional
	public Long processDataAndSaveProductRecord(UserDto userDto, Integer merchantId, List<SingleEditProductDto> variantList) {
		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT, String.format(SaveProductRecordHelper.CREATE_PRODUCT_FILE_NAME,
				variantList.get(0).getProduct().getAdditional().getLittleMall().getStoreSkuId().split(StringUtil.PRODUCT_SEPARATOR)[1], System.currentTimeMillis()), SaveProductStatus.WAIT_START, ClientIpHolder.getClientIp());

		List<SaveProductRecordRowDo> rowList = variantList.stream().map(variant -> {
			SaveProductRecordRowDo row = saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), variant, SaveProductStatus.WAIT_START, null);
			// generate relate data in row content
			productPreProcessingHelper.preProcessingThePlaceProduct(userDto, saveProductRecordDo, row);
			// generate iids data before send pm
			generateIIDSDataHelper.generateIIDSData(row);
			// add bu to send
			checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			return row;
		}).collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);

		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return saveProductRecordDo.getId();
	}

	@Transactional
	public void callProductMasterAndUpdateRecord(UserDto userDto, Long recordId) {
		SaveProductRecordDo record = saveProductRecordRepository.findById(recordId).get();
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findByRecordId(recordId);
		List<ProductMasterDto> productMasterDtoList = new ArrayList<>();
		List<String> skuList = new ArrayList<>();
		rowList.forEach(row -> {
			SingleEditProductDto singleEditProductFromRow = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			productMasterDtoList.add(singleEditProductFromRow.getProduct());
			skuList.add(singleEditProductFromRow.getProduct().getSkuId());
			singleEditProductFromRow.getProduct().setRecordRowId(row.getId());
			row.setContent(gson.toJson(singleEditProductFromRow));
		});
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, productMasterDtoList, skuList.toString(), record);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, record, rowList);
	}
}
