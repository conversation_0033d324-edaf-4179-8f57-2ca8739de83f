package com.shoalter.mms_product_api.service.little_mall_product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.mapper.SingleEditProductDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallRelationHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallSingleSaveService {
	private final PermissionHelper permissionHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final CheckBuHelper checkBuHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final LittleMallRelationHelper littleMallRelationHelper;
	private final SingleEditProductDtoMapper singleEditProductDtoMapper;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final MessageSource messageSource;
	private final Gson gson;

	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		Integer merchantId = (singleEditProductDto.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : singleEditProductDto.getProduct().getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);

		// check relation setting
		Pair<Boolean, String> checkRelationResult = littleMallRelationHelper.queryAndCheckRelationSetting(
			userDto,
			singleEditProductDto.getProduct().getProductId(),
			singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode(),
			singleEditProductDto.getRelation()
		);
		if (Boolean.FALSE.equals(checkRelationResult.getLeft())) {
			return ResponseDto.fail(List.of(checkRelationResult.getRight()));
		}

		List<SingleEditProductDto> primaryAndVariantList = generatePrimaryAndVariantList(singleEditProductDto);

		// checking
		List<String> errorMessages = validateProduct(userDto, singleEditProductDto, primaryAndVariantList);
		if (!errorMessages.isEmpty()) {
			return ResponseDto.fail(errorMessages);
		}

		// 建立relation
		requestProductMasterPutRelation(userDto, singleEditProductDto);

		Long recordId = SpringBeanProvider.getBean(LittleMallSingleSaveService.class).processDataAndSaveProductRecord(userDto, merchantId, singleEditProductDto, primaryAndVariantList);
		SpringBeanProvider.getBean(LittleMallSingleSaveService.class).callProductMasterAndUpdateRecord(userDto, recordId);

		return ResponseDto.success(ProductRecordResponseDto.builder().recordId(recordId).build());
	}

	private List<SingleEditProductDto> generatePrimaryAndVariantList(SingleEditProductDto singleEditProductDto) {
		return Stream.concat(
			singleEditProductDtoMapper.toSingleEditProductDto(singleEditProductDto, singleEditProductDto.getVariantSkuProductList()).stream(),
			Stream.of(singleEditProductDto)
		).collect(Collectors.toList());
	}

	private List<String> validateProduct(UserDto userDto, SingleEditProductDto singleEditProductDto, List<SingleEditProductDto> primaryAndVariantList) {
		List<String> errorMessages = new ArrayList<>();

		// check relation duplicated
		checkLittleMallProductHelper.checkRelation(singleEditProductDto.getRelation(), errorMessages);
		if (!errorMessages.isEmpty()) {
			return errorMessages;
		}

		// check product id cannot exist
		ProductMasterResultDto primaryFromProductMaster = checkLittleMallProductHelper.findPrimarySku(userDto, singleEditProductDto.getProduct().getProductId(), singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode());
		if (primaryFromProductMaster != null) {
			errorMessages.add(messageSource.getMessage("message48", null, null));
			return errorMessages;
		}

		// pre checking
		checkLittleMallProductHelper.checkCreateLittleMallProduct(userDto, primaryAndVariantList, singleEditProductDto.getProduct().getAdditional().getLittleMall(), errorMessages);
		return errorMessages;
	}

	private void requestProductMasterPutRelation(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		List<String> putVariantError = littleMallRelationHelper.requestProductMasterPutRelation(userDto,
			Optional.ofNullable(singleEditProductDto.getRelation()).map(Collections::singletonList).orElseGet(Collections::emptyList));
		if (!putVariantError.isEmpty()) {
			throw new SystemException(putVariantError.get(0));
		}
	}

	@Transactional
	public Long processDataAndSaveProductRecord(UserDto userDto, Integer merchantId, SingleEditProductDto singleEditProductDto, List<SingleEditProductDto> primaryAndVariantList) {
		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT, String.format(SaveProductRecordHelper.CREATE_PRODUCT_FILE_NAME,
				singleEditProductDto.getProduct().getSkuId(), System.currentTimeMillis()), SaveProductStatus.WAIT_START, ClientIpHolder.getClientIp());

		List<SaveProductRecordRowDo> rowList = primaryAndVariantList.stream().map(dto -> {
			SaveProductRecordRowDo row = saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), dto, SaveProductStatus.WAIT_START, null);
			// generate relate data in row content
			productPreProcessingHelper.preProcessingThePlaceProduct(userDto, saveProductRecordDo, row);
			// generate iids data before send pm
			generateIIDSDataHelper.generateIIDSData(row);
			// add bu to send
			checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			return row;
		}).collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);

		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return saveProductRecordDo.getId();
	}

	@Transactional
	public void callProductMasterAndUpdateRecord(UserDto userDto, Long recordId) {
		SaveProductRecordDo record = saveProductRecordRepository.findById(recordId).get();
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findByRecordId(recordId);
		List<ProductMasterDto> productMasterDtoList = new ArrayList<>();
		List<String> skuList = new ArrayList<>();
		rowList.forEach(row -> {
			SingleEditProductDto singleEditProductFromRow = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			productMasterDtoList.add(singleEditProductFromRow.getProduct());
			skuList.add(singleEditProductFromRow.getProduct().getSkuId());
			singleEditProductFromRow.getProduct().setRecordRowId(row.getId());
			row.setContent(gson.toJson(singleEditProductFromRow));
		});
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, productMasterDtoList, skuList.toString(), record);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, record, rowList);
	}
}
