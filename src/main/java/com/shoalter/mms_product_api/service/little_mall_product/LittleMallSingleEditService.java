package com.shoalter.mms_product_api.service.little_mall_product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.mapper.LittleMallRelationDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallRelationHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallVariantHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStoreSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantMatrixProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterRelationSettingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallSingleEditService {
	private final PermissionHelper permissionHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final CheckBuHelper checkBuHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final LittleMallRelationHelper littleMallRelationHelper;
	private final LittleMallRelationDataMapper littleMallRelationDataMapper;
	private final LittleMallVariantHelper littleMallVariantHelper;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final MessageSource messageSource;
	private final Gson gson;

	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		Integer merchantId = (singleEditProductDto.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : singleEditProductDto.getProduct().getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);

		// TODO IF phase 2 relation can edit, remove this method and replace littleMallRelationHelper.queryAndCheckRelationSetting
		ProductMasterRelationSettingResponseDto relationSetting = findRelation(userDto, singleEditProductDto);
		// check relation setting
		Pair<Boolean, String> checkRelationResult = littleMallRelationHelper.checkRelationSetting(relationSetting, singleEditProductDto.getProduct().getProductId(), singleEditProductDto.getRelation());
		if (Boolean.FALSE.equals(checkRelationResult.getLeft())) {
			return ResponseDto.fail(List.of(checkRelationResult.getRight()));
		}

		ProductMasterResultDto primaryFromProductMaster = findPrimarySku(userDto, singleEditProductDto);
		List<SingleEditProductDto> singleEditProductDtoList = generateModifiedSingleEditDtoList(userDto, singleEditProductDto, primaryFromProductMaster);

		// pre checking
		List<String> errorMessages = new ArrayList<>();
		checkLittleMallProductHelper.checkEditLittleMallProduct(userDto, singleEditProductDtoList, primaryFromProductMaster.getAdditional().getLittleMall(), errorMessages);
		if (!errorMessages.isEmpty()) {
			return ResponseDto.fail(errorMessages);
		}
		// TODO IF phase 2 relation can edit, need to remove set from query result
		updateRelation(userDto, singleEditProductDto, relationSetting);

		Long recordId = SpringBeanProvider.getBean(LittleMallSingleEditService.class).processDataAndSaveProductRecord(userDto, merchantId, singleEditProductDtoList, singleEditProductDto);
		SpringBeanProvider.getBean(LittleMallSingleEditService.class).callProductMasterAndUpdateRecord(userDto, recordId);

		return ResponseDto.success(ProductRecordResponseDto.builder().recordId(recordId).build());
	}

	private ProductMasterResultDto findPrimarySku(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		String storefrontStoreCode = singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode();
		String productId = singleEditProductDto.getProduct().getProductId();
		ProductMasterResultDto primaryFromProductMaster = checkLittleMallProductHelper.findPrimarySku(userDto, productId, storefrontStoreCode);
		if (primaryFromProductMaster == null) {
			throw new SystemException(messageSource.getMessage("message301", new String[]{"Product id ".concat(productId)}, null));
		} else if (singleEditProductDto.getProduct().getAdditional().getLittleMall().getIsPrimarySku() &&
			!singleEditProductDto.getProduct().getSkuId().equals(primaryFromProductMaster.getSkuId())) {
			throw new SystemException(messageSource.getMessage("message348", null, null));
		}
		return primaryFromProductMaster;
	}

	private List<SingleEditProductDto> generateModifiedSingleEditDtoList(UserDto userDto, SingleEditProductDto singleEditProductDto, ProductMasterResultDto primaryFromProductMaster) {
		boolean changeGlobalField = singleEditProductDto.getProduct().getAdditional().getLittleMall().getIsPrimarySku() && !littleMallVariantHelper.isPrimarySkuGlobalFieldsEquals(singleEditProductDto, primaryFromProductMaster);
		Map<String, VariantMatrixProductDto> variantList = Optional.ofNullable(singleEditProductDto.getVariantSkuProductList())
			.orElse(Collections.emptyList())
			.stream()
			.collect(Collectors.toMap(VariantMatrixProductDto::getStoreSkuId, Function.identity()));
		List<ProductMasterResultDto> skuList = new ArrayList<>();
		if (changeGlobalField) {
			skuList = littleMallVariantHelper.findProductIdSkusMap(userDto, Set.of(primaryFromProductMaster.getProductId()), primaryFromProductMaster.getAdditional().getLittleMall().getStoreCode()).get(primaryFromProductMaster.getProductId());
		} else if (!variantList.isEmpty()) {
			ProductMasterStoreSkuIdResponseDto storeSkuIdProductResponse = productMasterHelper.requestProductByStoreSkuId(
				userDto, FindStoreSkuIdProductRequestDto.generate(BuCodeEnum.LITTLE_MALL.name(), new ArrayList<>(variantList.keySet())));
			if (storeSkuIdProductResponse == null) {
				throw new SystemException(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR}, null));
			}
			if (StatusCodeEnum.FAIL.name().equals(storeSkuIdProductResponse.getStatus())) {
				throw new SystemException(storeSkuIdProductResponse.getMessage());
			}
			skuList = storeSkuIdProductResponse.getData();
		}

		List<SingleEditProductDto> singleEditProductDtoList = new ArrayList<>(List.of(singleEditProductDto));
		skuList.stream()
			.filter(sku -> !Objects.equals(sku.getSkuId(), singleEditProductDto.getProduct().getSkuId()))
			.forEach(sku -> {
				setFieldWithVariantData(sku, variantList);
				SingleEditProductDto variant = new SingleEditProductDto();
				variant.setProduct(ProductMasterDto.convertFromProductMasterResultDto(sku));
				if (changeGlobalField) {
					littleMallVariantHelper.setPrimarySkuGlobalFieldsToVariant(singleEditProductDto, variant);
				}
				singleEditProductDtoList.add(variant);
			});
		return singleEditProductDtoList;
	}

	private void setFieldWithVariantData(ProductMasterResultDto sku, Map<String, VariantMatrixProductDto> variantList) {
		VariantMatrixProductDto variantMatrixProductDto = variantList.get(sku.getAdditional().getLittleMall().getStoreSkuId());
		if (variantMatrixProductDto != null) {
			sku.getAdditional().getLittleMall().setMainPhoto(variantMatrixProductDto.getMainPhoto());
			sku.setOriginalPrice(variantMatrixProductDto.getOriginalPrice());
			sku.getAdditional().getLittleMall().setSellingPrice(variantMatrixProductDto.getSellingPrice());
			sku.getAdditional().getLittleMall().setVisibility(variantMatrixProductDto.getVisibility());
			sku.getAdditional().getLittleMall().setOnlineStatus(variantMatrixProductDto.getOnlineStatus());
		}
	}

	private ProductMasterRelationSettingResponseDto findRelation(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		String storefrontStoreCode = singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode();
		String productId = singleEditProductDto.getProduct().getProductId();
		List<ProductMasterRelationSettingResponseDto> productMasterRelationSettingResponseDtoList = productMasterHelper.requestLittleMallRelationSettingByParams(
			userDto, List.of(ProductMasterRelationSettingRequestDto.generate(storefrontStoreCode, productId)));
		if (productMasterRelationSettingResponseDtoList == null) {
			log.info("no product master result, storefrontStoreCode : {}, productId : {}", storefrontStoreCode, productId);
			throw new NoDataException();
		}
		if (productMasterRelationSettingResponseDtoList.isEmpty()) {
			log.info("storefrontStoreCode: {}, productId: {} without relation", storefrontStoreCode, productId);
			return null;
		}
		return productMasterRelationSettingResponseDtoList.get(0);
	}

	private void updateRelation(UserDto userDto, SingleEditProductDto singleEditProductDto, ProductMasterRelationSettingResponseDto relationSetting) {
		if (relationSetting == null) {
			log.info("storefrontStoreCode: {}, productId: {} without relation",
				singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode(),
				singleEditProductDto.getProduct().getProductId());
			return;
		}
		LittleMallRelationDto littleMallRelationDto = littleMallRelationDataMapper.toLittleMallRelationDto(relationSetting);
		singleEditProductDto.setRelation(littleMallRelationDto);

		List<String> putVariantError = littleMallRelationHelper.requestProductMasterPutRelation(userDto, Optional.ofNullable(singleEditProductDto.getRelation()).map(Collections::singletonList).orElseGet(Collections::emptyList));
		if (!putVariantError.isEmpty()) {
			throw new SystemException(putVariantError.get(0));
		}
	}

	@Transactional
	public Long processDataAndSaveProductRecord(UserDto userDto, Integer merchantId, List<SingleEditProductDto> singleEditProductDtoList, SingleEditProductDto singleEditProductDto) {
		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT, String.format(SaveProductRecordHelper.EDIT_PRODUCT_FILE_NAME, singleEditProductDto.getProduct().getSkuId(), System.currentTimeMillis()), SaveProductStatus.WAIT_START, ClientIpHolder.getClientIp());

		List<SaveProductRecordRowDo> rowList = singleEditProductDtoList.stream().map(dto -> {
			SaveProductRecordRowDo row = saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), dto, SaveProductStatus.WAIT_START, null);
			//generate relate data in row content
			productPreProcessingHelper.preProcessingThePlaceProduct(userDto, saveProductRecordDo, row);
			// generate iids data before send pm
			generateIIDSDataHelper.generateIIDSData(row);
			// add bu to send
			checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			return row;
		}).collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);

		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return saveProductRecordDo.getId();
	}

	@Transactional
	public void callProductMasterAndUpdateRecord(UserDto userDto, Long recordId) {
		SaveProductRecordDo record = saveProductRecordRepository.findById(recordId).get();
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findByRecordId(recordId);
		List<ProductMasterDto> productMasterDtoList = new ArrayList<>();
		List<String> skuList = new ArrayList<>();
		rowList.forEach(row -> {
			SingleEditProductDto singleEditProductFromRow = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			productMasterDtoList.add(singleEditProductFromRow.getProduct());
			skuList.add(singleEditProductFromRow.getProduct().getSkuId());
			singleEditProductFromRow.getProduct().setRecordRowId(row.getId());
			row.setContent(gson.toJson(singleEditProductFromRow));
		});
		SaveProductResultDto productMasterEditProductResult = productMasterHelper.requestEditProduct(userDto, productMasterDtoList, skuList.toString(), HttpMethod.PUT, record);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterEditProductResult, record, rowList);
	}
}
