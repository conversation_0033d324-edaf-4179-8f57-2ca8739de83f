package com.shoalter.mms_product_api.service.openApi;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.exception.OapiException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSavePartnerInfoRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.ProductTypeCodeRequestData;
import com.shoalter.mms_product_api.service.product.CheckProductMissingFieldService;
import com.shoalter.mms_product_api.service.product.helper.*;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.EverutsInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckErrorResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductPartnerInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResultDataDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.ValidationCheckUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class OapiHktvSingleSaveService {
	private final UserHelper userHelper;
	private final OapiHelper oapiHelper;
	private final CheckProductHelper checkProductHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final SaveProductHelper saveProductHelper;
	private final ProductMasterHelper productMasterHelper;
	private final ProductImageHelper productImageHelper;
	private final SyncBaseProductInfoHelper syncBaseProductInfoHelper;
	private final ProductPriceMonitorProductHelper productPriceMonitorProductHelper;
	private final ExchangeRateHelper exchangeRateHelper;
	private final StoreRepository storeRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final CheckProductMissingFieldService checkProductMissingFieldService;
	private final MessageSource messageSource;
	private final Gson gson;
	private static final long MAX_TIME_OUT = 330000L;

	public OapiResponseDto<OapiSingleSaveMainResponseData> start(String storefrontStoreCode, String platformCode, String businessType, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData) {
		String skuCode = oapiSingleSaveMainRequestData.getSkuCode();
		UserDto userDto = userHelper.generateOapiUserDto(OapiHelper.OPEN_API_SOURCE_IDENTIFIER_PREFIX + storefrontStoreCode);

		// query store, contract, merchant
		List<StoreContractMerchantDo> storeContractMerchantDoList = storeRepository.findStoreContractMerchantByStorefrontStoreCode(businessType, platformCode, storefrontStoreCode);
		StoreContractMerchantDo storeContractMerchantDo = null;
		if (storeContractMerchantDoList.isEmpty()) {
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), List.of(messageSource.getMessage("message184", new String[]{storefrontStoreCode}, null)));
		} else {
			storeContractMerchantDo = storeContractMerchantDoList.get(0);
		}

		// check white list
		if(!oapiHelper.isWhiteListMerchant(storeContractMerchantDo.getMerchantId())) {
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), List.of(messageSource.getMessage("message288", null, null)));
		}

		// pre checking
		List<String> preCheckingErrors = new ArrayList<>();
		ValidationCheckUtil.checkObjectFourByteEmoji(preCheckingErrors, oapiSingleSaveMainRequestData, null);
		ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> productMissingFieldCheckErrorResponseDto = checkProductMissingField(storefrontStoreCode, oapiSingleSaveMainRequestData);
		if (!preCheckingErrors.isEmpty() || StatusCodeEnum.FAIL.getCode() == productMissingFieldCheckErrorResponseDto.getStatus()) {
			if (StatusCodeEnum.FAIL.getCode() == productMissingFieldCheckErrorResponseDto.getStatus()) {
				preCheckingErrors.addAll(productMissingFieldCheckErrorResponseDto.getData().get(0).getMessages());
			}
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), preCheckingErrors);
		}

		// check sku
		CheckProductResultDto checkSkuIsExistResult = checkProductHelper.checkProductSkuExistsInStore(userDto, storeContractMerchantDo.getStoreCode(), List.of(skuCode));
		if (CollectionUtil.isNotEmpty(checkSkuIsExistResult.getErrorMessageList())) {
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), checkSkuIsExistResult.getErrorMessageList());
		}

		// data checking
		List<String> errors = checkProductHelper.checkOpenApiCreateProduct(oapiSingleSaveMainRequestData, storeContractMerchantDo);
		if (!errors.isEmpty()) {
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), errors);
		}

		// upload image
		OapiResponseDto<OapiSingleSaveMainResponseData> failUploadImageDto = uploadImage(userDto, oapiSingleSaveMainRequestData, storeContractMerchantDo);
		if (failUploadImageDto != null) {
			return failUploadImageDto;
		}

		// start the process of single save, left:record id, right:response data if fail
		Pair<Long, OapiResponseDto<OapiSingleSaveMainResponseData>> saveRecordResultDto = SpringBeanProvider.getBean(OapiHktvSingleSaveService.class).processDataAndSaveProductRecord(userDto, storefrontStoreCode, oapiSingleSaveMainRequestData, storeContractMerchantDo);
		if (saveRecordResultDto.getRight() != null) {
			return saveRecordResultDto.getRight();
		}

		// send to pm, left:row id, right:response data if fail
		Pair<Long, OapiResponseDto<OapiSingleSaveMainResponseData>> sendPmResultDto = SpringBeanProvider.getBean(OapiHktvSingleSaveService.class).callProductMasterAndUpdateRecordRow(userDto, saveRecordResultDto.getLeft(), skuCode);
		if (sendPmResultDto.getRight() != null) {
			return sendPmResultDto.getRight();
		}

		// check the record status every two seconds
		return checkRecordStatusEveryTwoSeconds(sendPmResultDto.getLeft(), skuCode);
	}

	@Transactional
	public Pair<Long, OapiResponseDto<OapiSingleSaveMainResponseData>> processDataAndSaveProductRecord(UserDto userDto, String storefrontStoreCode, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData, StoreContractMerchantDo storeContractMerchantDo) {
		SingleEditProductDto singleEditProductDto = generateSingleEditProductDto(storefrontStoreCode, storeContractMerchantDo, oapiSingleSaveMainRequestData);

		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createOapiSaveProductRecord(userDto, storeContractMerchantDo.getMerchantId(),
				SaveProductType.SINGLE_CREATE_PRODUCT,
				String.format(SaveProductRecordHelper.CREATE_PRODUCT_FILE_NAME, oapiSingleSaveMainRequestData.getSkuCode(), System.currentTimeMillis()),
				SaveProductStatus.WAIT_START);
		SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), singleEditProductDto, SaveProductStatus.WAIT_START, null);
		singleEditProductDto.getProduct().setRecordRowId(row.getId());

		if (singleEditProductDto.getProduct().getAdditional().getHktv() != null) {
			Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair = syncBaseProductInfoHelper.findVariantProductsFromProductMaster(userDto, List.of(singleEditProductDto.getProduct()));
			Pair<Boolean, String> passCheck = syncBaseProductInfoHelper.variantProductCheckAndUpdateRecordRows(productMasterResultPair, List.of(singleEditProductDto.getProduct()), saveProductRecordDo);
			if (!passCheck.getLeft()) {
				throw new OapiException(
					"Open API single save failed",
					OapiSingleSaveMainResponseData.builder().skuCode(oapiSingleSaveMainRequestData.getSkuCode()).status(OapiStatusCodeEnum.FAIL.getCode()).message(List.of(passCheck.getRight())).build());
			}
			syncBaseProductInfoHelper.handlePrimarySkuAndAddVariantProduct(productMasterResultPair, List.of(singleEditProductDto.getProduct()), saveProductRecordDo);
		}

		CheckProductResultDto check3PlResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, saveProductRecordDo, row, null);
		generateIIDSDataHelper.generateIIDSData(row);
		BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);
		ResponseDto<Void> checkResult = checkProductHelper.checkCreateProductHandler(userDto, saveProductRecordDo, row, check3PlResult, rmbRate);
		productPriceMonitorProductHelper.priceMonitorCreateProcess(row, userDto);

		if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
			checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			saveProductHelper.setFieldValueNullByRule(row);
		} else {
			row.setErrorMessage(StringUtil.generateErrorMessage(checkResult.getErrorMessageList()));
			row.setStatus(SaveProductStatus.FAIL);
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return Pair.of(row.getRecordId(), generateResponseData(row.getSku(), OapiStatusCodeEnum.FAIL.getCode(), List.of(row.getErrorMessage())));
		}
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}, source: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()), saveProductRecordDo.getSource());

		return Pair.of(row.getRecordId(), null);
	}

	@Transactional
	public Pair<Long, OapiResponseDto<OapiSingleSaveMainResponseData>> callProductMasterAndUpdateRecordRow(UserDto userDto, Long recordId, String skuCode) {
		//get data
		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
		List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.WAIT_START);
		if (rows.isEmpty()) {
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return Pair.of(null, generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), null));
		}
		SaveProductRecordRowDo row = rows.get(0);
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(singleEditProductDto.getProduct()), ProductMasterDto.class);

		//call product master
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, List.of(productMasterDto), row.getSku(), saveProductRecordDo);
		List<String> errorMessageList = saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, saveProductRecordDo, rows);
		if (!errorMessageList.isEmpty()) {
			return Pair.of(row.getId(), generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), List.of(row.getErrorMessage())));
		}

		return Pair.of(row.getId(), null);
	}

	private ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> checkProductMissingField(String storefrontStoreCode, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData) {
		String deliveryMethod = oapiHelper.findDeliveryMethod(ConstantType.PLATFORM_CODE_HKTV, oapiSingleSaveMainRequestData.getProductReadyMethod());
		HktvProductFieldDto hktvProductFieldDto = new HktvProductFieldDto();
		hktvProductFieldDto.setStorefrontStoreCode(storefrontStoreCode);
		hktvProductFieldDto.setProductId(oapiSingleSaveMainRequestData.getProductCode());
		hktvProductFieldDto.setSkuId(oapiSingleSaveMainRequestData.getSkuCode());
		List<String> productCatCodes = Optional.ofNullable(oapiSingleSaveMainRequestData.getProductCategories())
			.orElse(Collections.emptyList())
			.stream()
			.map(ProductTypeCodeRequestData::getProductCatCode)
			.collect(Collectors.toList());
		hktvProductFieldDto.setProductTypeCode(productCatCodes);
		hktvProductFieldDto.setPrimaryCategoryCode(oapiSingleSaveMainRequestData.getPrimaryCategoryCode());
		hktvProductFieldDto.setBrandCode(oapiSingleSaveMainRequestData.getBrandCode());
		hktvProductFieldDto.setProductReadyMethod(oapiSingleSaveMainRequestData.getProductReadyMethod());
		hktvProductFieldDto.setDeliveryMethod(deliveryMethod);
		hktvProductFieldDto.setSkuStatus(oapiSingleSaveMainRequestData.getOnOfflineStatus());
		hktvProductFieldDto.setIsPrimarySku(oapiSingleSaveMainRequestData.getIsPrimarySku());
		hktvProductFieldDto.setSkuNameEn(oapiSingleSaveMainRequestData.getSkuName());
		hktvProductFieldDto.setSkuNameCh(oapiSingleSaveMainRequestData.getSkuNameTchi());
		hktvProductFieldDto.setSkuNameSc(oapiSingleSaveMainRequestData.getSkuNameZhCN());
		hktvProductFieldDto.setSkuShortDescriptionEn(oapiSingleSaveMainRequestData.getSkuSDescEn());
		hktvProductFieldDto.setSkuShortDescriptionCh(oapiSingleSaveMainRequestData.getSkuSDescCh());
		hktvProductFieldDto.setSkuShortDescriptionSc(oapiSingleSaveMainRequestData.getSkuSDescZhCN());
		hktvProductFieldDto.setManufacturedCountry(oapiSingleSaveMainRequestData.getManuCountry());
		hktvProductFieldDto.setCurrency(oapiSingleSaveMainRequestData.getCurrencyCode());
		hktvProductFieldDto.setOriginalPrice(oapiSingleSaveMainRequestData.getOriginalPrice());
		hktvProductFieldDto.setUserMax(oapiSingleSaveMainRequestData.getUserMax() != null ? oapiSingleSaveMainRequestData.getUserMax().intValue() : null);
		hktvProductFieldDto.setPackingHeight(oapiSingleSaveMainRequestData.getPackHeight());
		hktvProductFieldDto.setPackingLength(oapiSingleSaveMainRequestData.getPackLength());
		hktvProductFieldDto.setPackingDepth(oapiSingleSaveMainRequestData.getPackDepth());
		hktvProductFieldDto.setPackingDimensionUnit(oapiSingleSaveMainRequestData.getPackDimensionUnit());
		hktvProductFieldDto.setWeight(oapiSingleSaveMainRequestData.getWeight());
		hktvProductFieldDto.setWeightUnit(oapiSingleSaveMainRequestData.getWeightUnit());
		hktvProductFieldDto.setPackingBoxType(oapiSingleSaveMainRequestData.getPackBoxType());
		hktvProductFieldDto.setCartonHeight(oapiSingleSaveMainRequestData.getCartonHeight());
		hktvProductFieldDto.setCartonLength(oapiSingleSaveMainRequestData.getCartonLength());
		hktvProductFieldDto.setCartonDepth(oapiSingleSaveMainRequestData.getCartonWidth());
		hktvProductFieldDto.setInvisibleFlag(oapiSingleSaveMainRequestData.getInvisibleFlag());
		hktvProductFieldDto.setBarcode(oapiSingleSaveMainRequestData.getBarcode());
		hktvProductFieldDto.setFeatureStartDate(oapiSingleSaveMainRequestData.getFeatureStartTime());
		hktvProductFieldDto.setFeatureEndDate(oapiSingleSaveMainRequestData.getFeatureEndTime());
		hktvProductFieldDto.setVoucherType(oapiSingleSaveMainRequestData.getVoucherType());
		hktvProductFieldDto.setVoucherDisplayType(oapiSingleSaveMainRequestData.getVoucherDisplayType());
		hktvProductFieldDto.setVoucherTemplateType(oapiSingleSaveMainRequestData.getVoucherTemplateType());
		hktvProductFieldDto.setExpiryType(oapiSingleSaveMainRequestData.getExpiryType());
		hktvProductFieldDto.setRedeemStartDate(oapiSingleSaveMainRequestData.getRedeemStartDate());
		hktvProductFieldDto.setFixedRedemptionDate(oapiSingleSaveMainRequestData.getFixedRedemptionDate());
		hktvProductFieldDto.setUponPurchaseDate(oapiSingleSaveMainRequestData.getUponPurchaseDate() != null ? oapiSingleSaveMainRequestData.getUponPurchaseDate().toString() : null);
		hktvProductFieldDto.setNeedRemovalServices(oapiSingleSaveMainRequestData.getRemovalServices());
		hktvProductFieldDto.setGoodsType(oapiSingleSaveMainRequestData.getGoodsType());
		hktvProductFieldDto.setWarrantyPeriodUnit(oapiSingleSaveMainRequestData.getWarrantyPeriodUnit());
		hktvProductFieldDto.setWarrantyPeriod(oapiSingleSaveMainRequestData.getWarrantyPeriod());
		hktvProductFieldDto.setWarrantySupplierEn(oapiSingleSaveMainRequestData.getWarrantySupplierEn());
		hktvProductFieldDto.setWarrantySupplierCh(oapiSingleSaveMainRequestData.getWarrantySupplierCh());
		hktvProductFieldDto.setWarrantySupplierSc(oapiSingleSaveMainRequestData.getWarrantySupplierZhCN());
		hktvProductFieldDto.setServiceCentreAddressEn(oapiSingleSaveMainRequestData.getServiceCentreAddressEn());
		hktvProductFieldDto.setServiceCentreAddressCh(oapiSingleSaveMainRequestData.getServiceCentreAddressCh());
		hktvProductFieldDto.setServiceCentreAddressSc(oapiSingleSaveMainRequestData.getServiceCentreAddressZhCN());
		hktvProductFieldDto.setServiceCentreEmail(oapiSingleSaveMainRequestData.getServiceCentreEmail());
		hktvProductFieldDto.setServiceCentreContact(oapiSingleSaveMainRequestData.getServiceCentreContact());
		hktvProductFieldDto.setReturnDays(oapiSingleSaveMainRequestData.getReturnDays());
		hktvProductFieldDto.setProductReadyDays(oapiSingleSaveMainRequestData.getProductReadyDays());
		hktvProductFieldDto.setPickupDays(oapiSingleSaveMainRequestData.getPickupDays());
		hktvProductFieldDto.setPickupTimeslot(oapiSingleSaveMainRequestData.getPickupTimeslot());
		hktvProductFieldDto.setAffiliateUrl(oapiSingleSaveMainRequestData.getAffiliateUrl());
		hktvProductFieldDto.setMainPhoto(oapiSingleSaveMainRequestData.getMainPhoto());
		hktvProductFieldDto.setInvoiceRemarkEn(oapiSingleSaveMainRequestData.getInvoiceRemarksEn());
		hktvProductFieldDto.setInvoiceRemarkCh(oapiSingleSaveMainRequestData.getInvoiceRemarksCh());
		hktvProductFieldDto.setInvoiceRemarkSc(oapiSingleSaveMainRequestData.getInvoiceRemarksZhCN());
		if (Optional.of(oapiSingleSaveMainRequestData)
			.map(OapiSingleSaveMainRequestData::getExternalPlatform)
			.isPresent()) {
			hktvProductFieldDto.setExternalPlatform(new ExternalPlatform());
			hktvProductFieldDto.getExternalPlatform().setSource(oapiSingleSaveMainRequestData.getExternalPlatform().getSource());
			hktvProductFieldDto.getExternalPlatform().setProductId(oapiSingleSaveMainRequestData.getExternalPlatform().getProductId());
			hktvProductFieldDto.getExternalPlatform().setSkuId(oapiSingleSaveMainRequestData.getExternalPlatform().getSkuId());
		}

		ProductMissingFieldCheckRequestDto productMissingFieldCheckRequestDto = new ProductMissingFieldCheckRequestDto(TemplateTypeEnum.ALL_COLUMN_OPEN_API, List.of(hktvProductFieldDto));
		return checkProductMissingFieldService.start(productMissingFieldCheckRequestDto);
	}

	private SingleEditProductDto generateSingleEditProductDto(String storefrontStoreCode, StoreContractMerchantDo storeContractMerchantDo, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData) {
		HktvProductDto hktvProductDto = generateHktvProductDto(storefrontStoreCode, storeContractMerchantDo, oapiSingleSaveMainRequestData);
		ProductMasterDto productMasterDto = generateProductMasterDto(storeContractMerchantDo, oapiSingleSaveMainRequestData, hktvProductDto);

		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDto);

		return singleEditProductDto;
	}

	private HktvProductDto generateHktvProductDto(String storefrontStoreCode, StoreContractMerchantDo storeContractMerchantDo, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData) {
		HktvProductDto hktvProductDto = new HktvProductDto();
		hktvProductDto.setContractNo(storeContractMerchantDo.getContractId());
		hktvProductDto.setStores(storeContractMerchantDo.getStoreCode());
		String productReadyMethod = oapiSingleSaveMainRequestData.getProductReadyMethod();
		hktvProductDto.setProductReadyMethod(productReadyMethod);
		hktvProductDto.setDeliveryMethod(oapiHelper.findDeliveryMethod(ConstantType.PLATFORM_CODE_HKTV, productReadyMethod));
		hktvProductDto.setStoreSkuId(storefrontStoreCode.concat(StringUtil.PRODUCT_SEPARATOR).concat(oapiSingleSaveMainRequestData.getSkuCode()));
		hktvProductDto.setProductTypeCode(oapiSingleSaveMainRequestData.getProductCategories().stream().map(ProductTypeCodeRequestData::getProductCatCode).collect(Collectors.toList()));
		hktvProductDto.setPrimaryCategoryCode(oapiSingleSaveMainRequestData.getPrimaryCategoryCode());
		hktvProductDto.setIsPrimarySku(oapiSingleSaveMainRequestData.getIsPrimarySku());
		hktvProductDto.setVisibility(StringUtil.toggleYN(oapiSingleSaveMainRequestData.getInvisibleFlag()));
		hktvProductDto.setSkuShortDescriptionEn(oapiSingleSaveMainRequestData.getSkuSDescEn());
		hktvProductDto.setSkuShortDescriptionCh(oapiSingleSaveMainRequestData.getSkuSDescCh());
		hktvProductDto.setSkuShortDescriptionSc(oapiSingleSaveMainRequestData.getSkuSDescZhCN());
		hktvProductDto.setSkuLongDescriptionEn(oapiSingleSaveMainRequestData.getSkuLDescEn());
		hktvProductDto.setSkuLongDescriptionCh(oapiSingleSaveMainRequestData.getSkuLDescCh());
		hktvProductDto.setSkuLongDescriptionSc(oapiSingleSaveMainRequestData.getSkuLDescZhCN());
		hktvProductDto.setFeatureStartTime(oapiSingleSaveMainRequestData.getFeatureStartTime());
		hktvProductDto.setFeatureEndTime(oapiSingleSaveMainRequestData.getFeatureEndTime());
		hktvProductDto.setVoucherType(oapiSingleSaveMainRequestData.getVoucherType());
		hktvProductDto.setVoucherDisplayType(oapiSingleSaveMainRequestData.getVoucherDisplayType());
		hktvProductDto.setExpiryType(oapiSingleSaveMainRequestData.getExpiryType());
		hktvProductDto.setRedeemStartDate(oapiSingleSaveMainRequestData.getRedeemStartDate());
		hktvProductDto.setVoucherTemplateType(oapiSingleSaveMainRequestData.getVoucherTemplateType());
		hktvProductDto.setFixedRedemptionDate(oapiSingleSaveMainRequestData.getFixedRedemptionDate());
		hktvProductDto.setUponPurchaseDate(oapiSingleSaveMainRequestData.getUponPurchaseDate());
		hktvProductDto.setFinePrintEn(oapiSingleSaveMainRequestData.getFinePrintEn());
		hktvProductDto.setFinePrintCh(oapiSingleSaveMainRequestData.getFinePrintCh());
		hktvProductDto.setFinePrintSc(oapiSingleSaveMainRequestData.getFinePrintZhCN());
		hktvProductDto.setCurrency(oapiSingleSaveMainRequestData.getCurrencyCode());
		hktvProductDto.setSellingPrice(oapiSingleSaveMainRequestData.getSellingPrice());
		hktvProductDto.setStyle(oapiSingleSaveMainRequestData.getStyle());
		hktvProductDto.setDiscountTextEn(oapiSingleSaveMainRequestData.getDiscountText());
		hktvProductDto.setDiscountTextCh(oapiSingleSaveMainRequestData.getDiscountTextTchi());
		hktvProductDto.setDiscountTextSc(oapiSingleSaveMainRequestData.getDiscountTextZhCN());
		hktvProductDto.setMallDollar(new BigDecimal(0));
		hktvProductDto.setVipMallDollar(new BigDecimal(0));
		hktvProductDto.setUserMax(oapiSingleSaveMainRequestData.getUserMax());
		hktvProductDto.setMainPhoto(oapiSingleSaveMainRequestData.getMainPhoto());
		hktvProductDto.setVariantProductPhoto(oapiSingleSaveMainRequestData.getOtherProductPhoto());
		hktvProductDto.setOtherPhoto(oapiSingleSaveMainRequestData.getOtherPhoto());
		hktvProductDto.setAdvertisingPhoto(oapiSingleSaveMainRequestData.getAdvertisingPhoto());
		hktvProductDto.setVideoLink(oapiSingleSaveMainRequestData.getVideoLink());
		hktvProductDto.setVideoLinkTextEn(oapiSingleSaveMainRequestData.getVideoLinkEn());
		hktvProductDto.setVideoLinkTextCh(oapiSingleSaveMainRequestData.getVideoLinkCh());
		hktvProductDto.setVideoLinkTextSc(oapiSingleSaveMainRequestData.getVideoLinkZhCN());
		hktvProductDto.setVideoLink2(oapiSingleSaveMainRequestData.getVideoLink2());
		hktvProductDto.setVideoLinkTextEn2(oapiSingleSaveMainRequestData.getVideoLinkEn2());
		hktvProductDto.setVideoLinkTextCh2(oapiSingleSaveMainRequestData.getVideoLinkCh2());
		hktvProductDto.setVideoLinkTextSc2(oapiSingleSaveMainRequestData.getVideoLinkZhCN2());
		hktvProductDto.setVideoLink3(oapiSingleSaveMainRequestData.getVideoLink3());
		hktvProductDto.setVideoLinkTextEn3(oapiSingleSaveMainRequestData.getVideoLinkEn3());
		hktvProductDto.setVideoLinkTextCh3(oapiSingleSaveMainRequestData.getVideoLinkCh3());
		hktvProductDto.setVideoLinkTextSc3(oapiSingleSaveMainRequestData.getVideoLinkZhCN3());
		hktvProductDto.setVideoLink4(oapiSingleSaveMainRequestData.getVideoLink4());
		hktvProductDto.setVideoLinkTextEn4(oapiSingleSaveMainRequestData.getVideoLinkEn4());
		hktvProductDto.setVideoLinkTextCh4(oapiSingleSaveMainRequestData.getVideoLinkCh4());
		hktvProductDto.setVideoLinkTextSc4(oapiSingleSaveMainRequestData.getVideoLinkZhCN4());
		hktvProductDto.setVideoLink5(oapiSingleSaveMainRequestData.getVideoLink5());
		hktvProductDto.setVideoLinkTextEn5(oapiSingleSaveMainRequestData.getVideoLinkEn5());
		hktvProductDto.setVideoLinkTextCh5(oapiSingleSaveMainRequestData.getVideoLinkCh5());
		hktvProductDto.setVideoLinkTextSc5(oapiSingleSaveMainRequestData.getVideoLinkZhCN5());
		hktvProductDto.setWarehouseId(oapiHelper.findFirstWarehouseId(storeContractMerchantDo.getStoreId(), productReadyMethod));
		hktvProductDto.setPackingSpecEn(oapiSingleSaveMainRequestData.getPackSpecEn());
		hktvProductDto.setPackingSpecCh(oapiSingleSaveMainRequestData.getPackSpecCh());
		hktvProductDto.setPackingSpecSc(oapiSingleSaveMainRequestData.getPackSpecZhCN());
		hktvProductDto.setInvoiceRemarksEn(oapiSingleSaveMainRequestData.getInvoiceRemarksEn());
		hktvProductDto.setInvoiceRemarksCh(oapiSingleSaveMainRequestData.getInvoiceRemarksCh());
		hktvProductDto.setInvoiceRemarksSc(oapiSingleSaveMainRequestData.getInvoiceRemarksZhCN());
		hktvProductDto.setReturnDays(oapiSingleSaveMainRequestData.getReturnDays());
		hktvProductDto.setProductReadyDays(oapiSingleSaveMainRequestData.getProductReadyDays());
		hktvProductDto.setPickupDays(oapiSingleSaveMainRequestData.getPickupDays());
		hktvProductDto.setPickupTimeslot(oapiSingleSaveMainRequestData.getPickupTimeslot());
		hktvProductDto.setWarranty(oapiSingleSaveMainRequestData.getWarranty());
		hktvProductDto.setNeedRemovalServices(oapiSingleSaveMainRequestData.getRemovalServices());
		hktvProductDto.setGoodsType(oapiSingleSaveMainRequestData.getGoodsType());
		hktvProductDto.setWarrantyPeriodUnit(oapiSingleSaveMainRequestData.getWarrantyPeriodUnit());
		hktvProductDto.setWarrantyPeriod(oapiSingleSaveMainRequestData.getWarrantyPeriod());
		hktvProductDto.setWarrantySupplierEn(oapiSingleSaveMainRequestData.getWarrantySupplierEn());
		hktvProductDto.setWarrantySupplierCh(oapiSingleSaveMainRequestData.getWarrantySupplierCh());
		hktvProductDto.setWarrantySupplierSc(oapiSingleSaveMainRequestData.getWarrantySupplierZhCN());
		hktvProductDto.setServiceCentreAddressEn(oapiSingleSaveMainRequestData.getServiceCentreAddressEn());
		hktvProductDto.setServiceCentreAddressCh(oapiSingleSaveMainRequestData.getServiceCentreAddressCh());
		hktvProductDto.setServiceCentreAddressSc(oapiSingleSaveMainRequestData.getServiceCentreAddressZhCN());
		hktvProductDto.setServiceCentreEmail(oapiSingleSaveMainRequestData.getServiceCentreEmail());
		hktvProductDto.setServiceCentreContact(oapiSingleSaveMainRequestData.getServiceCentreContact());
		hktvProductDto.setWarrantyRemarkEn(oapiSingleSaveMainRequestData.getWarrantyRemarkEn());
		hktvProductDto.setWarrantyRemarkCh(oapiSingleSaveMainRequestData.getWarrantyRemarkCh());
		hktvProductDto.setWarrantyRemarkSc(oapiSingleSaveMainRequestData.getWarrantyRemarkZhCN());
		hktvProductDto.setOnlineStatus(OnlineStatusEnum.valueOf(oapiSingleSaveMainRequestData.getOnOfflineStatus()));
		hktvProductDto.setStoreId(storeContractMerchantDo.getStoreId());
		hktvProductDto.setDeliveryDistrict(oapiSingleSaveMainRequestData.getProductOverseaDeliveryList());
		hktvProductDto.setAffiliateUrl(oapiSingleSaveMainRequestData.getAffiliateUrl());

		if (Optional.of(oapiSingleSaveMainRequestData)
			.map(OapiSingleSaveMainRequestData::getPartnerInfo)
			.map(OapiSingleSavePartnerInfoRequestData::getEveruts)
			.isPresent()) {
			hktvProductDto.setPartnerInfo(new ProductPartnerInfoDto());
			hktvProductDto.getPartnerInfo().setEveruts(new EverutsInfoDto());
			hktvProductDto.getPartnerInfo().getEveruts().setBuyerId(oapiSingleSaveMainRequestData.getPartnerInfo().getEveruts().getBuyerId());
			hktvProductDto.getPartnerInfo().getEveruts().setSkuId(oapiSingleSaveMainRequestData.getPartnerInfo().getEveruts().getSkuId());
		}

		if (Optional.of(oapiSingleSaveMainRequestData)
			.map(OapiSingleSaveMainRequestData::getExternalPlatform)
			.isPresent()) {
			hktvProductDto.setExternalPlatform(new ExternalPlatform());
			hktvProductDto.getExternalPlatform().setSource(oapiSingleSaveMainRequestData.getExternalPlatform().getSource());
			hktvProductDto.getExternalPlatform().setProductId(oapiSingleSaveMainRequestData.getExternalPlatform().getProductId());
			hktvProductDto.getExternalPlatform().setSkuId(oapiSingleSaveMainRequestData.getExternalPlatform().getSkuId());
		}

		return hktvProductDto;
	}

	private ProductMasterDto generateProductMasterDto(StoreContractMerchantDo storeContractMerchantDo, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData, HktvProductDto hktvProductDto) {
		ProductBarcodeDto productBarcodeDto = new ProductBarcodeDto();
		productBarcodeDto.setEan(oapiSingleSaveMainRequestData.getBarcode());
		productBarcodeDto.setSequenceNo(1);

		CartonSizeDto cartonSizeDto = null;
		if (oapiSingleSaveMainRequestData.getCartonLength() != null || oapiSingleSaveMainRequestData.getCartonWidth() != null || oapiSingleSaveMainRequestData.getCartonHeight() != null) {
			cartonSizeDto = new CartonSizeDto();
			cartonSizeDto.setLength(oapiSingleSaveMainRequestData.getCartonLength());
			cartonSizeDto.setWidth(oapiSingleSaveMainRequestData.getCartonWidth());
			cartonSizeDto.setHeight(oapiSingleSaveMainRequestData.getCartonHeight());
		}

		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setBrandId(oapiHelper.findBrandId(oapiSingleSaveMainRequestData.getBrandCode()));
		productMasterDto.setMerchantId(storeContractMerchantDo.getMerchantId());
		productMasterDto.setSkuId(oapiSingleSaveMainRequestData.getSkuCode());
		productMasterDto.setManufacturedCountry(oapiSingleSaveMainRequestData.getManuCountry());
		productMasterDto.setColourFamilies(oapiSingleSaveMainRequestData.getColorFamilies());
		productMasterDto.setColor(oapiSingleSaveMainRequestData.getColorEn());
		productMasterDto.setSizeSystem(oapiSingleSaveMainRequestData.getSizeSystem());
		productMasterDto.setSize(oapiSingleSaveMainRequestData.getSize());
		productMasterDto.setOption1(oapiSingleSaveMainRequestData.getField1());
		productMasterDto.setOption1Value(oapiSingleSaveMainRequestData.getValue1());
		productMasterDto.setOption2(oapiSingleSaveMainRequestData.getField2());
		productMasterDto.setOption2Value(oapiSingleSaveMainRequestData.getValue2());
		productMasterDto.setOption3(oapiSingleSaveMainRequestData.getField3());
		productMasterDto.setOption3Value(oapiSingleSaveMainRequestData.getValue3());
		productMasterDto.setBarcodes(List.of(productBarcodeDto));
		productMasterDto.setPackingHeight(oapiSingleSaveMainRequestData.getPackHeight());
		productMasterDto.setPackingLength(oapiSingleSaveMainRequestData.getPackLength());
		productMasterDto.setPackingDepth(oapiSingleSaveMainRequestData.getPackDepth());
		productMasterDto.setPackingDimensionUnit(oapiSingleSaveMainRequestData.getPackDimensionUnit());
		productMasterDto.setWeight(oapiSingleSaveMainRequestData.getWeight());
		productMasterDto.setWeightUnit(oapiSingleSaveMainRequestData.getWeightUnit());
		productMasterDto.setPackingBoxType(oapiSingleSaveMainRequestData.getPackBoxType());
		productMasterDto.setOriginalPrice(oapiSingleSaveMainRequestData.getOriginalPrice());
		productMasterDto.setProductId(oapiSingleSaveMainRequestData.getProductCode());
		productMasterDto.setMinimumShelfLife(oapiSingleSaveMainRequestData.getMinimumShelfLife());
		productMasterDto.setMerchantName(storeContractMerchantDo.getMerchantName());
		productMasterDto.setSkuNameEn(oapiSingleSaveMainRequestData.getSkuName());
		productMasterDto.setSkuNameCh(oapiSingleSaveMainRequestData.getSkuNameTchi());
		productMasterDto.setSkuNameSc(oapiSingleSaveMainRequestData.getSkuNameZhCN());
		productMasterDto.setCartonSizeList(cartonSizeDto != null ? List.of(cartonSizeDto) : null);
		BuProductDto buProductDto = new BuProductDto();
		buProductDto.setHktv(hktvProductDto);
		productMasterDto.setAdditional(buProductDto);
		return productMasterDto;
	}

	private OapiResponseDto<OapiSingleSaveMainResponseData> checkRecordStatusEveryTwoSeconds(Long rowId, String skuCode) {
		OapiSingleSaveMainResponseData responseData = new OapiSingleSaveMainResponseData();
		try {
			Long startTime = System.currentTimeMillis();
			boolean saveSuccessFlag = false;
			while (!saveSuccessFlag) {
					SaveProductRecordRowDo row = saveProductRecordRowRepository.findById(rowId).get();
					Integer status = row.getStatus();
					if (status == 1) {
						saveSuccessFlag = true;
						responseData.setSkuCode(skuCode);
						responseData.setStatus(OapiStatusCodeEnum.SUCCESS.getCode());
						break;
					}
					if (status == -1) {
						log.info("save mms product api fail");
						responseData.setSkuCode(skuCode);
						responseData.setStatus(OapiStatusCodeEnum.FAIL.getCode());
						responseData.setMessage(List.of(row.getErrorMessage().split(", \n")));
						break;
					}
					Long checkTime = System.currentTimeMillis();
					if (checkTime - startTime > MAX_TIME_OUT) {
						log.info("Save Product Time Out");
						responseData.setSkuCode(skuCode);
						responseData.setStatus(OapiStatusCodeEnum.FAIL.getCode());
						responseData.setMessage(List.of(messageSource.getMessage("message289", null, null)));
						break;
					}
					if (!saveSuccessFlag) {
						Thread.sleep(2000);
					}
			}

		} catch (Exception e) {
			responseData.setSkuCode(skuCode);
			responseData.setStatus(OapiStatusCodeEnum.FAIL.getCode());
			responseData.setMessage(List.of(e.getMessage()));
		}
		return generateResponseData(skuCode, responseData.getStatus(), responseData.getMessage());
	}

	private OapiResponseDto<OapiSingleSaveMainResponseData> generateResponseData(String skuCode, String status, List<String> message) {
		OapiSingleSaveMainResponseData responseData = OapiSingleSaveMainResponseData.builder()
			.skuCode(skuCode)
			.status(status)
			.message(message).build();
		return OapiResponseDto.<OapiSingleSaveMainResponseData>builder()
			.message(OapiStatusCodeEnum.SUCCESS.getCode().equals(status) ? "Open API single save successful" : "Open API single save failed")
			.code(responseData.getStatus())
			.data(responseData)
			.build();
	}

	private OapiResponseDto<OapiSingleSaveMainResponseData> uploadImage(UserDto userDto, OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData, StoreContractMerchantDo storeContractMerchantDo) {
		String skuCode = oapiSingleSaveMainRequestData.getSkuCode();
		UploadImageResponseDto uploadImageResponseDto = productImageHelper.generateUploadImage(skuCode, storeContractMerchantDo.getMerchantId(), ConstantType.PLATFORM_CODE_HKTV);

		// main photo
		String mainPhoto = oapiSingleSaveMainRequestData.getMainPhoto();
		ResponseDto<UploadImageResultDataDto> uploadMainPhotoResult = productImageHelper.imageUrlsUpload(userDto, mainPhoto, uploadImageResponseDto);
		if (StatusCodeEnum.FAIL.getCode() == uploadMainPhotoResult.getStatus()) {
			List<String> errorMessages = List.of(messageSource.getMessage("message167", new String[]{mainPhoto, "mainPhoto", String.join(StringUtil.COMMA, uploadMainPhotoResult.getErrorMessageList())}, null));
			return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), errorMessages);
		} else {
			oapiSingleSaveMainRequestData.setMainPhoto(uploadMainPhotoResult.getData().getUrl());
		}

		// other product photo
		if (oapiSingleSaveMainRequestData.getOtherProductPhoto() != null && !oapiSingleSaveMainRequestData.getOtherProductPhoto().isEmpty()) {
			List<String> successOtherProductPhotoList = new ArrayList<>();
			for (String otherProductPhoto : oapiSingleSaveMainRequestData.getOtherProductPhoto()) {
				ResponseDto<UploadImageResultDataDto> uploadOtherProductPhotoResult = productImageHelper.imageUrlsUpload(userDto, otherProductPhoto, uploadImageResponseDto);
				if (StatusCodeEnum.FAIL.getCode() == uploadOtherProductPhotoResult.getStatus()) {
					List<String> errorMessages = List.of(messageSource.getMessage("message167", new String[]{otherProductPhoto, "otherProductPhoto", String.join(StringUtil.COMMA, uploadOtherProductPhotoResult.getErrorMessageList())}, null));
					return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), errorMessages);
				} else {
					successOtherProductPhotoList.add(uploadOtherProductPhotoResult.getData().getUrl());
				}
			}
			oapiSingleSaveMainRequestData.setOtherProductPhoto(successOtherProductPhotoList);
		}

		// other photo
		if (oapiSingleSaveMainRequestData.getOtherPhoto() != null && !oapiSingleSaveMainRequestData.getOtherPhoto().isEmpty()) {
			List<String> successOtherPhotoList = new ArrayList<>();
			for (String otherPhoto : oapiSingleSaveMainRequestData.getOtherPhoto()) {
				ResponseDto<UploadImageResultDataDto> uploadOtherPhotoResult = productImageHelper.imageUrlsUpload(userDto, otherPhoto, uploadImageResponseDto);
				if (StatusCodeEnum.FAIL.getCode() == uploadOtherPhotoResult.getStatus()) {
					List<String> errorMessages = List.of(messageSource.getMessage("message167", new String[]{otherPhoto, "otherPhoto", String.join(StringUtil.COMMA, uploadOtherPhotoResult.getErrorMessageList())}, null));
					return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), errorMessages);
				} else {
					successOtherPhotoList.add(uploadOtherPhotoResult.getData().getUrl());
				}
			}
			oapiSingleSaveMainRequestData.setOtherPhoto(successOtherPhotoList);
		}

		// advertising photo
		if (StringUtils.isNotBlank(oapiSingleSaveMainRequestData.getAdvertisingPhoto())) {
			String advertisingPhoto = oapiSingleSaveMainRequestData.getAdvertisingPhoto();
			ResponseDto<UploadImageResultDataDto> uploadAdvertisingPhotoResult = productImageHelper.imageUrlsUpload(userDto, advertisingPhoto, uploadImageResponseDto);
			if (StatusCodeEnum.FAIL.getCode() == uploadAdvertisingPhotoResult.getStatus()) {
				List<String> errorMessages = List.of(messageSource.getMessage("message167", new String[]{advertisingPhoto, "advertisingPhoto", String.join(StringUtil.COMMA, uploadAdvertisingPhotoResult.getErrorMessageList())}, null));
				return generateResponseData(skuCode, OapiStatusCodeEnum.FAIL.getCode(), errorMessages);
			} else {
				oapiSingleSaveMainRequestData.setAdvertisingPhoto(uploadAdvertisingPhotoResult.getData().getUrl());
			}
		}

		return null;
	}
}
