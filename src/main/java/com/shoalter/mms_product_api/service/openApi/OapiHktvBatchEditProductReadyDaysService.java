package com.shoalter.mms_product_api.service.openApi;

import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditProductReadyDaysMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class OapiHktvBatchEditProductReadyDaysService {

	private final UserHelper userHelper;
	private final OapiHelper oapiHelper;
	private final MessageSource messageSource;
	private final BatchEditHelper batchEditHelper;


	public OapiResponseDto<OapiBatchEditMainResponseData> start(String storefrontStoreCode, String platformCode, String businessType, List<OapiBatchEditProductReadyDaysMainRequestData> requestData) {

		//validation check
		ResponseDto<StoreContractMerchantDo> validateResult = oapiHelper.validateStore(storefrontStoreCode, platformCode, businessType);
		if (StatusCodeEnum.FAIL.getCode() == validateResult.getStatus()) {
			return oapiHelper.generateBatchEditResponseData(OapiStatusCodeEnum.FAIL, null, validateResult.getErrorMessageList());
		}

		//contract type check
		StoreContractMerchantDo storeContractMerchantDo = validateResult.getData();
		if (!ContractType.EVERUTS.equals(storeContractMerchantDo.getContractType())) {
			return oapiHelper.generateBatchEditResponseData(OapiStatusCodeEnum.FAIL, null, List.of(messageSource.getMessage("message324", new String[]{storefrontStoreCode}, null)));
		}

		//create record
		UserDto userDto = userHelper.generateOapiUserDto(OapiHelper.OPEN_API_SOURCE_IDENTIFIER_PREFIX + storefrontStoreCode);
		ResponseDto<Long> recordResult = batchEditHelper.createOapiBatchEditRecord(userDto, storeContractMerchantDo, requestData, SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS);
		return oapiHelper.generateBatchEditResponseData(recordResult.getStatus() == StatusCodeEnum.SUCCESS.getCode() ?
			OapiStatusCodeEnum.SUCCESS : OapiStatusCodeEnum.FAIL, recordResult.getData(), recordResult.getErrorMessageList());

	}
}
