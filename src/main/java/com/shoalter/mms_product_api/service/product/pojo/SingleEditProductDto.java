package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SingleEditProductDto {
    private ProductMasterDto product;
    private EditInventoryDto inventory;
	@JsonProperty("variant_sku_product")
	@SerializedName("variant_sku_product")
	private List<VariantMatrixProductDto> variantSkuProductList;

	@Schema(hidden = true)
	private String errorMessage;//error report用

	//only for upload type = 21 , 22 & 23
	private ProductMigrationDto productMigration;

	//only for upload type = 26 ~ 29  and 31, 32
	private BatchUpdateProductInfoDto batchEditElement;

	// if all system successful, remove old image and old video
	private ProductOldMultimediaDto productOldMultimedia;

	private LittleMallRelationDto relation;
}
