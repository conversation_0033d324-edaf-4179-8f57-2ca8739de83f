package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.reflect.TypeToken;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.MmsThirdPartyBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.TooniesProductDetailResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.retry.RetryException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * This helper class is used to call the third party API
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MmsThirdPartySkuHelper {
	private final HttpRequestHelper httpRequestHelper;

	@Value("${mms-third-party-sku.api.url}")
	private String mmsThirdPartySkuUrl;
	@Value("${mms-third-party-sku.one-bound.detail.endpoint}")
	private String oneBoundDetailUrl;
	@Value("${mms-third-party-sku.toonies.detail.endpoint}")
	private String tooniesDetailUrl;
	private static final String SERVICE_NAME = "MMS_THIRD_PARTY_SKU";

	public OneBoundDetailResponseDto fetchSkuOneBoundDetail(OneBoundDetailRequestDto oneBoundDetailRequestDto) {
		String url = UriComponentsBuilder.fromUriString(mmsThirdPartySkuUrl)
			.path(oneBoundDetailUrl)
			.buildAndExpand(oneBoundDetailRequestDto.getStoreId(), oneBoundDetailRequestDto.getTargetProductCode())
			.toUriString();

		return httpRequestHelper.requestForBody(HttpRequestDto.<OneBoundDetailRequestDto, OneBoundDetailResponseDto>builder()
			.serviceName(SERVICE_NAME)
			.url(url)
			.method(HttpMethod.GET)
			.body(oneBoundDetailRequestDto)
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.build());
	}

	@Retryable(
		value = {HttpServerErrorException.class},
		maxAttempts = 4,
		backoff = @Backoff(delay = 1000)
	)
	public TooniesProductDetailResponseDto fetchSkuTooniesDetail(String productCode) {
		String url = UriComponentsBuilder.fromUriString(mmsThirdPartySkuUrl)
			.path(tooniesDetailUrl)
			.buildAndExpand(productCode)
			.toUriString();
		MmsThirdPartyBaseResponseDto<TooniesProductDetailResponseDto> result = httpRequestHelper.requestForBodyIncludeThrowHttpException(HttpRequestDto.<String, MmsThirdPartyBaseResponseDto<TooniesProductDetailResponseDto>>builder()
			.serviceName(SERVICE_NAME)
			.url(url)
			.method(HttpMethod.GET)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<MmsThirdPartyBaseResponseDto<TooniesProductDetailResponseDto>>() {
				}.getType()))
			.build());
		return (result == null || StatusCodeEnum.FAIL.getCode() == result.getStatus()) ? null : result.getData();
	}
}

