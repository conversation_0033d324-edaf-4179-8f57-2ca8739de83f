package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class PermissionHelper {
    private final MerchantRepository merchantRepository;
    private final StoreRepository storeRepository;
	private final ContractRepository contractRepository;
    private static final List<String> ALLOW_PERMISSION_SOURCE = List.of("SYSTEM", OapiHelper.OPEN_API_SOURCE_IDENTIFIER_PREFIX);
    private static final Set<String> ALLOW_FORCE_OFFLINE_ROLE_SET = Set.of(
			RoleCode.SUPER_SYSTEM_ADMIN,
			RoleCode.ADMIN,
			RoleCode.RM_ADMIN,
			RoleCode.OPERATION_ADMIN,
			RoleCode.OPS_DEPT_HEAD,
			RoleCode.QA,
			RoleCode.CQA,
			RoleCode.CQA_LEADER,
			RoleCode.MCS,
			RoleCode.MCSTL,
			RoleCode.MCS_APPROVER
	);

    public void checkPermission(UserDto userDto, Integer merchantId) {
        if (ALLOW_PERMISSION_SOURCE.stream().anyMatch(source ->
            userDto.getUserCode().equalsIgnoreCase(source) || userDto.getUserCode().startsWith(source))) {
            return;
        }

        int count = 0;
        if (ConstantType.ROLE_TYPE_SYSTEM.equals(userDto.getRoleType())) {
            switch (userDto.getRoleCode()) {
                case RoleCode.RM:
                    count = merchantRepository.countByRmUserIdAndMerchantId(userDto.getUserId(), merchantId);
                    break;
                case RoleCode.RML:
                    count = merchantRepository.countByBuRmlUserIdAndMerchantId(userDto.getUserId(), merchantId);
                    break;
                case RoleCode.DEPT_HEAD:
                    count = merchantRepository.countByDeptHeadId(userDto.getUserId(), merchantId);
                    break;
                case RoleCode.RMO:
                    count = merchantRepository.countByUserDeptCode(userDto.getUserId(), merchantId);
                    break;
                default:
                    count = merchantRepository.countByBuAdminUserIdAndMerchantId(userDto.getUserId(), merchantId);
            }
        } else if (ConstantType.ROLE_TYPE_MERCHANT.equals(userDto.getRoleType())) {
            count = merchantRepository.countByUserIdAndMerchantId(userDto.getUserId(), merchantId);
        }

        if (count == 0) {
			log.error("User does not have permission to access this merchant, user id : {}, merchant id : {}", userDto.getUserId(), merchantId);
            throw new SystemI18nException("message28", userDto.getRoleCode());
        }
    }


	public List<StoreMerchantViewDo> checkPermissionByStoreIds(UserDto userDto, List<Integer> storeIds) {
		List<StoreMerchantViewDo> storeMerchantViewDoList = storeRepository.findStoreMerchantViewDoByStoreIds(storeIds);
		if (storeMerchantViewDoList.isEmpty()) {
			throw new SystemI18nException("message264", "storeIds");
		}
		for (int merchantId : storeMerchantViewDoList.stream().map(StoreMerchantViewDo::getMerchantId).collect(Collectors.toList())) {
			checkPermission(userDto, merchantId);
		}

		return storeMerchantViewDoList;
	}

	public void checkPermissionByContractIds(UserDto userDto, List<Integer> contractIds) {
		List<Integer> merchantIds = contractRepository.findMerchantIdsByContractIds(contractIds);
		if (merchantIds.isEmpty()) {
			throw new SystemI18nException("message264", "contractIds");
		}
		for (int merchantId : merchantIds) {
			checkPermission(userDto, merchantId);
		}
	}

    /**
     * Check whether user has permission to perform force offline action.
     * Only whitelisted roles are allowed.
     */
    public void checkForceOfflinePermission(UserDto userDto) {
        if (!ALLOW_FORCE_OFFLINE_ROLE_SET.contains(userDto.getRoleCode())) {
            log.error("User role {} is not allowed to perform force offline", userDto.getRoleCode());
            throw new SystemI18nException("message131");
        }
    }
}
