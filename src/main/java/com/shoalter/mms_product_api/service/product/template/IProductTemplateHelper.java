package com.shoalter.mms_product_api.service.product.template;


import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface IProductTemplateHelper<T extends TemplateInterface<T>> {
	String PRODUCT_SHEET_NAME = "Product Template";

	default Workbook setTemplateBodyColumn(Workbook workbook, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList) {
		return workbook;
	}

	default Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> productList, List<SysParmDo> sysParmList, SaveProductRecordDo record) {
		return workbook;
	}

	default Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> productList, SaveProductRecordDo record) {
		return workbook;
	}

	int getColumnWidth(TemplateInterface<T> columnEnum);

	default boolean isLockColumn(TemplateInterface<T> columnEnum) {
		return false;
	}

	default void setLoveSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, AbstractReport.Num theColNum) {
		// Default do Nothing
	};

	default void setLoveSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, AbstractReport.Num theColNum, Pair<BusinessPlatformDo, List<SysParmDo>> necessaryDataPair) {
		// Default do Nothing
	};
}
