package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindMenuService {
    private final SysParmRepository sysParmRepository;

    public ResponseDto<List<SysParmDo>> start(String segment, String buCode) {
        List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndBuCode(segment, buCode);
        return ResponseDto.<List<SysParmDo>>builder().status(1).data(sysParmDoList).build();
    }
}
