package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ProductMasterViewDto implements Serializable {
	// from ProductMasterProductDto
	private String uuid;
	@JsonProperty("brand_id")
	@SerializedName("brand_id")
	private Integer brandId;
	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;
	@JsonProperty("manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;
	@JsonProperty("colour_families")
	@SerializedName("colour_families")
	private String colourFamilies;
	private String color;
	@JsonProperty("size_system")
	@SerializedName("size_system")
	private String sizeSystem;
	private String size;
	private String option1;
	@JsonProperty("option1_value")
	@SerializedName("option1_value")
	private String option1Value;
	private String option2;
	@JsonProperty("option2_value")
	@SerializedName("option2_value")
	private String option2Value;
	private String option3;
	@JsonProperty("option3_value")
	@SerializedName("option3_value")
	private String option3Value;
	private List<ProductBarcodeDto> barcodes;
	@JsonProperty("packing_height")
	@SerializedName("packing_height")
	private BigDecimal packingHeight;
	@JsonProperty("packing_length")
	@SerializedName("packing_length")
	private BigDecimal packingLength;
	@JsonProperty("packing_depth")
	@SerializedName("packing_depth")
	private BigDecimal packingDepth;
	@JsonProperty("packing_dimension_unit")
	@SerializedName("packing_dimension_unit")
	private String packingDimensionUnit;
	private BigDecimal weight;
	@JsonProperty("weight_unit")
	@SerializedName("weight_unit")
	private String weightUnit;
	@JsonProperty("storage_temperature")
	@SerializedName("storage_temperature")
	private String storageTemperature;
	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;
	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;
	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;
	@JsonProperty("minimum_shelf_life")
	@SerializedName("minimum_shelf_life")
	private Integer minimumShelfLife;
	@JsonProperty("merchant_name")
	@SerializedName("merchant_name")
	private String merchantName;
	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	private String skuNameEn;
	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	private String skuNameCh;
	// from ProductMasterResultDto
	private String id;
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
	@JsonProperty("create_time")
	@SerializedName("create_time")
	private Date createTime;
	@JsonProperty("create_user")
	@SerializedName("create_user")
	private String createUser;
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
	@JsonProperty("modified_time")
	@SerializedName("modified_time")
	private Date modifiedTime;
	@JsonProperty("modified_user")
	@SerializedName("modified_user")
	private String modifiedUser;
	private Integer version;
	// for frontend view data
	@JsonProperty("brand_name_en")
	@SerializedName("brand_name_en")
	private String brandNameEn;
	@JsonProperty("brand_name_chi")
	@SerializedName("brand_name_chi")
	private String brandNameChi;
	@JsonProperty("manufactured_country_desc")
	@SerializedName("manufactured_country_desc")
	private String manufacturedCountryDesc;
	@JsonProperty("storage_temperature_desc")
	@SerializedName("storage_temperature_desc")
	private String storageTemperatureDesc;
	@JsonProperty("packing_box_type_desc")
	@SerializedName("packing_box_type_desc")
	private String packingBoxTypeDesc;
	@JsonProperty("colour_families_desc")
	@SerializedName("colour_families_desc")
	private String colourFamiliesDesc;
	@JsonProperty("size_system_desc")
	@SerializedName("size_system_desc")
	private String sizeSystemDesc;
	@JsonProperty("option1_desc")
	@SerializedName("option1_desc")
	private String option1Desc;
	@JsonProperty("option2_desc")
	@SerializedName("option2_desc")
	private String option2Desc;
	@JsonProperty("option3_desc")
	@SerializedName("option3_desc")
	private String option3Desc;
	@JsonProperty("colour_desc")
	@SerializedName("colour_desc")
	private String colourDesc;
	@JsonProperty("size_desc")
	@SerializedName("size_desc")
	private String sizeDesc;
	@JsonProperty("option1_value_desc")
	@SerializedName("option1_value_desc")
	private String option1ValueDesc;
	@JsonProperty("option2_value_desc")
	@SerializedName("option2_value_desc")
	private String option2ValueDesc;
	@JsonProperty("option3_value_desc")
	@SerializedName("option3_value_desc")
	private String option3ValueDesc;
	@JsonProperty("carton_size")
	@SerializedName("carton_size")
	private List<CartonSizeDto> cartonSizeList;
	// 其他bu特有資料
	private BuProductViewDto additional;
}
