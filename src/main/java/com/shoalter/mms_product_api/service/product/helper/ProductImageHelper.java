package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ImageStatusRequestData;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResultDataDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResultDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResultStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.constant.ImageValidationConstants;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ResourceUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductImageHelper {
    private static final String SERVICE_NAME = "Image Server";

    private static final String OLD_IMAGE_SUFFIX = "_1200";
    private static final String OLD_IMAGE_PREFIX = "https:";
    private static final String OLD_IMAGE_CHECK_PREFIX = "//";

    private final Gson gson;
    private final HttpRequestHelper httpRequestHelper;
    private final MessageSource messageSource;

    private final String imagePath;
    private final String imageUploadUrl;
    private final String imageStatusUrl;
    private final String imageDeleteUrl;
    private final String imageBatchDeleteUrl;
    public static String imageDomain;
    public static String imageDomainModify;

    public ProductImageHelper(
            Gson gson, HttpRequestHelper httpRequestHelper, MessageSource messageSource, @Value("${upload.file.imagePath}") String imagePath,
            @Value("${image.upload.url}") String imageUploadUrl,
            @Value("${image.status.url}") String imageStatusUrl,
            @Value("${image.delete.url}") String imageDeleteUrl,
            @Value("${image.batch.delete.url}") String imageBatchDeleteUrl,
            @Value("${image.domain}") String imageDomain,
            @Value("${image.domain.modify}") String imageDomainModify
    ) {
        this.gson = gson;
        this.httpRequestHelper = httpRequestHelper;
        this.messageSource = messageSource;
        this.imagePath = imagePath;
        this.imageUploadUrl = imageUploadUrl;
        this.imageStatusUrl = imageStatusUrl;
        this.imageDeleteUrl = imageDeleteUrl;
        this.imageBatchDeleteUrl = imageBatchDeleteUrl;
        ProductImageHelper.imageDomain = imageDomain;
        ProductImageHelper.imageDomainModify = imageDomainModify;
    }


    public HttpStatus updateServerImageStatus(String userCode, List<String> imageUrlIdList) {
        if (CollectionUtil.isEmpty(imageUrlIdList)) {
            return null;
        }
        log.debug(String.format("imageUrlIdList = %s", gson.toJson(imageUrlIdList)));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

		return httpRequestHelper.requestForStatus(HttpRequestDto.<ImageStatusRequestData, Void>builder()
                .serviceName(SERVICE_NAME)
                .url(imageStatusUrl)
                .method(HttpMethod.PUT)
                .customHeaders(headers)
                .body(ImageStatusRequestData.builder()
                        .imageIds(imageUrlIdList)
                        .status("commit")
                        .build())
                .user(UserDto.builder().userCode(userCode).build())
                .build());
    }

    public HttpStatus requestDeleteImageByUrlId(String userCode, String urlId) {
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("imageId", urlId);

        return httpRequestHelper.requestForStatus(HttpRequestDto.<MultiValueMap<String, String>, Void>builder()
                .serviceName(SERVICE_NAME)
                .url(imageDeleteUrl)
                .method(HttpMethod.DELETE)
                .customHeaders(fileHeaders())
                .body(body)
                .user(UserDto.builder().userCode(userCode).build())
                .build());
    }

    public UploadImageResponseDto imageUpload(UserDto userDto, MultipartFile multipartFile, UploadImageResponseDto uploadImageResponseDto) {
        log.info("*****Start upload image********* ");
        UploadImageResultDto result = httpRequestHelper.requestForBody(HttpRequestDto.<MultiValueMap<String, Object>, UploadImageResultDto>builder()
                .serviceName(SERVICE_NAME)
                .url(imageUploadUrl)
                .method(HttpMethod.POST)
                .customHeaders(fileHeaders())
                .resultClass(UploadImageResultDto.class)
                .body(getUploadImageBody(multipartFile, uploadImageResponseDto))
                .user(userDto)
                .build());
        log.info("*****End upload image********* ");

        UploadImageResultStatusDto status = result.getStatus();
        if ("success".equals(status.getCode())) {
            UploadImageResultDataDto data = result.getData().get(0);
            uploadImageResponseDto.setUrlId(data.getId());
            uploadImageResponseDto.setFileName(data.getFileName());
            uploadImageResponseDto.setFilePath(data.getUrl());
            return uploadImageResponseDto;
        }
        throw new SystemException("upload imageFileServer failed ");
    }

	public ResponseDto<UploadImageResultDataDto> imageUrlsUpload(UserDto userDto, String imageUrl, UploadImageResponseDto uploadImageResponseDto) {
		UploadImageResultDto requestResult = httpRequestHelper.requestForBody(HttpRequestDto.<MultiValueMap<String, Object>, UploadImageResultDto>builder()
			.serviceName(SERVICE_NAME)
			.url(imageUploadUrl)
			.method(HttpMethod.POST)
			.customHeaders(fileHeaders())
			.resultClass(UploadImageResultDto.class)
			.body(getUploadImageUrlBody(imageUrl, uploadImageResponseDto))
			.user(userDto)
			.build());

		if(requestResult == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message157", null, null)));
		} else if (StatusCodeEnum.FAIL.name().equalsIgnoreCase(requestResult.getStatus().getCode())) {
			return ResponseDto.fail(requestResult.getStatus().getMessage());
		}

		return ResponseDto.success(requestResult.getData().get(0));
	}

	public HttpStatus requestDeleteImageByUrlIds(String userCode, List<String> urlIds) {
		MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
		body.add("imageIds", String.join(",", urlIds));
		return httpRequestHelper.requestForStatus(HttpRequestDto.<MultiValueMap<String, String>, Void>builder()
			.serviceName(SERVICE_NAME)
			.url(imageBatchDeleteUrl)
			.method(HttpMethod.DELETE)
			.customHeaders(fileHeaders())
			.body(body)
			.user(UserDto.builder().userCode(userCode).build())
			.build());
	}

    private MultiValueMap<String, Object> getUploadImageBody(MultipartFile multipartFile, UploadImageResponseDto uploadImageResponseDto) {
        MultiValueMap<String, Object> body = generateRequestMap(uploadImageResponseDto);
        body.add("imageFiles", multipartFile.getResource());
        return body;
    }

    private MultiValueMap<String, Object> getUploadImageUrlBody(String imageUrl, UploadImageResponseDto uploadImageResponseDto) {
        MultiValueMap<String, Object> body = generateRequestMap(uploadImageResponseDto);
        body.add("imageUrl", imageUrl);
        return body;
    }

	private MultiValueMap<String, Object> generateRequestMap(UploadImageResponseDto uploadImageResponseDto) {
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		body.add("systemName", "mms");
		body.add("functionName", "uploadProductImage");
		body.add("status", "upload");
		body.add("skuCode", uploadImageResponseDto.getSkuCode());
		body.add("merchantId", uploadImageResponseDto.getMerchantId());
		body.add("imageType", uploadImageResponseDto.getImageType());
		body.add("businessCode", uploadImageResponseDto.getPlatformCode());

		return body;
	}

    /**
     * Validates an uploaded image file against various criteria.
     *
     * @param multipartFile The image file to validate
     * @return A list of validation error messages, empty if the image is valid
     */
    public List<String> checkImage(MultipartFile multipartFile) throws IOException {
        List<String> errorList = new ArrayList<>();
        String fileName = multipartFile.getOriginalFilename();

        // Check if file is a valid image
        BufferedImage image = ImageIO.read(multipartFile.getInputStream());
        if (image == null) {
            errorList.add("Not Photo!");
            return errorList;
        }

        // Check file size
        long size = multipartFile.getSize();
        if (size > ImageValidationConstants.MAX_IMAGE_SIZE_BYTES) {
            errorList.add("  The image size should not be larger than " + ImageValidationConstants.MAX_IMAGE_SIZE_MB + " MB.");
        }

        // Check file extension
        String fileExtension = fileName != null ? StringUtils.getFilenameExtension(fileName) : null;
        if (fileExtension == null || !ImageValidationConstants.SUPPORTED_FILE_EXTENSIONS.contains(fileExtension.toLowerCase())) {
            errorList.add("  Unable to upload more than image.");
        }

        // Check image dimensions
        int width = image.getWidth();
        int height = image.getHeight();
        if (width < ImageValidationConstants.MIN_IMAGE_WIDTH || height < ImageValidationConstants.MIN_IMAGE_HEIGHT) {
            errorList.add("  The minimum image dimension ratio should be (" + ImageValidationConstants.MIN_IMAGE_WIDTH + " * " + ImageValidationConstants.MIN_IMAGE_HEIGHT + ").");
        }

        // Check filename validity
        if (fileName != null && (fileName.length() > ImageValidationConstants.MAX_FILENAME_LENGTH || !fileName.matches(ImageValidationConstants.IMAGE_NAME_REGEX))) {
            errorList.add("  Please ensure your file name contains only letters, numbers, and the following special characters: ! @ # $ % ^ & { } [ ] ( ) _ + - = , . ~ ' `. The file name should be between 1 and 255 characters long");
        }

        return errorList;
    }

    private HttpHeaders fileHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        return headers;
    }

    private String generateHref(String path) {
        String href;
        if (ResourceUtil.existsImageDomain(path)) {
            href = path;
        } else {
            href = imagePath + path;
        }
        return href;
    }

	public List<String> generateUrlId(List<String> urls) {
		return urls.stream().map(url -> url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."))).collect(Collectors.toList());
	}

	public UploadImageResponseDto generateUploadImage(String skuCode, Integer merchantId, String platformCode) {
		UploadImageResponseDto image = new UploadImageResponseDto();
		image.setSkuCode(skuCode);
		image.setMerchantId(merchantId);
		image.setPlatformCode(platformCode);
		image.setImageType("compress");
		return image;
	}

	public String convertPhotoUrl(String photo) {
		if (StringUtil.isEmpty(photo)) {
			return photo;
		}

		// "//images.hktvmall.com/xxx.jpg" turn to "https://images.hktvmall.com/xxx.jpg"
		if (photo.startsWith(OLD_IMAGE_CHECK_PREFIX)) {
			photo = OLD_IMAGE_PREFIX + photo;
		}

		if (!ResourceUtil.needModifiedImageDomain(photo)) {
			return photo;
		}

		int index = photo.lastIndexOf(".");
		if (OLD_IMAGE_SUFFIX.equals(photo.substring(index - OLD_IMAGE_SUFFIX.length(), index))) {
			return photo;
		}

		// "https://images.hktvmall.com/xxx.jpg" turn to "https://images.hktvmall.com/xxx_1200.jpg"
		return photo.substring(0, index) + OLD_IMAGE_SUFFIX + photo.substring(index);
	}

	public List<String> convertPhotoUrl(List<String> photos) {
		if (CollectionUtil.isEmpty(photos)) {
			return photos;
		}
		return photos.stream().map(this::convertPhotoUrl).collect(Collectors.toList());
	}

	public void convertPhotoUrl(HktvProductDto hktvProductDto) {
		if (hktvProductDto == null) {
			return;
		}
		hktvProductDto.setMainPhoto(convertPhotoUrl(hktvProductDto.getMainPhoto()));
		hktvProductDto.setOtherPhoto(convertPhotoUrl(hktvProductDto.getOtherPhoto()));
		hktvProductDto.setVariantProductPhoto(convertPhotoUrl(hktvProductDto.getVariantProductPhoto()));
		hktvProductDto.setAdvertisingPhoto(convertPhotoUrl(hktvProductDto.getAdvertisingPhoto()));
	}
}
