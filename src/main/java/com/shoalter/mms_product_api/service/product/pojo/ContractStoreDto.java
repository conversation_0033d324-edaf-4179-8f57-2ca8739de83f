package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ContractStoreDto {
	private Integer contractId;
	private String opportunityNo;
	private String opportunityName;
	private Integer merchantId;
	private String contractNo;
	private Integer rmId;
	private Date startDate;
	private Date endDate;
	private Integer durationYears;
	private Integer contractTypeId;
	private Integer masterContractId;
	private BigDecimal annualFee;
	private String status;
	private Date terminateDate;
	private String insurance;
	private Integer storeId;
	private String overseasCurrency;
	private BigDecimal commissionRate;
	private String contractTypeCode;
	private Integer busUnitId;
	private String storeCode;
	private String activeInd;
	private String storefrontStoreCode;
	private String storeName;
	private String storeNameTc;
	private String storefrontStoreStatus;
	private BigDecimal markUpRate;
	private String onlineStatus;
	private BigDecimal deliveryThershold;
	private BigDecimal deliveryFee;
	private String storeType;
	private String storeLandmarkFlag;
	private String merchantType;
	private Date upgradeDate;
}
