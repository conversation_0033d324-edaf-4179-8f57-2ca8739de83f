package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.QueryTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class MerchantHelper {

	private final MerchantRepository merchantRepository;
	private final SysParmRepository sysParmRepository;

	public boolean isBuySell(Integer merchantId) {
		List<SysParmDo> sysParmList = sysParmRepository.findBySegment("BUYSELL_MERCHANT");
		Set<String> buySaleMerchantSet = new HashSet<>();
		for (SysParmDo sysParmDo : sysParmList) {
			String value = sysParmDo.getParmValue();
			Collections.addAll(buySaleMerchantSet, value.split(","));
		}
		return buySaleMerchantSet.contains(String.valueOf(merchantId));
	}

	public boolean isVirtualStoreMerchant(Integer merchantId, String virtualStore) {
		List<SysParmDo> virtualStoreMerchants = sysParmRepository.findBySegmentAndCode("VIRTUAL_STORE_MERCHANT", virtualStore);
		Set<String> virtualStoreMerchantSet = new HashSet<>();
		for (SysParmDo virtualStoreMerchant : virtualStoreMerchants) {
			String[] virtualStoreMerchantIdArray = virtualStoreMerchant.getParmValue().split(",");
			Collections.addAll(virtualStoreMerchantSet, virtualStoreMerchantIdArray);
		}
		return virtualStoreMerchantSet.contains(String.valueOf(merchantId));
	}

	public List<Integer> findMerchantIdByRole(UserDto userDto) {
		List<Integer> merchantList = new ArrayList<>();
		if(ConstantType.ROLE_TYPE_SYSTEM.equals(userDto.getRoleType())){
			switch (userDto.getRoleCode()) {
				case RoleCode.RM:
					merchantList = merchantRepository.findMerchantIdByRmUserId(userDto.getUserId());
					break;
				case RoleCode.RMO:
					merchantList = merchantRepository.findMerchantIdByRmoUserId(userDto.getUserId());
					break;
				case RoleCode.RML:
					merchantList = merchantRepository.findMerchantIdByBuRmlUserId(userDto.getUserId());
					break;
				case RoleCode.DEPT_HEAD:
					merchantList = merchantRepository.findMerchantIdByDeptHeadUserId(userDto.getUserId());
					break;
				default:
					merchantList = merchantRepository.findMerchantIdByBuAdminUserId(userDto.getUserId());
					break;
			}
		}else if(ConstantType.ROLE_TYPE_MERCHANT.equals(userDto.getRoleType())){
			merchantList = merchantRepository.findMerchantIdByUserId(userDto.getUserId());
		}

		return merchantList;
	}

	public List<MerchantDo> findMerchantByRole(UserDto userDto){
		List<MerchantDo> merchantList = new ArrayList<>();
		if(ConstantType.ROLE_TYPE_SYSTEM.equals(userDto.getRoleType())){
			switch (userDto.getRoleCode()) {
				case RoleCode.RM:
					merchantList = merchantRepository.findByRmUserId(userDto.getUserId());
					break;
				case RoleCode.RML:
					merchantList = merchantRepository.findByBuRmlUserId(userDto.getUserId());
					break;
				case RoleCode.DEPT_HEAD:
					merchantList =  merchantRepository.findByDeptHeadId(userDto.getUserId());
					break;
				case RoleCode.RMO:
					merchantList =  merchantRepository.findByUserDeptCode(userDto.getUserId());
					break;
				default:
					merchantList = merchantRepository.findByBuAdminUserId(userDto.getUserId());
			}
		}else if(ConstantType.ROLE_TYPE_MERCHANT.equals(userDto.getRoleType())){
			merchantList = merchantRepository.findByUserId(userDto.getUserId());
		}
		return merchantList;
	}

	public QueryTypeEnum getQueryType(UserDto userDto) {
		switch (userDto.getRoleCode()) {
			case RoleCode.MERCHANT_OPERATOR:
			case RoleCode.MERCHANT_ADMIN:
			case RoleCode.MERCHANT:
			case RoleCode.MERCHANT_BASIC:
			case RoleCode.SELLER_CUSTOMER_SERVICE_AGENT:
				return QueryTypeEnum.FUZZY;
			default:
				return QueryTypeEnum.LEFT;
		}
	}

	public void convertMerchantListByMerchantName(ProductSearchRequestDto productSearchRequestDto, String merchantName) {
		if (StringUtil.isNotEmpty(merchantName) && CollectionUtil.isNotEmpty(productSearchRequestDto.getMerchantId())) {
			List<Integer> searchMerchantList = merchantRepository.findByMerchantNameKeyword(merchantName);
			List<Integer> permissionMerchantList = filterPermissionMerchantList(productSearchRequestDto.getMerchantId(), searchMerchantList);
			productSearchRequestDto.setMerchantId(permissionMerchantList);
		}
	}

	public List<Integer> filterPermissionMerchantList(List<Integer> permissionMerchantList, List<Integer> searchMerchantList) {
		if (CollectionUtil.isNotEmpty(searchMerchantList)) {
			return searchMerchantList.stream().filter(permissionMerchantList::contains).collect(Collectors.toList());
		}
		return searchMerchantList;
	}
}
