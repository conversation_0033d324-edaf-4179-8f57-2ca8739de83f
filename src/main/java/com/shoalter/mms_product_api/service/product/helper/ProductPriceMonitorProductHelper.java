package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ProductPriceMonitorProductHelper {
	private final ProductPriceMonitorProductRepository monitorProductRepository;
	private final StoreRepository storeRepository;
	private final SysParmRepository sysParmRepository;
	private final Gson gson;

	public void priceMonitorCreateProcess(SaveProductRecordRowDo row, UserDto userDto) {
		try {
			ProductMasterDto editProductDto = extractProductDto(row);
			if (isBasicHktvProductDataInvalid(editProductDto)) {
				return;
			}

			MonitorPlatformGroupEnum monitorPlatformGroupEnum = getPlatformGroupEnum(editProductDto);
			if (monitorPlatformGroupEnum == null) {
				log.info("create price monitor skip, monitorPlatformGroupEnum is null, product code: {}, skuId: {}, row id: {}, record id: {}", editProductDto.getProductId(), editProductDto.getSkuId(), row.getId(), row.getRecordId());
				return;
			}

			ThirdPartySourceEnum sourceEnum = monitorPlatformGroupEnum.getSourceEnum();
			if (!validateExternalPlatform(editProductDto, sourceEnum)) {
				return;
			}

			if (isStoreAndCurrencyInvalid(editProductDto, monitorPlatformGroupEnum, row, "create")) {
				return;
			}

			Optional<StoreDo> storeOpt = storeRepository.findByStoreCodeAndBuCode(editProductDto.getAdditional().getHktv().getStores(), monitorPlatformGroupEnum.getBuCodeEnum().name());
			if (storeOpt.isEmpty()) {
				log.warn("create or update price monitor skip, store not found, store code: {}", editProductDto.getAdditional().getHktv().getStores());
				return;
			}

			// Check if the SKU is in the whitelist for TMALL_RMB_HKTV_HKD_WHITELIST
			if (MonitorPlatformGroupEnum.TMALL_RMB_HKTV_HKD_WHITELIST.equals(monitorPlatformGroupEnum)) {
				if (isNotWhiteListSku(editProductDto, storeOpt.get())) {
					// Skip monitoring if the SKU is not in the whitelist
					return;
				}
			}

			String storefrontStoreCode = editProductDto.getAdditional().getHktv().getStorefrontStoreCode();
			log.info("enable monitor product record, product code: {}, skuId: {}, externalPlatform: {}, row id: {}, record id: {}, storefrontStoreCode: {}", editProductDto.getProductId(), editProductDto.getSkuId(), editProductDto.getAdditional().getHktv().getExternalPlatform(), row.getId(), row.getRecordId(), storefrontStoreCode);
			enableMonitor(monitorPlatformGroupEnum, editProductDto, userDto, storeOpt.get());
		} catch (Exception e) {
			log.error("create price monitor error, row id: {}, record id: {}", row.getId(), row.getRecordId(), e);
		}
	}


	public void priceMonitorUpdateProcess(SaveProductRecordRowDo row, ProductMasterResultDto beforeProductFromPm, UserDto userDto) {
		try {
			ProductMasterDto editProductDto = extractProductDto(row);
			if (isBasicHktvProductDataInvalid(editProductDto) ||
				isBasicHktvProductDataInvalid(beforeProductFromPm)) {
				return;
			}

			MonitorPlatformGroupEnum monitorPlatformGroupEnum = getPlatformGroupEnum(editProductDto);
			if (monitorPlatformGroupEnum == null) {
				log.info("update price monitor skip, monitorPlatformGroupEnum is null, product code: {}, skuId: {}, row id: {}, record id: {}", editProductDto.getProductId(), editProductDto.getSkuId(), row.getId(), row.getRecordId());
				return;
			}

			if (isStoreAndCurrencyInvalid(beforeProductFromPm, monitorPlatformGroupEnum, row, "update")) {
				return;
			}

			Optional<StoreDo> storeOpt = storeRepository.findByStoreCodeAndBuCode(editProductDto.getAdditional().getHktv().getStores(), monitorPlatformGroupEnum.getBuCodeEnum().name());
			if (storeOpt.isEmpty()) {
				log.warn("create or update price monitor skip, store not found, store code: {}", editProductDto.getAdditional().getHktv().getStores());
				return;
			}

			// Check if the SKU is in the whitelist for TMALL_RMB_HKTV_HKD_WHITELIST
			if (MonitorPlatformGroupEnum.TMALL_RMB_HKTV_HKD_WHITELIST.equals(monitorPlatformGroupEnum)) {
				if (isNotWhiteListSku(editProductDto, storeOpt.get())) {
					// Skip monitoring if the SKU is not in the whitelist
					return;
				}
			}

			String storefrontStoreCode = beforeProductFromPm.getAdditional().getHktv().getStorefrontStoreCode();
			PriceMonitorAction action = determinePriceMonitorAction(
				editProductDto.getAdditional().getHktv().getExternalPlatform(),
				beforeProductFromPm.getAdditional().getHktv().getExternalPlatform(),
				monitorPlatformGroupEnum
			);

			log.info("price monitor action for skuId: {}, action: {}", editProductDto.getSkuId(), action);

			switch (action) {
				case NEED_ENABLE_MONITOR:
					log.info("enable monitor product record, product code: {}, skuId: {}, new externalPlatform: {}, origin externalPlatform: {}, row id: {}, record id: {}, storefrontStoreCode: {}, currency: {}",
						editProductDto.getProductId(), editProductDto.getSkuId(), editProductDto.getAdditional().getHktv().getExternalPlatform(), beforeProductFromPm.getAdditional().getHktv().getExternalPlatform(), row.getId(), row.getRecordId(), storefrontStoreCode, editProductDto.getAdditional().getHktv().getCurrency());
					enableMonitor(monitorPlatformGroupEnum, editProductDto, userDto, storeOpt.get());
					break;
				case NEED_DISABLE_MONITOR:
					log.info("disable monitor product record, product code: {}, skuId: {}, new externalPlatform: {}, origin externalPlatform: {}, row id: {}, record id: {}, storefrontStoreCode: {}, currency: {}",
						editProductDto.getProductId(), editProductDto.getSkuId(), editProductDto.getAdditional().getHktv().getExternalPlatform(), beforeProductFromPm.getAdditional().getHktv().getExternalPlatform(), row.getId(), row.getRecordId(), storefrontStoreCode, beforeProductFromPm.getAdditional().getHktv().getCurrency());
					disableMonitor(monitorPlatformGroupEnum, beforeProductFromPm, userDto, storeOpt.get());
					break;
				default:
					break;
			}
		} catch (Exception e) {
			log.error("update price monitor error, row id: {}, record id: {}", row.getId(), row.getRecordId(), e);
		}
	}

	/**
	 * When MonitorPlatformGroupEnum is TMALL_RMB_HKTV_HKD_WHITELIST,
	 * Checks if the SKU is not in the whitelist.
	 */
	private boolean isNotWhiteListSku(ProductMasterDto editProductDto, StoreDo storeDo) {
		// check if the store code is in the whitelist
		Set<String> storefrontStoreCodeSet = Set.of(storeDo.getStorefrontStoreCode());
		List<SysParmDo> rmbStoreWhiteList = sysParmRepository.findBySegmentAndCodeIn(SysParmSegmentEnum.RMB_SKU_WHITELIST.name(), storefrontStoreCodeSet);
		if (rmbStoreWhiteList.isEmpty()) {
			log.warn("create price monitor skip, store code not in whitelist, store code: {}", editProductDto.getAdditional().getHktv().getStores());
			return true;
		}
		// check if the skuId is in the whitelist
		Set<String> whiteListSkuIdSet = rmbStoreWhiteList
			.stream()
			.map(SysParmDo::getParmValue)
			.filter(Objects::nonNull)
			.map(value -> value.split(StringUtil.COMMA))
			.flatMap(Arrays::stream)
			.collect(Collectors.toSet());
		if (!whiteListSkuIdSet.contains(editProductDto.getSkuId())) {
			log.warn("create price monitor skip, skuId not in whitelist, store code: {}, skuId: {}", editProductDto.getAdditional().getHktv().getStores(), editProductDto.getSkuId());
			return true;
		}

		// is whiteList sku
		log.info("create price monitor for whiteList sku, store code: {}, skuId: {}", editProductDto.getAdditional().getHktv().getStores(), editProductDto.getSkuId());
		return false;
	}

	/**
	 * Retrieves MonitorPlatformGroupEnum based on the provided ProductMasterDto.
	 */
	private static MonitorPlatformGroupEnum getPlatformGroupEnum(ProductMasterDto editProductDto) {
		String buCurrencyCode = editProductDto.getAdditional().getHktv().getCurrency();
		CurrencyEnum currencyEnum = CurrencyEnum.getByCode(buCurrencyCode);
		return MonitorPlatformGroupEnum.getByBuCurrencyAndSourceEnum(currencyEnum, ThirdPartySourceEnum.TMALL);
	}

	private ProductMasterDto extractProductDto(SaveProductRecordRowDo row) {
		String rowContent = row.getContent();
		SingleEditProductDto singleEditProductDto = gson.fromJson(rowContent, SingleEditProductDto.class);
		return singleEditProductDto.getProduct();
	}

	private void enableMonitor(MonitorPlatformGroupEnum monitorPlatformGroupEnum, ProductMasterDto editProductDto, UserDto userDto, StoreDo storeDo) {
		String storeSkuId = getStoreSkuId(storeDo.getStorefrontStoreCode(), editProductDto.getSkuId());
		String skuCode = editProductDto.getSkuId();
		String skuName = editProductDto.getSkuNameCh();
		String storefrontStoreCode = storeDo.getStorefrontStoreCode();
		Integer busUnitId = storeDo.getBusUnitId();
		Integer merchantId = editProductDto.getMerchantId();
		Integer storeId = storeDo.getId();
		String userCode = userDto.getUserCode();
		ExternalPlatform externalPlatform = editProductDto.getAdditional().getHktv().getExternalPlatform();

		createOrUpdateActiveMonitorRecord(storeSkuId, storefrontStoreCode, skuCode, skuName, busUnitId, merchantId, storeId, userCode, externalPlatform, monitorPlatformGroupEnum);
	}

	private void disableMonitor(MonitorPlatformGroupEnum monitorPlatformGroupEnum, ProductMasterResultDto beforeProductFromPm, UserDto userDto, StoreDo storeDo) {
		String storeSkuId = getStoreSkuId(storeDo.getStorefrontStoreCode(), beforeProductFromPm.getSkuId());
		Integer busUnitId = storeDo.getBusUnitId();
		String userCode = userDto.getUserCode();

		disableMonitorRecord(storeSkuId, busUnitId, monitorPlatformGroupEnum.getSourceEnum().getValue(), userCode);
	}

	private static String getStoreSkuId(String storefrontStoreCode, String skuId) {
		return storefrontStoreCode + StringUtil.PRODUCT_SEPARATOR + skuId;
	}

	/**
	 * Checks if the basic HKTV product data is invalid.
	 *
	 * @return true if invalid, false otherwise.
	 */
	private boolean isBasicHktvProductDataInvalid(ProductMasterProductDto productDto) {
		if (productDto == null) {
			return true;
		}

		return productDto.getAdditional() == null || productDto.getAdditional().getHktv() == null;
	}

	/**
	 * Validates external platform data
	 *
	 * @return true if valid, false otherwise
	 */
	private boolean validateExternalPlatform(ProductMasterDto productDto, ThirdPartySourceEnum sourceEnum) {
		return productDto.getAdditional().getHktv().getExternalPlatform() != null &&
			meetMonitorCriteria(productDto.getAdditional().getHktv().getExternalPlatform(), sourceEnum);
	}

	/**
	 * Checks if the store code and currency are valid.
	 *
	 * @return true if invalid, false otherwise.
	 */
	private boolean isStoreAndCurrencyInvalid(ProductMasterProductDto productDto, MonitorPlatformGroupEnum monitorPlatformGroupEnum, SaveProductRecordRowDo row, String operation) {
		String productId = productDto.getProductId();
		String skuId = productDto.getSkuId();
		String storeCode = productDto.getAdditional().getHktv().getStores();
		String currency = productDto.getAdditional().getHktv().getCurrency();

		if (storeCode == null) {
			log.info("{} price monitor skip because store code is null. product code: {}, skuId: {}, store code: {}, row id: {}, record id: {}", operation, productId, skuId, storeCode, row.getId(), row.getRecordId());
			return true;
		}

		if (!Objects.equals(monitorPlatformGroupEnum.getBuCurrencyEnum().name(), currency)) {
			log.warn("{} price monitor skip because currency is not matched. product code: {}, skuId: {}, currency code: {}, row id: {}, record id: {}", operation, productId, skuId, currency, row.getId(), row.getRecordId());
			return true;
		}

		return false;
	}

	/**
	 * create initial ProductPriceMonitorProductDo entity
	 */
	public static ProductPriceMonitorProductDo buildInitEntity(
		String storeSkuId,
		String storefrontStoreCode,
		String skuCode,
		String skuName,
		Integer busUnitId,
		Integer merchantId,
		Integer storeId,
		String sourcePlatform,
		String targetPlatform,
		String targetProductCode,
		String targetSkuCode,
		MonitorPlatformGroupEnum monitorPlatformGroupEnum,
		String operator
	) {
		ProductPriceMonitorProductDo entity = new ProductPriceMonitorProductDo();
		entity.setActiveInd(ActiveInd.ENABLE.getValue());
		entity.setPriceStatus(MonitorProductPriceStatus.PENDING.getValue());

		entity.setStoreSkuId(storeSkuId);
		entity.setStorefrontStoreCode(storefrontStoreCode);
		entity.setSkuCode(skuCode);
		entity.setSkuName(skuName);
		entity.setBusUnitId(busUnitId);
		entity.setMerchantId(merchantId);
		entity.setStoreId(storeId);

		entity.setSourcePlatform(sourcePlatform);
		entity.setSourceCurrencyCode(monitorPlatformGroupEnum.getBuCurrencyEnum().name());
		entity.setTargetPlatform(targetPlatform);
		entity.setTargetProductCode(targetProductCode);
		entity.setTargetSkuCode(targetSkuCode);
		entity.setTargetCurrencyCode(monitorPlatformGroupEnum.getThirdPartyCurrencyEnum().name());
		entity.setTargetOriginalPrice(null);
		entity.setTargetSellingPrice(null);
		entity.setTargetUrl(null);
		entity.setTargetPriceUpdatedDate(null);

		LocalDateTime now = LocalDateTime.now();
		entity.setCreatedDate(now);
		entity.setCreatedBy(operator);
		entity.setLastUpdatedDate(now);
		entity.setLastUpdatedBy(operator);
		return entity;
	}

	/**
	 * Enum representing the action to take for price monitor based on new and origin ExternalPlatform values.
	 */
	public enum PriceMonitorAction {
		NEED_ENABLE_MONITOR,   // new != null, origin == null
		NEED_DISABLE_MONITOR,  // new == null, origin != null
		NO_NEED_MONITOR         // all other cases
	}

	/**
	 * Determines the appropriate price monitor action based on the state of new and origin ExternalPlatform.
	 *
	 * @param newPlatform    the new ExternalPlatform (may be null)
	 * @param originPlatform the original ExternalPlatform (may be null)
	 * @return the PriceMonitorAction indicating what to do
	 */
	public static PriceMonitorAction determinePriceMonitorAction(ExternalPlatform newPlatform, ExternalPlatform originPlatform, MonitorPlatformGroupEnum monitorPlatformGroupEnum) {
		// Case 1: New platform added (enable monitoring)
		if (newPlatform != null && originPlatform == null) {
			return PriceMonitorAction.NEED_ENABLE_MONITOR;
		}

		// Case 2: Platform removed (disable monitoring)
		if (newPlatform == null && originPlatform != null) {
			return PriceMonitorAction.NEED_DISABLE_MONITOR;
		}

		// When both platforms exist, check if the source changed
		if (newPlatform != null && originPlatform != null && newPlatform.getSource() != null && originPlatform.getSource() != null) {

			String targetSource = monitorPlatformGroupEnum.getSourceEnum().getValue();
			boolean newHasTargetSource = newPlatform.getSource().contains(targetSource);
			boolean originHasTargetSource = originPlatform.getSource().contains(targetSource);

			// Case 3: Changed from other source to target source (enable)
			if (newHasTargetSource && !originHasTargetSource) {
				return PriceMonitorAction.NEED_ENABLE_MONITOR;
			}

			// Case 4: Changed from target source to other source (disable)
			if (!newHasTargetSource && originHasTargetSource) {
				return PriceMonitorAction.NEED_DISABLE_MONITOR;
			}
		}

		// Default: No changes to monitor
		return PriceMonitorAction.NO_NEED_MONITOR;
	}

	/**
	 * Check if the ExternalPlatform object meets the criteria for price monitoring.
	 * Criteria:
	 * - ExternalPlatform is not null
	 * - source contains "TMALL"
	 * - productId not blank
	 */
	public static boolean meetMonitorCriteria(ExternalPlatform ext, ThirdPartySourceEnum sourceEnum) {
		return ext != null
			&& ext.getSource() != null && ext.getSource().contains(sourceEnum.getValue())
			&& StringUtils.isNotBlank(ext.getProductId());
	}

	/**
	 * Add product to monitor list if it meets the criteria and does not exist yet.
	 * If it already exists, update its status to ENABLE and set priceStatus to PENDING.
	 *
	 * @return true if record was created, false if not needed or already exists
	 */
	public boolean createOrUpdateActiveMonitorRecord(
		String storeSkuId,
		String storefrontStoreCode,
		String skuCode,
		String skuName,
		Integer busUnitId,
		Integer merchantId,
		Integer storeId,
		String userCode,
		ExternalPlatform ext,
		MonitorPlatformGroupEnum monitorPlatformGroupEnum) {

		ThirdPartySourceEnum targetSourceEnum = monitorPlatformGroupEnum.getSourceEnum();
		if (!meetMonitorCriteria(ext, targetSourceEnum)) {
			return false;
		}
		Optional<ProductPriceMonitorProductDo> priceMonitorProductOpt = monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatform(storeSkuId, busUnitId, targetSourceEnum.getValue());

		if (priceMonitorProductOpt.isPresent()) {
			if (ActiveInd.DISABLE.getValue() == priceMonitorProductOpt.get().getActiveInd()) {
				reActiveMonitorRecord(userCode, ext, priceMonitorProductOpt.get());
				log.info("update monitor product record to active, storeSkuId: {}, busUnitId: {}, targetPlatform: {}", storeSkuId, busUnitId, targetSourceEnum.getValue());
			} else {
				updateExternalPlatformDetail(userCode, ext, priceMonitorProductOpt.get());
				log.warn("create update price monitor skip, record already exists, NewExternalPlatform: {}, originExternalPlatform: {}", ext, priceMonitorProductOpt.get().toPrintExPlatformDetail());
			}
		} else {
			log.info("create price monitor record, storeSkuId: {}, busUnitId: {}, targetPlatform: {}", storeSkuId, busUnitId, targetSourceEnum.getValue());
			ProductPriceMonitorProductDo entity = buildInitEntity(storeSkuId, storefrontStoreCode, skuCode, skuName, busUnitId, merchantId, storeId, monitorPlatformGroupEnum.getBuCodeEnum().name(), targetSourceEnum.getValue(), ext.getProductId(), ext.getSkuId(), monitorPlatformGroupEnum, userCode);
			monitorProductRepository.save(entity);
		}
		return true;
	}

	private void updateExternalPlatformDetail(String userCode, ExternalPlatform ext, ProductPriceMonitorProductDo productPriceMonitorProductDo) {
		// update product code and sku code from external platform
		productPriceMonitorProductDo.setTargetProductCode(ext.getProductId());
		productPriceMonitorProductDo.setTargetSkuCode(ext.getSkuId());
		productPriceMonitorProductDo.setLastUpdatedBy(userCode);
		productPriceMonitorProductDo.setLastUpdatedDate(LocalDateTime.now());
		monitorProductRepository.save(productPriceMonitorProductDo);
	}

	/**
	 * Re-activate a monitor product record by unique key (storeSkuId, busUnitId, targetPlatform):
	 * - Sets activeInd to ENABLE (1)
	 * - Sets priceStatus to PENDING (0)
	 */
	private void reActiveMonitorRecord(String userCode, ExternalPlatform ext, ProductPriceMonitorProductDo monitorProductDo) {
		monitorProductDo.setActiveInd(ActiveInd.ENABLE.getValue());
		monitorProductDo.setPriceStatus(MonitorProductPriceStatus.PENDING.getValue());
		// update product code and sku code from external platform
		monitorProductDo.setTargetProductCode(ext.getProductId());
		monitorProductDo.setTargetSkuCode(ext.getSkuId());
		monitorProductDo.setLastUpdatedBy(userCode);
		monitorProductDo.setLastUpdatedDate(LocalDateTime.now());
		monitorProductRepository.save(monitorProductDo);
	}

	/**
	 * Disable a monitor product record by unique key (storeSkuId, busUnitId, targetPlatform):
	 * - Sets activeInd to DISABLE (0)
	 * - Sets priceStatus to PENDING (0)
	 * If not found, does nothing.
	 *
	 * @param storeSkuId     unique store SKU ID
	 * @param busUnitId      business unit ID
	 * @param targetPlatform target platform string
	 * @param userCode       operator code for audit
	 */
	public void disableMonitorRecord(String storeSkuId, Integer busUnitId, String targetPlatform, String userCode) {
		monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatformAndActiveInd(
				storeSkuId, busUnitId, targetPlatform, ActiveInd.ENABLE.getValue())
			.ifPresent(monitorProductDo -> {
				monitorProductDo.setActiveInd(ActiveInd.DISABLE.getValue());
				monitorProductDo.setPriceStatus(MonitorProductPriceStatus.PENDING.getValue());
				monitorProductDo.setLastUpdatedBy(userCode);
				monitorProductDo.setLastUpdatedDate(LocalDateTime.now());
				monitorProductRepository.save(monitorProductDo);
				log.info("disable monitor product record, storeSkuId: {}, busUnitId: {}, targetPlatform: {}", storeSkuId, busUnitId, targetPlatform);
			});
	}
}
