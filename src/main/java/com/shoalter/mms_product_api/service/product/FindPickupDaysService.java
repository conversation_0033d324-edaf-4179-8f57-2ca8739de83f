package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.PickupDaysType;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.CONSIGNMENT;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.THIRD_PARTY;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB;

import java.util.List;

@RequiredArgsConstructor
@Service
public class FindPickupDaysService {

    private final SysParmRepository sysParmRepository;

    public ResponseDto<List<SysParmDo>> start(String buCode, Integer storeId, String productReadyMethodCode) {
        List<SysParmDo> sysParmDoList =
                sysParmRepository.findPickupDaysByBuCodeAndStoreId(buCode, storeId);
        List<String> ProductReadyMethodList = List.of(CONSIGNMENT, THIRD_PARTY, STANDARD_DELIVERY_SAME_DAY_IN_HUB, HYBRID_DELIVERY_CONSOLIDATED);
        if (ProductReadyMethodList.contains(productReadyMethodCode)) {
			sysParmDoList = sysParmRepository.findBySegmentAndBuCodeAndCode("PICKUP_DAYS", null, PickupDaysType.MON_SUN);
		}
        return ResponseDto.<List<SysParmDo>>builder().data(sysParmDoList).status(1).build();
    }
}
