package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class ThirdPartyHelper {
    private final HttpRequestHelper httpRequestHelper;

    @Value("${third-party.api.url}")
    private String thirdPartyApiUrl;
    @Value("${third-party.api.sku.validate.url}")
    private String skuValidateUrl;
    private static final String SERVICE_NAME = "Third_Party";


    public SkuValidateResponseDto skuValidate(UserDto userDto, List<SkuValidateRequestDto> skuValidateRequestDto, String identifier) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<List<SkuValidateRequestDto>, SkuValidateResponseDto>builder()
                .serviceName(SERVICE_NAME)
                .url(thirdPartyApiUrl + skuValidateUrl)
                .method(HttpMethod.POST)
                .body(skuValidateRequestDto)
                .resultClass(SkuValidateResponseDto.class)
                .user(userDto)
                .systemErrorCode(ErrorMessageTypeCode.THIRD_PARTY_SKU_VALIDATE_ERROR)
                .identifier(identifier)
                .build());
    }
}
