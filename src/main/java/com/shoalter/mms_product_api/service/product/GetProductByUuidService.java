package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.mapper.ProductResponseDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class GetProductByUuidService {

    private final ProductMasterHelper productMasterHelper;
    private final MessageSource messageSource;
    private final ProductImageHelper productImageHelper;
    private final ProductRepository productRepository;
    private final SaveProductRecordRowRepository saveProductRecordRowRepository;

    public ResponseDto<ProductResponseDto> start(UserDto userDto, String uuid, Boolean hasEditStatus) {

        ProductMasterSearchRequestDto productMasterSearchRequest = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(uuid)).build();
        List<ProductMasterResultDto> productMasterResultList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequest);

        if (CollectionUtil.isEmpty(productMasterResultList)) {
            return ResponseDto.<ProductResponseDto>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message127", null, null))).build();
        }

        ProductMasterResultDto productMasterResult = productMasterResultList.get(0);
        processHktvData(uuid, hasEditStatus, productMasterResult);
        ProductResponseDto productResponse = ProductResponseDataMapper.INSTANCE.toResponseDto(productMasterResult);

        return ResponseDto.<ProductResponseDto>builder().status(1).data(productResponse).build();
    }

    private void processHktvData(String uuid, Boolean hasEditStatus, ProductMasterResultDto productMasterResult) {

        if (productMasterResult.getAdditional() == null || productMasterResult.getAdditional().getHktv() == null) {
            return;
        }

        HktvProductDto hktvProductDto = productMasterResult.getAdditional().getHktv();
        productImageHelper.convertPhotoUrl(hktvProductDto);
        String status = productRepository.findStatusByUuid(uuid);
        hktvProductDto.setStatus(status);

        if (Boolean.TRUE.equals(hasEditStatus)) {
            int editingCount = saveProductRecordRowRepository.countByUuidAndStatus(uuid);
            boolean editingStatus = editingCount > 0;
            hktvProductDto.setHasEditing(editingStatus);
        }

        if (StringUtil.isNotEmpty(hktvProductDto.getVoucherTemplateType()) && hktvProductDto.getVoucherTemplateType().length() == 1) {
            hktvProductDto.setVoucherTemplateType(ConstantType.VOUCHER_TEMPLATE_TYPE_PREFIX + hktvProductDto.getVoucherTemplateType());
        }

    }

}
