package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/***
 * HKTV 產品報告資訊
 */
@Data
public class ProductReportHktvProductInfoDto {
    private String uuid;
    private String no;
    private Integer contractNo;
    private String stores;
    private String productReadyMethod;
    private String productId;
    private String skuId;
    private List<String> productTypeCode;
    private String primaryCategoryCode;
    private String isPrimarySku;
    private String visibility;
    private String onlineStatus;
    private String skuShortDescriptionEn;
    private String skuShortDescriptionCh;
    private String skuLongDescriptionEn;
    private String skuLongDescriptionCh;
    private String rmCode;
    private Integer minimumShelfLife;
    private String virtualStore;
    private String featureStartTime;
    private String featureEndTime;
    private String voucherType;
    private String voucherDisplayType;
    private String expiryType;
    private String redeemStartDate;
    private String voucherTemplateType;
    private String fixedRedemptionDate;
    private Integer uponPurchaseDate;
    private String finePrintEn;
    private String finePrintCh;
    private String termName;
    private String currency;
    private BigDecimal cost;
    private BigDecimal originalPrice;
    private BigDecimal sellingPrice;
    private String style;
    private String discountTextEn;
    private String discountTextCh;
    private BigDecimal mallDollar;
    private BigDecimal vipMallDollar;
    private Long userMax;
    private String mainPhoto;
    private String advertisingPhoto;
    private List<String> otherProductPhoto;
    private List<String> otherPhoto;
    private String mainVideo;
    private String videoLink;
    private String videoLinkEn;
    private String videoLinkCh;
    private Integer warehouse;
    private String storageType;
    private String packingSpecEn;
    private String packingSpecCh;
    private String invoiceRemarksEn;
    private String invoiceRemarksCh;
    private Integer returnDays;
    private String productReadyDays;
    private String pickupDays;
    private String pickupTimeslot;
    private String urgent;
    private String packingBoxType;
    private String warranty;
    private String needRemovalServices;
    private String goodsType;
    private String warrantyPeriodUnit;
    private Integer warrantyPeriod;
    private String warrantySupplierEn;
    private String warrantySupplierCh;
    private String serviceCentreAddressEn;
    private String serviceCentreAddressCh;
    private String serviceCentreEmail;
    private String serviceCentreContact;
    private String warrantyRemarkEn;
    private String warrantyRemarkCh;
	private ExternalPlatform externalPlatform;
}
