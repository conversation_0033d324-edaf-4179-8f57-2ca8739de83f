package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.product.template.LittleMallProductColumnEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductFieldsOptionsContentDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductFieldsOptionsPidDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductFieldsOptionsVidDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCol;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallProductTemplateHelper extends AbstractReport {

	private final LittleMallRelationHelper littleMallRelationHelper;

	public static final String PRODUCT_SHEET_NAME = "Product Information";
	public static final int BODY_START_ROW = 3;
	public static final int EXCEL_MAXIMUM_ROW_10003 = 10003;

	public Workbook start(UserDto userDto, String storefrontStoreCode) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		addProductInfoHeaderColumn(workbook, false);
		// find store productFieldOptions
		List<ProductFieldsOptionsPidDto> productFieldOptions = littleMallRelationHelper.getProductFieldOptionsByStore(userDto, storefrontStoreCode);
		addLovWithCustomLovSheet(productFieldOptions, workbook);
		return workbook;
	}

	public Workbook downloadLittleMallErrorReport(UserDto userDto, String storefrontStoreCode, boolean isEditErrorReport, List<SingleEditProductDto> singleEditProductDtoList) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		addProductInfoHeaderColumn(workbook, isEditErrorReport);
		// find store productFieldOptions
		List<ProductFieldsOptionsPidDto> productFieldOptions = littleMallRelationHelper.getProductFieldOptionsByStore(userDto, storefrontStoreCode);
		addLovWithCustomLovSheet(productFieldOptions, workbook);
		addErrorColumn(workbook, LittleMallProductColumnEnum.values().length, true, true);
		addLittleMallErrorBodyColumn(workbook, singleEditProductDtoList, productFieldOptions);
		return workbook;
	}


	protected void addLovWithCustomLovSheet(List<ProductFieldsOptionsPidDto> productFieldOptions, Workbook workbook) {
		Sheet productSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		Sheet lovSheet = workbook.createSheet(AbstractReport.LOV_SHEET_NAME);
		Map<String, Map<String, Object>> lovMap = new HashMap<>();
		Num theColNum = new Num(0);

		List<String> headers = new ArrayList<>(List.of(
			LittleMallProductColumnEnum.VISIBILITY.getColNameEnglish(),
			LittleMallProductColumnEnum.PRODUCT_READY_METHOD.getColNameEnglish()
		));

		setLovSheetHeaders(workbook, lovSheet, headers);
		setLovSheetValues(lovSheet, lovMap, theColNum);
		// 自定義的lov選單與header名稱跟選單的內容
		Map<String, List<String>> customDataMap = generateCustomDataMap(productFieldOptions);
		if (customDataMap.isEmpty()) {
			log.warn("customDataMap is empty");
			throw new NoDataException();
		}
		setCustomLovData(workbook, lovSheet, lovMap, theColNum, customDataMap);
		setProductInfoWithCustomValidations(productSheet, lovMap);
	}

	private Map<String, List<String>> generateCustomDataMap(List<ProductFieldsOptionsPidDto> productFieldOptions) {
		Map<String, List<String>> customDataMap = new LinkedHashMap<>();
		if (CollectionUtil.isEmpty(productFieldOptions)) {
			return customDataMap;
		}

		for (ProductFieldsOptionsPidDto productFieldOption : productFieldOptions) {
			if (CollectionUtil.isEmpty(productFieldOption.getContents())) {
				continue;
			}
			for (ProductFieldsOptionsContentDto content : productFieldOption.getContents()) {
				List<String> fieldOptions = new ArrayList<>();
				String fieldKey = "";

				// pid_sid : pidName+sidName
				if (ObjectUtils.isNotEmpty(content.getSidContent())) {
					fieldKey = String.format(ConstantType.EXCEL_VALIDATION_CATEGORY_FORMAT,
						productFieldOption.getPid(), content.getSidContent().getSid(),
						productFieldOption.getNameZh(), content.getSidContent().getNameZh());
					// pid : pidName
				} else {
					fieldKey = String.format(ConstantType.EXCEL_VALIDATION_FORMAT, productFieldOption.getPid(), productFieldOption.getNameZh());
				}

				for (ProductFieldsOptionsVidDto vidOption : content.getVids()) {
					String optionValue = String.format(ConstantType.EXCEL_VALIDATION_FORMAT, vidOption.getVid(), vidOption.getNameZh());
					fieldOptions.add(optionValue);
				}
				customDataMap.computeIfAbsent(fieldKey, k -> new ArrayList<>()).addAll(fieldOptions);
			}
		}
		return customDataMap;
	}

	private void addProductInfoHeaderColumn(XSSFWorkbook workbook, boolean isEditErrorReport) {
		XSSFSheet productSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(true);
		CellStyle instructionsStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		instructionsStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		instructionsStyle.setLocked(true);
		CellStyle notLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, false);
		notLockStyle.setLocked(false);

		// init style all cells are not locked
		CTCol col = productSheet.getCTWorksheet().getColsArray(0).addNewCol();
		col.setMin(1);
		col.setMax(16384);
		col.setWidth(9.15);
		col.setStyle(notLockStyle.getIndex());

		Font redFont = ExcelUtil.getRedFont(workbook);

		int colNum = 0;
		Row englishNameRow = CellUtil.getRow(0, productSheet);
		Row chineseNameRow = CellUtil.getRow(1, productSheet);
		Row instructionsRow = CellUtil.getRow(2, productSheet);
		englishNameRow.setHeight((short) (30 * 20));
		chineseNameRow.setHeight((short) (30 * 20));
		instructionsRow.setHeight((short) (120 * 20));

		for (LittleMallProductColumnEnum columnEnum : LittleMallProductColumnEnum.values()) {
			if (isEditErrorReport && isEditHiddenColumn(columnEnum)) {
				productSheet.setColumnWidth(columnEnum.getColumnNumber(), 0);
			} else {
				productSheet.setColumnWidth(columnEnum.getColumnNumber(), (short) (columnEnum.getWeight() * 256 + 184));
			}

			Cell englishNameCell = CellUtil.getCell(englishNameRow, colNum);
			Cell chineseNameCell = CellUtil.getCell(chineseNameRow, colNum);
			Cell instructionsCell = CellUtil.getCell(instructionsRow, colNum);
			instructionsCell.setCellValue(columnEnum.getInstructions());
			if (columnEnum.isRequired()) {
				setLastTextRed(chineseNameCell, redFont, columnEnum.getColNameChinese());
				setLastTextRed(englishNameCell, redFont, columnEnum.getColNameEnglish());
			} else {
				englishNameCell.setCellValue(columnEnum.getColNameEnglish());
				chineseNameCell.setCellValue(columnEnum.getColNameChinese());
			}
			// lock header cell
			englishNameCell.setCellStyle(headerStyle);
			chineseNameCell.setCellStyle(headerStyle);
			instructionsCell.setCellStyle(instructionsStyle);

			colNum++;
		}
		productSheet.protectSheet(LOV_PASSWORD);
	}


	protected void setLovSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> lovMap, Num theColNum) {
		//开始设置Lov的Value信息
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_YES_NO, ExcelUtil.YN_LIST, theColNum, lovMap);

		List<SysParmDo> productReadyMethodList = sysParmRepository.findBySegmentAndBuCodeAndCodeList(SysParmSegmentEnum.PRODUCT_READY_METHOD.name(), BuCodeEnum.LITTLE_MALL.name(), ProductReadyMethodType.LITTLE_MALL_PRODUCT_READY_METHOD);
		List<String> validationProductReadyMethodList = productReadyMethodList.stream()
			.map(SysParmUtil::getCodeAndShortDescString)
			.collect(Collectors.toList());
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRO_READY_METHOD, validationProductReadyMethodList, theColNum, lovMap);

		//sheetLov中的value值设置完成后，加密保护
		lovSheet.protectSheet(LOV_PASSWORD);
	}

	private void setProductInfoWithCustomValidations(Sheet dataSheet, Map<String, Map<String, Object>> lovMap) {
		for (LittleMallProductColumnEnum columnEnum : LittleMallProductColumnEnum.values()) {
			if (LittleMallProductColumnEnum.IS_PRIMARY_SKU_N_LOCK_COLUMN.contains(columnEnum)) {
				checkIsPrimarySkuLockColumnValidation(dataSheet, columnEnum, lovMap);
			} else if (null != columnEnum.getValidationName() && Boolean.FALSE.equals(columnEnum.isCustomGroupOption())) {
				setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), lovMap.get(columnEnum.getValidationName()), BODY_START_ROW);//设置一级菜单
			} else if (null != columnEnum.getValidationName() && Boolean.TRUE.equals(columnEnum.isCustomGroupOption()) && lovMap.get(columnEnum.getValidationName()) != null) {
				setCustomXSSFValidation(dataSheet, columnEnum.getColumnNumber(), lovMap.get(columnEnum.getValidationName()), BODY_START_ROW);//设置Custom一级菜单
			} else if (Boolean.TRUE.equals(columnEnum.isCustomGroupOption()) && null != columnEnum.getParent() && lovMap.get(columnEnum.getParentSegment()) != null) {
				setCustomOptionXSSFNameValidation(dataSheet, BODY_START_ROW, columnEnum.getParent().getColumnNumber(), columnEnum.getColumnNumber()); //设置Custom二级菜单
			}
		}
	}

	private void checkIsPrimarySkuLockColumnValidation(Sheet dataSheet, LittleMallProductColumnEnum columnEnum, Map<String, Map<String, Object>> map) {
		int dependColumnNum = LittleMallProductColumnEnum.IS_PRIMARY_SKU.getColumnNumber();
		switch (columnEnum) {
			case PRODUCT_READY_METHOD:
				setCombinedOptionAndDependLockValidation(dataSheet, BODY_START_ROW, EXCEL_MAXIMUM_ROW_10003, dependColumnNum, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));
				break;
			case SKU_NAME_CHI:
			case DISPLAY_IN_HKTVMALL_CATEGORY:
			case SKU_LONG_DESCRIPTION_CHI:
			case OTHER_PHOTO:
				setDependLockColumnValidation(dataSheet, BODY_START_ROW, EXCEL_MAXIMUM_ROW_10003, dependColumnNum, columnEnum.getColumnNumber());
				break;
			default:
				break;
		}
	}

	public void addLittleMallErrorBodyColumn(Workbook workbook, List<SingleEditProductDto> singleEditProductDtoList, List<ProductFieldsOptionsPidDto> productFieldOptions) {
		Sheet productSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle lockStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false, false, true);
		lockStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		lockStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

		AtomicInteger rowIndex = new AtomicInteger(BODY_START_ROW);
		Map<Integer, ProductFieldsOptionsPidDto> productFieldOptionsMap = productFieldOptions.stream().collect(Collectors.toMap(ProductFieldsOptionsPidDto::getPid, productFieldOptionsPidDto -> productFieldOptionsPidDto));

		singleEditProductDtoList.forEach(singleEditProductDto -> {
			if (singleEditProductDto == null ||
				singleEditProductDto.getProduct() == null ||
				singleEditProductDto.getProduct().getAdditional() == null) {
				return;
			}
			ProductMasterDto product = singleEditProductDto.getProduct();
			LittleMallProductDto littleMallProduct = product.getAdditional().getLittleMall();
			// convert product field and option ID to description
			Pair<String, String> fieldAndOptionDescription1 = littleMallRelationHelper.processFieldOptionToExcelDescription(littleMallProduct.getProductFieldCategory1(), littleMallProduct.getProductFieldOption1(), productFieldOptionsMap.get(littleMallProduct.getProductField1()));
			Pair<String, String> fieldAndOptionDescription2 = littleMallRelationHelper.processFieldOptionToExcelDescription(littleMallProduct.getProductFieldCategory2(), littleMallProduct.getProductFieldOption2(), productFieldOptionsMap.get(littleMallProduct.getProductField2()));
			Pair<String, String> fieldAndOptionDescription3 = littleMallRelationHelper.processFieldOptionToExcelDescription(littleMallProduct.getProductFieldCategory3(), littleMallProduct.getProductFieldOption3(), productFieldOptionsMap.get(littleMallProduct.getProductField3()));
			Pair<String, String> fieldAndOptionDescription4 = littleMallRelationHelper.processFieldOptionToExcelDescription(littleMallProduct.getProductFieldCategory4(), littleMallProduct.getProductFieldOption4(), productFieldOptionsMap.get(littleMallProduct.getProductField4()));

			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_ID.getColumnNumber(), product.getSkuId());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_ID.getColumnNumber(), product.getProductId());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_READY_METHOD.getColumnNumber(), littleMallProduct.getProductReadyMethod());
			setCellBooleanValueToYN(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.IS_PRIMARY_SKU.getColumnNumber(), littleMallProduct.getIsPrimarySku(), ExcelUtil.YES_NO_LIST);
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_NAME_CHI.getColumnNumber(), product.getSkuNameCh());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.ORIGINAL_PRICE.getColumnNumber(), product.getOriginalPrice());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.SELLING_PRICE.getColumnNumber(), littleMallProduct.getSellingPrice());
			setCellBooleanValueToYN(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.VISIBILITY.getColumnNumber(), littleMallProduct.getVisibility(), ExcelUtil.YES_NO_LIST);
			setCellBooleanValueToYN(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.ONLINE_STATUS.getColumnNumber(), littleMallProduct.getOnlineStatus(), ExcelUtil.YES_NO_LIST);
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.DISPLAY_IN_HKTVMALL_CATEGORY.getColumnNumber(), littleMallProduct.getDisplayInHktvmallCategory());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_LONG_DESCRIPTION_CHI.getColumnNumber(), littleMallProduct.getSkuLongDescriptionCh());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.MAIN_PHOTO.getColumnNumber(), littleMallProduct.getMainPhoto());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.OTHER_PHOTO.getColumnNumber(), littleMallProduct.getOtherPhoto());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_1.getColumnNumber(), fieldAndOptionDescription1.getLeft());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_1.getColumnNumber(), fieldAndOptionDescription1.getRight());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_2.getColumnNumber(), fieldAndOptionDescription2.getLeft());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_2.getColumnNumber(), fieldAndOptionDescription2.getRight());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_3.getColumnNumber(), fieldAndOptionDescription3.getLeft());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_3.getColumnNumber(), fieldAndOptionDescription3.getRight());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_4.getColumnNumber(), fieldAndOptionDescription4.getLeft());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_4.getColumnNumber(), fieldAndOptionDescription4.getRight());
			setCellValue(productSheet, lockStyle, rowIndex.get(), LittleMallProductColumnEnum.values().length, singleEditProductDto.getErrorMessage());

			rowIndex.addAndGet(1);
		});
	}

	private boolean isEditHiddenColumn(LittleMallProductColumnEnum columnEnum) {
		switch (columnEnum) {
			case MAIN_PHOTO:
			case OTHER_PHOTO:
			case SKU_LONG_DESCRIPTION_CHI:
				return true;
			default:
				return false;
		}
	}

	protected String getFileName() {
		return "LittleMallProduct";
	}

	protected void setLastTextRed(Cell cell, Font redFont, String text) {
		int length = text.length();
		XSSFRichTextString richTextEnglish = new XSSFRichTextString(text);
		richTextEnglish.applyFont(length - 1, length, redFont);
		cell.setCellValue(richTextEnglish);
	}


}
