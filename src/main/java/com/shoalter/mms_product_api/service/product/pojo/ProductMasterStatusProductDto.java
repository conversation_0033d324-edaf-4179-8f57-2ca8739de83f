package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class ProductMasterStatusProductDto implements Serializable {
    @JsonProperty("uuid")
    private String uuid;

    @JsonProperty("record_row_id")
	@SerializedName("record_row_id")
    private Long recordRowId;

    @JsonProperty("hktv")
    private List<ProductMasterStatusProductBuDto> hktv;

    @JsonProperty("3pl")
	@SerializedName("3pl")
    private List<ProductMasterStatusProductBuDto> thirdPartyLogistics;

    @JsonProperty("little_mall")
	@SerializedName("little_mall")
	private List<ProductMasterStatusProductBuDto> littleMall;

	@JsonProperty("mix")
	private List<ProductMasterStatusProductBuDto> mix;

	@JsonProperty("iids")
	private List<ProductMasterStatusProductBuDto> iids;
}
