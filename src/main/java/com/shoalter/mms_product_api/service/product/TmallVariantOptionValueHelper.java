package com.shoalter.mms_product_api.service.product;

import static com.shoalter.mms_product_api.config.product.TmallExportInfoSyncProgressEnum.COMPLETE;
import static com.shoalter.mms_product_api.config.product.TmallExportInfoSyncProgressEnum.FAILED;
import static com.shoalter.mms_product_api.config.product.TmallExportInfoSyncProgressEnum.PENDING;
import static com.shoalter.mms_product_api.config.product.TmallExportInfoSyncProgressEnum.PROCESSING_VARIANT;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.properties.TmallProperties;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallExportInfoProjection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TmallVariantOptionValueHelper {

	private final TmallProperties tmallProperties;
	private final BuExportHistoryRepository buExportHistoryRepository;
	private final Gson gson;

	public List<Integer> getTmallExportHistoryIdForProcessing() {

		int processingVariantLimit = tmallProperties.getCronjob().getProcessingVariantLimit();

		return new ArrayList<>(
				buExportHistoryRepository.findTmallProductDataByInformProductToSyncProgressAndLimit(
						PENDING.toString(),
						processingVariantLimit)
		);
	}

	public int getRemainingProcessingVariantQuota() {

		int processingVariantLimit = tmallProperties.getCronjob().getProcessingVariantLimit();
		List<TmallExportInfoProjection> tmallExportInfoProjections =
			buExportHistoryRepository.countByInformProductToSyncProgress(PROCESSING_VARIANT.toString());

		if (CollectionUtils.isNotEmpty(tmallExportInfoProjections)) {
			List<Integer> tmallExportInfoIds = tmallExportInfoProjections.stream()
				.map(TmallExportInfoProjection::getId).collect(Collectors.toList());
			log.info("[getRemainingProcessingVariantQuota] Tmall export info in progress (size={}), IDs: {}",
				tmallExportInfoProjections.size(), tmallExportInfoIds);
		}

		return Math.max(0, processingVariantLimit - tmallExportInfoProjections.size());
	}

	public void markTmallExportHistoryAsCompleteById(Integer id) {
		log.info("markTmallExportHistoryAsCompleteById id: {}", id);
		buExportHistoryRepository.updateInformProductToSyncProgressByIds(COMPLETE.toString(), Set.of(id));
	}

	public void markTmallExportHistoryAsProcessingByIds(Collection<Integer> ids) {
		log.info("markTmallExportHistoryAsProcessingById ids: {}", ids);
		buExportHistoryRepository.updateInformProductToSyncProgressByIds(PROCESSING_VARIANT.toString(),
			ids);
	}

	public void markTmallExportHistoryAsFailedById(Integer id) {
		log.info("markTmallExportHistoryAsFailedById ids: {}", id);
		buExportHistoryRepository.updateInformProductToSyncProgressByIds(FAILED.toString(),Set.of(id));
	}
}
