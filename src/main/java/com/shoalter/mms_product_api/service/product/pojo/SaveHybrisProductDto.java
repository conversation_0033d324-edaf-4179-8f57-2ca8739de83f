package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class SaveHybrisProductDto extends SaveHybrisProductRequestDto implements Serializable {

    //hybris
    private String mainImages;
    private String productImages;
    private String otherImages;
    private String pickupTimeSlot;
    private String pickupDays;
    private String penalty;
    private String rmCode;
    private String buyMax;
    private String commissionRate;
    private String mallDollar;
    private String mallDollarVip;
    private String originalPrice;
    private String originalMainlandPrice;
    private String sellingPrice;
    private String size;
    private String sizeSystem;
    private String userMax;
    private String fixedDeliveryDate; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java似乎也沒用到了
	// true : hybris will sync primary/variant variant_product_photo with same images (field : productImages)
    @SerializedName("replicateImage")
    @JsonProperty("replicateImage")
    private String replicateOtherVariants;
    private String offlineDate;
    private String redeemStartDate;
    private String redeemEndDate;
    private String brandCategoryId;
    private String brandId;
    private String brandEn;
    private String brandZh;
    private String brandZhCN;
    private String hktvMallId;
    private String merchantId;
    private String priority;
    @SerializedName("readyPickupDays")
    @JsonProperty("readyPickupDays")
    private String productReadyDays;
    private String uponPurchaseDate;
    private String warehouseId;
    private String warehouseSeqId; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java似乎也沒用到了
    private String barcode;// 在 mms RestfulWebserviceImpl.java找不到
    private String buStatus; //沒用到
    private String colorCh;
    private String colorEn;
    private String colorZhCN;
    private String colorFamiliar;
    private String consumable; //沒用到，在 mms RestfulWebserviceImpl.java找不到
    private String currencyCode;
    private String deliveryDetailsCh;
    private String deliveryDetailsEn;
    private String deliveryMethod;
    private String deliveryTitleCh;
    private String deliveryTitleEn;
    private String expiryType;
    private String featureEndTime;
    private String featureStartTime;
    private String finePrintCh;
    private String finePrintEn;
    private String finePrintZhCN;
    private String finePrintTitleCh; // 沒用到，在 mms RestfulWebserviceImpl.java找不到
    private String finePrintTitleEn; // 沒用到，在 mms RestfulWebserviceImpl.java找不到
    private String fixedDeliveryTimeslot; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String flashSale; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String forceOutOfStock; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String invisibleFlag;
    private String invoiceRemarksCh;
    private String invoiceRemarksEn;
    private String invoiceRemarksZhCN;
    private String isPrimarySku;
    private String manuCountryCh;
    private String manuCountryEn;
    private String manuCountryZhCN;
    private String packBoxType;
    private String packDepth;
    private String packDimensionUnit;
    private String packHeight;
    private String packLength;
    private String packSpecCh;
    private String packSpecEn;
    private String packSpecZhCN;
    private String paymentTerms;
    private String productCode;
    private String productReadyMethod;
    private String recomSellingPrice; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String recomSellingPriceEn; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String redeemAddress;
    private String redeemBusName;
    private String redeemId;
    private String redeemLatitude;
    private String redeemLocCity;
    private String redeemLongitude;
    private String redeemType;
    private String remarks;
    private String rsp; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String rspFontBakColor; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String rspFontColor; //在線上hktv mall product entity沒找到相關參數，在 mms RestfulWebserviceImpl.java找不到
    private String skuCode;
    private String skuLDescHktvCh;
    private String skuLDescHktvEn;
    private String skuLDescHktvZhCN;
    private String skuLTitleHktvCh;
    private String skuLTitleHktvEn;
    private String skuName;
    private String skuNameTchi;
    private String skuNameZhCN;
    private String skuSDescHktvCh;
    private String skuSDescHktvEn;
    private String skuSDescHktvZhCN;
    private String skuSTitleHktvCh;
    private String skuSTitleHktvEn;
    private String storageFee;
    private String status;
    private String storageType;
    private String urgent;
    @SerializedName("videoLink")
    @JsonProperty("videoLink")
    private String videoEntry;
    private String videoLinkCh;
    private String videoLinkEn;
    private String videoLinkZhCN;
	private String videoLinkList;
    private String voucherDisplayType;
    private String voucherTemplateType; // wsapi.json 沒有
    private String voucherType;
    private String website;
    private String weight;
    private String weightUnit;
    private String productHktvCatList;
    private String primaryHktvCatId;
    private String returnDays;
    private String action;
    @SerializedName("weeeOption")
    @JsonProperty("weeeOption")
    private String removalServices;
    private String cost;
    @SerializedName("option1CatCode")
    @JsonProperty("option1CatCode")
    private String field1;
    @SerializedName("option1CatValue")
    @JsonProperty("option1CatValue")
    private String value1;
    @SerializedName("option2CatCode")
    @JsonProperty("option2CatCode")
    private String field2;
    @SerializedName("option2CatValue")
    @JsonProperty("option2CatValue")
    private String value2;
    @SerializedName("option3CatCode")
    @JsonProperty("option3CatCode")
    private String field3;
    @SerializedName("option3CatValue")
    @JsonProperty("option3CatValue")
    private String value3;
    private String goodsType;
    private String warrantyPeriodUnit;
    private String warrantyPeriod;
    @SerializedName("warrantySupplierZh")
    @JsonProperty("warrantySupplierZh")
    private String warrantySupplierCh;
    private String warrantySupplierEn;
    private String warrantySupplierZhCN;
    @SerializedName("serviceCentreAddressZh")
    @JsonProperty("serviceCentreAddressZh")
    private String serviceCentreAddressCh;
    private String serviceCentreAddressEn;
    private String serviceCentreAddressZhCN;
    private String serviceCentreEmail;
    private String serviceCentreContact;
    @SerializedName("warrantyRemarkZh")
    @JsonProperty("warrantyRemarkZh")
    private String warrantyRemarkCh;
    private String warrantyRemarkEn;
    private String warrantyRemarkZhCN;
    private String shelfLife; // wsapi.json 沒有
    private String virtualStore;
    private String presellFruit;
    private String physicalStore;
    private String enableWarehouseFunction;
    private String exchangeRate;
    private String isEcoupon;
    private String advertisingImages;
    private String thumbnailVideo;
    private String mainVideo;
	private List<String> deliverableRegionCodes;

    //TODO check parameter
    private String colorEnCode;
    private String colorChCode;
    private String sizeSystemCode;
    private String sizeCode;
    private String colorFamiliarCode;
    private String value1Code;
    private String value2Code;
    private String value3Code;

    // mms有這裡沒有
    private String onlineDate; // 看mms code似乎也沒用到了
    private String optionDisplayOrder; // 在 mms RestfulWebserviceImpl.java找不到
    private String skuLDescHkbCh;
    private String skuLDescHkbEn;
    private String skuLTitleHkbCh;
    private String skuLTitleHkbEn;
    private String skuSDescHkbCh;
    private String skuSDescHkbEn;
    private String skuSTitleHkbCh;
    private String skuSTitleHkbEn;
    private String productHkbCatList;
    private String primaryHkbCatId;
    private String externalAffiliateUrl;

	private String pricePercentage;
	private String claimLinkEn;
	private String claimLinkZh;
	private String claimLinkZhCN;

	private Long buyerId;
	private List<String> salesChannel;
}
