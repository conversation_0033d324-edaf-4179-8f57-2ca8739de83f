package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.BatchImportSourceEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallCheckRelationErrorDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.BatchLittleMallProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallBatchCheckRelationResponseDto;
import com.shoalter.mms_product_api.service.shopline.ProductBatchImportHelper;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallBatchSaveService {

	private final SaveProductRecordRepository saveProductRecordRepository;

	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductBatchImportHelper productBatchImportHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final LittleMallBatchCheckRelationService littleMallBatchCheckRelationService;

	private final MessageSource messageSource;
	private final Gson gson;


	public ResponseDto<Long> start(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		if (CollectionUtil.isEmpty(batchLittleMallProductRequestDto.getLittleMallBatchDtoList())) {
			throw new NoDataException();
		}

		long startTime = System.currentTimeMillis();

		ResponseDto<Long> result;
		if (BatchImportSourceEnum.SHOPLINE.name().equals(batchLittleMallProductRequestDto.getSource())) {
			result = createShoplineImportRecord(userDto, batchLittleMallProductRequestDto);
		} else if (BatchImportSourceEnum.EXCEL.name().equals(batchLittleMallProductRequestDto.getSource())) {
			result = createExcelImportRecord(userDto, batchLittleMallProductRequestDto);
		} else {
			result = createNormalImportRecord(userDto, batchLittleMallProductRequestDto);
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by LittleMallBatchSaveService.start: {} milliseconds", endTime - startTime);

		return result;
	}

	private ResponseDto<Long> createNormalImportRecord(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {

		if (batchLittleMallProductRequestDto.getLittleMallBatchDtoList().size() > BatchCheckHelper.MAXIMUM_10000) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.MAXIMUM_10000)}, null)));
		}

		List<String> errorMessages = checkDuplicatedAndRelation(userDto, batchLittleMallProductRequestDto);
		if (CollectionUtil.isNotEmpty(errorMessages)) {
			return ResponseDto.fail(errorMessages);
		}

		Map<String, LittleMallRelationDto> relationProductIdMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(batchLittleMallProductRequestDto.getRelations())){
			for (LittleMallRelationDto relation : batchLittleMallProductRequestDto.getRelations()) {
				relationProductIdMap.putIfAbsent(relation.getProductId(), relation);
			}
		}
		List<SingleEditProductDto> singleEditProductDtoList =
			batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream()
				.map(littleMall -> LittleMallBatchDto.convertToSingleEditProductDtoWithRelation(littleMall, relationProductIdMap))
				.collect(Collectors.toList());

		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto, batchLittleMallProductRequestDto.getMerchantId(),
			SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT, batchLittleMallProductRequestDto.getFileName(), SaveProductStatus.WAIT_START, ClientIpHolder.getClientIp());
		List<SaveProductRecordRowDo> rowList = singleEditProductDtoList.stream()
			.map(product -> createSaveProductRecordRowDo(saveProductRecordDo.getId(), product, Set.of()))
			.collect(Collectors.toList());

		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);
		updateRecordStatusToChecking(saveProductRecordDo);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), rowList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.<Long>builder().data(saveProductRecordDo.getId()).status(1).build();
	}

	private List<String> checkDuplicatedAndRelation(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		List<String> errorMessages = new ArrayList<>();
		List<LittleMallBatchDto> products = batchLittleMallProductRequestDto.getLittleMallBatchDtoList();
		errorMessages.add(checkLittleMallProductHelper.checkPrimarySkuDuplicated(products));
		errorMessages.add(checkLittleMallProductHelper.checkSkuIdDuplicated(products));

		// check relation
		ResponseDto<LittleMallBatchCheckRelationResponseDto> result = littleMallBatchCheckRelationService.start(userDto, batchLittleMallProductRequestDto);
		if (result.getStatus() == StatusCodeEnum.FAIL.getCode() && result.getData() != null) {
			log.info("check relations and skus result error: {}", result.getData());
			if (CollectionUtil.isNotEmpty(result.getData().getRelationErrors())) {
				Set<String> stores = result.getData().getRelationErrors().stream().map(LittleMallCheckRelationErrorDto::getStorefrontStoreCode).collect(Collectors.toSet());
				Set<String> productIds = result.getData().getRelationErrors().stream().map(LittleMallCheckRelationErrorDto::getProductId).collect(Collectors.toSet());
				log.info("check relations error, storefrontStoreCode: {}, productId: {}", stores, productIds);
			}
			if (CollectionUtil.isNotEmpty(result.getData().getCheckIndexErrors())) {
				Set<String> skuIds = result.getData().getCheckIndexErrors().stream()
					.map(error -> {
						if (error.getIndex() >= 0 && error.getIndex() < products.size()) {
							LittleMallBatchDto product = products.get(error.getIndex());
							return product != null ? product.getSkuId() : null;
						}
						return null;
					})
					.filter(Objects::nonNull)
					.collect(Collectors.toSet());
				log.info("check sku error, skuIds: {}", skuIds);
			}
			errorMessages.add(messageSource.getMessage("message353", null, null));
		}
		return errorMessages.stream().filter(Objects::nonNull).collect(Collectors.toList());
	}

	private ResponseDto<Long> createShoplineImportRecord(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		if (!RoleCode.ALLOW_PRODUCT_IMPORT_FROM_SHOPLINE_ROLES.contains(userDto.getRoleCode())) {
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}

		List<SingleEditProductDto> singleEditProductDtoList =
			batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream()
				.map(littleMallBatchDto -> {
					littleMallBatchDto.setProductReadyMethod(ProductReadyMethodType.DEFAULT_LITTLE_MALL_PRODUCT_READY_METHOD);
					return LittleMallBatchDto.convertToSingleEditProductDto(littleMallBatchDto);
				})
				.collect(Collectors.toList());

		ResponseDto<Set<String>> productMasterResponse = productBatchImportHelper.checkLittleMallSkuIdFromProductMaster(userDto, batchLittleMallProductRequestDto);
		if (StatusCodeEnum.FAIL.getCode() == productMasterResponse.getStatus()) {
			return ResponseDto.fail(productMasterResponse.getErrorMessageList());
		}

		Set<String> existSkuIds = productMasterResponse.getData();
		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto, batchLittleMallProductRequestDto.getMerchantId(),
			SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE, batchLittleMallProductRequestDto.getFileName(), SaveProductStatus.USER_INVISIBLE_WAIT_START, ClientIpHolder.getClientIp());
		List<SaveProductRecordRowDo> rowList = singleEditProductDtoList.stream()
			.map(product -> createSaveProductRecordRowDo(saveProductRecordDo.getId(), product, existSkuIds))
			.collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), rowList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.success(saveProductRecordDo.getId());
	}

	private ResponseDto<Long> createExcelImportRecord(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		List<SingleEditProductDto> singleEditProductDtoList =
			batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream()
				.map(LittleMallBatchDto::convertToSingleEditProductDto)
				.collect(Collectors.toList());

		//check data length
		if (CollectionUtil.isEmpty(singleEditProductDtoList)) {
			throw new SystemException("No data.");
		} else if (singleEditProductDtoList.size() > BatchCheckHelper.MAXIMUM_10000) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.MAXIMUM_10000)}, null)));
		}

		//check repeated sku id
		ResponseDto<Set<String>> productMasterResponse = productBatchImportHelper.checkLittleMallSkuIdFromProductMaster(userDto, batchLittleMallProductRequestDto);
		if (StatusCodeEnum.FAIL.getCode() == productMasterResponse.getStatus()) {
			return ResponseDto.fail(productMasterResponse.getErrorMessageList());
		}

		//save record
		Set<String> existSkuIds = productMasterResponse.getData();
		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto, batchLittleMallProductRequestDto.getMerchantId(),
			SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL, batchLittleMallProductRequestDto.getFileName(), SaveProductStatus.WAIT_START, ClientIpHolder.getClientIp());
		List<SaveProductRecordRowDo> rowList = singleEditProductDtoList.stream()
			.map(product -> createSaveProductRecordRowDo(saveProductRecordDo.getId(), product, existSkuIds))
			.collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);
		updateRecordStatusToChecking(saveProductRecordDo);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), rowList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.success(saveProductRecordDo.getId());
	}

	private SaveProductRecordRowDo createSaveProductRecordRowDo(Long recordId, SingleEditProductDto productRequestDto, Set<String> existSkuIds) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(productRequestDto.getProduct().getSkuId() == null ? StringUtils.EMPTY : productRequestDto.getProduct().getSkuId());
		//if sku id repeated, save error record row and process other non-repeated sku
		String skuId = productRequestDto.getProduct().getSkuId();
		if (existSkuIds.contains(skuId)) {
			saveProductRecordRowDo.setStatus(SaveProductStatus.FAIL);
			saveProductRecordRowDo.setErrorMessage(messageSource.getMessage("message66", new String[]{skuId}, null));
		} else {
			saveProductRecordRowDo.setStatus(SaveProductStatus.CHECKING_PRODUCT);
		}
		saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
		return saveProductRecordRowDo;
	}

	private void updateRecordStatusToChecking(SaveProductRecordDo record) {
		record.setStatus(SaveProductStatus.CHECKING_PRODUCT);
		saveProductRecordRepository.save(record);
	}
}
