package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.template.HktvUploadProductExcelReportEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BusinessMapper;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.dao.mapper.store.StoreWarehouseMapper;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameEnViewDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.ITemplateService;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductUploadExcelDataDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.StoreWarehouseIdViewDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.type.ContractType.MAINLAND_MERCHANT_CONTRACT_SET;
import static com.shoalter.mms_product_api.util.ExcelUtil.EXPIRY_TYPE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.GOODS_TYPE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.OLD_YES_NO_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.PACK_DIMENSION_UNIT_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.PRODUCT_STATUS_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.RETURN_DAYS_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.STYLE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.WARRANTY_PERIOD_UNIT_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.WEIGHT_UNIT_LIST;
import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductUploadExcelHelper extends AbstractReport implements ITemplateService {

    private static final String PRODUCT_SHEET_NAME = "Product Template";

    private final StoreWarehouseRepository storeWarehouseRepository;
    private final BrandRepository brandRepository;

    private final BusinessMapper businessMapper;
    private final StoreWarehouseMapper storeWarehouseMapper;
    private final ProductMapper productMapper;
	private final MerchantStoreRepository merchantStoreRepository;

    public Workbook start(UserDto userDto, Integer storeId, Integer contractId, List<String> uuidList, List<SingleEditProductDto> productList, List<Integer> brandIdList, boolean isErrorReport) {
        long startTime = System.currentTimeMillis();
        boolean isEditReport = CollectionUtil.isNotEmpty(uuidList);
        Workbook workbook = generateTemplate(userDto, isEditReport, isErrorReport, storeId, contractId, uuidList, brandIdList, productList);
        long endTime = System.currentTimeMillis();
        log.info("Time taken by generate hktv upload template {} milliseconds", (endTime - startTime));
        return workbook;
    }

    private Workbook generateTemplate(UserDto userDto, boolean isEditReport, boolean isErrorReport, Integer storeId, Integer contractId, List<String> uuidList, List<Integer> brandIdList, List<SingleEditProductDto> productList) {
        Workbook workbook = new XSSFWorkbook();
        List<SysParmDo> sysParmList = searchBySegments(SEGMENT_PARENT_LIST);

        long startTime = System.currentTimeMillis();
        addDefaultStyle(workbook);
        setHeaderColumn(userDto, workbook, isEditReport, isErrorReport);
        addLovSheet(workbook, isEditReport, storeId, contractId, brandIdList, sysParmList);
        long endTime = System.currentTimeMillis();
        log.info("Time taken Generate Excel {} milliseconds", (endTime - startTime));
        workbook = setBodyColumn(workbook, isEditReport, uuidList, productList, sysParmList, isErrorReport);

        return workbook;
    }

    private void setHeaderColumn(UserDto userDto, Workbook workbook, boolean isEditReport, boolean isErrorReport) {
        Sheet dataSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
        CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
        headerStyle.setLocked(false);
        CellStyle lockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);

        int rowNum = 0;
        int colNum = 0;
        Row row = CellUtil.getRow(rowNum, dataSheet);
        row.setHeight((short) (30 * 20));

		boolean isMerchantRole = RoleCode.MERCHANT.equals(userDto.getRoleCode()) || RoleCode.MERCHANT_ADMIN.equals(userDto.getRoleCode());

		for (HktvUploadProductExcelReportEnum columnEnum : HktvUploadProductExcelReportEnum.values()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());

			int columnWidth = getColumnWidth(columnEnum, isMerchantRole, isEditReport);
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), columnWidth);

			boolean isLock = isLockColumn(columnEnum, isMerchantRole, isEditReport);
			if (isLock) {
				cell.setCellStyle(lockStyle);
			} else {
				cell.setCellStyle(headerStyle);
			}

			colNum++;
        }

        if (isEditReport) {
            dataSheet.protectSheet(LOV_PASSWORD);
        }

        if(isErrorReport){
            Cell cell = CellUtil.getCell(row, HktvUploadProductExcelReportEnum.values().length);
            cell.setCellValue("Error Reason");
            cell.setCellStyle(headerStyle);
        }
        dataSheet.createFreezePane(2, 1, 2, 1);//凍結窗格
    }


    private int getColumnWidth(HktvUploadProductExcelReportEnum columnEnum, boolean isMerchantRole, boolean isEditReport) {
        if (!isEditReport && isMerchantRole && columnEnum == HktvUploadProductExcelReportEnum.URGENT) {
            return 0;
        }

        switch (columnEnum) {
            case ID:
            case CONSUMABLE:
            case PRIORITY:
            case DELIVERY_TITLE_EN:
            case DELIVERY_TITLE_CH:
            case DELIVERY_DETAILS_EN:
            case DELIVERY_DETAILS_CH:
            case DELIVERY_COMPLETION_DAYS:
            case COLOR_CH:
            case SKU_S_TITLE_HKTV_EN:
            case SKU_S_TITLE_HKTV_CH:
            case SKU_L_TITLE_HKTV_EN:
            case SKU_L_TITLE_HKTV_CH:
            case FINE_PRINT_TITLE_EN:
            case FINE_PRINT_TITLE_CH:
            case MALL_DOLLAR:
            case MALL_DOLLAR_VIP:
                return 0;
            case BRAND_ID:
                return 5000;
            case PRODUCT_CODE:
            case WAREHOUSE:
                return 6000;
            case SKU_CODE:
            case FEATURE_START_TIME:
            case FEATURE_END_TIME:
            case TERM_NAME:
                return 7000;
            default:
                return columnEnum.getColumnName().length() * 400;
        }
    }

    private boolean isLockColumn(HktvUploadProductExcelReportEnum columnEnum, boolean isMerchantRole, boolean isEditReport) {
        if (columnEnum == HktvUploadProductExcelReportEnum.ID) {
            return true;
        }

        if (isEditReport) {
            switch (columnEnum) {
                case PRODUCT_CODE:
                case SKU_CODE:
                case VOUCHER_TYPE:
                case VOUCHER_DISPLAY_TYPE:
                case VOUCHER_TEMPLATE_TYPE:
                case EXPIRY_TYPE:
                    return true;
                default:
            }
        }
        if (isMerchantRole) {
            switch (columnEnum) {
                case DELIVERY_DETAILS_EN:
                case DELIVERY_DETAILS_CH:
                case DELIVERY_COMPLETION_DAYS:
                case CONSUMABLE:
                case PRIORITY:
                case DELIVERY_TITLE_EN:
                case DELIVERY_TITLE_CH:
                    return true;
                default:
            }
        }

        return false;
    }


    private void addLovSheet(Workbook workbook, boolean isEditReport, Integer storeId, Integer contractId, List<Integer> brandIdList, List<SysParmDo> sysParmList) {
        Sheet lovSheet = workbook.createSheet(AbstractReport.LOV_SHEET_NAME);
        Sheet dataSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
        Map<String, Map<String, Object>> map = new HashMap<>();
        Num theColNum = new Num(0);
        BusinessPlatformDo businessPlatformDo =
                businessMapper.findByBusinessCode(ProductMasterBusinessUnitType.HKTV).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));

        setLovSheetValues(lovSheet, map, theColNum, businessPlatformDo, storeId, contractId, brandIdList, sysParmList);
        setValidations(workbook, dataSheet, theColNum, map, businessPlatformDo.getPlatformId(), contractId);
        //保護選項內容
        if (isEditReport) {
            lovSheet.protectSheet(LOV_PASSWORD);
        }
    }

    private void setLovSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum, BusinessPlatformDo businessPlatformDo, Integer storeId, Integer contractId, List<Integer> brandIdList, List<SysParmDo> sysParmList) {
        Integer platformId = businessPlatformDo.getPlatformId();
        String buCode = businessPlatformDo.getBusinessCode();
        Integer buId = businessPlatformDo.getBusinessId();

        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CONTRACT_NO, List.of(String.valueOf(contractId)), theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORE_ID, List.of(String.valueOf(storeId)), theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_YES_NO, OLD_YES_NO_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WEIGHT_UNIT, WEIGHT_UNIT_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, PACK_DIMENSION_UNIT_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_EXPIRY_TYPE, EXPIRY_TYPE_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_RETURN_DAYS, RETURN_DAYS_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_STATUS, PRODUCT_STATUS_LIST, theColNum, map);

        //MMS-146
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_GOODS_TYPE, GOODS_TYPE_LIST, theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WARRANTY_PERIOD_UNIT, WARRANTY_PERIOD_UNIT_LIST, theColNum, map);

        List<String> manuCountryList = SysParmUtil.getParamDescList(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_MANU_COUNTRY, manuCountryList, theColNum, map);

        List<String> currencyList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.CURRENCY, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CURRENCY, currencyList, theColNum, map);

        List<String> packBoxTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
        List<String> packBoxThirdTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE_3PL_SKU, platformId);
        packBoxTypeList.addAll(packBoxThirdTypeList);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_BOX_TYPE, packBoxTypeList, theColNum, map);

        List<String> storageTemperatureList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.STORAGE_TEMPERATURE, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TEMPERATURE, storageTemperatureList, theColNum, map);

        List<String> deliveryMethodList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.DELIVERY_METHOD, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_METHOD, deliveryMethodList, theColNum, map);

        List<String> deliComDaysList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.DELIVERY_COMPLETION_DAYS, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_COMPLETION_DAYS, deliComDaysList, theColNum, map);

        List<String> pickupTimeslotList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.PICKUP_TIMESLOT, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_TIMESLOT, pickupTimeslotList, theColNum, map);

        List<String> proReadyDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_READY_DAYS, proReadyDaysList, theColNum, map);

        List<String> voucherTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TYPE, voucherTypeList, theColNum, map);

        List<String> voucherDisplayTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_DISPLAY_TYPE, voucherDisplayTypeList, theColNum, map);

        List<String> voucherTemplateTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TEMPLATE_TYPE, voucherTemplateTypeList, theColNum, map);

		Integer merchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);
		List<String> virtualStoreMerchantList = sysParmRepository.findBySegmentAndBuCode(SysParmSegment.VIRTUAL_STORE_MERCHANT, ProductMasterBusinessUnitType.HKTV)
				.stream().filter(sysParmDo -> isVirtualStoreMerchant(sysParmDo, merchantId))
				.map(SysParmUtil::getCodeAndShortDescString)
				.collect(Collectors.toList());
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VIRTUAL_STORE_MERCHANT, virtualStoreMerchantList, theColNum, map);

        List<String> storageTypeList = SysParmUtil.getParamValueList(sysParmList, SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TYPE, storageTypeList, theColNum, map);

        List<String> warehouseList = storeWarehouseRepository.findOldDropListByStoreId(storeId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WAREHOUSE, warehouseList, theColNum, map);

        //获取 product ready method list并设置lov
        List<SysParmDo> productReadyMethodList = searchProductReadyMethodByContractId(contractId, buCode);
        List<String> validationProductReadyMethodList = productReadyMethodList.stream()
                .map(SysParmUtil::getCodeAndShortDescString)
                .collect(Collectors.toList());
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRO_READY_METHOD, validationProductReadyMethodList, theColNum, map);

		List<String> pickupDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_DAYS, pickupDaysList, theColNum, map);

        List<String> brandNameEng;
        if (CollectionUtil.isNotEmpty(brandIdList)) {
            brandNameEng = brandRepository.findBrandNameById(brandIdList);
        } else {
            brandNameEng = brandRepository.findListByStatusAndBuId("A", buId);
        }
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_BRAND_NAME_ENG, brandNameEng, theColNum, map);

        List<String> payTermsList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PAYMENT_TERM, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PAY_TERMS, payTermsList, theColNum, map);

        List<String> sizeSystemList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_SIZE_SYSTEM, sizeSystemList, theColNum, map);

        List<String> colorFamiliesList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_COLOR_FAMILIES, colorFamiliesList, theColNum, map);

        List<String> termNameList = contractProdTermsRepository.getContractTermsNameList(contractId, storeId);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_TERM_NAME, termNameList, theColNum, map);

        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_FIELD, setProductFieldLovSheetValue(sysParmList, platformId, contractId), theColNum, map);
        setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STYLE, STYLE_LIST, theColNum, map);
    }

    private void setValidations(Workbook workbook, Sheet dataSheet, Num theColNum, Map<String, Map<String, Object>> map, Integer platformId, Integer contractId) {
        List<String> parentSegments = Arrays.stream(HktvUploadProductExcelReportEnum.values())
                .filter(columnEnum -> null == columnEnum.getValidationName() && null != columnEnum.getParent())
                .map(HktvUploadProductExcelReportEnum::getParentSegment)
                .collect(Collectors.toList());
        String contractType = contractRepository.findMainContractTypeInContract(contractId);
        List<SysParmDo> sysParmDoList = (!MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType))
            ? searchByParentSegmentsAndPlatformId(parentSegments, platformId).stream()
                .filter(sysParmDo -> !SysParmCodeEnum.PRODUCT_FIELD_MAINLAND_CODE_SET.contains(sysParmDo.getParentCode()))
                .collect(Collectors.toList())
            : searchByParentSegmentsAndPlatformId(parentSegments, platformId);

        for (HktvUploadProductExcelReportEnum columnEnum : HktvUploadProductExcelReportEnum.values()) {
            if (null != columnEnum.getValidationName() && null != columnEnum.getParent()) {
                createVLOOKUP(PRODUCT_SHEET_NAME, workbook, columnEnum.getValidationName(), columnEnum.getParent().getColumnNumber(), columnEnum.getColumnNumber(), map);//連動下拉選單
            } else if (null != columnEnum.getValidationName()) {
                setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));//设置一级菜单
            } else if (null != columnEnum.getParent()) {
                createXSSFName(PRODUCT_SHEET_NAME, workbook, columnEnum.getParentSegment(), columnEnum.getParent().getColumnNumber(), columnEnum.getColumnNumber(), theColNum, map, false, platformId, sysParmDoList);//设置二级菜单
            }
        }
    }

    private Workbook setBodyColumn(Workbook workbook, boolean isEditReport, List<String> uuidList, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList, boolean isErrorReport) {
        if (!isEditReport) {
            return workbook;
        }

        long startTime = System.currentTimeMillis();
        Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook,100);

        List<ProductUploadExcelDataDto> productUploadExcelDataDtoList = productMapper.findProductUploadExcelDataList(uuidList);
        Map<String, ProductUploadExcelDataDto> uuidMapProductData = productUploadExcelDataDtoList.stream().collect(Collectors.toMap(ProductUploadExcelDataDto::getUuid, product -> product));

        List<Integer> brandIdList = new ArrayList<>();
        List<Integer> warehouseIdList = new ArrayList<>();
        productList.forEach(singleEditProductDto -> {
                    brandIdList.add(singleEditProductDto.getProduct().getBrandId());
                    warehouseIdList.add(singleEditProductDto.getProduct().getAdditional().getHktv().getWarehouseId());
                }
        );
        BusinessPlatformDo businessPlatformDo =
                businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElse(new BusinessPlatformDo());
        Integer platformId = businessPlatformDo.getPlatformId();

        Map<String, String> manufacturedCountryMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId)
                .stream()
                .collect(Collectors.toMap(SysParmDo::getCode, SysParmDo::getShortDesc, (oldValue, newValue) -> oldValue));

        Map<String, String> colorEnglishMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COLOR, platformId)
                .stream()
                .collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));

        Map<String, String> voucherTypeMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId)
                .stream()
                .collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));
        Map<String, String> sizeSystemMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
        Map<String, String> sizeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE, platformId);
        Map<String, String> colorFamiliesMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
        Map<String, String> productFieldMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD, platformId);
        Map<String, String> productFieldValueMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD_VALUE, platformId);
        Map<String, String> packBoxTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
        Map<String, String> productReadyMethodMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_METHOD, platformId);
		Map<String, String> voucherTemplateTypeMap = getDirtyDataVoucherTemplateTypeMap(addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId));
        Map<String, String> voucherDisplayTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
        Map<String, String> currencyMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.CURRENCY, platformId);
        Map<String, String> productReadyDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
        Map<String, String> pickupDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);

        Map<Integer, String> brandNameByIdMap = brandRepository.findBrandNameEnByIdIn(brandIdList).stream().collect(Collectors.toMap(BrandNameEnViewDo::getId, BrandNameEnViewDo::getBrandNameEn));
        Map<Integer, String> warehouseIdByIdMap = storeWarehouseMapper.findWarehouseIdHaveDelimiter(warehouseIdList).stream().collect(Collectors.toMap(StoreWarehouseIdViewDto::getId, StoreWarehouseIdViewDto::getWarehouseId));

        Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
        CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleWithTimeStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR_M, tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
        AtomicInteger rowIndex = new AtomicInteger(1);

        productList.forEach(singleEditProductDto-> {
            ProductMasterDto product = singleEditProductDto.getProduct();
            HktvProductDto hktvProductDto = product.getAdditional().getHktv();
            ProductUploadExcelDataDto productUploadExcelDataDto = Optional.ofNullable(uuidMapProductData.get(product.getUuid())).orElse(new ProductUploadExcelDataDto());
            String colorEnglish = null;
            String colorChinese = null;
            String color = product.getColor();
            if (StringUtil.isNotEmpty(product.getColor())) {
                int index = product.getColor().indexOf("    ");
                if (index > 0) {
                    colorEnglish = color.substring(0, index);
                    colorChinese = color.substring(index + 4);
                }
            }

            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_CODE.getColumnNumber(), product.getProductId());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_CODE.getColumnNumber(), product.getSkuId());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_HKTV_CAT_LIST.getColumnNumber(), hktvProductDto.getProductTypeCode());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_HKTV_CAT_CODE_PRI.getColumnNumber(), hktvProductDto.getPrimaryCategoryCode());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.BRAND_ID.getColumnNumber(), brandNameByIdMap.get(product.getBrandId()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_READY_METHOD.getColumnNumber(), productReadyMethodMap.get(hktvProductDto.getProductReadyMethod()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ID.getColumnNumber(), productUploadExcelDataDto.getId());
            //online status is empty
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_STATUS.getColumnNumber(), "");
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WAREHOUSE.getColumnNumber(), warehouseIdByIdMap.get(hktvProductDto.getWarehouseId()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.TERM_NAME.getColumnNumber(), hktvProductDto.getTermName());
            setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.IS_PRIMARY_SKU.getColumnNumber(), hktvProductDto.getIsPrimarySku());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME.getColumnNumber(), product.getSkuNameEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME_TCHI.getColumnNumber(), product.getSkuNameCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME_SCHI.getColumnNumber(), product.getSkuNameSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_TITLE_HKTV_EN.getColumnNumber(), productUploadExcelDataDto.getSkuShortTitleHktvEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_TITLE_HKTV_CH.getColumnNumber(), productUploadExcelDataDto.getSkuShortTitleHktvCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_EN.getColumnNumber(), hktvProductDto.getSkuShortDescriptionEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_CH.getColumnNumber(), hktvProductDto.getSkuShortDescriptionCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_SC.getColumnNumber(), hktvProductDto.getSkuShortDescriptionSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_TITLE_HKTV_EN.getColumnNumber(), productUploadExcelDataDto.getSkuLongTitleHktvEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_TITLE_HKTV_CH.getColumnNumber(), productUploadExcelDataDto.getSkuLongTitleHktvCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_EN.getColumnNumber(), hktvProductDto.getSkuLongDescriptionEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_CH.getColumnNumber(), hktvProductDto.getSkuLongDescriptionCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_SC.getColumnNumber(), hktvProductDto.getSkuLongDescriptionSc());
            //photo not set
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MAIN_PHOTO_HKTVMALL.getColumnNumber(), "");
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MAIN_VIDEO.getColumnNumber(), hktvProductDto.getMainVideo());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OTHER_PRODUCT_PHOTO_HKTVMALL.getColumnNumber(), "");
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OTHER_PHOTO_HKTVMALL.getColumnNumber(), "");
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ADVERTISING_PHOTO_HKTVMALL.getColumnNumber(), "");
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK.getColumnNumber(), hktvProductDto.getVideoLink());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN.getColumnNumber(), hktvProductDto.getVideoLinkTextEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH.getColumnNumber(), hktvProductDto.getVideoLinkTextCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC.getColumnNumber(), hktvProductDto.getVideoLinkTextSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK2.getColumnNumber(), productUploadExcelDataDto.getVideoLink2());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN2.getColumnNumber(), productUploadExcelDataDto.getVideoLinkEn2());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH2.getColumnNumber(), productUploadExcelDataDto.getVideoLinkCh2());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC2.getColumnNumber(), productUploadExcelDataDto.getVideoLinkSc2());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK3.getColumnNumber(), productUploadExcelDataDto.getVideoLink3());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN3.getColumnNumber(), productUploadExcelDataDto.getVideoLinkEn3());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH3.getColumnNumber(), productUploadExcelDataDto.getVideoLinkCh3());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC3.getColumnNumber(), productUploadExcelDataDto.getVideoLinkSc3());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK4.getColumnNumber(), productUploadExcelDataDto.getVideoLink4());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN4.getColumnNumber(), productUploadExcelDataDto.getVideoLinkEn4());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH4.getColumnNumber(), productUploadExcelDataDto.getVideoLinkCh4());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC4.getColumnNumber(), productUploadExcelDataDto.getVideoLinkSc4());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK5.getColumnNumber(), productUploadExcelDataDto.getVideoLink5());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN5.getColumnNumber(), productUploadExcelDataDto.getVideoLinkEn5());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH5.getColumnNumber(), productUploadExcelDataDto.getVideoLinkCh5());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC5.getColumnNumber(), productUploadExcelDataDto.getVideoLinkSc5());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MANU_COUNTRY.getColumnNumber(), manufacturedCountryMap.get(product.getManufacturedCountry()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_FAMILIAR.getColumnNumber(), colorFamiliesMap.get(product.getColourFamilies()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_EN.getColumnNumber(), colorEnglishMap.get(colorEnglish));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_CH.getColumnNumber(), colorChinese);
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SIZE_SYSTEM.getColumnNumber(), sizeSystemMap.get(product.getSizeSystem()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SIZE.getColumnNumber(), sizeMap.get(product.getSize()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CURRENCY_CODE.getColumnNumber(), currencyMap.get(hktvProductDto.getCurrency()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COST.getColumnNumber(), convertDecimalToDouble(hktvProductDto.getCost()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ORIGINAL_PRICE.getColumnNumber(), convertDecimalToDouble(product.getOriginalPrice()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SELLING_PRICE.getColumnNumber(), convertDecimalToDouble(hktvProductDto.getSellingPrice()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MALL_DOLLAR.getColumnNumber(), convertDecimalToDouble(hktvProductDto.getMallDollar()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MALL_DOLLAR_VIP.getColumnNumber(), convertDecimalToDouble(hktvProductDto.getVipMallDollar()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.USER_MAX.getColumnNumber(), zeroToNull(hktvProductDto.getUserMax()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.STYLE.getColumnNumber(), hktvProductDto.getStyle());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT.getColumnNumber(), hktvProductDto.getDiscountTextEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT_ZH.getColumnNumber(), hktvProductDto.getDiscountTextCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT_SC.getColumnNumber(), hktvProductDto.getDiscountTextSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_EN.getColumnNumber(), hktvProductDto.getPackingSpecEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_CH.getColumnNumber(), hktvProductDto.getPackingSpecCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_SC.getColumnNumber(), hktvProductDto.getPackingSpecSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_HEIGHT.getColumnNumber(), convertDecimalToDouble(product.getPackingHeight()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_LENGTH.getColumnNumber(), convertDecimalToDouble(product.getPackingLength()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_DEPTH.getColumnNumber(), convertDecimalToDouble(product.getPackingDepth()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_DIMENSION_UNIT.getColumnNumber(), product.getPackingDimensionUnit());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WEIGHT.getColumnNumber(), convertDecimalToDouble(product.getWeight()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WEIGHT_UNIT.getColumnNumber(), product.getWeightUnit());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_BOX_TYPE.getColumnNumber(), packBoxTypeMap.get(product.getPackingBoxType()));

			if (CollectionUtil.isNotEmpty(product.getCartonSizeList())) {
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_HEIGHT.getColumnNumber(), product.getCartonSizeList().get(0).getHeight());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_DEPTH.getColumnNumber(), product.getCartonSizeList().get(0).getWidth());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_LENGTH.getColumnNumber(), product.getCartonSizeList().get(0).getLength());
            }else {
                setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_HEIGHT.getColumnNumber(), "");
                setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_DEPTH.getColumnNumber(), "");
                setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_LENGTH.getColumnNumber(), "");
            }
            setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVISIBLE_FLAG.getColumnNumber(), reverseYN(hktvProductDto.getVisibility()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.BARCODE.getColumnNumber(), Optional.ofNullable(product.getBarcodes()).orElse(new ArrayList<>()).stream().filter(productBarcodeDto -> 1 == productBarcodeDto.getSequenceNo()).map(ProductBarcodeDto::getEan).findFirst().orElse(""));
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), HktvUploadProductExcelReportEnum.FEATURE_START_TIME.getColumnNumber(), hktvProductDto.getFeatureStartTime());
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), HktvUploadProductExcelReportEnum.FEATURE_END_TIME.getColumnNumber(), hktvProductDto.getFeatureEndTime());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_TYPE.getColumnNumber(), voucherTypeMap.get(hktvProductDto.getVoucherType()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_DISPLAY_TYPE.getColumnNumber(), voucherDisplayTypeMap.get(hktvProductDto.getVoucherDisplayType()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_TEMPLATE_TYPE.getColumnNumber(), voucherTemplateTypeMap.get(hktvProductDto.getVoucherTemplateType()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.EXPIRY_TYPE.getColumnNumber(), hktvProductDto.getExpiryType());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CONSUMABLE.getColumnNumber(), productUploadExcelDataDto.getConsumable());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRIORITY.getColumnNumber(), productUploadExcelDataDto.getPriority());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD1.getColumnNumber(), productFieldMap.get(product.getOption1()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE1.getColumnNumber(), productFieldValueMap.get(product.getOption1Value()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD2.getColumnNumber(), productFieldMap.get(product.getOption2()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE2.getColumnNumber(), productFieldValueMap.get(product.getOption2Value()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD3.getColumnNumber(), productFieldMap.get(product.getOption3()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE3.getColumnNumber(), productFieldValueMap.get(product.getOption3Value()));
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY, rowIndex.get(), HktvUploadProductExcelReportEnum.REDEEM_START_DATE.getColumnNumber(), hktvProductDto.getRedeemStartDate());
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  rowIndex.get(), HktvUploadProductExcelReportEnum.REDEEM_END_DATE.getColumnNumber(), hktvProductDto.getFixedRedemptionDate());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.UPON_PURCHASE_DATE.getColumnNumber(), zeroToNull(hktvProductDto.getUponPurchaseDate()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_TITLE_EN.getColumnNumber(), productUploadExcelDataDto.getFinePrintTitleEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_TITLE_CH.getColumnNumber(), productUploadExcelDataDto.getFinePrintTitleCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_EN.getColumnNumber(), hktvProductDto.getFinePrintEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_CH.getColumnNumber(), hktvProductDto.getFinePrintCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_SC.getColumnNumber(), hktvProductDto.getFinePrintSc());
            setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.REMOVAL_SERVICES.getColumnNumber(), hktvProductDto.getNeedRemovalServices());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.GOODS_TYPE.getColumnNumber(), hktvProductDto.getGoodsType());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_PERIOD_UNIT.getColumnNumber(), hktvProductDto.getWarrantyPeriodUnit());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_PERIOD.getColumnNumber(), zeroToNull(hktvProductDto.getWarrantyPeriod()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_EN.getColumnNumber(), hktvProductDto.getWarrantySupplierEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_CH.getColumnNumber(), hktvProductDto.getWarrantySupplierCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_SC.getColumnNumber(), hktvProductDto.getWarrantySupplierSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_EN.getColumnNumber(), hktvProductDto.getServiceCentreAddressEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_CH.getColumnNumber(), hktvProductDto.getServiceCentreAddressCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_SC.getColumnNumber(), hktvProductDto.getServiceCentreAddressSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_EMAIL.getColumnNumber(), hktvProductDto.getServiceCentreEmail());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_CONTACT.getColumnNumber(), hktvProductDto.getServiceCentreContact());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_EN.getColumnNumber(), hktvProductDto.getWarrantyRemarkEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_CH.getColumnNumber(), hktvProductDto.getWarrantyRemarkCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_SC.getColumnNumber(), hktvProductDto.getWarrantyRemarkSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_EN.getColumnNumber(), hktvProductDto.getInvoiceRemarksEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_CH.getColumnNumber(), hktvProductDto.getInvoiceRemarksCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_SC.getColumnNumber(), hktvProductDto.getInvoiceRemarksSc());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.RETURN_DAYS.getColumnNumber(), hktvProductDto.getReturnDays());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_READY_DAYS.getColumnNumber(), productReadyDaysMap.get(hktvProductDto.getProductReadyDays()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PICKUP_DAYS.getColumnNumber(), pickupDaysMap.get(hktvProductDto.getPickupDays()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PICKUP_TIMESLOT.getColumnNumber(), hktvProductDto.getPickupTimeslot());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OVERSEA_DELIVERY.getColumnNumber(), productUploadExcelDataDto.getOverseaDelivery());
            setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.URGENT.getColumnNumber(), hktvProductDto.getUrgent());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_TITLE_EN.getColumnNumber(), productUploadExcelDataDto.getDeliveryTitleEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_TITLE_CH.getColumnNumber(), productUploadExcelDataDto.getDeliveryTitleCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_DETAILS_EN.getColumnNumber(), productUploadExcelDataDto.getDeliveryDetailsEn());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_DETAILS_CH.getColumnNumber(), productUploadExcelDataDto.getDeliveryDetailsCh());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_COMPLETION_DAYS.getColumnNumber(), productUploadExcelDataDto.getDeliveryCompletionDays());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MINIMUM_SHELF_LIFE.getColumnNumber(), zeroToNull(productUploadExcelDataDto.getMinimumShelfLife()));
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIRTUAL_STORE.getColumnNumber(), hktvProductDto.getVirtualStore());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.RM_CODE.getColumnNumber(), hktvProductDto.getRmCode());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.STORAGE_TYPE.getColumnNumber(), hktvProductDto.getStorageType());
            setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRE_SELL_FRUIT.getColumnNumber(), hktvProductDto.getPreSellFruit());
            setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PHYSICAL_STORE.getColumnNumber(), hktvProductDto.getPhysicalStore());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.AFFILIATE_URL.getColumnNumber(), hktvProductDto.getAffiliateUrl());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.EW_PERCENTAGE_SETTING.getColumnNumber(), convertDecimalToDouble(hktvProductDto.getEwPercentageSetting()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CLAIM_LINK_EN.getColumnNumber(), hktvProductDto.getClaimLinkEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CLAIM_LINK_CH.getColumnNumber(), hktvProductDto.getClaimLinkCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CLAIM_LINK_SC.getColumnNumber(), hktvProductDto.getClaimLinkSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PARTNER_PLATFORM.getColumnNumber(),
				ofNullable(hktvProductDto.getExternalPlatform())
					.map(ExternalPlatform::getSource)
					.map(source -> StringUtils.join(source, ","))
					.orElse(""));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PARTNER_PRODUCT_ID.getColumnNumber(), ofNullable(hktvProductDto.getExternalPlatform()).map(ExternalPlatform::getProductId).orElse(""));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PARTNER_SKU_ID.getColumnNumber(), ofNullable(hktvProductDto.getExternalPlatform()).map(ExternalPlatform::getSkuId).orElse(""));
			if(isErrorReport){
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.values().length, singleEditProductDto.getErrorMessage());
			}
            rowIndex.addAndGet(1);
        });


        long endTime = System.currentTimeMillis();
        log.info("Time taken by add product for hktv upload template {} milliseconds", (endTime - startTime));
        return tempWorkbook;
    }

    @Override
    public String getFileName() {
        return String.format("product_upload_update_%d.xlsx", System.currentTimeMillis());
    }

    @Override
    public HttpHeaders getResponseHeader() {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(new MediaType("application", "octet-stream"));
        header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + getFileName());
        return header;
    }
}
