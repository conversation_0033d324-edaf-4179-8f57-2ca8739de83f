package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.contract.ContractProdTermsRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractTypeRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.pojo.SetCellValueDto;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.type.ContractType.MAINLAND_MERCHANT_CONTRACT_SET;

@Slf4j
public abstract class AbstractReport {
	protected static final String LOV_SHEET_NAME = "LOV";
	public static final String LIST_VALUE = "listValue";
	private static final String LIST_COL = "listCol";
	private static final String LIST_START_COL = "listStartCol";
	private static final String LIST_END_COL = "listEndCol";
	private static final String LIST_START_ROW = "listStartRow";
	private static final String LIST_END_ROW = "listEndRow";
	protected static final String LOV_PASSWORD = "pwdais123!@#";
	protected static final String TEMPLATE_SHEET_PASSWORD = "pwdais123!@#";

	public static final List<String> SEGMENT_PARENT_LIST = List.of(
			SysParmSegment.COUNTRY_OF_ORIGIN, SysParmSegment.COLOR, SysParmSegment.COLOR_FAMILIES,
			SysParmSegment.SIZE, SysParmSegment.SIZE_SYSTEM, SysParmSegment.PRODUCT_FIELD, SysParmSegment.PRODUCT_FIELD_VALUE,
			SysParmSegment.STORAGE_TEMPERATURE, SysParmSegment.PACK_BOX_TYPE, SysParmSegment.PACK_BOX_TYPE_3PL_SKU, SysParmSegment.PRODUCT_READY_METHOD, SysParmSegment.DELIVERY_METHOD,
			SysParmSegment.CURRENCY, SysParmSegment.PRODUCT_READY_DAYS, SysParmSegment.PICKUP_DAYS,
			SysParmSegment.DELIVERY_COMPLETION_DAYS, SysParmSegment.PICKUP_TIMESLOT, SysParmSegment.VOUCHER_TYPE, SysParmSegment.VOUCHER_DISPLAY_TYPE,
			SysParmSegment.VOUCHER_TEMPLATE_TYPE, SysParmSegment.PAYMENT_TERM, SysParmSegment.VIRTUAL_STORE_MERCHANT, SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING,
			SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE
	);

	@Autowired
	public SysParmRepository sysParmRepository;

	@Autowired
	public ContractProdTermsRepository contractProdTermsRepository;

	@Autowired
	public ContractTypeRepository contractTypeRepository;

	@Autowired
	public ContractRepository contractRepository;

	public static class Num {
		int number;

		public Num(int number) {
			this.number = number;
		}

		public int getNumber() {
			return number;
		}

		public void addNum() {
			this.number += 1;
		}
	}

	public List<SysParmDo> searchBySegments(List<String> segments) {
		return sysParmRepository.findBySegments(segments);
	}

	public Map<String, List<String>> getStringsByParentSegment(String segment, Integer platformId) {
		if (SysParmSegment.VOUCHER_TYPE.equals(segment)) {
			return getSpeciallySegment(segment);
		}

		Map<String, List<String>> mapList = new HashMap<>();
		List<SysParmDo> paramList = sysParmRepository.findChildBySegmentAndPlatformId(segment, platformId);
		if (CollectionUtil.isNotEmpty(paramList)) {
			for (SysParmDo param : paramList) {
				List<String> strList = mapList.computeIfAbsent(param.getParentCode(), k -> new ArrayList<>());
				strList.add(SysParmUtil.getCodeAndShortDescString(param));
			}
		}
		return mapList;
	}

	public Map<String, List<String>> filerStringsByParentSegment(List<SysParmDo> sysParmDoList, String segment, Integer platformId) {
		if (SysParmSegment.VOUCHER_TYPE.equals(segment)) {
			return getSpeciallySegment(segment);
		}

		Map<String, List<String>> mapList = new HashMap<>();
		if (CollectionUtil.isNotEmpty(sysParmDoList)) {
			for (SysParmDo param : sysParmDoList) {
				if (param.getParentSegment().equals(segment) && (param.getPlatformId() == null || Objects.equals(param.getPlatformId(), platformId))) {
					List<String> strList = mapList.computeIfAbsent(param.getParentCode(), k -> new ArrayList<>());
					strList.add(SysParmUtil.getCodeAndShortDescString(param));
				}
			}
		}
		return mapList;
	}

	public List<SysParmDo> searchByParentSegmentsAndPlatformId(List<String> segments, Integer platformId) {
		return sysParmRepository.findChildBySegmentsAndPlatformId(segments, platformId);
	}

	public List<SysParmDo> searchProductReadyMethodByContractId(Integer contractId, String buCode) {
		List<String> productReadyList = contractTypeRepository.findLatestProductReadyMethodByContractId(contractId);

		if (CollectionUtil.isEmpty(productReadyList) && contractId != null) {
			String mainContractTypeCode = contractRepository.findMainContractTypeInContract(contractId);
			productReadyList = ProductReadyMethodType.generateRestrictedProductReadyCodeList(mainContractTypeCode);
		}

		return sysParmRepository.findBySegmentAndBuCodeAndCodeList("PRODUCT_READY_METHOD", buCode, productReadyList);
	}

	//voucher type = "HOKOBUY", can only select "CODE128B"
	private Map<String, List<String>> getSpeciallySegment(String segment) {
		Map<String, List<String>> mapList = new HashMap<>();
		if (SysParmSegment.VOUCHER_TYPE.equals(segment)) {
			List<String> hokobuy = new ArrayList<>();
			hokobuy.add("CODE128B : CODE128B");
			mapList.put("HOKOBUY", hokobuy);

			List<String> merchant = new ArrayList<>();
			merchant.add("PLAINTEXT : PLAINTEXT");
			merchant.add("CODE128A : CODE128A");
			merchant.add("CODE128B : CODE128B");
			merchant.add("CODE128C : CODE128C");
			merchant.add("QRCODE : QRCODE");
			mapList.put("MERCHANT", merchant);
		}
		return mapList;
	}

	public void addDefaultStyle(Workbook wb) {
		CellStyle style = wb.createCellStyle();
		style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
		style.setAlignment(HorizontalAlignment.CENTER);// 水平
	}

	public void setLovSheetHeaders(Workbook workbook, Sheet sheetLov, List<String> headers) {
		if (CollectionUtil.isEmpty(headers)) {
			return;
		}
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		Row rowLov = CellUtil.getRow(0, sheetLov);
		for (int i = 0; i < headers.size(); i++) {
			Cell cellLov = rowLov.createCell(i);
			cellLov.setCellValue(headers.get(i));
			cellLov.setCellStyle(headerStyle);
		}
	}

	/**
	 * 设置某些列的值只能sheet中某列输入预制的数据,显示下拉框.
	 */

	//通过定义的List，设置Lov Sheet中的Value，并储存map行row列col信息
	public void setLovSheetValue(Sheet sheetLov, String listName, List<String> theList, Num colNum, Map<String, Map<String, Object>> map) {
		Map<String, Object> theMap = new HashMap<>();
		int titleRowNum = 0;
		int lovNum = 0;

		for (int rowNum = titleRowNum + 1; rowNum < theList.size() + titleRowNum + 1; rowNum++, lovNum++) {
			Row rowLov = CellUtil.getRow(rowNum, sheetLov);
			if (rowLov != null) {
				Cell cellLov = rowLov.createCell(colNum.getNumber());
				cellLov.setCellValue(theList.get(lovNum));
			}
		}

		theMap.put(LIST_COL, colNum.getNumber());
		theMap.put(LIST_START_ROW, titleRowNum + 1);
		theMap.put(LIST_END_ROW, lovNum + titleRowNum);
		theMap.put(LIST_VALUE, theList);
		map.put(listName, theMap);
		colNum.addNum();
	}

	/**
	 *	write custom group data header(option parent) and options(child values) to Lov sheet
	 *	Map<String, List<String>> customDataMap : key: option parent name, value: child option values
	 */
	public void setCustomLovData(Workbook workbook, Sheet lovSheet, Map<String, Map<String, Object>> lovMap, Num theColNum, Map<String, List<String>> customDataMap) {
		if (customDataMap == null || customDataMap.isEmpty()) {
			return;
		}
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		Row parentRow = CellUtil.getRow(0, lovSheet);

		final int beginColNumber = theColNum.getNumber();
		int headerCellIndex = theColNum.getNumber();
		int bodyCellIndex = theColNum.getNumber();

		for (Map.Entry<String, List<String>> entry : customDataMap.entrySet()) {
			// header
			String parentName = entry.getKey();
			Cell cellLov = parentRow.createCell(headerCellIndex++);
			cellLov.setCellValue(parentName);
			cellLov.setCellStyle(headerStyle);

			// child option
			List<String> childOptions = entry.getValue();
			int childRowStartNum = 1;
			for (int rowNum = childRowStartNum, tempNum = 0; tempNum < childOptions.size(); rowNum++, tempNum++) {
				Row childRowLov = CellUtil.getRow(rowNum, lovSheet);
				Cell bodyCellLov = childRowLov.createCell(bodyCellIndex);
				bodyCellLov.setCellValue(childOptions.get(tempNum));
			}
			// 名稱管理員parent選項參照到的child選單內容range
			String rangeFormula = LOV_SHEET_NAME + "!$" + excelColIndexToStr(bodyCellIndex) + "$" + (childRowStartNum + 1) +
				":$" + excelColIndexToStr(bodyCellIndex) + "$" + (childRowStartNum + childOptions.size());
			// 名稱管理員的名稱不允許數字作為開頭、以及不能有空格及:
			String createNameKey = ExcelValidationName.VALIDATION_CUSTOM_OPTIONS + parentName.replaceAll("[ :]", "");
			createName(workbook, createNameKey, rangeFormula);
			bodyCellIndex++;
			theColNum.addNum();
		}

		// put custom options parent
		lovMap.put(ExcelValidationName.VALIDATION_CUSTOM_OPTIONS,
			new HashMap<>(
				Map.of(
					LIST_START_COL, beginColNumber,
					LIST_END_COL, bodyCellIndex,
					LIST_START_ROW, parentRow.getRowNum(),
					LIST_END_ROW, parentRow.getRowNum(),
					LIST_VALUE, new ArrayList<>(customDataMap.keySet())
				)
			));
	}

	public List<String> setProductFieldLovSheetValue(List<SysParmDo> sysParmList, Integer platformId, Integer contractId)
	{
		String contractType = contractRepository.findMainContractTypeInContract(contractId);
		List<SysParmDo> newSysParmList;
		if (!MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)) {
			newSysParmList = sysParmList.stream()
				.filter(sysParmDo -> !SysParmCodeEnum.PRODUCT_FIELD_MAINLAND_CODE_SET.contains(sysParmDo.getCode()))
				.collect(Collectors.toList());
		} else {
			newSysParmList = sysParmList;
		}
		return SysParmUtil.getParamCodeAndShortDescList(newSysParmList, SysParmSegment.PRODUCT_FIELD, platformId);
	}

	/**
	 * 设置二级菜单的名称管理器，一并设置二级菜单
	 *
	 * @param parentColNum Parent Column的Column值
	 * @param childColNum  Child Column的Column值
	 */
	public void createCustomXSSFName(Sheet currentSheet, int firstRow, Integer parentColNum, Integer childColNum) {
		setXSSFNameValidation(currentSheet, firstRow, parentColNum, childColNum);
	}

	public void setXSSFValidation(Sheet sheet, int sheetColNum, Map<String, Object> theMap) {
		setXSSFValidation(sheet, sheetColNum, theMap, 1);
	}

	public void setXSSFValidation(Sheet sheet, int sheetColNum, Map<String, Object> theMap, int firstRow) {
		int endRow = 65535;

		Integer colNum = (Integer) theMap.get(LIST_COL);
		Integer startRow = (Integer) theMap.get(LIST_START_ROW);
		Integer lastRow = (Integer) theMap.get(LIST_END_ROW);

		//设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, sheetColNum, sheetColNum);
		String cell = toFormulaRangeStr(colNum, startRow, lastRow);
		//设置下拉列表的值
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("INDIRECT(" + cell + ")");
		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		//设置数据验证（列表）
		validation.setSuppressDropDownArrow(true);
		//show ErrorBox
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	public void setCombinedOptionAndDependLockValidation(Sheet sheet, int startRow, int endRow,int dependColNum, int validationColNum, Map<String, Object> theMap) {
		Integer colNum = (Integer) theMap.get(LIST_COL);
		Integer firstRow = (Integer) theMap.get(LIST_START_ROW);
		Integer lastRow = (Integer) theMap.get(LIST_END_ROW);

		// 依賴哪一欄儲存格的條件
		String dependCol = excelColIndexToStr(dependColNum);
		// 下拉選單範圍
		String dropdownRange = toFormulaRangeStr(colNum, firstRow, lastRow);

		DataValidationHelper dvHelper = sheet.getDataValidationHelper();
		int dependCellIndex;
		for (int rowIndex  = startRow; rowIndex  <= endRow; rowIndex ++) {
			dependCellIndex = rowIndex + 1;
			// 不為N, 才可以選擇選單, 否則不可修改 (選單與鎖定需要逐行定義公式)
			String formula = "IF(" + "$" + dependCol + "$" + dependCellIndex  + "<>\"N\", INDIRECT(" + dropdownRange + "), FALSE)";
			CellRangeAddressList regions = new CellRangeAddressList(rowIndex, rowIndex, validationColNum, validationColNum);
			DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint(formula);

			DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
			validation.setSuppressDropDownArrow(true);
			validation.setShowErrorBox(true);
			sheet.addValidationData(validation);
		}
	}

	public void setDependLockColumnValidation(Sheet sheet, int startRow, int endRow, int dependColNum, int validationColNum) {
		CellRangeAddressList regions = new CellRangeAddressList(startRow, endRow, validationColNum, validationColNum);
		String dependCol = excelColIndexToStr(dependColNum);
		String formula = "IF(INDIRECT(\"" + dependCol + "\"&ROW())<>\"N\", TRUE, FALSE)";
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		DataValidationConstraint dvConstraint = dvHelper.createCustomConstraint(formula);
		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	/**
	 * 获取用数字编号对应的单元格下拉列表值的范围
	 */
	private String toFormulaRangeStr(int colNum, int startRow, int lastRow) {
		String col = excelColIndexToStr(colNum);
		return "\"" + LOV_SHEET_NAME + "!$" + col + "$" + (startRow + 1) + ":$" + col + "$" + (lastRow + 1) + "\"";
	}

	public void setCustomXSSFValidation(Sheet sheet, int sheetColNum, Map<String, Object> theMap, int firstRow) {
		int endRow = 65535;

		Integer startColNum = (Integer) theMap.get(LIST_START_COL);
		Integer endColNum = (Integer) theMap.get(LIST_END_COL);
		Integer startRow = (Integer) theMap.get(LIST_START_ROW);
		Integer lastRow = (Integer) theMap.get(LIST_END_ROW);

		//设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, sheetColNum, sheetColNum);
		String cell = toFormulaRangeStr(startColNum, endColNum, startRow, lastRow);
		//设置下拉列表的值
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("INDIRECT(" + cell + ")");
		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		//设置数据验证（列表）
		validation.setSuppressDropDownArrow(true);
		//show ErrorBox
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	/**
	 * set row cell value cannot edit
	 */
	public void setRowDataLockValidation(XSSFSheet productSheet, int firstRow, int lastRow) {
		DataValidationHelper dvHelper = productSheet.getDataValidationHelper();
		int firstCol = 0;
		int lastCol = productSheet.getRow(0).getLastCellNum() - 1;
		CellRangeAddressList headerRange = new CellRangeAddressList(firstRow, lastRow,firstCol, lastCol);

		DataValidationConstraint headerConstraint =
			dvHelper.createCustomConstraint("FALSE");

		DataValidation headerValidation =
			dvHelper.createValidation(headerConstraint, headerRange);
		headerValidation.setShowErrorBox(true);
		productSheet.addValidationData(headerValidation);
	}

	/**
	 * set columns cell value cannot edit
	 */
	public void setColumnsDataLockValidation(Sheet dataSheet, int firstRow, int lastRow, Set<Integer> lockColumns) {
		for (int columnNumber : lockColumns) {
			DataValidationHelper dvHelper = dataSheet.getDataValidationHelper();
			CellRangeAddressList dataRange = new CellRangeAddressList(firstRow, lastRow, columnNumber, columnNumber);

			DataValidationConstraint dataConstraint =
				dvHelper.createCustomConstraint("FALSE");

			DataValidation dataValidation =
				dvHelper.createValidation(dataConstraint, dataRange);
			dataValidation.setShowErrorBox(true);
			dataSheet.addValidationData(dataValidation);
		}
	}

	private String toFormulaRangeStr(int startColNum, int endColNum, int startRow, int lastRow) {
		String startCol = excelColIndexToStr(startColNum);
		String endCol = excelColIndexToStr(endColNum);
		return "\"" + LOV_SHEET_NAME + "!$" + startCol + "$" + (startRow + 1) + ":$" + endCol + "$" + (lastRow + 1) + "\"";
	}

	/**
	 * 转换Excel列数字为字母
	 */
	protected String excelColIndexToStr(int columnIndex) {
		if (columnIndex < 0) {
			return null;
		}
		StringBuilder columnStr = new StringBuilder();
		do {
			if (columnStr.length() > 0) {
				columnIndex--;
			}
			columnStr.insert(0, ((char) (columnIndex % 26 + 'A')));
			columnIndex = (columnIndex - columnIndex % 26) / 26;
		} while (columnIndex > 0);
		return columnStr.toString();
	}

	/**
	 * 设置二级菜单的名称管理器，一并设置二级菜单
	 *
	 * @param parentSegment 来自Sys param的Parent Segment
	 * @param parentColNum  Parent Column的Column值
	 * @param childColNum   Child Column的Column值
	 */
	public void createXSSFName(String currentSheet, Workbook wb, String parentSegment, Integer parentColNum, Integer childColNum, Num colNum, Map<String, Map<String, Object>> map, boolean combineSegment, Integer platformId, List<SysParmDo> sysParmDoList) {
		Sheet sheet = wb.getSheet(currentSheet);
		Sheet lovSheet = wb.getSheet(LOV_SHEET_NAME);
		Map<String, List<String>> sysParmByParentMap;
		if (sysParmDoList == null) {
			if (combineSegment) {
				sysParmByParentMap = getStringsByParentSegment(parentSegment, childColNum);
			} else {
				sysParmByParentMap = getStringsByParentSegment(parentSegment, platformId);
			}
		} else {
			if (combineSegment) {
				sysParmByParentMap = filerStringsByParentSegment(sysParmDoList, parentSegment, childColNum);
			} else {
				sysParmByParentMap = filerStringsByParentSegment(sysParmDoList, parentSegment, platformId);
			}
		}

		int titleRowNum = 0;
		int childRowStartNum = titleRowNum + 1;

		if (MapUtils.isNotEmpty(sysParmByParentMap)) {
			for (Map.Entry<String, List<String>> entry : sysParmByParentMap.entrySet()) {
				String parentSegmentCode = entry.getKey();
				List<String> sysParmList = entry.getValue();

				if (CollectionUtil.isNotEmpty(sysParmList)) {
					for (int rowNum = childRowStartNum, tempNum = 0; rowNum < sysParmList.size() + childRowStartNum; rowNum++, tempNum++) {//循环2
						Row rowLov = CellUtil.getRow(rowNum, lovSheet);
						Cell cellLov = rowLov.createCell(colNum.getNumber());
						cellLov.setCellValue(sysParmList.get(tempNum));
					}

					int childRowEndNum = childRowStartNum + sysParmList.size();

					Object o = map.get(parentSegmentCode);
					if (o == null) {
						createName(wb, parentSegmentCode, LOV_SHEET_NAME + "!$" + excelColIndexToStr(colNum.getNumber()) + "$" + (childRowStartNum + 1) + ":$" + excelColIndexToStr(colNum.getNumber()) + "$" + childRowEndNum);
						map.put(parentSegmentCode, new HashMap<>());
					}
					childRowStartNum = childRowEndNum;
				}

			}
		}

		setXSSFNameValidation(sheet, parentColNum, childColNum);
		colNum.addNum();
	}

	/**
	 * 设置第二级菜单
	 */
	public void setCustomOptionXSSFNameValidation(Sheet sheet, int firstRow, int parentColNum, int childColNum) {
		//设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList regions = new CellRangeAddressList(firstRow, 65535, childColNum, childColNum);
		//设置下拉列表的值
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		String theCell = "$" + excelColIndexToStr(parentColNum) + (firstRow + 1);
		String formula = "INDIRECT(\"customOptions\" & SUBSTITUTE(SUBSTITUTE(TRIM(" + theCell + "), \" : \", \"\"), \" \", \"\"))";
		DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint(formula);
		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		//设置数据验证（列表）
		validation.setSuppressDropDownArrow(true);
		//show ErrorBox
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	/**
	 * 设置第二级菜单
	 */
	private void setXSSFNameValidation(Sheet sheet, int firstRow, int parentColNum, int childColNum) {
		//设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList regions = new CellRangeAddressList(firstRow, 65535, childColNum, childColNum);
		//设置下拉列表的值
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		String theCell = "$" + excelColIndexToStr(parentColNum) + (firstRow + 1);
		DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("INDIRECT(" + theCell + ")");
		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		//设置数据验证（列表）
		validation.setSuppressDropDownArrow(true);
		//show ErrorBox
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	/**
	 * 设置第二级菜单
	 */
	private void setXSSFNameValidation(Sheet sheet, int parentColNum, int childColNum) {

		//设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
		CellRangeAddressList regions = new CellRangeAddressList(1, 65535, childColNum, childColNum);
		//设置下拉列表的值
		DataValidationHelper dvHelper = sheet.getDataValidationHelper();

		String theCell = "$" + excelColIndexToStr(parentColNum) + (1 + 1);
		DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint("INDIRECT(LEFT(" + theCell + ",FIND(\" : \"," + theCell + ")-1))");

		DataValidation validation = dvHelper.createValidation(dvConstraint, regions);
		//设置数据验证（列表）
		validation.setSuppressDropDownArrow(true);
		//show ErrorBox
		validation.setShowErrorBox(true);
		sheet.addValidationData(validation);
	}

	public void createVLOOKUP(String sheetName, Workbook wb, String parentSegment, Integer parentColumnNumber, Integer childColumnNumber, Map<String, Map<String, Object>> map) {
		Sheet dataSheet = wb.getSheet(sheetName);
		Integer colNum = (Integer) map.get(parentSegment).get(LIST_COL);
		CellStyle cellStyle = ExcelUtil.createTableHeaderStyle(wb, HorizontalAlignment.CENTER, false, false);
		cellStyle.setHidden(true);
		String cellValueFormat = "IFERROR(VLOOKUP($" + excelColIndexToStr(parentColumnNumber) + "$%d,LOV!$" + excelColIndexToStr(colNum) + ":$" + excelColIndexToStr(colNum - 1) + ",2,FALSE),\"\")";

		for (int rowNum = 1; rowNum < 65535; rowNum++) {
			Row rowLov = CellUtil.getRow(rowNum, dataSheet);
			Cell cellLov = rowLov.createCell(childColumnNumber);
			cellLov.setCellFormula(String.format(cellValueFormat, rowNum + 1));
			cellLov.setCellStyle(cellStyle);
		}
	}

	/**
	 * 创建名称管理器
	 * name:名称管理器的名称
	 * expression:名称管理器的表达式
	 */
	private void createName(Workbook wb, String name, String expression) {
		Name refer = wb.createName();
		try {
			refer.setRefersToFormula(expression);
			refer.setNameName(name);
		} catch (Exception e) {
			log.error("skip error ..." + e.getMessage(), e);
		}
	}



	public void setCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, String value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		if(StringUtil.isNotEmpty(value) && value.length()>32767){
			value = value.substring(0,32766);
		}
		cell.setCellValue(Optional.ofNullable(value).orElse(""));
		cell.setCellStyle(bodyStyle);
	}

	public void setDateCellValue(Sheet sheet, CellStyle dateFormatStyle, String parseStyle, String formatStyle, int rowNumber, int columnNumber, String value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		if (StringUtil.isNotEmpty(value)) {
			Date date = DateUtil.formatToDateFromDateStringAndFormatStyle(value, parseStyle, formatStyle);
			cell.setCellValue(date);
			cell.setCellStyle(dateFormatStyle);
		} else {
			cell.setCellValue("");
		}
	}

	/** default unLock */
	public void setUnLockCellValue(SetCellValueDto setCellValueDto, int columnNumber, String value) {
		setCellValue(setCellValueDto, columnNumber, false, value);
	}

	public void setCellValue(SetCellValueDto setCellValueDto, int columnNumber, boolean isLock, String value) {
		Row row = CellUtil.getRow(setCellValueDto.getRowNumber(), setCellValueDto.getSheet());
		Cell cell = CellUtil.getCell(row, columnNumber);
		if (StringUtil.isNotEmpty(value) && value.length() > 32767) {
			value = value.substring(0, 32766);
		}
		cell.setCellValue(Optional.ofNullable(value).orElse(""));
		if (isLock) {
			cell.setCellStyle(setCellValueDto.getLockStyle());
		} else {
			cell.setCellStyle(setCellValueDto.getNotLockStyle());
		}
	}

	public void setCellValue(SetCellValueDto setCellValueDto, int columnNumber, boolean isLock, List<String> values) {
		String value = null;
		if (CollectionUtil.isNotEmpty(values)) {
			value =values.stream()
				.filter(StringUtil::isNotEmpty)
				.collect(Collectors.joining(","))
			;
		}
		setCellValue(setCellValueDto, columnNumber, isLock, value);
	}

	public void setCellValue(SetCellValueDto setCellValueDto, int columnNumber, boolean isLock, Integer value) {
		setCellValue(setCellValueDto, columnNumber, isLock, value == null ? null : value.toString());
	}

	public void setYesNoCellValue(SetCellValueDto setCellValueDto, int columnNumber, boolean isLock, String value) {
		if (StringUtil.isNotEmpty(value)) {
			if (ConstantType.CONSTANT_YES.equalsIgnoreCase(value)) {
				value = ConstantType.CONSTANT_FULL_YES;
			} else if (ConstantType.CONSTANT_NO.equalsIgnoreCase(value)) {
				value = ConstantType.CONSTANT_FULL_NO;
			}
		}
		setCellValue(setCellValueDto, columnNumber, isLock, value);
	}

	public void setCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, List<String> value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		if (CollectionUtil.isNotEmpty(value)) {
			cell.setCellValue(value.stream()
				.filter(StringUtil::isNotEmpty)
				.collect(Collectors.joining(","))
			);
		}
		cell.setCellStyle(bodyStyle);
	}

	public void setCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, BigDecimal value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(Optional.ofNullable(value).orElse(new BigDecimal(0)).toPlainString());
		cell.setCellStyle(bodyStyle);
	}

	public void setCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, Integer value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(Optional.ofNullable(value).orElse(0));
		cell.setCellStyle(bodyStyle);
	}

	public void setCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, Long value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(Optional.ofNullable(value).orElse(0L));
		cell.setCellStyle(bodyStyle);
	}

	public void setNumberCellValueDefaultString(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, BigDecimal value) {
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		if (value != null){
			cell.setCellValue(value.toPlainString());
		} else {
			cell.setCellValue("");
		}
		cell.setCellStyle(bodyStyle);
	}

	/**
	 * if Boolean is true set Y
	 */
	public void setCellBooleanValueToYN(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, Boolean value, List<String> toYNStrFormatList) {
		String text;
		if (Boolean.TRUE.equals(value)) {
			text = toYNStrFormatList.get(0);
		} else {
			text = toYNStrFormatList.get(1);
		}
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(text);
		cell.setCellStyle(bodyStyle);
	}

	public void setCellBooleanValueToString(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, Boolean value, List<String> convertStringList) {
		String text;
		if (Boolean.TRUE.equals(value)) {
			text = convertStringList.get(0);
		} else {
			text = convertStringList.get(1);
		}
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(text);
		cell.setCellStyle(bodyStyle);
	}

	public String convertDecimalToDouble(BigDecimal value){
		DecimalFormat df = new DecimalFormat("#0.00");

		if(value!=null ){
			if(value.doubleValue()!=0)
				return df.format(value.doubleValue());
			else
				return "0";
		}
		return "";
	}

	public String zeroToNull(Long value) {
		if (value == null || value == 0L) {
			return null;
		} else {
			return String.valueOf(value);
		}
	}

	public String zeroToNull(Integer value) {
		if (value == null || value == 0L) {
			return null;
		} else {
			return String.valueOf(value);
		}
	}

	public void addErrorColumn(Workbook workbook, int colIndex, boolean isAddChineseHeader, boolean isUnLock) {
		addErrorColumn(workbook, colIndex, isAddChineseHeader, 16, isUnLock);
	}

	public void addErrorColumn(Workbook workbook, int colIndex, boolean isAddChineseHeader, int weight, boolean isUnLock) {
		Sheet dataSheet = workbook.getSheetAt(0);
		dataSheet.setColumnWidth(colIndex, (short) (weight * 256 + 184));
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		if(isUnLock){
			headerStyle.setLocked(false);
		}

		Row row = CellUtil.getRow(0, dataSheet);
		Cell cell = CellUtil.getCell(row, colIndex);
		cell.setCellValue("Error Reason");
		cell.setCellStyle(headerStyle);

		if (isAddChineseHeader) {
			Row chineseRow = CellUtil.getRow(1, dataSheet);
			Cell chineseCell = CellUtil.getCell(chineseRow, colIndex);
			chineseCell.setCellValue("錯誤原因");
			chineseCell.setCellStyle(headerStyle);
		}
	}

	public void setYseNoCellValue(Sheet sheet, CellStyle bodyStyle, int rowNumber, int columnNumber, String value) {
		if (StringUtil.isNotEmpty(value)) {
			if ("Y".equalsIgnoreCase(value)) {
				value = "Yes";
			} else if ("N".equalsIgnoreCase(value)) {
				value = "No";
			}
		}
		Row row = CellUtil.getRow(rowNumber, sheet);
		Cell cell = CellUtil.getCell(row, columnNumber);
		cell.setCellValue(Optional.ofNullable(value).orElse(""));
		cell.setCellStyle(bodyStyle);
	}

	public String reverseYN(String visible) {
		if ("Y".equalsIgnoreCase(visible)) {
			return "N";
		}
		if ("N".equalsIgnoreCase(visible)) {
			return "Y";
		}
		return visible;
	}

	public Map<String, String> addCodeAndShortDescMapByCode(List<SysParmDo> sysParmList, String segment, Integer platformId) {
		return SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, segment, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));
	}

	public String joinNonEmptyStrings(List<String> values){
		values = filterEmptyStrings(values);
		return values.isEmpty() ? null : String.join(",", values);
	}

	public List<String> filterEmptyStrings(List<String> values) {
		if (values != null && !values.isEmpty()) {
			return values.stream()
					.filter(str -> !str.isEmpty())
					.collect(Collectors.toList());
		}
		return List.of();
	}

	public boolean isVirtualStoreMerchant(SysParmDo sysParmDo, Integer merchantId){
		boolean isVirtualStoreMerchant = false;
		if (StringUtil.isNotEmpty(sysParmDo.getParmValue()) && merchantId != null) {
			List<String> merchantIds = Arrays.asList(sysParmDo.getParmValue().split(","));
			isVirtualStoreMerchant = merchantIds.contains(merchantId.toString());
		}

		return isVirtualStoreMerchant;
	}

	public Map<String, String> getDirtyDataVoucherTemplateTypeMap(Map<String, String> voucherTemplateTypeMap) {
		Map<String, String> map = new HashMap<>();
		voucherTemplateTypeMap.forEach((key, value) -> {
			map.put(key, value);
			map.put(key.substring(key.length() - 1), value);
		});
		return map;
	}
}
