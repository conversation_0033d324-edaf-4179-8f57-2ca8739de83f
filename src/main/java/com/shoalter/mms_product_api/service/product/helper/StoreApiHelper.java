package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductFieldsOptionsPidDto;
import com.shoalter.mms_product_api.service.product.pojo.request.StoreApiFindStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.request.StoreApiSearchProductFieldOptionsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreApiFindStoreResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreApiResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreSearchProductFieldOptionsMainResponseData;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Slf4j
public class StoreApiHelper {

	private static final String SERVICE_NAME = "Mms Store Api";

	@Value("${mms.store.api.url}")
	private String storeApiUrl;

	private final TokenHelper tokenHelper;
	private final HttpRequestHelper httpRequestHelper;

	public List<StoreApiFindStoreResponseDto> requestFindStore(UserDto userDto, StoreApiFindStoreRequestDto requestDto) {
		StoreApiResponseDto<List<StoreApiFindStoreResponseDto>> result = httpRequestHelper.requestForBody(HttpRequestDto.<StoreApiFindStoreRequestDto, StoreApiResponseDto<List<StoreApiFindStoreResponseDto>>>builder()
			.serviceName(SERVICE_NAME)
			.url(buildUrlWithParams(requestDto))
			.method(HttpMethod.GET)
			.customHeaders(generateHeaders(userDto))
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.MMS_STORE_API_GET_STORE_ERROR)
			.identifier("userId=" + userDto.getUserId().toString())
			.build());
		return result == null || result.getData() == null ? new ArrayList<>() : result.getData();
	}

	public List<ProductFieldsOptionsPidDto> getStoreProductFieldOptions(UserDto userDto, String storefrontStoreCode) {
		if (StringUtil.isEmpty(storefrontStoreCode)) {
			log.warn("getStoreProductFieldOptions Fail, storefrontStoreCode is empty, storefrontStoreCode:{}", storefrontStoreCode);
			return new ArrayList<>();
		}
		StoreApiResponseDto<List<ProductFieldsOptionsPidDto>> result = httpRequestHelper.requestForBody(HttpRequestDto.<String, StoreApiResponseDto<List<ProductFieldsOptionsPidDto>>>builder()
			.serviceName(SERVICE_NAME)
			.url(storeApiUrl + String.format("/api/stores/the-place/%s/product-field-options", storefrontStoreCode))
			.method(HttpMethod.GET)
			.customHeaders(generateHeaders(userDto))
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.MMS_STORE_API_GET_STORE_ERROR)
			.identifier("storefrontStoreCode=" + storefrontStoreCode)
			.build());
		return result == null || CollectionUtil.isEmpty(result.getData()) ? new ArrayList<>() : result.getData();
	}

	public StoreSearchProductFieldOptionsMainResponseData searchProductFieldOptionsApi(UserDto userDto, StoreApiSearchProductFieldOptionsRequestDto requestDto) {
		StoreApiResponseDto<StoreSearchProductFieldOptionsMainResponseData> result = httpRequestHelper.requestForBody(HttpRequestDto.<StoreApiSearchProductFieldOptionsRequestDto, StoreApiResponseDto<StoreSearchProductFieldOptionsMainResponseData>>builder()
			.serviceName(SERVICE_NAME)
			.url(storeApiUrl + "/api/stores/the-place/search/product-field-options")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.user(userDto)
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.systemErrorCode(ErrorMessageTypeCode.MMS_STORE_API_GET_STORE_ERROR)
			.build());

		return result == null ? null : result.getData();
	}

	private HttpHeaders generateHeaders(UserDto userDto) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("Authorization", "Bearer " + tokenHelper.generateToken(userDto));
		return headers;
	}

	private String buildUrlWithParams(StoreApiFindStoreRequestDto requestDto) {
		return UriComponentsBuilder
			.fromHttpUrl(storeApiUrl + "/api/stores/bu")
			.queryParamIfPresent("buCodes", Optional.ofNullable(requestDto.getBuCodes()))
			.queryParamIfPresent("merchantId", Optional.ofNullable(requestDto.getMerchantId()))
			.queryParamIfPresent("displayInactiveStore", Optional.ofNullable(requestDto.getDisplayInactiveStore()))
			.build()
			.toUriString();
	}
}
