package com.shoalter.mms_product_api.service.product.template;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.edit_column.ExtendedWarrantyTemplateColumnEnum;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.BatchUpdateEwProductBindingDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateExtendedWarrantyHelper extends AbstractReport implements IProductTemplateHelper<ExtendedWarrantyTemplateColumnEnum> {

	private final Gson gson;

	@Override
	public Workbook setTemplateBodyColumn(Workbook workbook, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList) {
		return null;
	}

	@Override
	public Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> rows, List<SysParmDo> sysParmList, SaveProductRecordDo record) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);

		rows.forEach(row -> {
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			BatchUpdateEwProductBindingDto batchUpdateEwProductBindingDto = singleEditProductDto.getBatchEditElement().getBatchUpdateEwProductBindingDto();
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), ExtendedWarrantyTemplateColumnEnum.PRODUCT_STORE_ID.getColumnNumber(), batchUpdateEwProductBindingDto.getStorefrontStoreCode());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), ExtendedWarrantyTemplateColumnEnum.PRODUCT_SKU_ID.getColumnNumber(), batchUpdateEwProductBindingDto.getSkuCode());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), ExtendedWarrantyTemplateColumnEnum.EW_STORE_ID.getColumnNumber(), reverseYN(batchUpdateEwProductBindingDto.getExtendedWarrantyStorefrontStoreCode()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), ExtendedWarrantyTemplateColumnEnum.EW_SKU_ID.getColumnNumber(), reverseYN(batchUpdateEwProductBindingDto.getExtendedWarrantySkuCode()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), ExtendedWarrantyTemplateColumnEnum.values().length, row.getErrorMessage());
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	@Override
	public int getColumnWidth(TemplateInterface<ExtendedWarrantyTemplateColumnEnum> columnEnum) {
		return columnEnum.getColumnName().length() * 400;
	}

	@Override
	public boolean isLockColumn(TemplateInterface<ExtendedWarrantyTemplateColumnEnum> columnEnum) {
		return false;
	}
}
