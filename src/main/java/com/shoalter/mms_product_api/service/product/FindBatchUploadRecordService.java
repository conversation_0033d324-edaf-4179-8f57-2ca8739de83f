package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SaveProductSource;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysUserRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.UserIdAndCodeDto;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.MerchantHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchUploadDto;
import com.shoalter.mms_product_api.service.product.pojo.EditHistoryDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class FindBatchUploadRecordService {

	private static final String FAIL = "FAIL";
	private static final String SUCCESS = "SUCCESS";
	private static final String PENDING = "PENDING";

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SysUserRepository sysUserRepository;

	private final MerchantHelper merchantHelper;

	public ResponseDto<PageDto<EditHistoryDto>> start(UserDto userDto, FindRecordHistoryRequestDto requestDto) {
		FindRecordHistoryDto findRecordHistoryDto = FindRecordHistoryDto.builder()
			.skuId(requestDto.getSkuId())
			.merchantName(requestDto.getMerchantName())
			.userId(userDto.getUserId())
			.statusList(generateStatusList(requestDto.getStatusList()))
			.merchantIdList(merchantHelper.findMerchantIdByRole(userDto))
			.filterOutUploadTypeList(generateFilterOutUploadTyeList(userDto))
			.uploadTypeList(requestDto.getUploadTypeList() == null ? List.of() : requestDto.getUploadTypeList())
			.startDate(requestDto.getStartTimestamp() == null ? null : new Date(requestDto.getStartTimestamp()))
			.endDate(requestDto.getEndTimestamp() == null ? null : new Date(requestDto.getEndTimestamp()))
			.build();

		Pageable pageable = PageUtil.createPageable(getDefault(requestDto.getPageSize(), 20),
			getDefault(requestDto.getPageNumber(), 1), "recordId", Sort.Direction.valueOf("DESC"));

		Page<BatchUploadDto> pageList = saveProductRecordRepository.findBatchUploadRecords(findRecordHistoryDto, pageable);
		List<EditHistoryDto> editHistoryDtos = generateEditHistoryDtos(pageList.getContent());

		long totalCount = pageList.getTotalElements();
		long pageCount = pageList.getTotalPages();
		PageDto<EditHistoryDto> page = PageDto.<EditHistoryDto>builder().list(editHistoryDtos).pageCount(pageCount).totalCount(totalCount).build();
		return ResponseDto.<PageDto<EditHistoryDto>>builder().data(page).status(1).build();
	}

	private Integer getDefault(Integer value, int defaultValue) {
		return value < 1 ? defaultValue : value;
	}

	private List<Integer> generateStatusList(List<String> statusSet) {
		List<Integer> statusList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(statusSet)) {
			if (statusSet.contains(SUCCESS)) {
				statusList.add(SaveProductStatus.SUCCESS);
			}
			if (statusSet.contains(FAIL)) {
				statusList.add(SaveProductStatus.FAIL);
			}
			if (statusSet.contains(PENDING)) {
				statusList.addAll(SaveProductStatus.PENDING_STATUS_LIST);
			}
		} else {
			statusList.add(SaveProductStatus.SUCCESS);
			statusList.add(SaveProductStatus.FAIL);
			statusList.addAll(SaveProductStatus.PENDING_STATUS_LIST);
		}
		return statusList;
	}

	private List<Integer> generateFilterOutUploadTyeList(UserDto userDto) {
		List<Integer> filterOutUploadTypeList = new ArrayList<>();
		if (!RoleCode.EW_SKU_ALLOW_ROLE_SET.contains(userDto.getRoleCode())) {
			filterOutUploadTypeList.add(SaveProductType.BATCH_CRATE_BINDING_EXTENDED_WARRANTY);
			filterOutUploadTypeList.add(SaveProductType.BATCH_DELETE_BINDING_EXTENDED_WARRANTY);
		}
		return filterOutUploadTypeList;
	}

	private List<EditHistoryDto> generateEditHistoryDtos(List<BatchUploadDto> batchUploadDtos) {
		List<Integer> uploadUserIdList = batchUploadDtos.stream().map(BatchUploadDto::getUploadUserId).collect(Collectors.toList());

		Map<Integer, String> userIdAndCodeDtos = sysUserRepository.findCodeAndIdByUserIds(uploadUserIdList).stream().collect(Collectors.toMap(UserIdAndCodeDto::getId, UserIdAndCodeDto::getUserCode));
		List<EditHistoryDto> editHistoryDtos = new ArrayList<>();
		for (BatchUploadDto batchUploadDto : batchUploadDtos) {

			String sourceIdentifier = batchUploadDto.getSourceIdentifier();
			if (sourceIdentifier == null) {
				Integer uploadUserId = batchUploadDto.getUploadUserId();
				if (Objects.equals(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemId(), uploadUserId) || Objects.equals(SystemUserEnum.SYSTEM.getSystemId(), uploadUserId)) {
					sourceIdentifier = SystemUserEnum.SYSTEM.getSystemCode();
				} else {
					sourceIdentifier = userIdAndCodeDtos.get(batchUploadDto.getUploadUserId());
				}
			}

			editHistoryDtos.add(EditHistoryDto.builder()
					.recordId(batchUploadDto.getRecordId())
					.uploadType(batchUploadDto.getUploadType())
					.fileName(batchUploadDto.getFileName())
					.status(batchUploadDto.getStatus())
					.uploadBy(sourceIdentifier)
					.dateOfUpload(batchUploadDto.getDateOfUpload())
					.build());
		}
		return editHistoryDtos;
	}

}
