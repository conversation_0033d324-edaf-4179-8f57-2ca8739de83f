package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.asynctask.CheckRequestPMRecordProductTask;
import com.shoalter.mms_product_api.asynctask.CreateSaveProductRecordTask;
import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CheckApprovalDealDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.product.helper.*;
import com.shoalter.mms_product_api.service.product.old_flow.helper.CheckEditProductFromPromotionContractHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantMatrixProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ResourceUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class SingleEditProductService {

	private final PermissionHelper permissionHelper;
	private final SaveProductHelper saveProductHelper;
	private final CheckProductHelper checkProductHelper;
	private final CheckEditProductFromPromotionContractHelper checkEditProductFromPromotionContractHelper;
	private final SyncBaseProductInfoHelper syncBaseProductInfoHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final ProductRepository productRepository;
	private final MessageSource messageSource;
	private final CreateSaveProductRecordTask createSaveProductRecordTask;
	private final Gson gson;
	private final ApprovalDealHelper approvalDealHelper;
	private final CheckRequestPMRecordProductTask checkRequestPMRecordProductTask;
	private final PromotionHelper promotionHelper;
	private final ProductPriceMonitorProductHelper productPriceMonitorProductHelper;
	private final ExchangeRateHelper exchangeRateHelper;

	// saveProductType: SINGLE_EDIT_PRODUCT || SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT
	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, SingleEditProductDto product, int saveProductType, String clientIp) {
		ResponseDto<ProductRecordResponseDto> responseDto = SpringBeanProvider.getBean(SingleEditProductService.class).processing(userDto, product, saveProductType, clientIp);
		if (StatusCodeEnum.SUCCESS.getCode() == responseDto.getStatus()) {
			SpringBeanProvider.getBean(SingleEditProductService.class).callProductMasterAndUpdateRecordRow(responseDto.getData().getRecordId());
		}
		return responseDto;
	}

	@Transactional
	public ResponseDto<ProductRecordResponseDto> processing(UserDto userDto, SingleEditProductDto product, int saveProductType, String clientIp) {
		// check user permission
		Integer merchantId = (product.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : product.getProduct().getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);

		// validate product matrix sku
		boolean hasMatrix = CollectionUtil.isNotEmpty(product.getVariantSkuProductList());
		if (hasMatrix) {
			List<String> errorMsg = checkMatrixSkuIsExistInStore(userDto, product);
			if (CollectionUtil.isNotEmpty(errorMsg)) {
				return ResponseDto.<ProductRecordResponseDto>builder().status(StatusCodeEnum.FAIL.getCode()).errorMessageList(errorMsg).build();
			}
		}

		convertHktvProductPhoto(product);

		// create save_product_record and row for edit a single product
		// SaveProductStatus: WAIT_START(-2)
		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(
			userDto,
			merchantId,
			saveProductType,
			String.format(SaveProductRecordHelper.EDIT_PRODUCT_FILE_NAME, product.getProduct().getSkuId(), System.currentTimeMillis()),
			SaveProductStatus.WAIT_START,
			clientIp);
		SaveProductRecordRowDo editeRow = saveProductRecordRowHelper.createProductRecordRowDo(
			saveProductRecordDo.getId(),
			product,
			SaveProductStatus.WAIT_START,
			null);
		product.getProduct().setRecordRowId(editeRow.getId());

		// generate matrix product
		if (hasMatrix) {
			createSaveProductRecordTask.generateMatrixProduct(userDto, product, clientIp);
		}

		// check edit product
		boolean isCheckProductsSuccess = checkAndGenerateProductData(userDto, editeRow, saveProductRecordDo);

		if (isCheckProductsSuccess) {
			ProductMasterDto baseProduct = gson.fromJson(editeRow.getContent(), SingleEditProductDto.class).getProduct();
			// check variant sku have original primary sku that need to set to non-primary
			Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair = syncBaseProductInfoHelper.findVariantProductsFromProductMaster(userDto, List.of(baseProduct));
			Pair<Boolean, String> passCheck = syncBaseProductInfoHelper.variantProductCheckAndUpdateRecordRows(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);
			if (!passCheck.getLeft()) {
				throw new BadRequestException(passCheck.getRight());
			}
			boolean createVariant = syncBaseProductInfoHelper.handlePrimarySkuAndAddVariantProduct(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);
			if (createVariant) {
				saveProductRecordDo.setFileName(String.format(SaveProductRecordHelper.VARIANT_EDIT_PRODUCT_FILE_NAME, baseProduct.getSkuId(), System.currentTimeMillis()));
			}

			List<SaveProductRecordRowDo> toNonPrimaryRecordRows =
				saveProductRecordRowRepository.findByRecordIdAndStatus(saveProductRecordDo.getId(), SaveProductStatus.PROCESSING);
			toNonPrimaryRecordRows.forEach(data -> {
				//prepare send to pm data
				prepareSendToPmData(data, saveProductRecordDo);
			});
		}

		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.<ProductRecordResponseDto>builder()
			.status(StatusCodeEnum.SUCCESS.getCode())
			.data(ProductRecordResponseDto.builder().recordId(saveProductRecordDo.getId()).build())
			.build();
	}

	private List<String> checkMatrixSkuIsExistInStore(UserDto userDto, SingleEditProductDto product) {
		List<String> emptyErrorList = new ArrayList<>();

		if (product.getProduct().getAdditional() != null && product.getProduct().getAdditional().getHktv() != null) {
			List<String> skuList = product.getVariantSkuProductList().stream().map(VariantMatrixProductDto::getSkuId).collect(Collectors.toList());
			String stores = product.getProduct().getAdditional().getHktv().getStores();

			CheckProductResultDto checkSkuIsExistResult = checkProductHelper.checkProductSkuExistsInStore(userDto, stores, skuList);
			return checkSkuIsExistResult.getErrorMessageList();
		}

		return emptyErrorList;
	}

	@Transactional
	public void callProductMasterAndUpdateRecordRow(Long recordId) {
		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
		List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.REQUESTING_PM);
		if (rows.isEmpty()) {
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return;
		}

		ProductMasterResponseDto productMasterResponseDto = checkRequestPMRecordProductTask.requestSendProductToProductMaster(saveProductRecordDo, rows);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterResponseDto, saveProductRecordDo, rows);
	}

	private void convertHktvProductPhoto(SingleEditProductDto product) {
		HktvProductDto hktvProductDto = product.getProduct().getAdditional().getHktv();
		if (hktvProductDto != null) {
			if (StringUtil.isNotEmpty(hktvProductDto.getMainPhoto())) {
				hktvProductDto.setMainPhoto(convertPhoto(hktvProductDto.getMainPhoto()));
			}
			if (StringUtil.isNotEmpty(hktvProductDto.getAdvertisingPhoto())) {
				hktvProductDto.setAdvertisingPhoto(convertPhoto(hktvProductDto.getAdvertisingPhoto()));
			}
			if (CollectionUtil.isNotEmpty(hktvProductDto.getVariantProductPhoto())) {
				hktvProductDto.setVariantProductPhoto(hktvProductDto.getVariantProductPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
			}
			if (CollectionUtil.isNotEmpty(hktvProductDto.getOtherPhoto())) {
				hktvProductDto.setOtherPhoto(hktvProductDto.getOtherPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
			}
		}
	}

	private String convertPhoto(String photo) {
		return ResourceUtil.existsImageDomain(photo) && photo.lastIndexOf("_1200.") != -1 ?
			photo.substring(0, photo.lastIndexOf("_1200.")) + photo.substring(photo.lastIndexOf(".")) :
			photo;
	}

	private boolean checkAndGenerateProductData(UserDto userDto, SaveProductRecordRowDo row, SaveProductRecordDo saveProductRecordDo) {
		boolean isCheckProductsSuccess = true;
		//request pm by product uuid to find product
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(row.getUuid())).build();
		List<ProductMasterResultDto> productList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		ProductMasterResultDto beforeProduct = productList.get(0);

		if (CollectionUtil.isEmpty(productList)) {
			setRecordAndRowFail(row, saveProductRecordDo, StringUtil.generateErrorMessage(List.of(messageSource.getMessage("message127", null, null))));
			isCheckProductsSuccess = false;
		} else if (beforeProduct.getAdditional().getHktv() == null) {
			setRecordAndRowFail(row, saveProductRecordDo, StringUtil.generateErrorMessage(List.of(messageSource.getMessage("message227", null, null))));
			isCheckProductsSuccess = false;
		} else {
			CheckApprovalDealDto checkApprovalDealResult = null;
			//generate relate data in row content
			CheckProductResultDto check3plResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, saveProductRecordDo, row, beforeProduct);
			generateIIDSDataHelper.generateIIDSData(row);


			String storeSkuId = beforeProduct.getAdditional().getHktv().getStoreSkuId();
			List<MembershipPricingEventSetDto> checkPricingResults = promotionHelper.checkMembershipPricingEventSet(userDto, List.of(storeSkuId));

			ResponseDto<Void> checkResult;
			if (saveProductRecordDo.getUploadType() == SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT) {
				checkResult = checkEditProductFromPromotionContractHelper.checkEditProductHandler(userDto, row);
			} else {
				ProductDo productDo = productRepository.findByUuid(row.getUuid()).get();
				MembershipPricingEventSetDto checkPricingResult = checkPricingResults == null ? null : checkPricingResults.get(0);
				BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);
				checkResult = checkProductHelper.checkEditProductHandler(userDto, saveProductRecordDo, row, check3plResult, beforeProduct, productDo, checkPricingResult, rmbRate);
				productPriceMonitorProductHelper.priceMonitorUpdateProcess(row, beforeProduct, userDto);
				checkApprovalDealResult = approvalDealHelper.processAndCheckApproval(userDto, row, beforeProduct, ApprovalDealTypeEnum.COMMISSION_RATE);
				if (CollectionUtil.isNotEmpty(checkApprovalDealResult.getErrorMessage())) {
					checkResult.getErrorMessageList().addAll(checkApprovalDealResult.getErrorMessage());
					checkResult = ResponseDto.fail(checkResult.getErrorMessageList());
				}
			}

			if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				if (checkApprovalDealResult != null && checkApprovalDealResult.isNeedToApproval()) {
					row = approvalDealHelper.convertWaitingApprovalFieldsFromOriginalValue(row, beforeProduct, ApprovalDealTypeEnum.COMMISSION_RATE);
				}
				//prepare send to pm data
				prepareSendToPmData(row, saveProductRecordDo);
			} else {
				setRecordAndRowFail(row, saveProductRecordDo, StringUtil.generateErrorMessage(checkResult.getErrorMessageList()));
				isCheckProductsSuccess = false;
			}
		}

		return isCheckProductsSuccess;
	}

	private void setRecordAndRowFail(SaveProductRecordRowDo row, SaveProductRecordDo saveProductRecordDo, String errorMessage) {
		row.setErrorMessage(errorMessage);
		row.setStatus(SaveProductStatus.FAIL);
		saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
	}

	private void prepareSendToPmData(SaveProductRecordRowDo row, SaveProductRecordDo saveProductRecordDo) {
		checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
		saveProductHelper.setFieldValueNullByRule(row);
		row.setStatus(SaveProductStatus.REQUESTING_PM);
	}
}
