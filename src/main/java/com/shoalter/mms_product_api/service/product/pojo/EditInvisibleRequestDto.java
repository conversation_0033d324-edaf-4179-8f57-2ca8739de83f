package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EditInvisibleRequestDto {

	@JsonProperty("invisible")
	@NotNull(message = "uuids cannot be null")
	private Boolean invisible;

	@JsonProperty("products")
	@NotEmpty(message = "products cannot be empty")
	private List<@Valid EditInvisibleProductRequestDto> products;
}
