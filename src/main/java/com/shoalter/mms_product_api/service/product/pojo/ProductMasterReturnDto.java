package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.product.edit_column.SaveProductRecordRowTempStatusEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowTempDo;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class ProductMasterReturnDto implements Serializable {
    private String uuid;
    private String result;
    @JsonProperty("failed_reason")
	@SerializedName("failed_reason")
    private List<String> failedReason;
	@JsonProperty("force_rollback")
	@SerializedName("force_rollback")
	private boolean forceRollback;

	public static ProductMasterReturnDto generate(SaveProductRecordRowTempDo recordInfo) {
		String status = recordInfo.getStatus() == SaveProductRecordRowTempStatusEnum.SUCCESS ?
			SaveProductRecordRowTempStatusEnum.SUCCESS.name() : SaveProductRecordRowTempStatusEnum.FAIL.name();

		List<String> errorMessages = recordInfo.getStatus() != SaveProductRecordRowTempStatusEnum.SUCCESS && recordInfo.getErrorMessage() != null ?
			List.of(recordInfo.getErrorMessage()) : null;

		return ProductMasterReturnDto.builder()
			.uuid(recordInfo.getContent().getUuid())
			.result(status)
			.failedReason(errorMessages)
			.forceRollback(recordInfo.getStatus() == SaveProductRecordRowTempStatusEnum.ROLLBACK_FAIL)
			.build();
	}
}
