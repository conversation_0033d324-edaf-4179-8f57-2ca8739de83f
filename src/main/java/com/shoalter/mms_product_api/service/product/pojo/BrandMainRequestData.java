package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.Max;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandMainRequestData {
	private String buCode;
	private Integer brandId;
	private String brand;
	private Integer page = 0;
	@Max(1000)
	private Integer size = 10;
	private String sort = "brandCode";
	private String direction = "asc";
}
