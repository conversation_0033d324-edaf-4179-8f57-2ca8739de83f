package com.shoalter.mms_product_api.service.product.helper;

import static com.shoalter.mms_product_api.config.type.ContractType.INSURANCE_CONTRACT_SET;
import static com.shoalter.mms_product_api.util.StringUtil.URL_PATTERN;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.CategoryConfig;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.ExpiryTypeEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductDeliverMethod;
import com.shoalter.mms_product_api.config.product.ProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductSource;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.config.type.PickupDaysType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.config.type.StorageTemperatureType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandDo;
import com.shoalter.mms_product_api.dao.repository.business.BuCategoryRestrictionRepository;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryOverseaRepository;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuCategoryRestrictionDo;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractProdTermsRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractTypeRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractTypeDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantStoreDo;
import com.shoalter.mms_product_api.dao.repository.product.EverutsBuyerRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreOverseaDeliveryRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreOverseaDeliveryDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveEverutsRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSavePartnerInfoRequestData;
import com.shoalter.mms_product_api.service.product.CheckProductMissingFieldService;
import com.shoalter.mms_product_api.service.product.FindPackingBoxTypeService;
import com.shoalter.mms_product_api.service.product.FindProductReadyMethodService;
import com.shoalter.mms_product_api.service.product.FindReadyDaysService;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckContractProdTermResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByHktvStoreProductMasterRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.EverutsInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductCheckDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductPartnerInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateResponseDto;
import com.shoalter.mms_product_api.util.BigDecimalUtil;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DataColorUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.ValidationCheckUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@SuppressWarnings("ALL")
@RequiredArgsConstructor
@Service
@Slf4j
public class CheckProductHelper {

    private static final String YOUTUBE_LINK_ID_REGEX = "[a-zA-Z0-9-_]+";
    private static final String SHELF_LIFE_REGEX = "^\\d+$";
    private static final String RM_CODE_REGEX = "^[A-Za-z0-9-|]+$";
    private static final String PHYSICAL_STORE_REGEX = "^[A-Za-z0-9-,]+$";
    private static final Pattern IMAGE_PATTERN_1 = Pattern.compile("<img.*src=(.*?)[^>]*?>");
    private static final Pattern IMAGE_PATTERN_2 = Pattern.compile("(http(s?):)([/|.\\w\\s-])*\\.(?:jpg|gif|png)");
    private static final Pattern TABLE_REGX_PATTERN = Pattern.compile("(</?tr[^>]*+>)|(</?td[^>]*+>)|(</?table[^>]*+>)");
	private static final Pattern BARCODE_PATTERN = Pattern.compile("[\\w!@#$%^&*:”<>?|=()+~\\-';\\\\,.]+");
	private final Set<String> PACKING_DIMENSION_UNIT = Set.of(ConstantType.PACKING_DIMENSION_UNIT_MM, ConstantType.PACKING_DIMENSION_UNIT_CM, ConstantType.PACKING_DIMENSION_UNIT_M);
	private final Set<String> WEIGHT_UNIT = Set.of(ConstantType.WEIGHT_UNIT_G, ConstantType.WEIGHT_UNIT_KG);
	private final int RETURN_DAYS_31_VALUE = 31;

	private final ProductStorePromotionMapper productStorePromotionMapper;
    private final ContractRepository contractRepository;
    private final ContractTypeRepository contractTypeRepository;
    private final ContractProdTermsRepository contractProdTermsRepository;
    private final SysParmRepository sysParmRepository;
    private final BuProductCategoryRepository buProductCategoryRepository;
    private final BuProductCategoryOverseaRepository buProductCategoryOverseaRepository;
    private final BrandRepository brandRepository;
    private final StoreRepository storeRepository;
    private final ProductRepository productRepository;
    private final StoreWarehouseRepository storeWarehouseRepository;
    private final MerchantStoreRepository merchantStoreRepository;
    private final SaveProductRecordRowRepository saveProductRecordRowRepository;
    private final StoreOverseaDeliveryRepository storeOverseaDeliveryRepository;
	private final BuCategoryRestrictionRepository buCategoryRestrictionRepository;
	private final EverutsBuyerRepository everutsBuyerRepository;

    private final ProductMasterHelper productMasterHelper;
    private final ThirdPartyHelper thirdPartyHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final MerchantHelper merchantHelper;
	private final PromotionHelper promotionHelper;
	private final SysParamHelper sysParamHelper;
	private final BuProductCategoryHelper buProductCategoryHelper;
    private final MessageSource messageSource;
	private final FindProductReadyMethodService findProductReadyMethodService;
	private final FindPackingBoxTypeService findPackBoxTypeService;
	private final FindReadyDaysService findReadyDaysService;
	private final ExchangeRateHelper exchangeRateHelper;

    private final Gson gson;
	private final CheckProductMissingFieldService checkProductMissingFieldService;

	public ResponseDto<Void> checkEditProductHandler(UserDto userDto, SaveProductRecordDo productRecord, SaveProductRecordRowDo row, CheckProductResultDto check3PlResult,
													 ProductMasterResultDto beforeProductFromPm, ProductDo beforeProductFromDb, MembershipPricingEventSetDto checkPricingResult, BigDecimal rmbRate) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = singleEditProductDto.getProduct();

		if (productMasterDto.getAdditional().getHktv() == null) {
			return ResponseDto.success(null);
		}

		if (beforeProductFromDb != null) {
			boolean isSameMerchantId = beforeProductFromDb.getMerchantId().equals(productMasterDto.getMerchantId());
			if (StringUtil.isNotEquals(beforeProductFromDb.getSkuCode(), productMasterDto.getSkuId()) && isSameMerchantId) {
				return ResponseDto.fail(List.of(messageSource.getMessage("message105", null, null)));
			}
			if (StringUtil.isNotEquals(beforeProductFromDb.getProductCode(), productMasterDto.getProductId()) && isSameMerchantId) {
				return ResponseDto.fail(List.of(messageSource.getMessage("message222", null, null)));
			}
		} else {
			return ResponseDto.fail(List.of(messageSource.getMessage("message51", null, null)));
		}

		StoreDo store = storeRepository.findHktvStoreByStoreCode(productMasterDto.getAdditional().getHktv().getStores()).orElse(null);
		if (store == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message69", null, null)));
		}

		Optional<BuProductCategoryDo> primaryCategory = buProductCategoryHelper.getBuProductCategoryDo(productMasterDto, beforeProductFromPm.getAdditional().getHktv());
		if (primaryCategory.isEmpty()) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message49", new String[]{productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode()}, null)));
		}

		boolean isNewProduct = false;
		boolean isOfflineDueToRollback = productMasterDto.getAdditional().getHktv().getOfflineDueToRollback() != null && productMasterDto.getAdditional().getHktv().getOfflineDueToRollback();
		ProductCheckDto productCheckDo = ProductCheckDto.generateProductCheckDo(productMasterDto, store, primaryCategory.get(), isNewProduct, isOfflineDueToRollback,
			beforeProductFromPm.getAdditional().getHktv(), check3PlResult, checkPricingResult);

		CheckProductResultDto checkProductResultDto = checkEditProduct(userDto, productCheckDo, beforeProductFromDb, beforeProductFromPm, productRecord, rmbRate);

		return ResponseDto.<Void>builder()
				.status(checkProductResultDto.getErrorMessageList().isEmpty() ? 1 : -1)
				.errorMessageList(checkProductResultDto.getErrorMessageList()).build();
	}

	public ResponseDto<Void> checkCreateProductHandler(UserDto userDto, SaveProductRecordDo saveProductRecordDo, SaveProductRecordRowDo row, CheckProductResultDto check3PlResult, BigDecimal rmbRate) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = singleEditProductDto.getProduct();

		if (productMasterDto.getAdditional().getHktv() == null) {
			return ResponseDto.<Void>builder().status(1).build();
		}

		StoreDo store = storeRepository.findHktvStoreByStoreCode(productMasterDto.getAdditional().getHktv().getStores()).orElse(null);
		if (store == null) {
			return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message69", null, null))).build();
		}

		Optional<BuProductCategoryDo> primaryCategory = buProductCategoryHelper.getBuProductCategoryDo(productMasterDto, null);
		if (primaryCategory.isEmpty()) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message49", new String[]{productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode()}, null)));
		}

		boolean isNewProduct = true;
		boolean isOfflineDueToRollback = productMasterDto.getAdditional().getHktv().getOfflineDueToRollback() != null
				&& productMasterDto.getAdditional().getHktv().getOfflineDueToRollback();
		ProductCheckDto productCheckDo = ProductCheckDto.generateProductCheckDo(productMasterDto, store, primaryCategory.get(), isNewProduct, isOfflineDueToRollback, null, check3PlResult, null);

		CheckProductResultDto checkProductResultDto = checkCreateProduct(
				userDto, productCheckDo, saveProductRecordDo, rmbRate);
		return ResponseDto.<Void>builder()
				.status(checkProductResultDto.getErrorMessageList().isEmpty() ? 1 : -1)
				.errorMessageList(checkProductResultDto.getErrorMessageList()).build();
	}

	private CheckProductResultDto checkCreateProduct(UserDto userDto, ProductCheckDto productCheckDo,
		SaveProductRecordDo saveProductRecordDo, BigDecimal rmbRate) {
		long startTime = System.currentTimeMillis();

		CheckProductResultDto checkProductResultDto = checkAllProduct(userDto, productCheckDo, null,
			null, saveProductRecordDo, rmbRate);
		List<String> errorMessageList = new ArrayList<>(checkProductResultDto.getErrorMessageList());
		CheckProductResultDto checkSkuIdResult = checkSkuId(productCheckDo.getSkuId());
		errorMessageList.addAll(checkSkuIdResult.getErrorMessageList());
		CheckProductResultDto checkPackingAndBarcodeResultDto = checkProductPackingAndBarcode(
			productCheckDo.getBuCode(), productCheckDo.getReadyMethodCode(),
			productCheckDo.getPrimaryCategory(), productCheckDo.getPackingBoxTypeCode(),
			productCheckDo.getWeight(), productCheckDo.getHeight(),
			productCheckDo.getDepth(), productCheckDo.getLength(), productCheckDo.getStorageTemperature(),
			productCheckDo.getBarcodeDtoList(), productCheckDo.getWeightUnit(),
			productCheckDo.getPackingDimensionUnit());
		errorMessageList.addAll(checkPackingAndBarcodeResultDto.getErrorMessageList());
		CheckContractProdTermResultDto checkContractProdTermResult = checkContractProdTerm(
			productCheckDo.getInsuranceContractProdTermName(), productCheckDo.getContractId(),
			productCheckDo.getStore().getId(), productCheckDo.getReadyMethodCode(),
			productCheckDo.getSkuId(), productCheckDo.getPrimaryCategory(), productCheckDo.getBrandId());
		errorMessageList.addAll(checkContractProdTermResult.getErrorMessageList());
		CheckProductResultDto checkSkuIsExistResult = checkProductSkuExistsInStore(userDto,
			productCheckDo.getStore().getStoreCode(), List.of(productCheckDo.getSkuId()));
		errorMessageList.addAll(checkSkuIsExistResult.getErrorMessageList());
		CheckProductResultDto checkEmojiResult = checkSkuNameContainEmoji(productCheckDo.getSkuNameEn(),
			productCheckDo.getSkuNameCh(), productCheckDo.getSkuNameSc());
		errorMessageList.addAll(checkEmojiResult.getErrorMessageList());
		CheckProductResultDto checkCreateMallDollarResult = checkCreateMallDollar(
			productCheckDo.getMallDollar(), productCheckDo.getVipMallDollar());
		errorMessageList.addAll(checkCreateMallDollarResult.getErrorMessageList());
		long endTime = System.currentTimeMillis();
		log.info("Time taken by checkCreateProduct method : {} milliseconds", (endTime - startTime));
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty())
			.errorMessageList(errorMessageList).build();

    }

	private CheckProductResultDto checkEditProduct(UserDto userDto, ProductCheckDto productCheckDto, ProductDo existProductDo,
												  ProductMasterResultDto beforeProduct, SaveProductRecordDo productRecord, BigDecimal rmbRate) {
        long startTime = System.currentTimeMillis();
		List<String> errorMessageList = new ArrayList<>();
		String contractType = null;
		switch (productRecord.getUploadType()) {
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				errorMessageList.addAll(checkVisibility(productCheckDto.getVisibility(), productCheckDto.getReadyMethodCode(), existProductDo.getStatus()).getErrorMessageList());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				errorMessageList.addAll(checkContractProdTerm(productCheckDto.getInsuranceContractProdTermName(), productCheckDto.getContractId(),
					productCheckDto.getStore().getId(), productCheckDto.getReadyMethodCode(), productCheckDto.getSkuId(), productCheckDto.getPrimaryCategory(), productCheckDto.getBrandId()).getErrorMessageList());
				errorMessageList.addAll(checkForceOfflineProductOnlineStatus(productCheckDto.getOnlineStatus(), beforeProduct).getErrorMessageList());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
				contractType = contractRepository.findMainContractTypeInContract(productCheckDto.getContractId());
				errorMessageList.addAll(checkPriceRelateFields(productCheckDto.getOriginalPrice(), productCheckDto.getCost(), productCheckDto.getCurrency(), productCheckDto.getReadyMethodCode(), contractType, rmbRate).getErrorMessageList());
				errorMessageList.addAll(checkProductPriceUpdateDuringPromotion(beforeProduct.getUuid(), productCheckDto, beforeProduct).getErrorMessageList());
				errorMessageList.addAll(checkOriginalPriceAndSellingPrice(productCheckDto.getOriginalPrice(), productCheckDto.getSellingPrice()).getErrorMessageList());
				errorMessageList.addAll(checkPlusPrice(productCheckDto.getOriginalPrice(), productCheckDto.getSellingPrice(), productCheckDto.getCheckPricingResult()).getErrorMessageList());
				errorMessageList.addAll(checkStyle(productCheckDto.getStyle()).getErrorMessageList());
				errorMessageList.addAll(checkDiscountText(productCheckDto.getDiscountTextEn(), productCheckDto.getDiscountTextCh(), productCheckDto.getDiscountTextSc()).getErrorMessageList());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				errorMessageList.addAll(checkPackingInformation(beforeProduct.getAdditional().getHktv().getProductReadyMethod(), productCheckDto.getWeight(), productCheckDto.getHeight(), productCheckDto.getDepth(),
					productCheckDto.getLength(), productCheckDto.getWeightUnit(), productCheckDto.getPackingDimensionUnit()).getErrorMessageList());
				errorMessageList.addAll(checkPackingBoxType(productCheckDto.getPackingBoxTypeCode(), beforeProduct.getStorageTemperature(), beforeProduct.getAdditional().getHktv().getProductReadyMethod(),
					productCheckDto.getPrimaryCategory().getProductCatCode()).getErrorMessageList());
				errorMessageList.addAll(checkPackingSpec(productCheckDto.getPackingSpecEn(), productCheckDto.getPackingSpecCh(), productCheckDto.getPackingSpecSc()).getErrorMessageList());
				errorMessageList.addAll(checkCartonSize(productCheckDto.getCartonSizeDtoList()).getErrorMessageList());
				errorMessageList.addAll(checkReadyMethodAndPackingBoxType(beforeProduct.getAdditional().getHktv().getProductReadyMethod(), productCheckDto.getPackingBoxTypeCode(), productCheckDto.getContractId(), productCheckDto.getPrimaryCategoryCode()).getErrorMessageList());
				errorMessageList.addAll(checkCategoryCodeAndPackingBoxType(productCheckDto.getBuCode(), productCheckDto.getPrimaryCategory(), productCheckDto.getPackingBoxTypeCode()).getErrorMessageList());
				if(productCheckDto.getCartonSizeDtoList() != null)
				{
					errorMessageList.addAll(checkCartonPackingInformation(productCheckDto.getCartonSizeDtoList().get(0).getHeight(), productCheckDto.getCartonSizeDtoList().get(0).getLength(),
						productCheckDto.getCartonSizeDtoList().get(0).getWidth()).getErrorMessageList());
				}
				errorMessageList.addAll(checkPackageConfirmed(beforeProduct, productCheckDto.getDepth(), productCheckDto.getHeight(),
					productCheckDto.getLength(), productCheckDto.getWeight(), productCheckDto.getPackingDimensionUnit(), productCheckDto.getWeightUnit()).getErrorMessageList());

				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				errorMessageList.addAll(checkSkuId(productCheckDto.getSkuId()).getErrorMessageList());
				errorMessageList.addAll(checkMembershipPricingEventSet(userDto, productCheckDto.getDeliveryDistrictList(), productCheckDto.getStore().getStorefrontStoreCode(), productCheckDto.getSkuId(), productCheckDto.getCheckPricingResult()).getErrorMessageList());
				break;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				contractType = contractRepository.findMainContractTypeInContract(productCheckDto.getContractId());
				errorMessageList.addAll(checkProductReadyDays(contractType, productCheckDto.getReadyMethodCode(), productCheckDto.getProductReadyDays()).getErrorMessageList());
				break;
			default:
				// BATCH_EDIT_PRODUCT only mms1.0 call mms2.0 use
				HktvProductDto beforeHktvProductDto = beforeProduct.getAdditional().getHktv();
				errorMessageList.addAll(checkAllProduct(userDto, productCheckDto, existProductDo.getStatus(), beforeProduct, productRecord, rmbRate).getErrorMessageList());
				errorMessageList.addAll(checkProductPackingAndBarcode(productCheckDto.getBuCode(), productCheckDto.getReadyMethodCode(), productCheckDto.getPrimaryCategory(),
					productCheckDto.getPackingBoxTypeCode(), productCheckDto.getWeight(), productCheckDto.getHeight(), productCheckDto.getDepth(), productCheckDto.getLength(),
					productCheckDto.getStorageTemperature(), productCheckDto.getBarcodeDtoList(), productCheckDto.getWeightUnit(), productCheckDto.getPackingDimensionUnit()).getErrorMessageList());
				errorMessageList.addAll(checkProductPriceUpdateDuringPromotion(existProductDo.getUuid(), productCheckDto, beforeProduct).getErrorMessageList());
				errorMessageList.addAll(checkPrimarySku(existProductDo, productCheckDto.getIsPrimarySku(), productRecord.getUploadType()).getErrorMessageList());
				errorMessageList.addAll(checkPackageConfirmed(beforeProduct, productCheckDto.getDepth(), productCheckDto.getHeight(),
					productCheckDto.getLength(), productCheckDto.getWeight(), productCheckDto.getPackingDimensionUnit(), productCheckDto.getWeightUnit()).getErrorMessageList());
				errorMessageList.addAll(checkBarcodeLock(beforeProduct, productCheckDto.getBarcodeDtoList()).getErrorMessageList());
				errorMessageList.addAll(checkContractProdTerm(productCheckDto.getInsuranceContractProdTermName(), productCheckDto.getContractId(),
					productCheckDto.getStore().getId(), productCheckDto.getReadyMethodCode(), productCheckDto.getSkuId(), productCheckDto.getPrimaryCategory(), productCheckDto.getBrandId()).getErrorMessageList());
				errorMessageList.addAll(checkSkuNameContainEmoji(productCheckDto.getSkuNameEn(), productCheckDto.getSkuNameCh(), productCheckDto.getSkuNameSc()).getErrorMessageList());
				errorMessageList.addAll(checkEditMallDollar(beforeHktvProductDto.getMallDollar(), beforeHktvProductDto.getVipMallDollar(), productCheckDto.getMallDollar(), productCheckDto.getVipMallDollar()).getErrorMessageList());
				errorMessageList.addAll(checkEditEwSku(productCheckDto.getPrimaryCategoryCode(), beforeHktvProductDto.getPrimaryCategoryCode()).getErrorMessageList());
				errorMessageList.addAll(checkUpdateEverutsField(productCheckDto, beforeProduct).getErrorMessageList());
				// check cannot change fields
				CheckProductResultDto checkCannotChangeFieldsResult = checkCannotChangeFields(productCheckDto, existProductDo);
				errorMessageList.addAll(checkCannotChangeFieldsResult.getErrorMessageList());
				CheckProductResultDto checkMembershipPricingEventSetResultDto= checkMembershipPricingEventSet(userDto, productCheckDto.getDeliveryDistrictList(), productCheckDto.getStore().getStorefrontStoreCode(), productCheckDto.getSkuId(), productCheckDto.getCheckPricingResult());
				errorMessageList.addAll(checkMembershipPricingEventSetResultDto.getErrorMessageList());
				CheckProductResultDto checkPlusPriceResultDto = checkPlusPrice(productCheckDto.getOriginalPrice(), productCheckDto.getSellingPrice(), productCheckDto.getCheckPricingResult());
				errorMessageList.addAll(checkPlusPriceResultDto.getErrorMessageList());
				CheckProductResultDto checkProductReadyMethodWithPrimaryCategoryOrBrandModifiedResult = checkProductReadyMethodWithPrimaryCategoryOrBrandModified(productCheckDto, beforeProduct);
				errorMessageList.addAll(checkProductReadyMethodWithPrimaryCategoryOrBrandModifiedResult.getErrorMessageList());
				CheckProductResultDto checkedForceOfflineProductOnlineStatusResult = checkForceOfflineProductOnlineStatus(productCheckDto.getOnlineStatus(), beforeProduct);
				errorMessageList.addAll(checkedForceOfflineProductOnlineStatusResult.getErrorMessageList());
				break;
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by checkEditProduct method : {} milliseconds", (endTime - startTime));
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkCreateMallDollar(BigDecimal mallDollar, BigDecimal vipMallDollar) {
		List<String> errorMessage = new ArrayList<>();

		if (mallDollar != null && mallDollar.compareTo(BigDecimal.ZERO) != 0) {
			errorMessage.add(messageSource.getMessage("message242", null, null));
		}

		if (vipMallDollar != null && vipMallDollar.compareTo(BigDecimal.ZERO) != 0) {
			errorMessage.add(messageSource.getMessage("message243", null, null));
		}

		return CheckProductResultDto.generate(errorMessage);
	}

	private CheckProductResultDto checkEditMallDollar(BigDecimal beforeMallDollar, BigDecimal beforeVipMallDollar, BigDecimal afterMallDollar, BigDecimal afterVipMallDollar) {
		List<String> errorMessage = new ArrayList<>();
		if (beforeMallDollar == null) {
			beforeMallDollar = BigDecimal.ZERO;
		}
		if (beforeVipMallDollar == null) {
			beforeVipMallDollar = BigDecimal.ZERO;
		}
		if (afterMallDollar == null) {
			afterMallDollar = BigDecimal.ZERO;
		}
		if (afterVipMallDollar == null) {
			afterVipMallDollar = BigDecimal.ZERO;
		}

		if (beforeMallDollar.compareTo(afterMallDollar) != 0) {
			errorMessage.add(messageSource.getMessage("message242", null, null));
		}
		if (beforeVipMallDollar.compareTo(afterVipMallDollar) != 0) {
			errorMessage.add(messageSource.getMessage("message243", null, null));
		}

		return CheckProductResultDto.generate(errorMessage);
	}

	private CheckProductResultDto checkAllProduct(UserDto userDto, ProductCheckDto productCheckDto,
												  String productStatus, ProductMasterResultDto beforeProduct, SaveProductRecordDo productRecord, BigDecimal rmbRate){
		String contractType = contractRepository.findMainContractTypeInContract(productCheckDto.getContractId());

		CheckProductResultDto checkProductIdResult = checkProductId(productCheckDto.getProductId());
		List<String> errorMessageList = new ArrayList<>(checkProductIdResult.getErrorMessageList());
		CheckProductResultDto checkProductReadyMethodResult = checkProductReadyMethod(productCheckDto.getReadyMethodCode(), productCheckDto.getCheck3PlResult());
		errorMessageList.addAll(checkProductReadyMethodResult.getErrorMessageList());
		CheckProductResultDto checkSkuNameResult = checkSkuName(productCheckDto.getSkuNameEn(), productCheckDto.getSkuNameCh(), productCheckDto.getSkuNameSc());
		errorMessageList.addAll(checkSkuNameResult.getErrorMessageList());
		CheckProductResultDto checkBrandResult = checkBrandApproved(productCheckDto.getBrandId());
		errorMessageList.addAll(checkBrandResult.getErrorMessageList());
		CheckProductResultDto checkMallDollarResult = checkMallDollar(userDto, productCheckDto.getBuCode(), productCheckDto.getMallDollar(), productCheckDto.getVipMallDollar());
		errorMessageList.addAll(checkMallDollarResult.getErrorMessageList());
		CheckProductResultDto checkWarehouseResult = checkWarehouse(productCheckDto.getBuCode(), productCheckDto.getReadyMethodCode(), productCheckDto.getWarehouseId(),productCheckDto.getStorageType());
		errorMessageList.addAll(checkWarehouseResult.getErrorMessageList());
		CheckProductResultDto checkVideoLinkResult = checkVideoLink(productCheckDto.getVideoLink());
		errorMessageList.addAll(checkVideoLinkResult.getErrorMessageList());
		CheckProductResultDto checkVideoLinkTextResult = checkVideoLinkText(productCheckDto.getVideoLinkTextEn(), productCheckDto.getVideoLinkTextCh(), productCheckDto.getVideoLinkTextSc(), 1);
		errorMessageList.addAll(checkVideoLinkTextResult.getErrorMessageList());
		CheckProductResultDto checkVideoLink2Result = checkVideoLink(productCheckDto.getVideoLink2());
		errorMessageList.addAll(checkVideoLink2Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText2Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn2(), productCheckDto.getVideoLinkTextCh2(), productCheckDto.getVideoLinkTextSc2(), 2);
		errorMessageList.addAll(checkVideoLinkText2Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink3Result = checkVideoLink(productCheckDto.getVideoLink3());
		errorMessageList.addAll(checkVideoLink3Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText3Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn3(), productCheckDto.getVideoLinkTextCh3(), productCheckDto.getVideoLinkTextSc3(), 3);
		errorMessageList.addAll(checkVideoLinkText3Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink4Result = checkVideoLink(productCheckDto.getVideoLink4());
		errorMessageList.addAll(checkVideoLink4Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText4Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn4(), productCheckDto.getVideoLinkTextCh4(), productCheckDto.getVideoLinkTextSc4(), 4);
		errorMessageList.addAll(checkVideoLinkText4Result.getErrorMessageList());
		CheckProductResultDto checkVideoLink5Result = checkVideoLink(productCheckDto.getVideoLink5());
		errorMessageList.addAll(checkVideoLink5Result.getErrorMessageList());
		CheckProductResultDto checkVideoLinkText5Result = checkVideoLinkText(productCheckDto.getVideoLinkTextEn5(), productCheckDto.getVideoLinkTextCh5(), productCheckDto.getVideoLinkTextSc5(), 5);
		errorMessageList.addAll(checkVideoLinkText5Result.getErrorMessageList());
		if (!productCheckDto.isSkuOfflineOrWillBeOffline()) {
			CheckProductResultDto checkPrimaryCategoryResult = checkPrimaryCategory(productCheckDto.getBuCode(), productCheckDto.getPrimaryCategory(), productCheckDto.getProductTypeCodeList(), productCheckDto.getReadyMethodCode());
			errorMessageList.addAll(checkPrimaryCategoryResult.getErrorMessageList());
		}
		CheckProductResultDto checkBatchBrandResult = checkBatchBrand(productCheckDto.isNewProduct(), productCheckDto.getMerchantId(), productCheckDto.getStore().getId(), productCheckDto.getProductId(), productCheckDto.getBrandId(), productCheckDto.isOfflineDueToRollback());
		errorMessageList.addAll(checkBatchBrandResult.getErrorMessageList());
		CheckProductResultDto checkUrgentFlagResult = checkUrgentFlagUpdatedByMerchant(userDto, productCheckDto.getBeforeUrgent(), productCheckDto.getUrgent());
		errorMessageList.addAll(checkUrgentFlagResult.getErrorMessageList());
		CheckProductResultDto checkReadyMethodAndPackingBoxTypeResult = checkReadyMethodAndPackingBoxType(productCheckDto.getReadyMethodCode(), productCheckDto.getPackingBoxTypeCode(), productCheckDto.getContractId(), productCheckDto.getPrimaryCategoryCode());
		errorMessageList.addAll(checkReadyMethodAndPackingBoxTypeResult.getErrorMessageList());
		CheckProductResultDto checkLandMarkCategoryResult = checkProductLandMarkCat(productCheckDto.getBuCode(), productCheckDto.getStore(), productCheckDto.getProductTypeCodeList());
		errorMessageList.addAll(checkLandMarkCategoryResult.getErrorMessageList());
		CheckProductResultDto checkWeightLimitByDeliveryResult = checkWeightLimitByDeliveryMethod(productCheckDto.getBuCode(), productCheckDto.getDeliveryMethodCode(), productCheckDto.getWeight(), productCheckDto.getWeightUnit());
		errorMessageList.addAll(checkWeightLimitByDeliveryResult.getErrorMessageList());
		CheckProductResultDto checkTimeSlotResult = checkTimeSlot(productCheckDto.getReadyMethodCode(), productCheckDto.getPickupTimeslot());
		errorMessageList.addAll(checkTimeSlotResult.getErrorMessageList());
		CheckProductResultDto checkBuySellStoreResult = checkBuySellStore(productCheckDto.getMerchantId(), productCheckDto.getReadyMethodCode(), productCheckDto.getRmCode(), productCheckDto.getPreSellFruit(), productCheckDto.getPhysicalStore(), productCheckDto.isNewProduct(), productCheckDto.getBeforeRmcode());
		errorMessageList.addAll(checkBuySellStoreResult.getErrorMessageList());
		CheckProductResultDto checkDescriptionResult = checkDescription(productCheckDto.getSkuSDescHktvEn(), productCheckDto.getSkuSDescHktvCh(), productCheckDto.getSkuSDescHktvSc(), productCheckDto.getSkuLDescHktvEn(), productCheckDto.getSkuLDescHktvCh(), productCheckDto.getSkuLDescHktvSc());
		errorMessageList.addAll(checkDescriptionResult.getErrorMessageList());
		CheckProductResultDto checkProductDataForUploadResult = checkProductDataForUpload(productCheckDto.getProductId(), productCheckDto.getSkuId(), productCheckDto.getReadyMethodCode(), productCheckDto.getMainPhoto(), productCheckDto.getOtherPhoto(), productCheckDto.getVariantProductPhoto(), productCheckDto.getAdvertisePhoto());
		errorMessageList.addAll(checkProductDataForUploadResult.getErrorMessageList());
		CheckProductResultDto checkPickupDayResult = checkPickupDay(productCheckDto.getPickupDays(), productCheckDto.getStore().getId(), productCheckDto.getReadyMethodCode());
		errorMessageList.addAll(checkPickupDayResult.getErrorMessageList());
		CheckProductResultDto checkOptionFieldAndValueResult = checkOptionFieldAndValue(productCheckDto.getField1(), productCheckDto.getValue1(), productCheckDto.getField2(), productCheckDto.getValue2(), productCheckDto.getField3(), productCheckDto.getValue3());
		errorMessageList.addAll(checkOptionFieldAndValueResult.getErrorMessageList());
		CheckProductResultDto checkSizeSystemResult = checkSizeSystem(productCheckDto.getSizeSystem(), productCheckDto.getSize());
		errorMessageList.addAll(checkSizeSystemResult.getErrorMessageList());
		CheckProductResultDto checkColorFamilyResult = checkColorFamily(productCheckDto.getColorFamiliar(), productCheckDto.getColorEn());
		errorMessageList.addAll(checkColorFamilyResult.getErrorMessageList());
		CheckProductResultDto checkPricesResult = checkPriceRelateFields(productCheckDto.getOriginalPrice(), productCheckDto.getCost(), productCheckDto.getCurrency(), productCheckDto.getReadyMethodCode(), contractType, rmbRate);
		errorMessageList.addAll(checkPricesResult.getErrorMessageList());
		CheckProductResultDto checkMinimumShelfLifeResult = checkMinimumShelfLife(productCheckDto.getMinimumShelfLife());
		errorMessageList.addAll(checkMinimumShelfLifeResult.getErrorMessageList());
		CheckProductResultDto checkRemovalServiceResult = checkRemovalService(productCheckDto.getRemovalService(), productCheckDto.getProductTypeCodeList(), productCheckDto.getPrimaryCategory());
		errorMessageList.addAll(checkRemovalServiceResult.getErrorMessageList());
		List<String> originalOverseaDeliveryDistrictList = beforeProduct == null ? null : beforeProduct.getAdditional().getHktv().getDeliveryDistrict();
		CheckProductResultDto checkOverseaDeliveryDistrictFormatResult = checkOverseaDeliveryDistrict(productRecord.getSource(), userDto, productCheckDto.getDeliveryDistrictList(),
			productCheckDto.getStore(), productCheckDto.getPrimaryCategory(), productCheckDto.getDeliveryMethodCode(), originalOverseaDeliveryDistrictList, productCheckDto.getReadyMethodCode());
		errorMessageList.addAll(checkOverseaDeliveryDistrictFormatResult.getErrorMessageList());
		CheckProductResultDto checkDeliveryMethodResult = checkDeliveryMethod(productCheckDto.getDeliveryMethodCode(), productCheckDto.getReadyMethodCode(), contractType);
		errorMessageList.addAll(checkDeliveryMethodResult.getErrorMessageList());
		CheckProductResultDto checkEvoucherAndContractTypeResult = checkEvoucherAndContractType(productCheckDto.getReadyMethodCode(), contractType, productCheckDto.getPrimaryCategory());
		errorMessageList.addAll(checkEvoucherAndContractTypeResult.getErrorMessageList());
		CheckProductResultDto checkInsuranceAndContractTypeResult = checkInsuranceAndContractType(contractType, productCheckDto.getPrimaryCategory());
		errorMessageList.addAll(checkInsuranceAndContractTypeResult.getErrorMessageList());
		CheckProductResultDto checkVirtualStoreResult = checkVirtualStore(productCheckDto.getMerchantId(), productCheckDto.getVirtualStore());
		errorMessageList.addAll(checkVirtualStoreResult.getErrorMessageList());
		CheckProductResultDto checkUserMaxResult = checkUserMax(contractType, productCheckDto.getUserMax(), userDto.getRoleCode(), productCheckDto.getBeforeUserMax());
		errorMessageList.addAll(checkUserMaxResult.getErrorMessageList());
		CheckProductResultDto checkStorageTypeResult = checkStorageType(productCheckDto.getBuCode(), productCheckDto.isNewProduct(), productCheckDto.getReadyMethodCode(), productCheckDto.getStorageType(), productCheckDto.getBeforeStorageType());
		errorMessageList.addAll(checkStorageTypeResult.getErrorMessageList());
		CheckProductResultDto checkProductTypeResult = checkProductType(productCheckDto.getBuCode(), productCheckDto.getProductTypeCodeList(), productCheckDto.isSkuOfflineOrWillBeOffline());
		errorMessageList.addAll(checkProductTypeResult.getErrorMessageList());
		CheckProductResultDto checkProductReadyDaysResult = checkProductReadyDays(contractType, productCheckDto.getReadyMethodCode(), productCheckDto.getProductReadyDays());
		errorMessageList.addAll(checkProductReadyDaysResult.getErrorMessageList());
		CheckProductResultDto checkBarcodeResult = checkBarcode(productCheckDto.getBarcodeDtoList());
		errorMessageList.addAll(checkBarcodeResult.getErrorMessageList());
		CheckProductResultDto checkReturnDayResult = checkReturnDay(productCheckDto.getReturnDays(),
			contractType);
		errorMessageList.addAll(checkReturnDayResult.getErrorMessageList());
		CheckProductResultDto checkCountryResult = checkManufactureCountry(productCheckDto.getManufactureCountry());
		errorMessageList.addAll(checkCountryResult.getErrorMessageList());
		CheckProductResultDto checkMainPhotoResult = checkMainPhoto(productCheckDto.getMainPhoto());
		errorMessageList.addAll(checkMainPhotoResult.getErrorMessageList());
		CheckProductResultDto checkAffiliateUrlResult = checkAffiliateUrl(productCheckDto.getAffiliateUrl(), productCheckDto.getReadyMethodCode(), contractType);
		errorMessageList.addAll(checkAffiliateUrlResult.getErrorMessageList());
		CheckProductResultDto checkDiscountTextResult = checkDiscountText(productCheckDto.getDiscountTextEn(), productCheckDto.getDiscountTextCh(), productCheckDto.getDiscountTextSc());
		errorMessageList.addAll(checkDiscountTextResult.getErrorMessageList());
		CheckProductResultDto checkPackingSpecResult = checkPackingSpec(productCheckDto.getPackingSpecEn(), productCheckDto.getPackingSpecCh(), productCheckDto.getPackingSpecSc());
		errorMessageList.addAll(checkPackingSpecResult.getErrorMessageList());
		CheckProductResultDto checkCartonSizeResult = checkCartonSize(productCheckDto.getCartonSizeDtoList());
		errorMessageList.addAll(checkCartonSizeResult.getErrorMessageList());
		CheckProductResultDto checkVisibilityResult = checkVisibility(productCheckDto.getVisibility(), productCheckDto.getReadyMethodCode(), productStatus);
		errorMessageList.addAll(checkVisibilityResult.getErrorMessageList());
		CheckProductResultDto checkDateResult = checkDateAndExpiryType(productCheckDto.getRedeemStartDate(), productCheckDto.getFixRedemptionDate(), productCheckDto.getFeatureStartDate(), productCheckDto.getFeatureEndDate(), productCheckDto.getUponPurchaseDate(), productCheckDto.getExpiryType());
		errorMessageList.addAll(checkDateResult.getErrorMessageList());
		CheckProductResultDto checkVoucherTypeAndExpiryTypeResult = checkVoucherType(productCheckDto.getVoucherType(), productCheckDto.getVoucherDisplayType(), productCheckDto.getVoucherTemplateType());
		errorMessageList.addAll(checkVoucherTypeAndExpiryTypeResult.getErrorMessageList());
		CheckProductResultDto checkFinePrintResult = checkFinePrint(productCheckDto.getFinePrintEn(), productCheckDto.getFinePrintCh(), productCheckDto.getFinePrintSc());
		errorMessageList.addAll(checkFinePrintResult.getErrorMessageList());
		CheckProductResultDto checkGoodsTypeResult = checkGoodsType(productCheckDto.getGoodsType());
		errorMessageList.addAll(checkGoodsTypeResult.getErrorMessageList());
		CheckProductResultDto checkWarrentyResult = checkWarrenty(productCheckDto.getWarrantyPeriodUnit(), productCheckDto.getWarrantyPeriod(), productCheckDto.getWarrantySupplierEn(),
			productCheckDto.getWarrantySupplierCh(), productCheckDto.getWarrantySupplierSc(), productCheckDto.getWarrantyRemarkEn(), productCheckDto.getWarrantyRemarkCh(), productCheckDto.getWarrantyRemarkSc());
		errorMessageList.addAll(checkWarrentyResult.getErrorMessageList());
		CheckProductResultDto checkServiceCentreAndInvoiceRemarksResult = checkServiceCentreAndInvoiceRemarks(productCheckDto.getServiceCentreAddressEn(), productCheckDto.getServiceCentreAddressCh(), productCheckDto.getServiceCentreAddressSc(),
			productCheckDto.getServiceCentreEmail(), productCheckDto.getServiceCentreContact(), productCheckDto.getInvoiceRemarksEn(), productCheckDto.getInvoiceRemarksCh(), productCheckDto.getInvoiceRemarksSc());
		errorMessageList.addAll(checkServiceCentreAndInvoiceRemarksResult.getErrorMessageList());
		CheckProductResultDto checkStyleResult = checkStyle(productCheckDto.getStyle());
		errorMessageList.addAll(checkStyleResult.getErrorMessageList());
		if (!productCheckDto.isSkuOfflineOrWillBeOffline()) {
			CheckProductResultDto checkProductTypeByContractTypeCode = checkProductCategoryByContractTypeCode(contractType, productCheckDto.getProductTypeCodeList());
			errorMessageList.addAll(checkProductTypeByContractTypeCode.getErrorMessageList());
		}
		CheckProductResultDto checkOriginalPriceAndSellingPriceResult = checkOriginalPriceAndSellingPrice(productCheckDto.getOriginalPrice(), productCheckDto.getSellingPrice());
		errorMessageList.addAll(checkOriginalPriceAndSellingPriceResult.getErrorMessageList());
		CheckProductResultDto checkEwSkuResult = checkEwSku(productCheckDto);
		errorMessageList.addAll(checkEwSkuResult.getErrorMessageList());
		CheckProductResultDto checkExternalPlatformResult = checkExternalPlatorm(userDto, productCheckDto.getExternalPlatform(), beforeProduct);
		errorMessageList.addAll(checkExternalPlatformResult.getErrorMessageList());
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public CheckProductResultDto checkProductPackingAndBarcode(
		String buCode, String readyMethodCode, BuProductCategoryDo primaryCategory,
		String packingBoxTypeCode, BigDecimal weight, BigDecimal height, BigDecimal depth, BigDecimal length,
		String storageTemperature, List<ProductBarcodeDto> barcodeDtoList, String weightUnit, String packingDimensionUnit) {
		List<String> errorMessageList = new ArrayList<>();
		errorMessageList.addAll(checkCategoryCodeAndPackingBoxType(buCode, primaryCategory, packingBoxTypeCode).getErrorMessageList());
		errorMessageList.addAll(checkPackingInformation(readyMethodCode, weight, height, depth, length, weightUnit, packingDimensionUnit).getErrorMessageList());
		errorMessageList.addAll(checkPackingBoxType(packingBoxTypeCode, storageTemperature, readyMethodCode, primaryCategory.getProductCatCode()).getErrorMessageList());
		errorMessageList.addAll(checkBarCode(barcodeDtoList, readyMethodCode).getErrorMessageList());
		return CheckProductResultDto.builder().result(errorMessageList.isEmpty()).errorMessageList(errorMessageList).build();
	}

	public CheckProductResultDto checkSkuId(String id) {
		List<String> errorMessageList = new ArrayList<>();
		if (!id.matches(ConstantType.REGEX_CONDITION_PRODUCT_CODE)) {
			errorMessageList.add(messageSource.getMessage("message65", new String[]{id}, null));
		}
		if(id.length() > ConstantType.MAX_CHARACTERS_39){
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU ID length" , String.valueOf(ConstantType.MAX_CHARACTERS_39)}, null));
		}
		if (id.contains("_S_")) {
			errorMessageList.add(messageSource.getMessage("message64", new String[]{id}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

    public CheckProductResultDto checkProductId(String id) {
        List<String> errorMessageList = new ArrayList<>();
        if (!id.matches(ConstantType.REGEX_CONDITION_PRODUCT_CODE)) {
            errorMessageList.add(messageSource.getMessage("message53", new String[]{id}, null));
        }
        if (id.contains("_S_")) {
            errorMessageList.add(messageSource.getMessage("message52", new String[]{id}, null));
        }
		if(id.length() > ConstantType.MAX_CHARACTERS_244){
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"Product ID length" , String.valueOf(ConstantType.MAX_CHARACTERS_244)}, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkManufactureCountry(String manufactureCountry) {
		List<String> errorMessageList = new ArrayList<>();

		Set<String> countryCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.COUNTRY_OF_ORIGIN.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (manufactureCountry != null && !countryCodes.contains(manufactureCountry)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Manufactured Country"}, null));
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkProductType(String buCode, List<String> productTypeCodeList, boolean skuOfflineOrWillBeOffline) {
        List<String> errorMessageList = new ArrayList<>();
		if (CollectionUtil.isEmpty(productTypeCodeList)) {
			errorMessageList.add(messageSource.getMessage("message188", null, null));
		}

		if (!skuOfflineOrWillBeOffline) {
			for (String productTypeCode : productTypeCodeList) {
				BuProductCategoryDo buProductCategoryDo = buProductCategoryRepository.findByBuCodeAndProductCatCode(buCode, productTypeCode);
				if (buProductCategoryDo != null) {
					String subCode = productTypeCode.substring(productTypeCode.length() - 1);
					if (!"1".equalsIgnoreCase(subCode)) {
						errorMessageList.add(messageSource.getMessage("message30", null, null));
					}
				} else {
					errorMessageList.add(messageSource.getMessage("message56", new String[]{String.valueOf(productTypeCode)}, null));
				}
			}
		}

		if (!productTypeCodeList.isEmpty() && productTypeCodeList.size() > ConstantType.MAX_PRODUCT_TYPE_CODE_SIZE) {
			errorMessageList.add(messageSource.getMessage("message246", null, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkPrimaryCategory(String buCode, BuProductCategoryDo primaryCategoryCodeDo, List<String> productTypeCodeList, String productReadyMethod) {
        List<String> errorMessageList = new ArrayList<>();
        BuProductCategoryDo buProductCategoryDo = buProductCategoryRepository.findByBuCodeAndIndex(buCode, primaryCategoryCodeDo.getId());
        if (buProductCategoryDo == null) {
            errorMessageList.add(messageSource.getMessage("message47", null, null));
        } else {
            String primaryCategoryCode = buProductCategoryDo.getProductCatCode();
            int count = sysParmRepository.countBySegmentAndBuCodeAndParmValue("PRIMARY_CAT_LIST", buCode, primaryCategoryCode);
            if (count > 0) {
                errorMessageList.add(messageSource.getMessage("message47", null, null));
            }

			String categoryPrefix = primaryCategoryCode.substring(0, 4);
			if ((CategoryConfig.SERVICE_DEALS_CATEGORY_PREFIX.equals(categoryPrefix) && !ProductReadyMethodType.RESTRICT_SERVICE_DEALS_CATEGORY_SET.contains(productReadyMethod)) ||
				(CategoryConfig.INSURANCE_CATEGORY_PREFIX.equals(categoryPrefix) && !ProductReadyMethodType.RESTRICT_INSURANCE_CATEGORY_SET.contains(productReadyMethod))) {
				errorMessageList.add(messageSource.getMessage("message277", new String[]{primaryCategoryCode, productReadyMethod}, null));
			}

        }

		if(!productTypeCodeList.isEmpty() && !productTypeCodeList.contains(primaryCategoryCodeDo.getProductCatCode())){
			errorMessageList.add(messageSource.getMessage("message247", null, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckContractProdTermResultDto checkContractProdTerm(
            String insuranceContractProdTermName, Integer contractId, Integer storeId, String productReadyMethodCode, String skuId,
            BuProductCategoryDo primaryCategory, Integer brandId) {
        List<String> errorMessageList = new ArrayList<>();


        ContractProdTermsDo contractProdTermsDo = null;
        //If select contract type is “Fixed cost contract”
        //Need to bypass the lookup terms logic, direct return the user selected terms to screen
        ContractTypeDo contractTypeDo = contractTypeRepository.findByContractId(contractId);

        boolean isCheckTermName = contractTypeDo != null && ContractType.FIXED_COST_CONTRACT.equalsIgnoreCase(contractTypeDo.getCode());
        Integer lookupContractId = contractId;
        List<ContractDo> supplementaryContractList =
                contractRepository.findSupplementaryContractList(contractId, new Date());
        if (supplementaryContractList != null && !supplementaryContractList.isEmpty()) {
            lookupContractId = supplementaryContractList.get(0).getId();
        }
        if (isCheckTermName) {
			String productTermName = null;
			if (StringUtil.isEmpty(insuranceContractProdTermName)) {
				errorMessageList.add(messageSource.getMessage("message125", null, null));
			} else {
				productTermName = insuranceContractProdTermName.split("\\(")[0];
				Set<String> termNameList = contractProdTermsRepository.getContractTermsNameList(lookupContractId, storeId).stream()
						.filter(StringUtil::isNotEmpty)
						.map(termName -> termName.split("\\(")[0])
						.collect(Collectors.toSet());
				if (!termNameList.contains(productTermName)) {
					errorMessageList.add(messageSource.getMessage("message264", new String[]{"Term Name"}, null));
				}
			}

            List<ContractProdTermsDo> contractProdTermsDoList = contractProdTermsRepository.findByTermsNameAndContractIdAndStoreId(productTermName, lookupContractId, storeId, Sort.by(Sort.Direction.DESC, "id"));
            if (CollectionUtil.isNotEmpty(contractProdTermsDoList)) {
                // 前端傳過來的termName是由termName和fixedCost組合而成的 ex：termName(12)
                String fixedCostValue = insuranceContractProdTermName.split("\\(")[1].split("\\)")[0];
                if (StringUtil.isNotEmpty(fixedCostValue)) {
                    BigDecimal fixedCost = new BigDecimal(fixedCostValue);
                    ContractProdTermsDo fixedCostContractProdTerm = contractProdTermsDoList.stream()
                            .filter(contractProdTerms -> contractProdTerms.getFixedCost().compareTo(fixedCost) == 0)
                            .findFirst()
                            .orElse(null);
                    if (fixedCostContractProdTerm != null) {
                        contractProdTermsDo = fixedCostContractProdTerm;
                    } else {
                        contractProdTermsDo = contractProdTermsDoList.get(0);
                    }
                } else {
                    contractProdTermsDo = contractProdTermsDoList.get(0);
                }
            }
        } else {
            String productCatCode = primaryCategory.getProductCatCode();
            String productPrimaryCategoryPrefix = productCatCode.substring(0, 4);
            log.info(String.format("contractId = %d, storeId = %d, skuCode = %s, brandId = %d, productCatCode = %s, productPrimaryCategoryPrefix = %s, productReadyMethod = %s",
                    lookupContractId, storeId, skuId, brandId, productCatCode, productPrimaryCategoryPrefix, productReadyMethodCode));
            List<ContractProdTermsDo> contractProdTermsDoList = contractProdTermsRepository.findMatchStore(lookupContractId, storeId, skuId, brandId, productReadyMethodCode, productCatCode);
            if (CollectionUtil.isNotEmpty(contractProdTermsDoList)) {
                contractProdTermsDo = contractProdTermsDoList.get(0);
            }
        }

        if (contractProdTermsDo == null) {
            errorMessageList.add(messageSource.getMessage("message12", null, null));
        }

        return CheckContractProdTermResultDto.builder().contractProdTerms(contractProdTermsDo)
                .errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkProductSkuExistsInStore(UserDto userDto, String store, List<String> skuList) {
        List<String> errorMessageList = new ArrayList<>();

        CheckSkuIdByHktvStoreProductMasterRequestDto checkSkuIdDto = new CheckSkuIdByHktvStoreProductMasterRequestDto(store, skuList);
        CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByHktvStore(userDto, checkSkuIdDto);

        if (checkSkuIdResultDto == null) {
            log.error("Unable to request product master check sku.");
            errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR}, null));
        } else if (CollectionUtil.isNotEmpty(checkSkuIdResultDto.getExistsSkus())) { // 若 existsSkus 有值表示 sku 已存在，若是 existsSkus 為空陣列則表示無 sku 重複
            errorMessageList.add(messageSource.getMessage("message6", new String[]{String.join(", ", checkSkuIdResultDto.getExistsSkus())}, null));
        }

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkCategoryCodeAndPackingBoxType(
            String buCode, BuProductCategoryDo primaryCategory, String packingBoxTypeCode) {
        List<String> errorMessageList = new ArrayList<>();

        boolean checkPass = false;
        List<SysParmDo> categoryRestrictionList =
                sysParmRepository.findBySegmentAndBuCodeAndCode("CATEGORY_RESTRICTION", buCode, packingBoxTypeCode);
        String primaryCategoryCode = primaryCategory.getProductCatCode();

        if (CollectionUtil.isEmpty(categoryRestrictionList)) {
            checkPass = true;
        } else {
            for (SysParmDo categoryRestriction : categoryRestrictionList) {
                String[] checkCategoryCodes = categoryRestriction.getParmValue().split(",");
                for (String checkCategoryCode : checkCategoryCodes) {
                    if (primaryCategoryCode.startsWith(checkCategoryCode)) {
                        checkPass = true;
                        break;
                    }
                }
            }
        }
        if (!checkPass) {
            errorMessageList.add(messageSource.getMessage("message46", null, null));
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkBrandApproved(Integer brandId) {
        List<String> errorMessageList = new ArrayList<>();
		if (brandId == null) {
			errorMessageList.add(messageSource.getMessage("message301", new String[]{"Brand"}, null));
		} else {
			BrandDo brand = brandRepository.findById(brandId).orElseThrow(() -> new SystemException("Brand doesn't exist"));
			if (!"A".equals(brand.getStatus())) {
				errorMessageList.add(messageSource.getMessage("message72", null, null));
			}
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkProductPriceUpdateDuringPromotion(String uuid, ProductCheckDto productCheckDto, ProductMasterResultDto beforeProduct) {
		List<String> errorMessageList = new ArrayList<>();

		Integer count = productStorePromotionMapper.countProductStorePromotionByUuId(uuid);
		if (count == null || count == 0) {
			return CheckProductResultDto.generate(errorMessageList);
		}

		HktvProductDto beforeHktvProductDto = beforeProduct.getAdditional().getHktv();
		if (!BigDecimalUtil.isEqual(beforeProduct.getOriginalPrice(), productCheckDto.getOriginalPrice())) {
			errorMessageList.add(messageSource.getMessage("message70", new String[]{"Original Price"}, null));
		}

		if (!BigDecimalUtil.isEqual(beforeHktvProductDto.getSellingPrice(), productCheckDto.getSellingPrice())) {
			errorMessageList.add(messageSource.getMessage("message70", new String[]{"Selling Price"}, null));
		}

		if (StringUtil.isNotEmpty(productCheckDto.getDiscountTextEn())) {
			if (!Objects.equals(beforeHktvProductDto.getDiscountTextEn(), productCheckDto.getDiscountTextEn())) {
				errorMessageList.add(messageSource.getMessage("message70", new String[]{"Discount Text (Eng)"}, null));
			}
		}

		if (StringUtil.isNotEmpty(productCheckDto.getDiscountTextCh())) {
			if (!Objects.equals(beforeHktvProductDto.getDiscountTextCh(), productCheckDto.getDiscountTextCh())) {
				errorMessageList.add(messageSource.getMessage("message70", new String[]{"Discount Text (Chi)"}, null));
			}
		}

		if (StringUtil.isNotEmpty(productCheckDto.getDiscountTextSc())) {
			if (!Objects.equals(beforeHktvProductDto.getDiscountTextSc(), productCheckDto.getDiscountTextSc())) {
				errorMessageList.add(messageSource.getMessage("message70", new String[]{"Discount Text (SC)"}, null));
			}
		}

		if (!Objects.equals(beforeHktvProductDto.getStyle(), productCheckDto.getStyle())) {
			errorMessageList.add(messageSource.getMessage("message70", new String[]{"Style"}, null));
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkMallDollar(
            UserDto userDto, String buCode, BigDecimal mallDollar, BigDecimal vipMallDollar) {
		List<String> errorMessageList = new ArrayList<>();
		String roleCode = userDto.getRoleCode();

		//Update Product Detail, Check MallDollar
		List<String> changeMalldollarRolecodeList = List.of("RM", "RML", "RMO");
		if (changeMalldollarRolecodeList.contains(roleCode)) {
			if (mallDollar != null) {
				if (mallDollar.compareTo(BigDecimal.valueOf(mallDollar.intValue())) != 0) {
					errorMessageList.add(messageSource.getMessage("message26", null, null));
				}

				String[] mallDollarList = findMallDollar(buCode);
				if (!checkMallDollerInRange(mallDollarList, mallDollar)) {
					errorMessageList.add(messageSource.getMessage("message25", mallDollarList, null));
				}

			}
			if (vipMallDollar != null) {
				if (vipMallDollar.compareTo(BigDecimal.valueOf(vipMallDollar.intValue())) != 0) {
					errorMessageList.add(messageSource.getMessage("message83", null, null));
				}

				String[] vipMallDollarList = findVipMallDollars(buCode);
				if (!checkMallDollerInRange(vipMallDollarList, vipMallDollar)) {
					errorMessageList.add(messageSource.getMessage("message82", vipMallDollarList, null));
				}
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private String[] findMallDollar(String buCode) {
		String[] strings = null;
		List<SysParmDo> sysParmDoList = sysParmRepository.
				findBySegmentAndBuCodeAndCode(SysParmSegment.MALL_DOLLAR, buCode, "MALL_DOLLAR");
		if (CollectionUtil.isNotEmpty(sysParmDoList)) {
			SysParmDo parmVo = sysParmDoList.get(0);
			String parmValue = parmVo.getParmValue();
			if (StringUtil.isNotEmpty(parmValue)) {
				strings = parmValue.split("-");
			}
		}
		return strings;
	}

	private boolean checkMallDollerInRange(String[] range,BigDecimal mallDollar){
		boolean result=false;
		if(range!=null && range.length==2){
			String min = range[0];
			String max = range[1];
			try {
				if (mallDollar.doubleValue() >= Double.parseDouble(min) && mallDollar.doubleValue() <= Double.parseDouble(max)) {
					result=true;
				}
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
		}
		return result;

	}

	private String[] findVipMallDollars(String buCode) {
		String[] vipMallDollars = null;
		List<SysParmDo> parList = sysParmRepository.
				findBySegmentAndBuCodeAndCode(SysParmSegment.MALL_DOLLAR, buCode, "VIP_MALL_DOLLAR");
		if (CollectionUtil.isNotEmpty(parList)) {
			SysParmDo parmVo = parList.get(0);
			String parmValue = parmVo.getParmValue();
			if (StringUtil.isNotEmpty(parmValue))
				vipMallDollars = parmValue.split("-");
		}
		return vipMallDollars;
	}

    public CheckProductResultDto checkWarehouse(String buCode, String readyMethodCode, Integer storeWarehouseId, String storageType) {

		List<String> errorMessageList = new ArrayList<>();
		if (storeWarehouseId == null){
			errorMessageList.add(messageSource.getMessage("message101", null, null));
		} else {
			try{
				StoreWarehouseDo storeWarehouseDo = storeWarehouseRepository.findById(storeWarehouseId).
						orElseThrow(() -> new SystemException(messageSource.getMessage("message101", null, null)));

				//Search merchant id to check if it is buy sell merchant
				MerchantStoreDo merchantStoreDo = merchantStoreRepository.findByStoreId(storeWarehouseDo.getStoreId()).
						orElseThrow(() -> new SystemException(messageSource.getMessage("message102", null, null)));

				Integer merchantId = merchantStoreDo.getMerchantId();

				if (storeWarehouseDo.getSeqNo() == null) {
					errorMessageList.add(messageSource.getMessage("message22", null, null));
				} else if (!checkMatchWarehouseSeqNoForReadyMethod(buCode, String.valueOf(storeWarehouseDo.getSeqNo()), readyMethodCode, merchantId)) {
					errorMessageList.add(messageSource.getMessage("message22", null, null));
				} else if (!checkStoreWarehouseEntity(storeWarehouseDo)) {
					errorMessageList.add(messageSource.getMessage("message39", null, null));
				} else {
					if (storeWarehouseDo.getLastSynchronizeDate() == null) {
						//3 .check syn OIX
						errorMessageList.add(messageSource.getMessage("message39", null, null));
					}
				}

				// check SYS_PARM's CODE field is match with STORAGE_TYPE when product ready method is Consignment
				if (readyMethodCode.equals(ProductReadyMethodType.CONSIGNMENT)) {
					if (!checkMatchWarehouseCodeWithStorageType(storageType, storeWarehouseId, String.valueOf(storeWarehouseDo.getSeqNo()))) {
						errorMessageList.add(messageSource.getMessage("message145", new String[]{storageType}, null));
					}
				}
			}catch (SystemException e){
				errorMessageList.add(e.getMessage());
			}
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	private boolean checkMatchWarehouseCodeWithStorageType(String storageType, Integer storeWarehouseId, String storeWarehouseSeqNo) {
		boolean match = true;
		Optional<SysParmDo> sysParmDo = sysParmRepository.findBySegmentAndCodeAndParmValue(SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING, storeWarehouseSeqNo, storageType);
		if (!sysParmDo.isPresent()) {
			match = false;
		}

		return match;
	}

    public boolean checkMatchWarehouseSeqNoForReadyMethod(
            String buCode, String seqNo, String readyMethodCode, Integer merchantId) {
        List<String> readyMethodSeqNoList = findReadyMethodSeqNo(buCode, readyMethodCode, merchantId);
        if (CollectionUtil.isEmpty(readyMethodSeqNoList)) {
            return false;
        }

        log.debug(String.format("seqNo = %s, seqNoList = %s", seqNo, readyMethodSeqNoList));
        boolean match = false;
        if (CollectionUtil.isNotEmpty(readyMethodSeqNoList)) {
            for (String readyMethodSeqNoStr : readyMethodSeqNoList) {
                if (readyMethodSeqNoStr.equals(seqNo)) {
                    match = true;
                    break;
                }
            }
        }

        return match;
    }

    private List<String> findReadyMethodSeqNo(String buCode, String readyMethodCode, Integer merchantId) throws SystemException {
        List<String> list = new ArrayList<>();

        if (StringUtil.isEmpty(readyMethodCode) || merchantId == null) {
            return list;
        }

        List<SysParmDo> parmVoList;
        if (ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(readyMethodCode)) {
			parmVoList = sysParmRepository.findBySegmentAndBuCodeAndCodeAndShortDesc(SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE, buCode, readyMethodCode, "11");
        } else {
            parmVoList = sysParmRepository.findBySegmentAndBuCodeAndCode(SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE, buCode, readyMethodCode);
        }

        if (CollectionUtil.isEmpty(parmVoList)) {
            return list;
        }
        SysParmDo parmVo = parmVoList.get(0);
        String longDesc = parmVo.getLongDesc();
        if (StringUtil.isEmpty(longDesc)) {
            return list;
        }
        String[] descArray = longDesc.split(",");
        Set<String> set = new HashSet<>();
        for (String seqStr : descArray) {
            if (set.add(seqStr) && StringUtil.isNotEmpty(seqStr))
                list.add(seqStr);
        }

        return list;
    }

    private boolean isBuySellMerchant(Integer merchantId) {
        List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndCode("BUYSELL_MERCHANT", "BUYSELL_MERCHANT");
        HashSet<String> buySellMerchantSet = new HashSet<>();
        for (SysParmDo sysParmDo : sysParmDoList) {
            String[] merchantIdArray = sysParmDo.getParmValue().split(",");
            buySellMerchantSet.addAll(Arrays.asList(merchantIdArray));
        }
        return buySellMerchantSet.contains(String.valueOf(merchantId));
    }

    public boolean checkStoreWarehouseEntity(StoreWarehouseDo storeWarehouseDo) {
        return !StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress1()) &&
                !StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress2()) &&
                !StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress3()) &&
                !StringUtil.isEmpty(storeWarehouseDo.getWarehouseAddress4()) &&
                !StringUtil.isEmpty(storeWarehouseDo.getWarehouseContactName()) &&
                !StringUtil.isEmpty(storeWarehouseDo.getWarehouseContactPhoneNo());
    }

    public CheckProductResultDto checkVideoLink(String videoLink) {
        List<String> errorMessageList = new ArrayList<>();
        if (StringUtil.isNotEmpty(videoLink)) {
            String youtubeLinkId = videoLink.trim().substring(1);
            if (!videoLink.trim().startsWith("=") || !youtubeLinkId.matches(YOUTUBE_LINK_ID_REGEX)) {
                errorMessageList.add(messageSource.getMessage("message79", null, null));
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	private CheckProductResultDto checkVideoLinkText(String videoLinkTextEn, String videoLinkTextCh, String vedioLinkTextSc, int index) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotNullOrBlank(videoLinkTextEn)) {
			if (videoLinkTextEn.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message81", null, null));
			}
			if (videoLinkTextEn.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "Eng", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}
		if (StringUtil.isNotNullOrBlank(videoLinkTextCh)) {
			if (videoLinkTextCh.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message80", null, null));
			}
			if (videoLinkTextCh.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "Chi", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}
		if (StringUtil.isNotNullOrBlank(vedioLinkTextSc)) {
			if (vedioLinkTextSc.contains("|")) {
				errorMessageList.add(messageSource.getMessage("message330", null, null));
			}
			if (vedioLinkTextSc.length() > ConstantType.MAX_VIDEO_TEXT_LENGTH) {
				errorMessageList.add(messageSource.getMessage("message267", new String[]{Integer.toString(index), "SC", Integer.toString(ConstantType.MAX_VIDEO_TEXT_LENGTH)}, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkPrimarySku(ProductDo existProductDo, String isPrimarySku, int saveProductType) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(isPrimarySku) && StringUtil.isNotEmpty(existProductDo.getIsPrimarySku())) {
			boolean isChangeIsPrimarySku = StringUtil.isNotEquals(isPrimarySku, existProductDo.getIsPrimarySku());
			switch (saveProductType) {
				// for mms1.0 logic
				case SaveProductType.BATCH_EDIT_PRODUCT:
				// mms2.0 batch edit cannot edit primary sku
				case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
					if (isChangeIsPrimarySku) {
						errorMessageList.add(messageSource.getMessage("message24", null, null));
					}
					break;
				// mms2.0 “Yes” can’t edit to other value
				default:
					if ("Y".equals(existProductDo.getIsPrimarySku()) && isChangeIsPrimarySku) {
						errorMessageList.add(messageSource.getMessage("message24", null, null));
					}

			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

    public CheckProductResultDto checkBatchBrand(
            boolean isNewProduct, Integer merchantId, Integer storeId,
            String productId, Integer brandId, boolean offlineDueToRollback) {
        List<String> errorMessageList = new ArrayList<>();
        List<ProductDo> productList = productRepository.findByStoreIdAndMerchantIdAndIsPrimarySkuAndProductCode(storeId, merchantId, "Y", productId);

        if (CollectionUtil.isNotEmpty(productList) && Boolean.FALSE.equals(offlineDueToRollback)) {
            ProductDo product = productList.get(0);
            if (isNewProduct &&
                    !brandId.equals(product.getBrandId())) {
                errorMessageList.add(messageSource.getMessage("message7", null, null));
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	public CheckProductResultDto checkUrgentFlagUpdatedByMerchant(
		UserDto userDto, String beforeUrgent, String currentUrgent) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(currentUrgent) && !ExcelUtil.YN_LIST.contains(currentUrgent)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Urgent"}, null));
		}

		if (RoleCode.MERCHANT_ADMIN.equalsIgnoreCase(userDto.getRoleCode()) ||
			RoleCode.MERCHANT.equalsIgnoreCase(userDto.getRoleCode())) {
			if (currentUrgent == null) {
				currentUrgent = "";
			}
			if (beforeUrgent == null) {
				beforeUrgent = "";
			}
			if (!beforeUrgent.equalsIgnoreCase(currentUrgent)) {
				errorMessageList.add(messageSource.getMessage("message76", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

    public CheckProductResultDto checkReadyMethodAndPackingBoxType(String readyMethodCode, String packingBoxTypeCode, Integer contractId, String primaryCategoryCode) {
        List<String> errorMessageList = new ArrayList<>();
        String merchantDelivery = "M";
        String eVoucher = "E";

        if (merchantDelivery.equals(readyMethodCode) && "U".equalsIgnoreCase(packingBoxTypeCode)) {
            errorMessageList.add(messageSource.getMessage("message73", null, null));
        }
        if (eVoucher.equals(readyMethodCode) && !"L".equalsIgnoreCase(packingBoxTypeCode)) {
            String packingBoxTypeDesc = findPackBoxTypeDesc("L");
            errorMessageList.add(messageSource.getMessage("message15", new String[]{packingBoxTypeDesc}, null));
        }
		if (findProductReadyMethodService.start(contractId, BuCodeEnum.HKTV.name()).getData().stream()
			.noneMatch(data -> data.getCode().equals(readyMethodCode))) {
			errorMessageList.add(messageSource.getMessage("message231", null, null));
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public String findPackBoxTypeDesc(String packingBoxTypeCode) {
        List<SysParmDo> list = sysParmRepository.findBySegmentAndCode("PACK_BOX_TYPE", packingBoxTypeCode);
        if (CollectionUtil.isNotEmpty(list)) {
            String desc = list.get(0).getShortDesc();
            if (StringUtil.isNotEmpty(desc)) {
                return desc;
            }
        }
        return null;
    }

    public CheckProductResultDto checkProductLandMarkCat(String buCode, StoreDo storeDo, List<String> productTypeCodeList) {
        List<String> errorMessageList = new ArrayList<>();
        List<String> productCategoryCodeList = new ArrayList<>();
        for (String productTypeCode : productTypeCodeList) {
            buProductCategoryRepository.findByProductCatCode(ConstantType.PLATFORM_CODE_HKTV, productTypeCode).ifPresent(buProductCategoryDo -> productCategoryCodeList.add(buProductCategoryDo.getProductCatCode()));
        }
        if (CollectionUtil.isNotEmpty(productCategoryCodeList)) {
            List<SysParmDo> landmarkCatCodeSysParmList = sysParmRepository.findBySegmentAndBuCode("LANDMARK_CAT_CODE", buCode);

            if (!"Y".equals(storeDo.getStoreLandmarkFlag()) && CollectionUtil.isNotEmpty(landmarkCatCodeSysParmList)
                    && isLandMarkStoreProduct(landmarkCatCodeSysParmList.get(0).getParmValue(), productCategoryCodeList)) {
                errorMessageList.add(messageSource.getMessage("message71", new String[]{landmarkCatCodeSysParmList.get(0).getParmValue()}, null));
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private boolean isLandMarkStoreProduct(String landMarkCatCode, List<String> categoryCodeList) {
        for (String categoryCode : categoryCodeList) {
            if (categoryCode.startsWith(landMarkCatCode)) {
                return true;
            }
        }
        return false;
    }

    public CheckProductResultDto checkPackingInformation(String readyMethodCode, BigDecimal weight, BigDecimal height, BigDecimal depth,
														 BigDecimal length, String weightUnit, String packingDimensionUnit) {
        List<String> errorMessageList = new ArrayList<>();
        // If product ready method is "Merchant Delivery" or "E-Voucher" or "DISPLAY_STORE", no need to check packing information.
        String[] unnecessaryCheckingArray = {ProductReadyMethodType.MERCHANT_DELIVERY, ProductReadyMethodType.E_VOUCHER, ProductReadyMethodType.DISPLAY_STORE};
        if (Arrays.stream(unnecessaryCheckingArray).noneMatch(e -> e.equals(readyMethodCode))) {
            if (weight == null)
                errorMessageList.add(messageSource.getMessage("message92", null, null));

            if (height == null)
                errorMessageList.add(messageSource.getMessage("message84", null, null));

            if (depth == null)
                errorMessageList.add(messageSource.getMessage("message85", null, null));

            if (length == null)
                errorMessageList.add(messageSource.getMessage("message86", null, null));

			if (StringUtil.isNullOrBlank(packingDimensionUnit)) {
				errorMessageList.add(messageSource.getMessage("message143", null, null));
			}

			if (StringUtil.isNullOrBlank(weightUnit)) {
				errorMessageList.add(messageSource.getMessage("message144", null, null));
			}
        }

		if(weight != null) {
			// Check weight
			if (weight.compareTo(BigDecimal.valueOf(0)) == 0) {
				errorMessageList.add(messageSource.getMessage("message87", null, null));
			} else if (weight.compareTo(BigDecimal.ZERO) < 0) {
				errorMessageList.add(messageSource.getMessage("message262", new String[]{"Weight"}, null));
			} else if (ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)) {
				if (ConstantType.WEIGHT_UNIT_G.equals(weightUnit) && weight.compareTo(BigDecimal.valueOf(20000)) > 0) {
					errorMessageList.add(messageSource.getMessage("message358", null, null));
				} else if (ConstantType.WEIGHT_UNIT_KG.equals(weightUnit) && weight.compareTo(BigDecimal.valueOf(20)) > 0) {
					errorMessageList.add(messageSource.getMessage("message358", null, null));
				}
			} else if (weight.compareTo(ConstantType.DIMENSION_MAX.subtract(BigDecimal.ONE)) > 0) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Weight", ConstantType.DIMENSION_MAX.toPlainString()}, null));
			}
		}

		if(height != null) {
			// Check height
			if (height.compareTo(BigDecimal.valueOf(0)) == 0) {
				errorMessageList.add(messageSource.getMessage("message88", null, null));
			} else if (height.compareTo(BigDecimal.ZERO) < 0) {
				errorMessageList.add(messageSource.getMessage("message262", new String[]{"Packing Height"}, null));
			} else if (ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)) {
				if (ConstantType.PACKING_DIMENSION_UNIT_CM.equals(packingDimensionUnit) && height.compareTo(BigDecimal.valueOf(130)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_MM.equals(packingDimensionUnit) && height.compareTo(BigDecimal.valueOf(1300)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_M.equals(packingDimensionUnit) && height.compareTo(BigDecimal.valueOf(1.3)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				}
			} else if (height.compareTo(ConstantType.DIMENSION_MAX.subtract(BigDecimal.ONE)) > 0) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Packing Height", ConstantType.DIMENSION_MAX.toPlainString()}, null));
			}
		}

		if(depth != null) {
			// Check depth
			if (depth.compareTo(BigDecimal.valueOf(0)) == 0) {
				errorMessageList.add(messageSource.getMessage("message89", null, null));
			} else if (depth.compareTo(BigDecimal.ZERO) < 0) {
				errorMessageList.add(messageSource.getMessage("message262", new String[]{"Packing Depth"}, null));
			} else if (ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)) {
				if (ConstantType.PACKING_DIMENSION_UNIT_CM.equals(packingDimensionUnit) && depth.compareTo(BigDecimal.valueOf(130)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_MM.equals(packingDimensionUnit) && depth.compareTo(BigDecimal.valueOf(1300)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_M.equals(packingDimensionUnit) && depth.compareTo(BigDecimal.valueOf(1.3)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				}
			} else if (depth.compareTo(ConstantType.DIMENSION_MAX.subtract(BigDecimal.ONE)) > 0) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Packing Depth", ConstantType.DIMENSION_MAX.toPlainString()}, null));
			}
		}

		if(length != null) {
			// Check length
			if (length.compareTo(BigDecimal.valueOf(0)) == 0) {
				errorMessageList.add(messageSource.getMessage("message90", null, null));
			} else if (length.compareTo(BigDecimal.ZERO) < 0) {
				errorMessageList.add(messageSource.getMessage("message262", new String[]{"Packing Length"}, null));
			} else if (ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)) {
				if (ConstantType.PACKING_DIMENSION_UNIT_CM.equals(packingDimensionUnit) && length.compareTo(BigDecimal.valueOf(130)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_MM.equals(packingDimensionUnit) && length.compareTo(BigDecimal.valueOf(1300)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_M.equals(packingDimensionUnit) && length.compareTo(BigDecimal.valueOf(1.3)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				}
			} else if (length.compareTo(ConstantType.DIMENSION_MAX.subtract(BigDecimal.ONE)) > 0) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Packing Length", ConstantType.DIMENSION_MAX.toPlainString()}, null));
			}
		}

		if (StringUtil.isNotNullOrBlank(packingDimensionUnit) && !PACKING_DIMENSION_UNIT.contains(packingDimensionUnit)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Packing Dimension Unit"}, null));
		} else if (StringUtil.isNotNullOrBlank(packingDimensionUnit) && ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)) {
			if (height != null || depth != null || length != null) {
				BigDecimal total = Optional.ofNullable(height).orElse(BigDecimal.ZERO)
					.add(Optional.ofNullable(depth).orElse(BigDecimal.ZERO))
					.add(Optional.ofNullable(length).orElse(BigDecimal.ZERO));
				if (ConstantType.PACKING_DIMENSION_UNIT_CM.equals(packingDimensionUnit) && total.compareTo(BigDecimal.valueOf(180)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_MM.equals(packingDimensionUnit) && total.compareTo(BigDecimal.valueOf(1800)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				} else if (ConstantType.PACKING_DIMENSION_UNIT_M.equals(packingDimensionUnit) && total.compareTo(BigDecimal.valueOf(1.8)) > 0) {
					errorMessageList.add(messageSource.getMessage("message357", null, null));
				}
			}
		}
		if (StringUtil.isNotNullOrBlank(weightUnit) && !WEIGHT_UNIT.contains(weightUnit)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Weight Unit"}, null));
		}
		checkDimension(errorMessageList, packingDimensionUnit, height, "Packing Height");
		checkDimension(errorMessageList, packingDimensionUnit, length, "Packing Lenth");
		checkDimension(errorMessageList, packingDimensionUnit, depth, "Packing Depth");
		if (ConstantType.WEIGHT_UNIT_G.equals(weightUnit) && weight != null && weight.compareTo(BigDecimal.valueOf(weight.intValue())) != 0) {
			errorMessageList.add(messageSource.getMessage("message238", null, null));
		}
		if (ConstantType.WEIGHT_UNIT_KG.equals(weightUnit) && weight != null && weight.scale() > 2) {
			errorMessageList.add(messageSource.getMessage("message239", null, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	private void checkDimension(List<String> errorMessageList, String packingDimensionUnit, BigDecimal demension, String errorMessageType) {
		if (ConstantType.PACKING_DIMENSION_UNIT_MM.equals(packingDimensionUnit) && demension != null && demension.compareTo(BigDecimal.valueOf(demension.intValue())) != 0) {
			errorMessageList.add(messageSource.getMessage("message235", new String[]{errorMessageType}, null));
		} else if (ConstantType.PACKING_DIMENSION_UNIT_CM.equals(packingDimensionUnit) && demension != null && demension.scale() > 1) {
			errorMessageList.add(messageSource.getMessage("message236", new String[]{errorMessageType}, null));
		} else if (ConstantType.PACKING_DIMENSION_UNIT_M.equals(packingDimensionUnit) && demension != null && demension.scale() > 2) {
			errorMessageList.add(messageSource.getMessage("message237", new String[]{errorMessageType}, null));
		}
	}

    public CheckProductResultDto checkWeightLimitByDeliveryMethod(
            String buCode, String deliveryMethodCode, BigDecimal weight, String weightUnit) {

        List<String> errorMessageList = new ArrayList<>();
        List<SysParmDo> deliveryMethodList = sysParmRepository.findBySegmentAndBuCodeAndCode("DELIVERY_METHOD", buCode, deliveryMethodCode);
        if (!deliveryMethodList.isEmpty() && StringUtil.isNotEmpty(deliveryMethodList.get(0).getParmValue())) {
            String deliveryMethodValue = deliveryMethodList.get(0).getParmValue();
            List<SysParmDo> weightLimitList = sysParmRepository.findBySegmentAndBuCodeAndCode("WEIGHT_LIMIT_FOR_PACKAGE", buCode, deliveryMethodValue);

            if (!weightLimitList.isEmpty() && StringUtil.isNotEmpty(weightLimitList.get(0).getParmValue())) {
                BigDecimal weightLimitForPackage;
                try {
                    // According to Delivery Method, get the weight limit of this package. The default weight unit is kilogram.
                    weightLimitForPackage = new BigDecimal(weightLimitList.get(0).getParmValue());
                } catch (Exception e) {
                    weightLimitForPackage = null;
                }
                BigDecimal originPackageWeight = formatPackageWeightToKilogram(weight, weightUnit);
                // The weight limit should be greater than 0, if not, it will not check the package weight.
                if (weightLimitForPackage != null &&
                        originPackageWeight != null &&
                        weightLimitForPackage.compareTo(new BigDecimal(0)) > 0 &&
                        originPackageWeight.compareTo(weightLimitForPackage) > 0) {
                    // The package weight cannot over the weight limit of this package
                    errorMessageList.add(messageSource.getMessage("message17", new String[]{weightLimitList.get(0).getParmValue(), deliveryMethodList.get(0).getShortDesc()}, null));
                }
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private BigDecimal formatPackageWeightToKilogram(BigDecimal weight, String weightUnit) {
        if (weight != null && weightUnit != null) {
            BigDecimal convertWeight;
            if ("g".equals(weightUnit)) {
                convertWeight = weight.divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_DOWN);
            } else {
                convertWeight = weight;
            }
            return convertWeight;
        } else {
            return null;
        }
    }

    public CheckProductResultDto checkTimeSlot(String readyMethodCode, String pickupTimeslot) {
		List<String> errorMessageList = new ArrayList<>();
		Set<String> timeslotCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.PICKUP_TIMESLOT.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (pickupTimeslot != null && !timeslotCodes.contains(pickupTimeslot)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Pickup Timeslot"}, null));
		}
        if (StringUtil.isNotEmpty(readyMethodCode) && StringUtil.isNotEmpty(pickupTimeslot)) {
        	if ((readyMethodCode.equals(ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE) || readyMethodCode.equals(ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY))
					&& !pickupTimeslot.equals("AM/PM")) {
				errorMessageList.add(messageSource.getMessage("message67", null, null));
			}
			if ((readyMethodCode.equals(ProductReadyMethodType.CONSIGNMENT) || readyMethodCode.equals(ProductReadyMethodType.THIRD_PARTY))
					&& !pickupTimeslot.equals("AM/PM/EV")) {
				errorMessageList.add(messageSource.getMessage("message148", null, null));
			}
			if(List.of(ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB, ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED).contains(readyMethodCode)
					&& !pickupTimeslot.equals("AM/PM/EV")){
				errorMessageList.add(messageSource.getMessage("message152", null, null));
			}
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkBuySellStore(
            Integer merchantId, String productReadyMethod, String rmCode, String preSellFruit, String physicalStore, boolean isNewProduct, String beforeRmCode) {
        List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(rmCode) && rmCode.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"RM Code", ConstantType.MAX_CHARACTERS_50.toString()}, null));
		}

        if (isBuySellMerchant(merchantId)) {
            CheckProductResultDto checkRmCodeResult = checkRmCode(productReadyMethod, isNewProduct, rmCode, beforeRmCode);
            errorMessageList.addAll(checkRmCodeResult.getErrorMessageList());
            CheckProductResultDto checkPreSellFruitResult = checkPreSellFruit(preSellFruit, productReadyMethod, physicalStore);
            errorMessageList.addAll(checkPreSellFruitResult.getErrorMessageList());
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private CheckProductResultDto checkMinimumShelfLife(Integer minimumShelfLife) {
        List<String> errorMessageList = new ArrayList<>();
        if (null != minimumShelfLife && !String.valueOf(minimumShelfLife).matches(SHELF_LIFE_REGEX)) {
            errorMessageList.add(messageSource.getMessage("message63", null, null));
        }
		if (null != minimumShelfLife && minimumShelfLife < 1) {
			errorMessageList.add(messageSource.getMessage("message114", null, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	private CheckProductResultDto checkRmCode(String productReadyMethod, boolean isNewProduct, String currentRmCode, String beforeRmCode) {
		List<String> errorMessageList = new ArrayList<>();

		if (!ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productReadyMethod)) {
			return CheckProductResultDto.generate(errorMessageList);
		}

        if (isNewProduct) {
			if (StringUtil.isEmpty(currentRmCode)) {
				errorMessageList.add(messageSource.getMessage("message37", null, null));
			}
			if (StringUtil.isNotEmpty(currentRmCode) && !currentRmCode.matches(RM_CODE_REGEX)) {
				errorMessageList.add(messageSource.getMessage("message60", null, null));
			}
		} else {
			if (!StringUtil.generateDefaultStringValue(beforeRmCode).equalsIgnoreCase(StringUtil.generateDefaultStringValue(currentRmCode))) {
				errorMessageList.add(messageSource.getMessage("message150", null, null));
			}
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private CheckProductResultDto checkPreSellFruit(String preSellFruit, String productReadyMethod, String physicalStore) {
        List<String> errorMessageList = new ArrayList<>();

		if(StringUtil.isNotEmpty(physicalStore) && physicalStore.length() > ConstantType.MAX_CHARACTERS_1000){
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"Physical Store length", String.valueOf(ConstantType.MAX_CHARACTERS_1000)}, null));
		}

		if ("Y".equalsIgnoreCase(preSellFruit)) {
			if ("NS".equalsIgnoreCase(productReadyMethod)) {
				if (StringUtil.isEmpty(physicalStore)) {
					errorMessageList.add(messageSource.getMessage("message36", null, null));
				} else {
					if (!physicalStore.matches(PHYSICAL_STORE_REGEX)) {
						errorMessageList.add(messageSource.getMessage("message33", null, null));
					}
				}
			} else {
				errorMessageList.add(messageSource.getMessage("message45", null, null));
			}
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    public CheckProductResultDto checkDescription(String skuSDescHktvEn, String skuSDescHktvCh, String skuSDescHktvSc, String skuLDescHktvEn, String skuLDescHktvCh, String skuLDescHktvSc) {
        List<String> errorMessageList = new ArrayList<>();
        Map<String, String> descriptionMap = new HashMap<>();
        descriptionMap.put("SKU Short Description (in English)", skuSDescHktvEn);
        descriptionMap.put("SKU Short Description (in Chinese)", skuSDescHktvCh);
        descriptionMap.put("SKU Short Description (in SC)", skuSDescHktvSc);
        descriptionMap.put("SKU Long Description (Eng) ", skuLDescHktvEn);
        descriptionMap.put("SKU Long Description (Chi) ", skuLDescHktvCh);
        descriptionMap.put("SKU Long Description (SC) ", skuLDescHktvSc);

        for (Map.Entry<String, String> description : descriptionMap.entrySet()) {
			if(StringUtil.isNotEmpty(description.getValue())){
				if (IMAGE_PATTERN_1.matcher(description.getValue()).find() || IMAGE_PATTERN_2.matcher(description.getValue()).find()) {
					errorMessageList.add(messageSource.getMessage("message2", new String[]{description.getKey()}, null));
				}

				if (TABLE_REGX_PATTERN.matcher(description.getValue()).find()) {
					errorMessageList.add(messageSource.getMessage("message3", new String[]{description.getKey()}, null));
				}
			}
        }

		errorMessageList.addAll(checkTextLength(skuSDescHktvEn, "SKU Short Description (Eng)", ConstantType.MAX_CHARACTERS_500, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuSDescHktvEn) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuSDescHktvEn).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Short Description (Eng)"}, null));
		}

		errorMessageList.addAll(checkTextLength(skuSDescHktvCh, "SKU Short Description (Chi)", ConstantType.MAX_CHARACTERS_500, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuSDescHktvCh) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuSDescHktvCh).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Short Description (Chi)"}, null));
		}

		errorMessageList.addAll(checkTextLength(skuSDescHktvSc, "SKU Short Description (SC)", ConstantType.MAX_CHARACTERS_500, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuSDescHktvSc) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuSDescHktvSc).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Short Description (SC)"}, null));
		}

		errorMessageList.addAll(checkTextLength(skuLDescHktvEn, "SKU Long Description (Eng)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuLDescHktvEn) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuLDescHktvEn).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Long Description (Eng)"}, null));
		}

		errorMessageList.addAll(checkTextLength(skuLDescHktvCh, "SKU Long Description (Chi)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuLDescHktvCh) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuLDescHktvCh).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Long Description (Chi)"}, null));
		}

		errorMessageList.addAll(checkTextLength(skuLDescHktvSc, "SKU Long Description (SC)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_65535));
		if (StringUtil.isNotEmpty(skuLDescHktvSc) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuLDescHktvSc).find()) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Long Description (SC)"}, null));
		}

        return CheckProductResultDto.generate(errorMessageList);
    }

    public CheckProductResultDto checkPackingBoxType(String packingBoxTypeCode, String storageTemperature, String readyMethodCode, String primaryCategoryCode) {
        List<String> errorMessageList = new ArrayList<>();
        boolean errorFlag = false;

		Set<String> packingBoxTypeCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.PACK_BOX_TYPE.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (packingBoxTypeCodes != null && !packingBoxTypeCodes.contains(packingBoxTypeCode)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Packing Box Type"}, null));
		} else if (findPackBoxTypeService.start(BuCodeEnum.HKTV.name(), primaryCategoryCode, readyMethodCode).getData().stream()
			.noneMatch(data -> data.getCode().equals(packingBoxTypeCode))) {
			errorMessageList.add(messageSource.getMessage("message240", null, null));
		}

        if (StringUtil.isNotNullOrBlank(storageTemperature)) {
            switch (packingBoxTypeCode) {
                case "E":
                case "T":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.FROZEN));
                    break;
                case "M":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.CHILLED));
                    break;
                case "J":
				case "W":
                case "N":
                case "K":
                case "U":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.CHILLED));
                    break;
                case "O":
                case "V":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.AIRCON));
                    break;
                case "P":
                case "S":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.AIRCON));
                    break;
                case "G":
                case "F":
                case "Y":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.ROOM_TEMPERATURE));
                    break;
                case "R":
                case "Q":
                case "H":
                case "L":
                case "X":
                    errorFlag = !(storageTemperature.equalsIgnoreCase(StorageTemperatureType.ROOM_TEMPERATURE));
                    break;
                default:
            }
        }

        if (errorFlag) {
            errorMessageList.add(messageSource.getMessage("message31", new String[]{packingBoxTypeCode}, null));
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private CheckProductResultDto checkProductDataForUpload(String productCode, String skuCode, String productReadyMethod, String mainPhoto,
															List<String> otherPhoto, List<String> variantProductPhoto, String advertisePhoto) {
        List<String> errorMessageList = new ArrayList<>();
        checkRequired(productCode, "Product ID", errorMessageList);
        checkRequired(skuCode, "Sku ID", errorMessageList);
        checkRequired(productReadyMethod, "Product Ready Method", errorMessageList);
        checkRequired(mainPhoto, "Main Photo", errorMessageList);
		boolean emptyContain = false;
		if (CollectionUtil.isNotEmpty(otherPhoto)) {
			if (otherPhoto.size() > ConstantType.OTHER_PHOTO_MAX) {
				errorMessageList.add(messageSource.getMessage("message265", new String[]{String.valueOf(ConstantType.OTHER_PHOTO_MAX)}, null));
			}
			if (otherPhoto.stream().anyMatch(data -> data.isBlank())) {
				emptyContain = true;
			}
		}
		if (CollectionUtil.isNotEmpty(variantProductPhoto)) {
			if (variantProductPhoto.size() > ConstantType.VARIANT_PRODUCT_PHOTO_MAX) {
				errorMessageList.add(messageSource.getMessage("message266", new String[]{String.valueOf(ConstantType.VARIANT_PRODUCT_PHOTO_MAX)}, null));
			}
			if (variantProductPhoto.stream().anyMatch(data -> data.isBlank())) {
				emptyContain = true;
			}
		}
		if (advertisePhoto != null && advertisePhoto.isBlank()) {
			emptyContain = true;
		} else if (StringUtil.isNotEmpty(advertisePhoto) && advertisePhoto.split(",").length > ConstantType.ADVERTISE_PHOTO_MAX) {
			errorMessageList.add(messageSource.getMessage("message283", new String[]{String.valueOf(ConstantType.ADVERTISE_PHOTO_MAX)}, null));
		}

		if (emptyContain) {
			errorMessageList.add(messageSource.getMessage("message335", null, null));
		}
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private void checkRequired(String str, String parameterName, List<String> errorMessageList) {
        if (StringUtils.isBlank(str)) {
            errorMessageList.add(messageSource.getMessage("message93", new String[]{parameterName}, null));
        }
    }

    private CheckProductResultDto checkPickupDay(String pickupDays, Integer storeId, String readyMethodCode) {
        List<String> errorMessageList = new ArrayList<>();

		Set<String> pickUpDaysCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.PICKUP_DAYS.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (pickupDays != null && !pickUpDaysCodes.contains(pickupDays)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Pickup Days"}, null));
		}
        List<String> necessaryMSUProductReadyMethodList = List.of(ProductReadyMethodType.CONSIGNMENT, ProductReadyMethodType.THIRD_PARTY
				,ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB, ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED);
		if (necessaryMSUProductReadyMethodList.contains(readyMethodCode)
				&& !PickupDaysType.MON_SUN.equalsIgnoreCase(pickupDays)) {
			errorMessageList.add(messageSource.getMessage("message100", null, null));
		}
		if (!necessaryMSUProductReadyMethodList.contains(readyMethodCode)
				&& sysParmRepository.countStorePickupDay(pickupDays, storeId, ConstantType.SYS_PARAM_SEGMENT_PICKUP_DAYS) == 0) {
			errorMessageList.add(messageSource.getMessage("message100", null, null));
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	public CheckProductResultDto checkReturnDay(Integer returnDays, String contractType) {
		List<String> errorMessageList = new ArrayList<>();

		List<SysParmDo> sysParmDos = sysParmRepository.findBySegment(
			SysParmSegmentEnum.RETURN_DAYS.name());
		Set<Integer> returnDaysCode = sysParmDos.stream().map(data -> Integer.parseInt(data.getCode()))
			.collect(Collectors.toSet());
		if (returnDays != null && !returnDaysCode.contains(returnDays)) {
			errorMessageList.add(
				messageSource.getMessage("message264", new String[]{"Return Days"}, null));
		}

		if (StringUtils.isEmpty(contractType)) {
			errorMessageList.add(messageSource.getMessage("message14", null, null));
			return CheckProductResultDto.builder().errorMessageList(errorMessageList)
				.result(errorMessageList.isEmpty()).build();
		}

		boolean isInsuranceContract = INSURANCE_CONTRACT_SET.contains(contractType);
		boolean is31DaysReturn = returnDays == RETURN_DAYS_31_VALUE;

		if (is31DaysReturn != isInsuranceContract) {
			errorMessageList.add(messageSource.getMessage("message365", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList)
			.result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkOptionFieldAndValue(String field1, String value1,
														   String field2, String value2,
														   String field3, String value3) {
		List<String> errorMessageList = new ArrayList<>();

		checkFieldAndValue(field1, value1, 1, errorMessageList);
		checkFieldAndValue(field2, value2, 2, errorMessageList);
		checkFieldAndValue(field3, value3, 3, errorMessageList);

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private void checkFieldAndValue(String field, String value, int index, List<String> errorMessageList) {
		List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentsHavingParentCode(List.of(SysParmSegmentEnum.PRODUCT_FIELD_VALUE.name()));
		Map<String, Set<String>> fieldToValuesMap = sysParmDoList.stream()
			.filter(sysParamDo -> Objects.nonNull(sysParamDo.getParentCode()))
			.collect(Collectors.groupingBy(data -> data.getParentCode(),  Collectors.mapping(data -> data.getCode(), Collectors.toSet())));

		if (StringUtils.isEmpty(field) && StringUtils.isNotEmpty(value)) {
			errorMessageList.add(messageSource.getMessage("message94", new Object[]{index, value}, null));
		}

		if (StringUtils.isNotEmpty(field)) {
			if (StringUtils.isEmpty(value)) {
				//noinspection ConstantConditions
				errorMessageList.add(messageSource.getMessage("message95", new Object[]{index, field}, null));
			}

			if (!fieldToValuesMap.containsKey(field)) {
				errorMessageList.add(messageSource.getMessage("message264", new Object[]{"Field " + index}, null));
			} else if (StringUtils.isNotEmpty(value) && !fieldToValuesMap.get(field).contains(value)) {
				errorMessageList.add(messageSource.getMessage("message264", new Object[]{"Value " + index}, null));
			}
		}
	}

	private CheckProductResultDto checkSizeSystem(String sizeSystem, String size) {
		List<String> errorMessageList = new ArrayList<>();

		Map<String, Set<String>> segmentMap = sysParmRepository.findBySegments(List.of(SysParmSegmentEnum.SIZE_SYSTEM.name(), SysParmSegmentEnum.SIZE.name())).stream()
			.collect(Collectors.groupingBy(data -> data.getSegment(), Collectors.mapping(data -> data.getCode(), Collectors.toSet())));
		if (StringUtil.isNotEmpty(sizeSystem) && !segmentMap.get(SysParmSegmentEnum.SIZE_SYSTEM.name()).contains(sizeSystem)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Size System"}, null));
		}
		if (StringUtil.isNotEmpty(size) && !segmentMap.get(SysParmSegmentEnum.SIZE.name()).contains(size)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Size"}, null));
		}

		if (StringUtils.isNotEmpty(sizeSystem) && StringUtils.isEmpty(size)) {
			errorMessageList.add(messageSource.getMessage("message96", new Object[]{sizeSystem}, null));
		}

		if (StringUtils.isEmpty(sizeSystem) && StringUtils.isNotEmpty(size)) {

			errorMessageList.add(messageSource.getMessage("message97", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkColorFamily(String colorFamiliar, String colorEn) {
		List<String> errorMessageList = new ArrayList<>();
		Map<String, List<SysParmDo>> segmentMap = sysParmRepository.findBySegments(List.of(SysParmSegmentEnum.COLOR_FAMILIES.name(), SysParmSegmentEnum.COLOR.name())).stream()
			.collect(Collectors.groupingBy(data -> data.getSegment()));

		if (StringUtil.isNotEmpty(colorFamiliar) && !segmentMap.get(SysParmSegmentEnum.COLOR_FAMILIES.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet()).contains(colorFamiliar)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Colour Families"}, null));
		}

		Set<String> colorEns = new HashSet<>();
		segmentMap.get(SysParmSegmentEnum.COLOR.name())
			.forEach(data -> {
				colorEns.add(data.getCode());
				colorEns.add(DataColorUtil.getColorFormatForOldMms(data));
			});
		if (StringUtils.isNotEmpty(colorEn) && !colorEns.contains(colorEn)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Color (Eng)"}, null));
		}

		if (StringUtils.isNotEmpty(colorFamiliar) && StringUtils.isEmpty(colorEn)) {
			errorMessageList.add(messageSource.getMessage("message98", new Object[]{colorFamiliar}, null));
		}

		if (StringUtils.isEmpty(colorFamiliar) && StringUtils.isNotEmpty(colorEn)) {
			errorMessageList.add(messageSource.getMessage("message99", new Object[]{colorFamiliar}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkPriceRelateFields(BigDecimal originalPrice, BigDecimal cost, String currency, String productReadyMethod, String contractType, BigDecimal rmbRate) {
		List<String> errorMessageList = new ArrayList<>();

		// currency
		Set<String> currenctCodes = sysParmRepository.findBySegment(SysParmSegmentEnum.CURRENCY.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (currency != null && !currenctCodes.contains(currency)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Currency"}, null));
		} else if (currency != null && ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)) {
			if (!CurrencyEnum.RMB.name().equals(currency)) {
				errorMessageList.add(messageSource.getMessage("message359", null, null));
			}
			if (rmbRate == null) {
				errorMessageList.add(messageSource.getMessage("message361", null, null));
			}
		} else if (currency != null && !CurrencyEnum.HKD.name().equals(currency)) {
			errorMessageList.add(messageSource.getMessage("message284", null, null));
		}

		// cost
		if (cost != null && cost.compareTo(BigDecimal.ZERO) < 0) {
			errorMessageList.add(messageSource.getMessage("message262", new String[]{"Cost"}, null));
		}

		// only oversea delivery can input cost
		if (!ProductReadyMethodType.OVERSEA_DELIVERY.equals(productReadyMethod) && cost != null) {
			errorMessageList.add(messageSource.getMessage("message233", null, null));
		// if product ready method is oversea delivery, original price must be null
		} else if (ProductReadyMethodType.OVERSEA_DELIVERY.equals(productReadyMethod) && originalPrice != null) {
			errorMessageList.add(messageSource.getMessage("message281", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkOriginalPriceAndSellingPrice(BigDecimal originalPrice, BigDecimal sellingPrice) {
		List<String> errorMessageList = new ArrayList<>();
		if (originalPrice == null) {
			errorMessageList.add(messageSource.getMessage("message110", null, null));
			return CheckProductResultDto.generate(errorMessageList);
		}

		if (originalPrice.compareTo(BigDecimal.ZERO) < 0) {
			errorMessageList.add(messageSource.getMessage("message262", new String[]{"Original Price"}, null));
		} else if (originalPrice.compareTo(ConstantType.PRICE_MAX) > 0) {
			errorMessageList.add(messageSource.getMessage("message263", new String[]{"Original Price", ConstantType.PRICE_MAX.toPlainString()}, null));
		}

		if (sellingPrice != null) {
			if (originalPrice.compareTo(sellingPrice) < 0) {
				errorMessageList.add(messageSource.getMessage("message111", null, null));
			} else if (sellingPrice.compareTo(BigDecimal.ZERO) < 0) {
				errorMessageList.add(messageSource.getMessage("message262", new String[]{"Selling Price"}, null));
			} else if (sellingPrice.compareTo(ConstantType.PRICE_MAX) > 0) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Selling Price", ConstantType.PRICE_MAX.toPlainString()}, null));
			}
		}

		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkBarCode(List<ProductBarcodeDto> barcodeDtoList, String readyMethodCode) {
		List<String> errorMessageList = new ArrayList<>();
        barcodeDtoList.forEach(productBarcodeDto -> {
            String barcode = productBarcodeDto.getEan();
            if (StringUtil.isNotEmpty(barcode)) {
                if (!BARCODE_PATTERN.matcher(barcode).matches()) {
                    errorMessageList.add(messageSource.getMessage("message112", null, null));
                }
                if (barcode.length() > 100) {
                    errorMessageList.add(messageSource.getMessage("message116", null, null));
                }
            }
        });

        if (ProductReadyMethodType.THIRD_PARTY.equalsIgnoreCase(readyMethodCode) && !is3PLProductReadyMethodContainMainBarcode(barcodeDtoList)) {
			errorMessageList.add(messageSource.getMessage("message139", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private boolean is3PLProductReadyMethodContainMainBarcode(List<ProductBarcodeDto> barcodeDtoList) {
    	boolean is3PLProductReadyMethodContainMainBarcode = false;
		for (ProductBarcodeDto productBarcodeDto : barcodeDtoList) {
			if (Objects.equals(1, productBarcodeDto.getSequenceNo()) && StringUtil.isNotEmpty(productBarcodeDto.getEan())) {
				is3PLProductReadyMethodContainMainBarcode = true;
			}
		}
    	return is3PLProductReadyMethodContainMainBarcode;
	}

	private CheckProductResultDto checkRemovalService(String removalService, List<String> productTypeCodeList, BuProductCategoryDo primaryCategory) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(removalService) && !ExcelUtil.YN_LIST.contains(removalService.toUpperCase())) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Need Removal Services"}, null));
		}

		if (StringUtil.isNotEmpty(removalService) && removalService.equalsIgnoreCase(ConstantType.Y)) {
			List<SysParmDo> removalServiceList = sysParmRepository.findBySegmentAndBuCode("CATEGORY_FOR_REMOVAL_SERVICE", ConstantType.PLATFORM_CODE_HKTV);

			List<String> removalServiceCategoryList = new ArrayList<>();
			removalServiceList.forEach(removalServiceElement -> {
				String[] removalServiceArray = removalServiceElement.getParmValue().split(",");
				removalServiceCategoryList.addAll(Arrays.asList(removalServiceArray));
			});

			boolean isProductTypeCodeError = true;
			// check product type code exist in removalServiceCategoryList or not
			for (String productTypeCode : productTypeCodeList) {
				for (String removalServiceCategory : removalServiceCategoryList) {
					if (productTypeCode.startsWith(removalServiceCategory)) {
						isProductTypeCodeError = false;
					}
				}
			}
			boolean isPrimaryCategoryError = true;
			// check priamry code exist in removalServiceCategoryList or not
			for (String removalServiceCategory : removalServiceCategoryList) {
				if (primaryCategory.getProductCatCode().startsWith(removalServiceCategory)) {
					isPrimaryCategoryError = false;
				}
			}

			if (isProductTypeCodeError || isPrimaryCategoryError) {
				errorMessageList.add(messageSource.getMessage("message115", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public BigDecimal calculateJapanAndKoreaContractOriginalPrice(BigDecimal cost, BigDecimal markupRate, String currency) {
		// if contract is J or K : original price = (cost + cost * markUpRate / 100) * exchangeRate
		List<String> exchangeRateList = sysParmRepository.findParmValueBySegmentAndCodeAndPlatformId("EXCHANGE_RATE", currency, ConstantType.PLATFORM_CODE_HKTV);
		BigDecimal exchangeRate = new BigDecimal(exchangeRateList.get(0));
		markupRate = markupRate == null ? new BigDecimal("0") : markupRate;
		BigDecimal productOfMarkUpRateAndCost = (cost.multiply(markupRate)).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
		BigDecimal orginalPrice = (cost.add(productOfMarkUpRateAndCost)).multiply(exchangeRate).setScale(0, RoundingMode.HALF_UP);
		log.info("JK contract after calculate： exchange_rate = {}, markup_rate = {}, cost = {}, original_price = {}", exchangeRate, markupRate, cost, orginalPrice);
		return orginalPrice;
	}

	public void checkBarcodeEnable(List<SingleEditProductDto> productList) {
    	Set<String> tplBarcodeSet = new HashSet<>();
    	Set<String> nonTplBarcodeSet = new HashSet<>();
    	Set<String> failedBarcodeSet = new HashSet<>();
    	Set<String> nonSyncToTplBarcodeSet = new HashSet<>();

    	productList.stream()
				.filter(product -> CollectionUtil.isNotEmpty(product.getProduct().getBarcodes()))
				.forEach(product -> {
					HktvProductDto hktvProductDto = product.getProduct().getAdditional().getHktv();
                    List<ProductBarcodeDto> barcodeDtoList = product.getProduct().getBarcodes();

					if (hktvProductDto != null) {
                        boolean is3pl = hktvProductDto.getProductReadyMethod().equalsIgnoreCase(ProductReadyMethodType.THIRD_PARTY);
                        barcodeDtoList.forEach( barcodeDto->{
                            String barcode =  barcodeDto.getEan();
                            if (is3pl) {
                                if (!tplBarcodeSet.add(barcode)) {
                                    failedBarcodeSet.add(barcode);
                                }
                            } else {
                                if (!nonTplBarcodeSet.add(barcode)) {
                                    nonSyncToTplBarcodeSet.add(barcode);
                                }
                            }
                        });

					}
				});
    	tplBarcodeSet.forEach(tplBarcode -> {
    		if (nonTplBarcodeSet.contains(tplBarcode)) {
    			failedBarcodeSet.add(tplBarcode);
			}
		});

    	for (SingleEditProductDto product : productList) {
    		if (CollectionUtil.isNotEmpty(product.getProduct().getBarcodes())) {
                AtomicBoolean hasDuplicateBarcode = new AtomicBoolean(false);
                AtomicBoolean isSyncTo3PL = new AtomicBoolean(true);
                product.getProduct().getBarcodes().forEach( barcodeDto->{
                    String barcode =  barcodeDto.getEan();
                    if(!hasDuplicateBarcode.get()){
                        hasDuplicateBarcode.set(failedBarcodeSet.contains(barcode));
                    }

                    if(isSyncTo3PL.get()){
                        isSyncTo3PL.set(nonSyncToTplBarcodeSet.contains(barcode));
                    }
                });

    			if (hasDuplicateBarcode.get()) {
					saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getProduct().getRecordRowId(), messageSource.getMessage("message118", null, null));
    				continue;
				}
				product.getProduct().setDisableTo3PL(isSyncTo3PL.get());
				saveProductRecordRowRepository.updateRowContentByRecordRowId(product.getProduct().getRecordRowId(), gson.toJson(product));
			}
		}
	}

    public CheckProductResultDto checkPackInfoBy3PL(UserDto userDto, List<SkuValidateRequestDto> skuValidateRequestDtoList, String identifier) {
        List<String> errorMessageList = new ArrayList<>();

        SkuValidateResponseDto skuValidateResponseDto = thirdPartyHelper.skuValidate(userDto, skuValidateRequestDtoList, identifier);

        if (skuValidateResponseDto == null) {
            String errorMessage = "TPL: " + messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.THIRD_PARTY_SKU_VALIDATE_ERROR}, null);
            errorMessageList.add(errorMessage);
        } else if (Boolean.FALSE.equals(skuValidateResponseDto.getSuccess())) {
            errorMessageList = skuValidateResponseDto.getReason();
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private CheckProductResultDto checkPackageConfirmed(ProductMasterResultDto beforeProduct, BigDecimal packingDepth, BigDecimal packingHeight, BigDecimal packingLength,
                                                        BigDecimal weight, String packingDimensionUnit, String weightUnit) {
        List<String> errorMessageList = new ArrayList<>();

        if (beforeProduct.getAdditional().getThirdParty() != null && beforeProduct.getAdditional().getThirdParty().isPackageConfirmed()) {
            boolean result = Stream.of(packingDepth, packingHeight, packingLength, weight, packingDimensionUnit, weightUnit,
                            beforeProduct.getPackingDepth(), beforeProduct.getPackingHeight(), beforeProduct.getPackingLength(),
                            beforeProduct.getWeight(), beforeProduct.getPackingDimensionUnit(), beforeProduct.getWeightUnit())
                    .allMatch(Objects::nonNull);
            result = result && packingDepth.compareTo(beforeProduct.getPackingDepth()) == 0;
            result = result && packingHeight.compareTo(beforeProduct.getPackingHeight()) == 0;
            result = result && packingLength.compareTo(beforeProduct.getPackingLength()) == 0;
            result = result && weight.compareTo(beforeProduct.getWeight()) == 0;
            result = result && StringUtils.equals(packingDimensionUnit, beforeProduct.getPackingDimensionUnit());
            result = result && StringUtils.equals(weightUnit, beforeProduct.getWeightUnit());

            if (!result) {
                errorMessageList.add(messageSource.getMessage("message119", null, null));
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

    private CheckProductResultDto checkBarcodeLock(ProductMasterResultDto beforeProduct, List<ProductBarcodeDto> currentBarcodeDtoList) {
        List<String> errorMessageList = new ArrayList<>();

        List<ProductBarcodeDto> beforeBarcodeDtoList = beforeProduct.getBarcodes();
        if(CollectionUtil.isNotEmpty(beforeBarcodeDtoList)){
            AtomicBoolean isSucess = new AtomicBoolean(true);
            beforeBarcodeDtoList.forEach( beforeBarcodeDto->{
				// if barcode is lock, then barcode EAN can not change
                if(isSucess.get() && beforeBarcodeDto.isLock()){
                    String beforeBarcode = beforeBarcodeDto.getEan();
                    String currentBarcode = currentBarcodeDtoList.stream()
                            .filter(currentBarcodeDto-> currentBarcodeDto.getSequenceNo().equals(beforeBarcodeDto.getSequenceNo()))
                            .map(ProductBarcodeDto::getEan)
                            .findFirst()
                            .orElse(null);

                    if(!beforeBarcode.equals(currentBarcode)){
                       isSucess.set(false);
                    }

                }
            });

            if (!isSucess.get()) {
                errorMessageList.add(messageSource.getMessage("message119", null, null));
            }
        }
        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }

	private CheckProductResultDto checkBarcode(List<ProductBarcodeDto> barcodes) {
		List<String> errorMessageList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(barcodes) && barcodes.stream().anyMatch(barcode -> StringUtil.ALL_EMOJI_PATTERN.matcher(barcode.getEan()).find())) {
			errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Barcode"}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkOverseaDeliveryDistrict(String source, UserDto userDto, List<String> deliveryDistrictList, StoreDo store,
															  BuProductCategoryDo category, String deliveryMethod, List<String> originalDeliveryDistrictList, String readyMethodCode) {
		List<String> errorMessageList = new ArrayList<>();

		if (CollectionUtil.isEmpty(deliveryDistrictList)) {
			return CheckProductResultDto.generate(errorMessageList);
		} else if (ProductReadyMethodType.MAINLAND_PRODUCT_READY_METHOD.contains(readyMethodCode)) {
			errorMessageList.add(messageSource.getMessage("message356", new String[]{readyMethodCode}, null));
		}

		//check Oversea Delivery District existance
		List<SysParmDo> overseaDeliveryList = sysParmRepository.findBySegmentAndPlatformId(SysParmSegment.OVERSEA_DELIVERY, 1);
		List<String> overseaDeliveryRegionCodeList = overseaDeliveryList.stream().map(SysParmDo::getCode).collect(Collectors.toList());

		boolean isWrongOverseaDeliveryFormat = false;
		for (String deliveryDistrict : deliveryDistrictList) {
			if (CollectionUtil.isNotEmpty(overseaDeliveryRegionCodeList) && !overseaDeliveryRegionCodeList.contains(deliveryDistrict)) {
				isWrongOverseaDeliveryFormat = true;
			}
		}

		if (isWrongOverseaDeliveryFormat) {
			errorMessageList.add(messageSource.getMessage("message123", null, null));
			return CheckProductResultDto.generate(errorMessageList);
		}

		//checkOverseaDeliveryDistrictMatchedStoreSetting
		List<StoreOverseaDeliveryDo> storeOverseaDeliveryDoList = storeOverseaDeliveryRepository.findByStoreId(store.getId());
		List<String> overseaDeliveryCountryList = storeOverseaDeliveryDoList.stream().map(StoreOverseaDeliveryDo::getRegion).collect(Collectors.toList());

		List<String> deliveryDistrictNonMatchedList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(overseaDeliveryCountryList)) {
			deliveryDistrictNonMatchedList = deliveryDistrictList.stream().filter(country -> !overseaDeliveryCountryList.contains(country)).collect(Collectors.toList());
		} else {
			deliveryDistrictNonMatchedList.addAll(deliveryDistrictList);
		}

		if (CollectionUtil.isNotEmpty(deliveryDistrictNonMatchedList)) {
			errorMessageList.add(messageSource.getMessage("message122", deliveryDistrictNonMatchedList.toArray(String[]::new), null));
		}

		//checkDeliveryDistrictAndCategoryOverseaShippable
		List<String> categoryShippableRegionList = Optional.ofNullable(buProductCategoryOverseaRepository.findByCategoryId(category.getId())).orElse(new ArrayList<>());
		for (String region : deliveryDistrictList) {
			boolean isCategoryOverseaShippable = categoryShippableRegionList.contains(region);
			if (!isCategoryOverseaShippable) {
				errorMessageList.add(messageSource.getMessage("message121", new String[]{category.getProductCatName(), region}, null));
			}
		}

		//checkOverseaDeliveryDistrictAndDeliveryMethod
		List<SysParmDo> overseaMethodList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.OVERSEA_METHOD, deliveryMethod, 1);
		List<String> overseaMethodCodeList = new ArrayList<>();
		for (SysParmDo overseaMethod : overseaMethodList) {
			if (StringUtil.isNotEmpty(overseaMethod.getParmValue())) {
				String[] overseaMethodCode = overseaMethod.getParmValue().split(",");
				overseaMethodCodeList = Arrays.asList(overseaMethodCode);
			}
		}

		List<String> nonMatchDeliveryMethodList = new ArrayList<>();
		for (String deliveryDistrict : deliveryDistrictList) {
			if (!overseaMethodCodeList.contains(deliveryDistrict)) {
				nonMatchDeliveryMethodList.add(deliveryDistrict);
			}
		}

		if (CollectionUtil.isNotEmpty(nonMatchDeliveryMethodList)) {
			errorMessageList.add(messageSource.getMessage("message120", new Object[]{deliveryMethod, nonMatchDeliveryMethodList}, null));
		}

		//check Oversea Delivery Region Restriction (MS-5851)
		Set<String> restrictedRegions = sysParamHelper.getSplitSystemParamsBySegmentAndBuCode(SysParmSegmentEnum.ADMIN_ALLOW_OVERSEA, BuCodeEnum.HKTV.name()).stream().collect(Collectors.toSet());
		if (SaveProductSource.OPEN_API.equals(source)) {
			List<String> notAllowedRegions = deliveryDistrictList.stream()
				.filter(district -> restrictedRegions.contains(district))
				.collect(Collectors.toList());

			if (!notAllowedRegions.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message343", new String[]{notAllowedRegions.toString()}, null));
			}
		} else if (!RoleCode.ALLOW_ADD_OVERSEA_DELIVERY_ROLES.contains(userDto.getRoleCode())) {
			List<String> notAllowedRegions = deliveryDistrictList.stream()
				.filter(district -> (originalDeliveryDistrictList == null || !originalDeliveryDistrictList.contains(district)) && restrictedRegions.contains(district))
				.collect(Collectors.toList());
			if (!notAllowedRegions.isEmpty()) {
				errorMessageList.add(messageSource.getMessage("message343", new String[]{notAllowedRegions.toString()}, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkDeliveryMethod(String deliveryMethod, String productReadyMethod, String contractType) {
		List<String> errorMessageList = new ArrayList<>();
		// productReadyMethod = DS, contractType = DS => deliveryMethod Always Merchant Delivery
		if (StringUtils.equals(ProductReadyMethodType.DISPLAY_STORE, productReadyMethod) && StringUtils.equals(ContractType.DISPLAY_STORE_CONTRACT, contractType)) {
			if(StringUtil.isNotEquals(ProductDeliverMethod.MERCHANT_DELIVERY, deliveryMethod)) {
				errorMessageList.add(messageSource.getMessage("message122", new String[]{deliveryMethod}, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkEvoucherAndContractType(String productReadyMethod, String contractType, BuProductCategoryDo primaryCategory) {
		List<String> errorMessageList = new ArrayList<>();
		String productCatCode = primaryCategory.getProductCatCode();
		String productPrimaryCategoryPrefix = productCatCode.substring(0, 4);
		boolean isEvoucherContractType = contractType != null && ContractType.SERVICE_DEAL.equalsIgnoreCase(contractType);
		boolean isEvoucherProductReadyMethod = ProductReadyMethodType.E_VOUCHER.equalsIgnoreCase(productReadyMethod);
		boolean isEvoucherCategoryPrefix = CategoryConfig.SERVICE_DEALS_CATEGORY_PREFIX.equalsIgnoreCase(productPrimaryCategoryPrefix);
		if (isEvoucherContractType) {
			if (!isEvoucherProductReadyMethod || !isEvoucherCategoryPrefix) {
				errorMessageList.add(messageSource.getMessage("message124", null, null));
			}
		} else {
			if (isEvoucherProductReadyMethod) {
				errorMessageList.add(messageSource.getMessage("message124", null, null));
			}
			if (isEvoucherCategoryPrefix && !ProductReadyMethodType.DISPLAY_STORE.equalsIgnoreCase(productReadyMethod)) {
				errorMessageList.add(messageSource.getMessage("message124", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public CheckProductResultDto checkInsuranceAndContractType(String contractType, BuProductCategoryDo primaryCategory) {
		List<String> errorMessageList = new ArrayList<>();
		String productCatCode = primaryCategory.getProductCatCode();
		String productPrimaryCategoryPrefix = productCatCode.substring(0, 4);
		boolean isInsuranceContractType = contractType != null && INSURANCE_CONTRACT_SET.contains(contractType);
		boolean isInsuranceCategoryPrefix = CategoryConfig.INSURANCE_CATEGORY_PREFIX.equalsIgnoreCase(productPrimaryCategoryPrefix);
		if (isInsuranceContractType) {
			if (!isInsuranceCategoryPrefix) {
				errorMessageList.add(messageSource.getMessage("message292", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private boolean isProductTypeCodeListContainsEvoucherCategory(List<String> productTypeCodeList) {
    	for (String productTypeCode : productTypeCodeList) {
    		if (CategoryConfig.SERVICE_DEALS_CATEGORY_PREFIX.equalsIgnoreCase(productTypeCode.substring(0, 4))) {
    			return true;
			}
		}
    	return false;
	}

	private CheckProductResultDto checkVirtualStore(Integer merchantId, String virtualStore) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(virtualStore)) {
			if (!merchantHelper.isVirtualStoreMerchant(merchantId, virtualStore)) {
				errorMessageList.add(messageSource.getMessage("message130", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

    CheckProductResultDto checkProductReadyDays(String contractType, String readyMethodCode, String productReadyDays) {
		List<String> errorMessageList = new ArrayList<>();
		if (ContractType.EVERUTS.equals(contractType)) {
			Pattern pattern = Pattern.compile("^[0-9]\\d*$");
			Matcher matcher = pattern.matcher(productReadyDays);
			if (!matcher.matches()) {
				log.info("Invalid Product Ready Days : {}", productReadyDays);
				errorMessageList.add(messageSource.getMessage("message364", new String[]{"Product Ready Days"}, null));
			}
			return CheckProductResultDto.generate(errorMessageList);
		}

		Set<String> productReadyDaysCode = sysParmRepository.findBySegment(SysParmSegmentEnum.PRODUCT_READY_DAYS.name()).stream().map(SysParmDo::getCode).collect(Collectors.toSet());
		if (productReadyDays != null && !productReadyDaysCode.contains(productReadyDays)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Product Ready Days"}, null));
		}

        if (ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB.equals(readyMethodCode)
            && !productReadyDays.equals("0")) {
            errorMessageList.add(messageSource.getMessage("message153", null, null));
        }

		if (ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED.equals(readyMethodCode)){
			Set<String> validNumbers = IntStream.rangeClosed(3, 5)
				.mapToObj(String::valueOf)
				.collect(Collectors.toSet());
			if (productReadyDays != null && !validNumbers.contains(productReadyDays)) {
				errorMessageList.add(messageSource.getMessage("message278", new String[]{readyMethodCode}, null));
			}
		}

		List<SysParmDo> validProductReadyDays = findReadyDaysService.start(BuCodeEnum.HKTV.name(), readyMethodCode).getData();
		if (CollectionUtil.isNotEmpty(validProductReadyDays) && validProductReadyDays.stream().noneMatch(data -> data.getParmValue().equals(productReadyDays))) {
			errorMessageList.add(messageSource.getMessage("message278", new String[]{readyMethodCode}, null));
		}

        return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
    }


	public CheckProductResultDto checkCartonPackingInformation(Integer cartonHeight, Integer cartonLength, Integer cartonDepth) {
		List<String> errorMessageList = new ArrayList<>();
		if(cartonHeight == null && cartonLength == null && cartonDepth == null){
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}

		if (cartonHeight == null || cartonLength == null || cartonDepth == null){
			errorMessageList.add(messageSource.getMessage("message140", null, null));
		}

		if(cartonHeight == 0 || cartonLength == 0 || cartonDepth == 0){
			errorMessageList.add(messageSource.getMessage("message141", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkUserMax(String contractType, Long userMax, String roleCode, Long beforeUserMax) {
		List<String> errorMessageList = new ArrayList<>();
		boolean isEvoucherContractType = contractType != null && ContractType.SERVICE_DEAL.equalsIgnoreCase(contractType);
		if (isEvoucherContractType) {
			if (userMax == null) {
				errorMessageList.add(messageSource.getMessage("message203", null, null));
			} else if (userMax > ConstantType.MAX_EVOUCHER_USER_MAX) {
				errorMessageList.add(messageSource.getMessage("message234", new String[]{ConstantType.MAX_EVOUCHER_USER_MAX.toString()}, null));
			} else if (userMax < ConstantType.MIN_EVOUCHER_USER_MAX) {
				errorMessageList.add(messageSource.getMessage("message269", new String[]{ConstantType.MIN_EVOUCHER_USER_MAX.toString()}, null));
			}
			// Non-evoucher
		} else {
			// merchant can't edit
			if (RoleCode.MERCHANT_ADMIN.equalsIgnoreCase(roleCode) ||
				RoleCode.MERCHANT.equalsIgnoreCase(roleCode)) {
				if(!Objects.equals(userMax, beforeUserMax)) {
					errorMessageList.add(messageSource.getMessage("message77", null, null));
				}
			} else {
				if (userMax != null && (userMax < ConstantType.MIN_USER_MAX || userMax > ConstantType.MAX_NORMAL_USER_MAX)) {
					errorMessageList.add(messageSource.getMessage("message234", new String[]{ConstantType.MAX_NORMAL_USER_MAX.toString()}, null));
				}
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkStorageType(String buCode, boolean isNewProduct, String productReadyMethod, String currentStorageType, String beforeStorageType) {

		List<String> errorMessageList = new ArrayList<>();
		if(StringUtil.isNotEmpty(currentStorageType)) {
			int parmCount = sysParmRepository.countBySegmentAndBuCodeAndParmValue(SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING, buCode, currentStorageType);

			if (parmCount == 0) {
				errorMessageList.add(messageSource.getMessage("message264", new String[]{"Storage Type"}, null));
			}
		}
		if (ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productReadyMethod)) {
			if (isNewProduct && StringUtil.isEmpty(currentStorageType)) {
				errorMessageList.add(messageSource.getMessage("message38", null, null));
			}
			boolean isStorageTypeUpdated = StringUtil.generateDefaultStringValue(beforeStorageType).equalsIgnoreCase(StringUtil.generateDefaultStringValue(currentStorageType));
			if (!isNewProduct && !isStorageTypeUpdated) {
				errorMessageList.add(messageSource.getMessage("message149", null, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkSkuNameContainEmoji(String skuNameEn, String skuNameCh, String skuNameSc) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(skuNameEn) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameEn).find()) {
			errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameEn}, null));
		}

		if (StringUtil.isNotEmpty(skuNameCh) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameCh).find()) {
			errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameCh}, null));
		}

		if (StringUtil.isNotEmpty(skuNameSc) && StringUtil.FOUR_BYTE_EMOJI_PATTERN.matcher(skuNameSc).find()) {
			errorMessageList.add(messageSource.getMessage("message169", new String[]{skuNameSc}, null));
		}

		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkProductReadyMethod(String productReadyMethodCode, CheckProductResultDto check3PlResult){
		List<String> errorMessageList = new ArrayList<>();
		List<String> productReadyMethodSysParmCodes = sysParmRepository.findBySegmentAndBuCode(SysParmSegment.PRODUCT_READY_METHOD, ConstantType.HKTV)
				.stream().map(sysParm-> sysParm.getCode()).collect(Collectors.toList());

		if(!productReadyMethodSysParmCodes.contains(productReadyMethodCode)){
			errorMessageList.add(messageSource.getMessage("message248", null, null));
		}

		if (ProductReadyMethodType.THIRD_PARTY.equals(productReadyMethodCode) && check3PlResult != null && CollectionUtil.isNotEmpty(check3PlResult.getErrorMessageList())) {
			errorMessageList.addAll(check3PlResult.getErrorMessageList());
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkSkuName(String skuNameEn, String skuNameCh, String skuNameSc) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(skuNameEn) && skuNameEn.length() > ConstantType.MAX_CHARACTERS_100) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
		}

		if (StringUtil.isNotEmpty(skuNameCh) && skuNameCh.length() > ConstantType.MAX_CHARACTERS_100) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name Chi length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
		}

		if (StringUtil.isNotEmpty(skuNameSc) && skuNameSc.length() > ConstantType.MAX_CHARACTERS_100) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"SKU Name SC length", String.valueOf(ConstantType.MAX_CHARACTERS_100)}, null));
		}

		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkMainPhoto(String mainPhoto) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(mainPhoto) && mainPhoto.split(",").length > ConstantType.LIMIT_OF_MAIN_PHOTO) {
			errorMessageList.add(messageSource.getMessage("message260", new String[]{String.valueOf(ConstantType.LIMIT_OF_MAIN_PHOTO)}, null));
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkAffiliateUrl(String affiliateUrl, String readyMethodCode, String contractType) {
		List<String> errorMessageList = new ArrayList<>();

		// When contract = DS (Display Store Contract) & Product Ready Method = DS(Display)
		if (StringUtils.equals(ProductReadyMethodType.DISPLAY_STORE, readyMethodCode) && StringUtils.equals(ContractType.DISPLAY_STORE_CONTRACT, contractType)) {
			if(StringUtil.isNullOrBlank(affiliateUrl)) {
				errorMessageList.add(messageSource.getMessage("message215", null, null));
			} else {
				if (affiliateUrl.length() > ConstantType.MAX_CHARACTERS_1024) {
					errorMessageList.add(messageSource.getMessage("message279", new String[]{"Affiliate Url length", String.valueOf(ConstantType.MAX_CHARACTERS_1024)}, null));
				}
				if(!URL_PATTERN.matcher(affiliateUrl).matches()) {
					errorMessageList.add(messageSource.getMessage("message294", null, null));
				}
			}
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkDiscountText(String discountTextEn, String discountTextCh, String discountTextSc) {
		List<String> errorMessageList = new ArrayList<>();
		if (StringUtil.isNotEmpty(discountTextEn) && discountTextEn.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (Eng)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		if (StringUtil.isNotEmpty(discountTextCh) && discountTextCh.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (Chi)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		if (StringUtil.isNotEmpty(discountTextSc) && discountTextSc.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Discount Text (SC)", String.valueOf(ConstantType.MAX_CHARACTERS_50)}, null));
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkPackingSpec(String packingSpecEn, String packingSpecCh, String packingSpecSc) {
		List<String> errorMessageList = new ArrayList<>();
		if(StringUtil.isNotEmpty(packingSpecEn)){
			if (packingSpecEn.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (Eng)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecEn).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (Eng)"}, null));
			}
		}
		if(StringUtil.isNotEmpty(packingSpecCh)){
			if (packingSpecCh.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (Chi)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecCh).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (Chi)"}, null));
			}
		}
		if(StringUtil.isNotEmpty(packingSpecSc)){
			if (packingSpecSc.length() > ConstantType.MAX_CHARACTERS_20) {
				errorMessageList.add(messageSource.getMessage("message270", new String[]{"Packing Spec (SC)", String.valueOf(ConstantType.MAX_CHARACTERS_20)}, null));
			}
			if (StringUtil.ALL_EMOJI_PATTERN.matcher(packingSpecSc).find()) {
				errorMessageList.add(messageSource.getMessage("message280", new String[]{"SKU Packing Spec (SC)"}, null));
			}
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkCartonSize(List<CartonSizeDto> cartonSizeDtoList) {
		List<String> errorMessageList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(cartonSizeDtoList)) {
			for (CartonSizeDto cartonSizeDto : cartonSizeDtoList) {
				if (cartonSizeDto.getHeight() != null && cartonSizeDto.getHeight() < 0) {
					errorMessageList.add(messageSource.getMessage("message271", new String[]{"Carton Height(mm)"}, null));
				}
				if (cartonSizeDto.getWidth() != null && cartonSizeDto.getWidth() < 0) {
					errorMessageList.add(messageSource.getMessage("message271", new String[]{"Carton Depth(mm)"}, null));
				}
				if (cartonSizeDto.getLength() != null && cartonSizeDto.getLength() < 0) {
					errorMessageList.add(messageSource.getMessage("message271", new String[]{"Carton Length(mm)"}, null));
				}

				if ((cartonSizeDto.getHeight() != null &&cartonSizeDto.getHeight() == 0) ||
					(cartonSizeDto.getWidth() != null &&cartonSizeDto.getWidth() == 0) ||
					(cartonSizeDto.getLength() != null &&cartonSizeDto.getLength() == 0)) {
					errorMessageList.add(messageSource.getMessage("message141", null, null));
				}
			}


		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkVisibility(String visibility, String productReadyMethod, String productStatus) {
		List<String> errorMessageList = new ArrayList<>();
		if (visibility != null && !ExcelUtil.YN_LIST.contains(visibility)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Invisible flag"}, null));
		}
		if (ProductReadyMethodType.E_VOUCHER.equals(productReadyMethod) && ConstantType.CONSTANT_YES.equals(visibility) && !StringUtils.equals(productStatus, ProductStatus.COMPLETED)) {
			errorMessageList.add(messageSource.getMessage("message272", null, null));
		}
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkDateAndExpiryType(String redeemStartDate, String fixRedemptionDate,
											String featureStartDate, String featureEndDate, Integer uponPurchaseDate, String expiryType) {
		List<String> errorMessageList = new ArrayList<>();

		LocalDateTime redeemStartDateTime = checkIso8601UtcPatten(redeemStartDate, errorMessageList, "Redeem Start Date");
		LocalDateTime fixRedemptionDateTime = checkIso8601UtcPatten(fixRedemptionDate, errorMessageList, "Fixed Redemption Date");
		LocalDateTime featureStartDateTime = checkIso8601UtcPatten(featureStartDate, errorMessageList, "Feature Start Date/Time");
		LocalDateTime featureEndDateTime = checkIso8601UtcPatten(featureEndDate, errorMessageList, "Feature End Date/Time");

		if(StringUtil.isNotEmpty(expiryType) && !ExpiryTypeEnum.isContains(expiryType)){
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Expiry Type"}, null));

		} else if (ExpiryTypeEnum.FIXED.name().equals(expiryType)){
			if (redeemStartDateTime != null && fixRedemptionDateTime != null && fixRedemptionDateTime.toLocalDate().isBefore(redeemStartDateTime.toLocalDate())) {
				errorMessageList.add(messageSource.getMessage("message275", new String[]{"Redeem Start Date", "Fixed Redemption Date"}, null));
			}
			if (fixRedemptionDateTime != null && featureEndDateTime != null && fixRedemptionDateTime.toLocalDate().isBefore(featureEndDateTime.toLocalDate())) {
				errorMessageList.add(messageSource.getMessage("message274", new String[]{"Fixed Redemption Date", "Feature End Date"}, null));
			}
			if(uponPurchaseDate != null){
				errorMessageList.add(messageSource.getMessage("message285", new String[]{"Upon Purchase Date", "Expiry type is FIXED"}, null));
			}

		} else if (ExpiryTypeEnum.RELATIVELY.name().equals(expiryType)){
			if (fixRedemptionDateTime != null) {
				errorMessageList.add(messageSource.getMessage("message285", new String[]{"Fixed Redemption Date(yyyy-MM-dd)", "Expiry type is Relative"}, null));
			}
			if (uponPurchaseDate != null && uponPurchaseDate > ConstantType.UPON_PURCHASE_DATE_MAX) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Upon Purchase Date", Integer.toString(ConstantType.UPON_PURCHASE_DATE_MAX)}, null));
			}
		}

		if (redeemStartDateTime != null && featureStartDateTime != null && redeemStartDateTime.toLocalDate().isBefore(featureStartDateTime.toLocalDate())) {
			errorMessageList.add(messageSource.getMessage("message274", new String[]{"Redeem Start Date", "Feature Start Date"}, null));
		}
		if (featureStartDateTime != null && featureEndDateTime != null && featureEndDateTime.isBefore(featureStartDateTime)) {
			errorMessageList.add(messageSource.getMessage("message275", new String[]{"Feature Start Date", "Feature End Date"}, null));
		}

		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkVoucherType(String voucherType, String voucherDisplayType, String voucherTemplateType){
		List<String> errorMessageList = new ArrayList<>();
		List<SysParmDo> sysParmDoList = sysParmRepository.findBySegments(List.of(SysParmSegmentEnum.VOUCHER_TYPE.name(), SysParmSegmentEnum.VOUCHER_DISPLAY_TYPE.name(), SysParmSegmentEnum.VOUCHER_TEMPLATE_TYPE.name()));
		Map<String, Set<String>> segmentCodeMap = sysParmDoList.stream()
			.collect(Collectors.groupingBy(data -> data.getSegment(), Collectors.mapping(data -> data.getCode(), Collectors.toSet())));
		Set<String> voucherTemplateTypeSet = new HashSet<>();
		// SysParm save TemplateA or TemplateB, but voucherTemplateType data is A or TemplateA or B or TemplateB
		segmentCodeMap.get(SysParmSegmentEnum.VOUCHER_TEMPLATE_TYPE.name()).forEach(data -> {
			voucherTemplateTypeSet.add(data);
			voucherTemplateTypeSet.add(data.substring(data.length() - 1));
		});

		if (StringUtil.isNotEmpty(voucherType) && !segmentCodeMap.get(SysParmSegmentEnum.VOUCHER_TYPE.name()).contains(voucherType)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Voucher Type"}, null));
		}
		if (StringUtil.isNotEmpty(voucherDisplayType) && !segmentCodeMap.get(SysParmSegmentEnum.VOUCHER_DISPLAY_TYPE.name()).contains(voucherDisplayType)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Voucher Display Type"}, null));
		}
		if (StringUtil.isNotEmpty(voucherTemplateType) && !voucherTemplateTypeSet.contains(voucherTemplateType)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Voucher Template Type"}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkFinePrint(String finePrintEn, String finePrintCh, String finePrintSc) {
		List<String> errorMessageList = new ArrayList<>();
		errorMessageList.addAll(checkTextLength(finePrintEn, "Fine Print(Eng)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_30000));
		errorMessageList.addAll(checkTextLength(finePrintCh, "Fine Print(Chi)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_30000));
		errorMessageList.addAll(checkTextLength(finePrintSc, "Fine Print(SC)", ConstantType.MAX_CHARACTERS_10000, ConstantType.MAX_CHARACTERS_30000));
		return CheckProductResultDto.generate(errorMessageList);
	}

	private CheckProductResultDto checkGoodsType(String goodsType) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(goodsType) && !ExcelUtil.GOODS_TYPE_LIST.contains(goodsType)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Goods Type"}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkWarrenty(String warrantyPeriodUnit, Integer warrantyPeriod, String warrantySupplierEn,
												String warrantySupplierCh, String warrantySupplierSc, String warrantyRemarkEn, String warrantyRemarkCh, String warrantyRemarkSc) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(warrantyPeriodUnit) && !ExcelUtil.WARRANTY_PERIOD_UNIT_LIST.contains(warrantyPeriodUnit)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Warranty Period Unit"}, null));
		}
		if (warrantyPeriod != null) {
			if (warrantyPeriod < 0) {
				errorMessageList.add(messageSource.getMessage("message271", new String[]{"Warranty Period"}, null));
			} else if (warrantyPeriod > ConstantType.WARRANTY_PERIOD_MAX) {
				errorMessageList.add(messageSource.getMessage("message263", new String[]{"Warranty Period", Integer.toString(ConstantType.WARRANTY_PERIOD_MAX)}, null));
			}
		}
		if (StringUtil.isNotEmpty(warrantySupplierEn) && warrantySupplierEn.length() > ConstantType.MAX_CHARACTERS_200) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Supplier(Eng)", ConstantType.MAX_CHARACTERS_200.toString()}, null));
		}
		if (StringUtil.isNotEmpty(warrantySupplierCh) && warrantySupplierCh.length() > ConstantType.MAX_CHARACTERS_200) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Supplier(Chi)", ConstantType.MAX_CHARACTERS_200.toString()}, null));
		}
		if (StringUtil.isNotEmpty(warrantySupplierSc) && warrantySupplierSc.length() > ConstantType.MAX_CHARACTERS_200) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Supplier(SC)", ConstantType.MAX_CHARACTERS_200.toString()}, null));
		}
		if (StringUtil.isNotEmpty(warrantyRemarkEn) && warrantyRemarkEn.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Remark (Eng)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(warrantyRemarkCh) && warrantyRemarkCh.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Remark (Chi)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(warrantyRemarkSc) && warrantyRemarkSc.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Warranty Remark (SC)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkServiceCentreAndInvoiceRemarks(String serviceCentreAddressEn, String serviceCentreAddressCh, String serviceCentreAddressSc, String serviceCentreEmail,
																	  String serviceCentreContact, String invoiceRemarksEn, String invoiceRemarksCh, String invoiceRemarksSc) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(invoiceRemarksEn) && invoiceRemarksEn.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Invoice Remark(Eng)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(invoiceRemarksCh) && invoiceRemarksCh.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Invoice Remark(Chi)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(invoiceRemarksSc) && invoiceRemarksSc.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Invoice Remark(SC)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(serviceCentreAddressEn) && serviceCentreAddressEn.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Service Centre Address(Eng)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(serviceCentreAddressCh) && serviceCentreAddressCh.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Service Centre Address(Chi)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(serviceCentreAddressSc) && serviceCentreAddressSc.length() > ConstantType.MAX_CHARACTERS_1000) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Service Centre Address(SC)", ConstantType.MAX_CHARACTERS_1000.toString()}, null));
		}
		if (StringUtil.isNotEmpty(serviceCentreEmail) && serviceCentreEmail.length() > ConstantType.MAX_CHARACTERS_200) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Service Centre Email", ConstantType.MAX_CHARACTERS_200.toString()}, null));
		}
		if (StringUtil.isNotEmpty(serviceCentreContact) && serviceCentreContact.length() > ConstantType.MAX_CHARACTERS_50) {
			errorMessageList.add(messageSource.getMessage("message270", new String[]{"Service Centre Contact", ConstantType.MAX_CHARACTERS_50.toString()}, null));
		}

		if (StringUtil.isNotEmpty(serviceCentreEmail) && !StringUtil.EMAIL_PATTERN.matcher(serviceCentreEmail).matches()) {
			errorMessageList.add(messageSource.getMessage("message276", null, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkStyle(String style) {
		List<String> errorMessageList = new ArrayList<>();

		if (StringUtil.isNotEmpty(style) && !ExcelUtil.STYLE_LIST.contains(style)) {
			errorMessageList.add(messageSource.getMessage("message264", new String[]{"Style"}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	 public CheckProductResultDto checkForceOfflineProductOnlineStatus(OnlineStatusEnum onlineStatus, ProductMasterResultDto beforeProduct) {
		List<String> errorMessageList = new ArrayList<>();

		// hktv product
		if (Objects.nonNull(beforeProduct.getAdditional()) && Objects.nonNull(beforeProduct.getAdditional().getHktv())) {
			// if FORCE_OFFLINE is true CAN NOT set ONLINE_STATUS(online)
			if (Boolean.TRUE.equals(beforeProduct.getAdditional().getHktv().getForceOffline()) &&
				Objects.nonNull(onlineStatus) &&
				OnlineStatusEnum.ONLINE.equals(onlineStatus)) {
				errorMessageList.add(messageSource.getMessage("message372", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkProductCategoryByContractTypeCode(String contractTypeCode, List<String> productTypeCodeList) {
		List<String> errorMessageList = new ArrayList<>();

		boolean isMainlandContract = ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractTypeCode);
		if (isMainlandContract && CollectionUtil.isNotEmpty(productTypeCodeList)) {
			List<BuCategoryRestrictionDo> buCategoryRestrictionForMainland = buCategoryRestrictionRepository.findByProductReadyMethodAndProductCatCodesAndBuCode(ProductReadyMethodType.MAINLAND_DELIVERY, productTypeCodeList, ConstantType.PLATFORM_CODE_HKTV);
			if (CollectionUtils.isNotEmpty(buCategoryRestrictionForMainland)) {
				log.warn("Restrition Category Can not set for product. Restrition Categories: [{}], Contract Type: {}", buCategoryRestrictionForMainland.stream().map(BuCategoryRestrictionDo::getProductCatCode).filter(Objects::nonNull).collect(Collectors.joining(", ")), contractTypeCode);
				buCategoryRestrictionForMainland.forEach(buCategoryRestrictionDo -> errorMessageList.add(messageSource.getMessage("message282", new String[]{buCategoryRestrictionDo.getProductCatCode(), contractTypeCode}, null)));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}


	public LocalDateTime checkIso8601UtcPatten(String localDateTimeString, List<String> errorMessageList, String errorMessage){
		if (localDateTimeString != null) {
			if (DateUtil.ISO8601_UTC_PATTEN.matcher(localDateTimeString).matches()) {
				try {
					return LocalDateTime.parse(localDateTimeString, DateTimeFormatter.ofPattern(DateUtil.ISO8601_UTC));
				} catch (Exception e) {
					errorMessageList.add(messageSource.getMessage("message273", new String[]{errorMessage, DateUtil.ISO8601_UTC + " or " + DateUtil.ISO8601_UTC_WITHOUT_SSSZ}, null));
				}
			} else if (DateUtil.ISO8601_UTC_PATTEN_WITHOUT_SSSZ.matcher(localDateTimeString).matches()) {
				try {
					return LocalDateTime.parse(localDateTimeString, DateTimeFormatter.ofPattern(DateUtil.ISO8601_UTC_WITHOUT_SSSZ));
				} catch (Exception e) {
					errorMessageList.add(messageSource.getMessage("message273", new String[]{errorMessage, DateUtil.ISO8601_UTC + " or " + DateUtil.ISO8601_UTC_WITHOUT_SSSZ}, null));
				}
			} else {
				errorMessageList.add(messageSource.getMessage("message273", new String[]{errorMessage, DateUtil.ISO8601_UTC + " or " + DateUtil.ISO8601_UTC_WITHOUT_SSSZ}, null));
			}
		}
		return null;
	}

	private List<String> checkTextLength(String checkText, String errorMessageString, Integer maxCharactersWithoutHtml, Integer maxCharactersWithHtml) {
		List<String> errorMessages = new ArrayList<>();
		if (StringUtil.isEmpty(checkText)) {
			return errorMessages;
		}

		// has html tag or no include html tag -> retun same error message
		if (StringUtil.HTML_PATTERN.matcher(checkText).matches()) {
			if (checkText.length() > maxCharactersWithHtml) {
				errorMessages.add(messageSource.getMessage("message270", new String[]{errorMessageString, String.valueOf(maxCharactersWithHtml)}, null));
			}
		} else {
			if (checkText.length() > maxCharactersWithoutHtml) {
				errorMessages.add(messageSource.getMessage("message270", new String[]{errorMessageString, String.valueOf(maxCharactersWithoutHtml)}, null));
			}
		}
		return errorMessages;
	}

	private CheckProductResultDto checkEwSku(ProductCheckDto productCheckDto) {
		List<String> errorMessageList = new ArrayList<>();
		BigDecimal ewPercentageSetting = productCheckDto.getEwPercentageSetting();
		// not EW and ewPercentageSetting != null
		if (!CategoryConfig.EW_SKU_CATEGORY.equals(productCheckDto.getPrimaryCategoryCode())) {
			if (ewPercentageSetting != null) {
				errorMessageList.add(messageSource.getMessage("message297", null, null));
			}
			return CheckProductResultDto.generate(errorMessageList);
		}
		// is EW
		if (ewPercentageSetting == null ||
			ewPercentageSetting.compareTo(BigDecimal.ZERO) <= 0 ||
			ewPercentageSetting.compareTo(ConstantType.MAX_EW_PERCANTAGE_SETTING) > 0) {
			errorMessageList.add(messageSource.getMessage("message296", new String[]{ConstantType.MAX_EW_PERCANTAGE_SETTING.toPlainString()}, null));
		}

		if (StringUtil.isNotEmpty(productCheckDto.getClaimLinkEn()) && productCheckDto.getClaimLinkEn().length() > ConstantType.MAX_CHARACTERS_1024) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"Claim Link(EN) length", String.valueOf(ConstantType.MAX_CHARACTERS_1024)}, null));
		}
		if (StringUtil.isNotEmpty(productCheckDto.getClaimLinkCh()) && productCheckDto.getClaimLinkCh().length() > ConstantType.MAX_CHARACTERS_1024) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"Claim Link(CH) length", String.valueOf(ConstantType.MAX_CHARACTERS_1024)}, null));
		}
		if (StringUtil.isNotEmpty(productCheckDto.getClaimLinkSc()) && productCheckDto.getClaimLinkSc().length() > ConstantType.MAX_CHARACTERS_1024) {
			errorMessageList.add(messageSource.getMessage("message279", new String[]{"Claim Link(SC) length", String.valueOf(ConstantType.MAX_CHARACTERS_1024)}, null));
		}

		List<String> catgeoryCodeList = productCheckDto.getProductTypeCodeList();
		if (CollectionUtil.isEmpty(catgeoryCodeList) ||
			catgeoryCodeList.size() > 1 ||
			!CategoryConfig.EW_SKU_CATEGORY.equals(catgeoryCodeList.get(0))) {
			errorMessageList.add(messageSource.getMessage("message298", new String[]{CategoryConfig.EW_SKU_CATEGORY}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkEditEwSku(String primaryCategoryCode, String beforePrimaryCategoryCode) {
		List<String> errorMessageList = new ArrayList<>();

		if (CategoryConfig.EW_SKU_CATEGORY.equals(beforePrimaryCategoryCode) &&
			!CategoryConfig.EW_SKU_CATEGORY.equals(primaryCategoryCode)) {
			errorMessageList.add(messageSource.getMessage("message298", new String[]{CategoryConfig.EW_SKU_CATEGORY}, null));
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkExternalPlatorm(UserDto userDto, ExternalPlatform externalPlatform, ProductMasterResultDto beforeProduct) {
		List<String> errorMessageList = new ArrayList<>();

		if (!Optional.ofNullable(externalPlatform).isPresent()) {
			return CheckProductResultDto.builder()
				.errorMessageList(errorMessageList)
				.result(errorMessageList.isEmpty())
				.build();
		}

		if (externalPlatform.getSource() != null && !externalPlatform.getSource().isEmpty()) {
			externalPlatform.getSource().stream().forEach(source -> {
				errorMessageList.addAll(checkTextLength(source, "Partner Platform",
					ConstantType.MAX_CHARACTERS_100, ConstantType.MAX_CHARACTERS_65535));
			});
		}

		if (StringUtil.isNotEmpty(externalPlatform.getProductId())) {
			errorMessageList.addAll(checkTextLength(externalPlatform.getProductId(), "Partner Product ID",
				ConstantType.MAX_CHARACTERS_100, ConstantType.MAX_CHARACTERS_65535));
		}

		if (StringUtil.isNotEmpty(externalPlatform.getSkuId())) {
			errorMessageList.addAll(checkTextLength(externalPlatform.getSkuId(), "Partner SKU ID",
				ConstantType.MAX_CHARACTERS_100, ConstantType.MAX_CHARACTERS_65535));
		}

		boolean hasEditPermission = ConstantType.ROLE_TYPE_SYSTEM.equals(userDto.getRoleType()) ||
									TokenHelper.SYSTEM_USER_ROLE_CODE.equals(userDto.getRoleCode()) ||
									SystemUserEnum.SYSTEM.getSystemRoleCode().equals(userDto.getUserCode());

		if (!hasEditPermission &&
			Optional.ofNullable(beforeProduct)
				.map(ProductMasterProductDto::getAdditional)
				.map(BuProductDto::getHktv)
				.map(HktvProductDto::getExternalPlatform)
				.isPresent()) {

			checkProductMissingFieldService.checkExternalPlatform(errorMessageList, externalPlatform);

			ExternalPlatform beforeExternalPlatform = beforeProduct.getAdditional().getHktv().getExternalPlatform();
			if (beforeExternalPlatform.getSource() != null &&
				!CollectionUtils.isEqualCollection(externalPlatform.getSource(), beforeExternalPlatform.getSource())) {
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Partner Platform"}, null));
			}

			if (StringUtils.isNotEmpty(beforeExternalPlatform.getProductId()) &&
				isFieldModified(externalPlatform.getProductId(), beforeExternalPlatform.getProductId())) {
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Partner Product ID"}, null));
			}

			if (StringUtils.isNotEmpty(beforeExternalPlatform.getSkuId()) &&
				isFieldModified(externalPlatform.getSkuId(), beforeExternalPlatform.getSkuId())) {
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Partner SKU ID"}, null));
			}
		}

		return CheckProductResultDto.builder()
			.errorMessageList(errorMessageList)
			.result(errorMessageList.isEmpty())
			.build();
	}

	private CheckProductResultDto checkCannotChangeFields(ProductCheckDto productCheckDto, ProductDo existProductDo) {
		List<String> errorMessageList = new ArrayList<>();
		if (productCheckDto == null || existProductDo == null) {
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}
		// e-voucher can not change voucherType and voucherDisplayType and expiryType
		if (StringUtils.equals(ProductReadyMethodType.E_VOUCHER, productCheckDto.getReadyMethodCode())) {
			if (StringUtil.isNotEquals(productCheckDto.getVoucherType(), existProductDo.getVoucherType())){
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Voucher Type"}, null));
			}
			if (StringUtil.isNotEquals(productCheckDto.getVoucherDisplayType(), existProductDo.getVoucherDisplayType())){
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Voucher Display Type"}, null));
			}
			if (StringUtil.isNotEquals(productCheckDto.getExpiryType(), existProductDo.getExpiryType())){
				errorMessageList.add(messageSource.getMessage("message306", new String[]{"Expiry Type"}, null));
			}
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkMembershipPricingEventSet(UserDto userDto, List<String> deliveryDistrictList, String storeCode, String skuId, MembershipPricingEventSetDto checkPricingResultDto) {
		List<String> errorMessageList = new ArrayList<>();

		List<String> deliveryRestrictList = Optional.ofNullable(deliveryDistrictList)
			.orElse(Collections.emptyList())
			.stream()
			.filter(district -> PromotionHelper.CHECK_OVERSEA_REGION.contains(district))
			.collect(Collectors.toList());

		if (deliveryRestrictList.isEmpty()) {
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}

		if (checkPricingResultDto == null) {
			log.error("Unable to request promotion check membership pricing event set.");
			errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PROMOTION_CHECK_MEMBERSHIP_PRICING_ONGOING}, null));
		} else if (checkPricingResultDto.getIsEventSet()) {
			errorMessageList.add(messageSource.getMessage("message334", new String[]{String.join(", ", deliveryRestrictList)}, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private CheckProductResultDto checkPlusPrice(BigDecimal originalPrice, BigDecimal sellingPrice, MembershipPricingEventSetDto checkPricingResultDto) {
		List<String> errorMessageList = new ArrayList<>();
		BigDecimal adjustedOriginalPrice = originalPrice.multiply(promotionHelper.PRICE_THRESHOLD);
		BigDecimal adjustedSellingPrice = sellingPrice != null && sellingPrice.compareTo(BigDecimal.ZERO) > 0  ? sellingPrice.multiply(promotionHelper.PRICE_THRESHOLD) : null;

		if (checkPricingResultDto == null) {
			log.error("Unable to request promotion check membership pricing event set.");
			errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PROMOTION_CHECK_MEMBERSHIP_PRICING_ONGOING}, null));
		} else if (checkPricingResultDto.getIsEventSet()) {
			if (adjustedOriginalPrice.compareTo(checkPricingResultDto.getPlusPrice()) < 0) {
				log.info("Adjusted original price {} is less than plus price {}", adjustedOriginalPrice, checkPricingResultDto.getPlusPrice());
				errorMessageList.add(messageSource.getMessage("message326", null, null));
			}

			if (adjustedSellingPrice != null && adjustedSellingPrice.compareTo(checkPricingResultDto.getPlusPrice()) < 0) {
				log.info("Adjusted selling price {} is less than plus price {}", adjustedSellingPrice, checkPricingResultDto.getPlusPrice());
				errorMessageList.add(messageSource.getMessage("message327", null, null));
			}
		}

		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	public List<String> checkOpenApiCreateProduct(OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData, StoreContractMerchantDo storeContractMerchantDo) {
		List<String> errorMessages = new ArrayList<>();
		if (!StringUtils.equalsAny(oapiSingleSaveMainRequestData.getOnOfflineStatus(), OnlineStatusEnum.ONLINE.name(), OnlineStatusEnum.OFFLINE.name())) {
			errorMessages.add(messageSource.getMessage("message264", new String[]{"SKU Status"}, null));
		}
		if (!ExcelUtil.YN_LIST.contains(oapiSingleSaveMainRequestData.getIsPrimarySku())) {
			errorMessages.add(messageSource.getMessage("message264", new String[]{"Is Primary SKU"}, null));
		}

		if (ContractType.EVERUTS.equals(storeContractMerchantDo.getContractType())) {
			if (oapiSingleSaveMainRequestData.getPartnerInfo() == null || oapiSingleSaveMainRequestData.getPartnerInfo().getEveruts() == null) {
				errorMessages.add(messageSource.getMessage("message307", new String[]{"Buyer ID"}, null));
				errorMessages.add(messageSource.getMessage("message307", new String[]{"Everuts SKU ID"}, null));
			} else {
				String everutsSkuId = oapiSingleSaveMainRequestData.getPartnerInfo().getEveruts().getSkuId();
				ValidationCheckUtil.checkNullOrEmpty(errorMessages, oapiSingleSaveMainRequestData.getPartnerInfo().getEveruts().getBuyerId(), messageSource.getMessage("message307", new String[]{"Buyer ID"}, null));
				ValidationCheckUtil.checkNullOrEmpty(errorMessages, everutsSkuId, messageSource.getMessage("message307", new String[]{"Everuts SKU ID"}, null));

				if (everutsSkuId != null && everutsSkuId.length() > ConstantType.MAX_CHARACTERS_255) {
					errorMessages.add(messageSource.getMessage("message270", new String[]{"Everuts SKU ID", ConstantType.MAX_CHARACTERS_255.toString()}, null));
				}
			}
		}

		Optional<Long> buyerIdOpt = Optional.ofNullable(oapiSingleSaveMainRequestData)
			.map(OapiSingleSaveMainRequestData::getPartnerInfo)
			.map(OapiSingleSavePartnerInfoRequestData::getEveruts)
			.map(OapiSingleSaveEverutsRequestData::getBuyerId);
		if (buyerIdOpt.isPresent()) {
			Long buyerId = buyerIdOpt.get();
			if (buyerId < 1) {
				errorMessages.add(messageSource.getMessage("message320", null, null));
			} else if (everutsBuyerRepository.findById(buyerId).isEmpty()) {
				errorMessages.add(messageSource.getMessage("message264", new String[]{"Buyer ID"}, null));
			}
		}

		return errorMessages;
	}

	private CheckProductResultDto checkProductReadyMethodWithPrimaryCategoryOrBrandModified(ProductCheckDto productCheckDto, ProductMasterResultDto beforeProduct) {
		List<String> errorMessageList = new ArrayList<>();
		if (beforeProduct == null || beforeProduct.getAdditional() == null || beforeProduct.getAdditional().getHktv() == null) {
			return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
		}

		boolean isProductReadyMethodModified = isFieldModified(productCheckDto.getReadyMethodCode(), beforeProduct.getAdditional().getHktv().getProductReadyMethod());
		boolean isPrimaryCategoryOrBrandModified = isFieldModified(productCheckDto.getPrimaryCategoryCode(), beforeProduct.getAdditional().getHktv().getPrimaryCategoryCode())
			|| isFieldModified(productCheckDto.getBrandId(), beforeProduct.getBrandId());

		if (isPrimaryCategoryOrBrandModified && isProductReadyMethodModified) {
			errorMessageList.add(messageSource.getMessage("message293", null, null));
		}
		return CheckProductResultDto.builder().errorMessageList(errorMessageList).result(errorMessageList.isEmpty()).build();
	}

	private boolean isFieldModified(Object newValue, Object originalValue) {
		return (newValue != null && !newValue.equals(originalValue)) || (newValue == null && originalValue != null);
	}

	public CheckProductResultDto checkUpdateEverutsField(ProductCheckDto productCheckDto, ProductMasterResultDto beforeProduct) {
		List<String> errorMessages = new ArrayList<>();

		//user is allow to edit everuts field if previous everuts field is null or empty
		if (Optional.ofNullable(beforeProduct.getAdditional().getHktv())
			.map(HktvProductDto::getPartnerInfo)
			.map(ProductPartnerInfoDto::getEveruts)
			.isEmpty() ||
			Optional.ofNullable(productCheckDto)
				.map(ProductCheckDto::getPartnerInfo)
				.map(ProductPartnerInfoDto::getEveruts)
				.isEmpty()) {
			return CheckProductResultDto.generate(errorMessages);
		}

		EverutsInfoDto beforeEveruts = beforeProduct.getAdditional().getHktv().getPartnerInfo().getEveruts();
		EverutsInfoDto afterEveruts = productCheckDto.getPartnerInfo().getEveruts();

		if (StringUtil.isNotEmpty(beforeEveruts.getSkuId()) && !Objects.equals(beforeEveruts.getSkuId(), afterEveruts.getSkuId())) {
			errorMessages.add(messageSource.getMessage("message321", new String[]{"Everuts Sku ID"}, null));
		}

		if (beforeEveruts.getBuyerId() != null && !Objects.equals(beforeEveruts.getBuyerId(), afterEveruts.getBuyerId())) {
			errorMessages.add(messageSource.getMessage("message321", new String[]{"Buyer ID"}, null));
		}
		return CheckProductResultDto.generate(errorMessages);
	}

}
