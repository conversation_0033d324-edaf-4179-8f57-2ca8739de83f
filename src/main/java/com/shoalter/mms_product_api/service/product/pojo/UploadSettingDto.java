package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
public class UploadSettingDto {
    private String imageUpload;
    private String imageStatus;
    private String imageDelete;
    private String imagesDomain;
    private String uploadFileVideo;
    private String videoUpload;
    private String videoStatus;
    
    private Set<String> imagesDomainList;

    public Set<String> getImagesDomainList() {
        if(null == imagesDomainList)  imagesDomainList = new HashSet<>(Arrays.asList(imagesDomain.split(",")));
        return imagesDomainList;
    }
}

