package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantStoreDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreInfoDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.mapper.OapiDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditPriceMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditProductReadyDaysMainRequestData;
import com.shoalter.mms_product_api.service.product.CheckProductMissingFieldService;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOnlineStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaReserveRegionDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPackingDimensionDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPriceDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductBaseDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductReadyDaysDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditTranslateDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditVisibilityDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchUpdateProductInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.OapiBatchEditProductBaseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductForceOfflineDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckErrorResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BatchEditHelper {
	private final PermissionHelper permissionHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final CheckProductMissingFieldService checkProductMissingFieldService;
	private final ProductRepository productRepository;
	private final StoreRepository storeRepository;
	private final MerchantStoreRepository merchantStoreRepository;
	private final MessageSource messageSource;
	private final Gson gson;
	private final OapiDataMapper oapiDataMapper;

	@Transactional
	public <T extends BatchEditProductBaseDto> ResponseDto<Set<Long>> createBatchEditRecord(
		UserDto userDto, BatchEditProductRequestDto<T> batchEditProductRequestDto, int uploadType, String clientIp) {

		//data check
		if (CollectionUtil.isEmpty(batchEditProductRequestDto.getData())) {
			throw new NoDataException();
		}

		if (batchEditProductRequestDto.getData().size() > BatchCheckHelper.MAXIMUM_10000) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.MAXIMUM_10000)}, null)));
		}

		List<Pair<T, String>> failProductList = new ArrayList<>();
		Set<String> storeSkuIds = new HashSet<>();
		List<T> existStoreProductList = new ArrayList<>();

		//check store existence
		Set<String> storefrontStoreCodes = batchEditProductRequestDto.getData().stream().map(BatchEditProductBaseDto::getStorefrontStoreCode).collect(Collectors.toSet());
		Set<String> existStores = storeRepository.findByStorefrontStoreCode(storefrontStoreCodes).stream().map(StoreDo::getStorefrontStoreCode).collect(Collectors.toSet());
		Map<Map.Entry<String, String>, Long> skuIdCountMap = batchEditProductRequestDto.getData().stream()
			.collect(Collectors.groupingBy(product -> Map.entry(product.getStorefrontStoreCode(), product.getSkuCode()), Collectors.counting()));

		batchEditProductRequestDto.getData().forEach(batchEditDto -> {
			List<String> errorMessages = new ArrayList<>();
			if (!existStores.contains(batchEditDto.getStorefrontStoreCode())) {
				errorMessages.add(messageSource.getMessage("message69", null, null));
			} else {
				existStoreProductList.add(batchEditDto);
				storeSkuIds.add(batchEditDto.getStorefrontStoreCode() + StringUtil.PRODUCT_SEPARATOR + batchEditDto.getSkuCode());
			}

			if (skuIdCountMap.get(Map.entry(batchEditDto.getStorefrontStoreCode(), batchEditDto.getSkuCode())) > 1) {
				errorMessages.add(messageSource.getMessage("message137", null, null));
			}

			if (!errorMessages.isEmpty()) {
				failProductList.add(Pair.of(batchEditDto, StringUtils.join(errorMessages, ", ")));
			}
		});

		//prepare data
		Map<Integer, List<Pair<T, ProductStoreInfoDo>>> merchantIdToProductMap = new HashMap<>();
		Map<String, ProductStoreInfoDo> storeProductMap = productRepository.findProductStoreInfoByBuCodeAndStoreSkuIds(BuCodeEnum.HKTV.name(), storeSkuIds).stream()
			.collect(Collectors.toMap(data -> data.getStorefrontStoreCode() + data.getSkuCode(), Function.identity()));
		existStoreProductList.forEach(batchEditDto -> {
			ProductStoreInfoDo productStoreInfoDo = storeProductMap.get(batchEditDto.getStorefrontStoreCode() + batchEditDto.getSkuCode());
			if (productStoreInfoDo == null) {
				if (uploadType == SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE) {
					// when batchEditDto is bundle product, find the merchant id of the store, because the product is not exist in product table
					Integer translateStoreMerchantId = findMerchantIdBy(batchEditDto.getStorefrontStoreCode());
					merchantIdToProductMap.putIfAbsent(translateStoreMerchantId, new ArrayList<>());
					merchantIdToProductMap.get(translateStoreMerchantId).add(Pair.of(batchEditDto, null));
				} else {
					failProductList.add(Pair.of(batchEditDto, messageSource.getMessage("message51", null, null)));
				}
			} else {
				merchantIdToProductMap.putIfAbsent(productStoreInfoDo.getMerchantId(), new ArrayList<>());
				merchantIdToProductMap.get(productStoreInfoDo.getMerchantId()).add(Pair.of(batchEditDto, productStoreInfoDo));
			}
		});

		//validation
		merchantIdToProductMap.keySet().forEach(merchantId -> permissionHelper.checkPermission(userDto, merchantId));

		String fileNamePrefix = batchEditProductRequestDto.getFileName().substring(0, batchEditProductRequestDto.getFileName().lastIndexOf("."));
		String fileNameSuffix = batchEditProductRequestDto.getFileName().substring(batchEditProductRequestDto.getFileName().lastIndexOf("."));
		AtomicInteger index = new AtomicInteger(1);
		boolean isMultipleFile = (CollectionUtil.isNotEmpty(failProductList) && !merchantIdToProductMap.isEmpty()) || merchantIdToProductMap.size() > 1;

		//generate fail product record if any
		if (CollectionUtil.isNotEmpty(failProductList)) {
			String fileNamePreFixByType = customizeFileNamePreFixByType(uploadType, fileNamePrefix, 0);
			String fileName = isMultipleFile ? fileNamePreFixByType + StringUtil.UNDERLINE + index.getAndIncrement() + fileNameSuffix : batchEditProductRequestDto.getFileName();
			SaveProductRecordDo saveProductRecordDo =
				saveProductRecordHelper.createSaveProductRecord(userDto, ConstantType.NON_EXISTENT_MERCHANT_ID, uploadType,
					fileName, SaveProductStatus.FAIL, clientIp);

			List<SaveProductRecordRowDo> saveProductRecordRowDoList = failProductList.stream()
				.map(pair -> saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(),
					generateSingleEditProductDto(uploadType, pair.getLeft(), null), SaveProductStatus.FAIL, pair.getRight()))
				.collect(Collectors.toList());

			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		}

		Set<Long> saveProductRecordIdSet = new HashSet<>();
		//save product record
		merchantIdToProductMap.forEach((merchantId, productStoreInfoDoList) -> {
			String fileNamePreFixByType = customizeFileNamePreFixByType(uploadType, fileNamePrefix, merchantId);
			String fileName = isMultipleFile ? fileNamePreFixByType + StringUtil.UNDERLINE + index.getAndIncrement() + fileNameSuffix : batchEditProductRequestDto.getFileName();
			SaveProductRecordDo saveProductRecordDo =
				saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, uploadType,
					fileName, SaveProductStatus.PROCESSING, clientIp);
			List<SaveProductRecordRowDo> saveProductRecordRowDoList = productStoreInfoDoList.stream()
				.map(editDataPair -> {
					SingleEditProductDto productRequestDto = generateSingleEditProductDto(uploadType, editDataPair.getLeft(), editDataPair.getRight());
					return saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), productRequestDto, SaveProductStatus.PROCESSING, null);
				})
				.collect(Collectors.toList());
			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
			saveProductRecordIdSet.add(saveProductRecordDo.getId());
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		});
		return ResponseDto.success(saveProductRecordIdSet);
	}

	private String customizeFileNamePreFixByType(int uploadType, String fileNamePrefix, Integer merchantId) {
		if (SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE == uploadType) {
			return String.format("%s%s%d%s%s", fileNamePrefix, StringUtil.UNDERLINE, merchantId, StringUtil.UNDERLINE, LocalDateTime.now().format(ConstantType.YYYY_MM_DD_HH_MM_SS));
		} else {
			return fileNamePrefix;
		}
	}

	private Integer findMerchantIdBy(String storefrontStoreCode) {
		return merchantStoreRepository.findByStorefrontStoreCode(storefrontStoreCode, BuCodeEnum.HKTV.name()).stream()
			.findFirst()
			.map(MerchantStoreDo::getMerchantId)
			.orElseThrow(() -> new SystemException(messageSource.getMessage("message102", null, null)));
	}

	@Transactional
	public <T extends OapiBatchEditProductBaseDto> ResponseDto<Long> createOapiBatchEditRecord(UserDto userDto, StoreContractMerchantDo storeContractMerchantDo, List<T> requestData, int uploadType) {
		//data check
		if (CollectionUtil.isEmpty(requestData)) {
			throw new NoDataException();
		}

		if (requestData.size() > BatchCheckHelper.OAPI_BATCH_EDIT_MAXIMUM) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.OAPI_BATCH_EDIT_MAXIMUM)}, null)));
		}
		//create record
		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createOapiSaveProductRecordWithQueueProtocol(
			userDto,
			storeContractMerchantDo.getMerchantId(),
			uploadType,
			String.format(SaveProductRecordHelper.BATCH_EDIT_PRODUCT_FILE_NAME, System.currentTimeMillis()),
			SaveProductStatus.PROCESSING
		);

		Set<String> storeSkuIds = requestData.stream().map(data -> storeContractMerchantDo.getStorefrontCode() + StringUtil.PRODUCT_SEPARATOR + data.getSkuCode()).collect(Collectors.toSet());
		Map<String, ProductStoreInfoDo> storeProductMap = productRepository.findProductStoreInfoByBuCodeAndStoreSkuIds(BuCodeEnum.HKTV.name(), storeSkuIds).stream()
			.collect(Collectors.toMap(ProductStoreInfoDo::getSkuCode, Function.identity()));

		//create record row
		List<SaveProductRecordRowDo> saveProductRecordRowDoList = requestData.stream()
			.map(editData -> {
				ResponseDto<SingleEditProductDto> checkResult = checkAndGenerateOapiSingleEditProductDto(uploadType, editData, storeProductMap.get(editData.getSkuCode()));
				int recordRowStatus = checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode() ? SaveProductStatus.PROCESSING : SaveProductStatus.FAIL;
				return saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), checkResult.getData(), recordRowStatus,
					recordRowStatus == SaveProductStatus.PROCESSING ? null : StringUtils.join(checkResult.getErrorMessageList(), StringUtils.LF));
			})
			.collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.success(saveProductRecordDo.getId());
	}

	private <T extends OapiBatchEditProductBaseDto> ResponseDto<SingleEditProductDto> checkAndGenerateOapiSingleEditProductDto(
		int uploadType, T batchEditDto, ProductStoreInfoDo productStoreInfoDo) {

		List<String> errorMessage = new ArrayList<>();

		if (uploadType == SaveProductType.BATCH_EDIT_PRODUCT_PRICE) {
			BatchEditPriceDto dto = oapiDataMapper.toOapiBatchEditPriceMainRequestData((OapiBatchEditPriceMainRequestData) batchEditDto);
			if (productStoreInfoDo == null) {
				errorMessage.add(messageSource.getMessage("message51", null, null));
				return ResponseDto.generate(generateSingleEditProductDto(uploadType, dto, productStoreInfoDo), errorMessage);
			}
			dto.setStorefrontStoreCode(productStoreInfoDo.getStorefrontStoreCode());
			HktvProductFieldDto hktvProductFieldDto = HktvProductFieldDto.generate(dto);
			ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> checkResult = checkProductMissingFieldService.start(new ProductMissingFieldCheckRequestDto(TemplateTypeEnum.SKU_PRICE, List.of(hktvProductFieldDto)));

			if (checkResult.getStatus() == StatusCodeEnum.FAIL.getCode()) {
				errorMessage.addAll(checkResult.getData().get(0).getMessages());
			}
			return ResponseDto.generate(generateSingleEditProductDto(uploadType, dto, productStoreInfoDo), errorMessage);

		} else if (uploadType == SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS) {
			BatchEditProductReadyDaysDto batchEditProductReadyDaysDto = oapiDataMapper.toBatchEditProductReadyDaysDto((OapiBatchEditProductReadyDaysMainRequestData) batchEditDto);
			if (productStoreInfoDo == null) {
				errorMessage.add(messageSource.getMessage("message51", null, null));
				return ResponseDto.generate(generateSingleEditProductDto(uploadType, batchEditProductReadyDaysDto, productStoreInfoDo), errorMessage);
			}
			batchEditProductReadyDaysDto.setStorefrontStoreCode(productStoreInfoDo.getStorefrontStoreCode());
			return ResponseDto.generate(generateSingleEditProductDto(uploadType, batchEditProductReadyDaysDto, productStoreInfoDo), errorMessage);
		}
		throw new SystemException(messageSource.getMessage("message75", null, null));
	}

	private <T extends BatchEditProductBaseDto> SingleEditProductDto generateSingleEditProductDto(int uploadType, T batchEditDto, ProductStoreInfoDo productStoreInfoDo) {
		SingleEditProductDto productRequestDto = new SingleEditProductDto();

		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setSkuId(batchEditDto == null ? null : batchEditDto.getSkuCode());
		productMasterDto.setUuid(productStoreInfoDo == null ? null : productStoreInfoDo.getUuid());
		productRequestDto.setProduct(productMasterDto);

		BatchUpdateProductInfoDto batchEditProductDto = new BatchUpdateProductInfoDto();
		switch (uploadType) {
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				batchEditProductDto.setPackingDimension((BatchEditPackingDimensionDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
				batchEditProductDto.setSkuPrice((BatchEditPriceDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				batchEditProductDto.setOnlineStatus((BatchEditOnlineStatusDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				batchEditProductDto.setVisibility((BatchEditVisibilityDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				batchEditProductDto.setBatchEditOverseaReserveRegionDto((BatchEditOverseaReserveRegionDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				batchEditProductDto.setBatchEditTranslateDto((BatchEditTranslateDto) batchEditDto);
				// when batchEditDto is bundle product, set the uuid from the dto
				if (productStoreInfoDo == null) {
					productMasterDto.setUuid(((BatchEditTranslateDto) Objects.requireNonNull(batchEditDto)).getUuid());
					if (productMasterDto.getUuid() == null) {
						throw new SystemException("[BATCH_EDIT_HKTV_PRODUCT_TRANSLATE] product uuid is null, batchEditDto: " + batchEditDto);
					}
				}
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				batchEditProductDto.setBatchEditProductReadyDaysDto((BatchEditProductReadyDaysDto) batchEditDto);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE:
				batchEditProductDto.setProductForceOfflineDto((ProductForceOfflineDto) batchEditDto);
				break;
			default:
				break;
		}
		productRequestDto.setBatchEditElement(batchEditProductDto);

		return productRequestDto;
	}

	public void updateSaveProductRecordRowContent(SaveProductRecordDo productRecord, SaveProductRecordRowDo row, ProductMasterResultDto beforeProduct) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDtoFromProductMaster = gson.fromJson(gson.toJson(beforeProduct, ProductMasterResultDto.class), ProductMasterDto.class);
		productMasterDtoFromProductMaster.setRecordRowId(row.getId());
		switch (productRecord.getUploadType()) {
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				BatchEditPackingDimensionDto batchEditPackingDimensionDto = singleEditProductDto.getBatchEditElement().getPackingDimension();
				productMasterDtoFromProductMaster.getAdditional().getHktv().setPackingSpecEn(batchEditPackingDimensionDto.getPackingSpecEn());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setPackingSpecCh(batchEditPackingDimensionDto.getPackingSpecCh());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setPackingSpecSc(batchEditPackingDimensionDto.getPackingSpecSc());
				productMasterDtoFromProductMaster.setWeight(batchEditPackingDimensionDto.getWeight());
				productMasterDtoFromProductMaster.setWeightUnit(batchEditPackingDimensionDto.getWeightUnit());
				productMasterDtoFromProductMaster.setPackingHeight(batchEditPackingDimensionDto.getPackingHeight());
				productMasterDtoFromProductMaster.setPackingLength(batchEditPackingDimensionDto.getPackingLength());
				productMasterDtoFromProductMaster.setPackingDepth(batchEditPackingDimensionDto.getPackingDepth());
				productMasterDtoFromProductMaster.setPackingDimensionUnit(batchEditPackingDimensionDto.getPackingDimensionUnit());
				productMasterDtoFromProductMaster.setPackingBoxType(batchEditPackingDimensionDto.getPackingBoxType());

				if (batchEditPackingDimensionDto.getCartonHeight() == null ||
					batchEditPackingDimensionDto.getCartonLength() == null ||
					batchEditPackingDimensionDto.getCartonDepth() == null) {
					productMasterDtoFromProductMaster.setCartonSizeList(null);
				} else {
					CartonSizeDto cartonSizeDto = new CartonSizeDto();
					cartonSizeDto.setHeight(batchEditPackingDimensionDto.getCartonHeight());
					cartonSizeDto.setWidth(batchEditPackingDimensionDto.getCartonDepth());
					cartonSizeDto.setLength(batchEditPackingDimensionDto.getCartonLength());
					productMasterDtoFromProductMaster.setCartonSizeList(List.of(cartonSizeDto));
				}
				break;

			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
				BatchEditPriceDto batchEditPriceDto = singleEditProductDto.getBatchEditElement().getSkuPrice();
				productMasterDtoFromProductMaster.setOriginalPrice(batchEditPriceDto.getOriginalPrice());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setSellingPrice(batchEditPriceDto.getSellingPrice() == null ? BigDecimal.ZERO : batchEditPriceDto.getSellingPrice());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setStyle(batchEditPriceDto.getStyle());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setDiscountTextEn(batchEditPriceDto.getDiscountTextEn());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setDiscountTextCh(batchEditPriceDto.getDiscountTextCh());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setDiscountTextSc(batchEditPriceDto.getDiscountTextSc());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				BatchEditOnlineStatusDto batchEditOnlineStatusDto = singleEditProductDto.getBatchEditElement().getOnlineStatus();
				productMasterDtoFromProductMaster.getAdditional().getHktv().setOnlineStatus(batchEditOnlineStatusDto.getOnlineStatus());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				BatchEditVisibilityDto batchEditVisibilityDto = singleEditProductDto.getBatchEditElement().getVisibility();
				productMasterDtoFromProductMaster.getAdditional().getHktv().setVisibility(batchEditVisibilityDto.getVisibility());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				BatchEditOverseaReserveRegionDto batchEditOverseaReserveRegionDto = singleEditProductDto.getBatchEditElement().getBatchEditOverseaReserveRegionDto();
				List<String> reserveRegion = Optional.ofNullable(batchEditOverseaReserveRegionDto.getReserveRegion())
					.orElse(Collections.emptyList())
					.stream()
					.filter(Optional.ofNullable(productMasterDtoFromProductMaster.getAdditional().getHktv().getDeliveryDistrict()).orElse(Collections.emptyList())::contains)
					.collect(Collectors.toList());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setDeliveryDistrict(reserveRegion);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
				HktvProductDto newHktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
				HktvProductDto productMasterHktvProductDto = productMasterDtoFromProductMaster.getAdditional().getHktv();
				newHktvProductDto.setMallDollar(productMasterHktvProductDto.getMallDollar());
				newHktvProductDto.setVipMallDollar(productMasterHktvProductDto.getVipMallDollar());
				newHktvProductDto.setContractNo(productMasterHktvProductDto.getContractNo());
				// if mainVideo unchanged, get original mainVideo and thumbnailVideo
				if (StringUtil.isEmpty(newHktvProductDto.getMainVideo())) {
					newHktvProductDto.setMainVideo(productMasterHktvProductDto.getMainVideo());
					newHktvProductDto.setThumbnailVideo(productMasterHktvProductDto.getThumbnailVideo());
				}
				if (StringUtil.isEmpty(newHktvProductDto.getMainPhoto())) {
					newHktvProductDto.setMainPhoto(productMasterHktvProductDto.getMainPhoto());
				}
				if (StringUtil.isEmpty(newHktvProductDto.getAdvertisingPhoto())) {
					newHktvProductDto.setAdvertisingPhoto(productMasterHktvProductDto.getAdvertisingPhoto());
				}
				if (CollectionUtil.isEmpty(newHktvProductDto.getOtherPhoto())) {
					newHktvProductDto.setOtherPhoto(productMasterHktvProductDto.getOtherPhoto());
				}
				if (CollectionUtil.isEmpty(newHktvProductDto.getVariantProductPhoto())) {
					newHktvProductDto.setVariantProductPhoto(productMasterHktvProductDto.getVariantProductPhoto());
				}

				// FORCE_OFFLINE: migration the origin force offline status/case-number from product-master-product-dto into the new-product-dto
				// these 2 fields are not editable in the UI/BATCH_EDIT_PRODUCT_FROM_EXCEL, so we need to patch them from the product master
				newHktvProductDto.setForceOffline(productMasterHktvProductDto.getForceOffline());
				newHktvProductDto.setCaseNumber(productMasterHktvProductDto.getCaseNumber());

				singleEditProductDto.getProduct().setMerchantName(productMasterDtoFromProductMaster.getMerchantName());
				break;

			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				row.setUuid(beforeProduct.getUuid());
				boolean isPrimarySku = singleEditProductDto.getProduct().getAdditional().getLittleMall().getIsPrimarySku();
				// public fields
				if (Boolean.TRUE.equals(isPrimarySku)) {
					productMasterDtoFromProductMaster.getAdditional().getLittleMall().setProductReadyMethod(singleEditProductDto.getProduct().getAdditional().getLittleMall().getProductReadyMethod());
					productMasterDtoFromProductMaster.setSkuNameCh(singleEditProductDto.getProduct().getSkuNameCh());
					productMasterDtoFromProductMaster.getAdditional().getLittleMall().setDisplayInHktvmallCategory(singleEditProductDto.getProduct().getAdditional().getLittleMall().getDisplayInHktvmallCategory());
					productMasterDtoFromProductMaster.getAdditional().getLittleMall().setSkuLongDescriptionCh(singleEditProductDto.getProduct().getAdditional().getLittleMall().getSkuLongDescriptionCh());
					if (CollectionUtil.isNotEmpty(singleEditProductDto.getProduct().getAdditional().getLittleMall().getOtherPhoto()) && singleEditProductDto.getProduct().getAdditional().getLittleMall().getOtherPhoto().stream().anyMatch(StringUtils::isNotBlank)) {
						productMasterDtoFromProductMaster.getAdditional().getLittleMall().setOtherPhoto(singleEditProductDto.getProduct().getAdditional().getLittleMall().getOtherPhoto());
					}
				}
				// private fields
				productMasterDtoFromProductMaster.getAdditional().getLittleMall().setSellingPrice(singleEditProductDto.getProduct().getAdditional().getLittleMall().getSellingPrice());
				productMasterDtoFromProductMaster.getAdditional().getLittleMall().setVisibility(singleEditProductDto.getProduct().getAdditional().getLittleMall().getVisibility());
				productMasterDtoFromProductMaster.setOriginalPrice(singleEditProductDto.getProduct().getOriginalPrice());
				productMasterDtoFromProductMaster.getAdditional().getLittleMall().setOnlineStatus(singleEditProductDto.getProduct().getAdditional().getLittleMall().getOnlineStatus());
				if (StringUtils.isNotBlank(singleEditProductDto.getProduct().getAdditional().getLittleMall().getMainPhoto())) {
					productMasterDtoFromProductMaster.getAdditional().getLittleMall().setMainPhoto(singleEditProductDto.getProduct().getAdditional().getLittleMall().getMainPhoto());
				}
				break;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				BatchEditTranslateDto batchEditTranslateDto = singleEditProductDto.getBatchEditElement().getBatchEditTranslateDto();
				BatchEditTranslateDto.HktvField hktvField = batchEditTranslateDto.getHktvField();

				productMasterDtoFromProductMaster.setSkuNameSc(batchEditTranslateDto.getSkuNameSc());

				HktvProductDto hktvProductDto = productMasterDtoFromProductMaster.getAdditional().getHktv();
				hktvProductDto.setSkuShortDescriptionSc(hktvField.getSkuShortDescriptionSc());
				hktvProductDto.setSkuLongDescriptionSc(hktvField.getSkuLongDescriptionSc());
				hktvProductDto.setPackingSpecSc(hktvField.getPackingSpecSc());
				hktvProductDto.setInvoiceRemarksSc(hktvField.getInvoiceRemarksSc());
				hktvProductDto.setFinePrintSc(hktvField.getFinePrintSc());
				hktvProductDto.setVideoLinkTextSc(hktvField.getVideoLinkTextSc());
				hktvProductDto.setVideoLinkTextSc2(hktvField.getVideoLinkTextSc2());
				hktvProductDto.setVideoLinkTextSc3(hktvField.getVideoLinkTextSc3());
				hktvProductDto.setVideoLinkTextSc4(hktvField.getVideoLinkTextSc4());
				hktvProductDto.setVideoLinkTextSc5(hktvField.getVideoLinkTextSc5());
				hktvProductDto.setClaimLinkSc(hktvField.getClaimLinkSc());
				hktvProductDto.setWarrantySupplierSc(hktvField.getWarrantySupplierSc());
				hktvProductDto.setWarrantyRemarkSc(hktvField.getWarrantyRemarkSc());
				hktvProductDto.setServiceCentreAddressSc(hktvField.getServiceCentreAddressSc());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				BatchEditProductReadyDaysDto productReadyDaysDto = singleEditProductDto.getBatchEditElement().getBatchEditProductReadyDaysDto();
				productMasterDtoFromProductMaster.getAdditional().getHktv().setProductReadyDays(productReadyDaysDto.getProductReadyDays());
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE:
				ProductForceOfflineDto productForceOfflineDto = singleEditProductDto.getBatchEditElement().getProductForceOfflineDto();
				// set force offline flag(true/false) from String value(ForceOfflineStatusEnum: Suspended/Offline)
				productMasterDtoFromProductMaster.getAdditional().getHktv().setForceOffline(productForceOfflineDto.getForceOffline());
				productMasterDtoFromProductMaster.getAdditional().getHktv().setCaseNumber(productForceOfflineDto.getCaseNumber());

				// set online status to OFFLINE if force-offline is true
				if (productForceOfflineDto.isForceOffline()) {
					productMasterDtoFromProductMaster.getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
				}

				break;
			default:
				break;
		}

		if (SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL != productRecord.getUploadType()) {
			singleEditProductDto.setProduct(productMasterDtoFromProductMaster);
		}
		row.setContent(gson.toJson(singleEditProductDto));
	}
}
