package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.product.ContractMapper;
import com.shoalter.mms_product_api.dao.repository.merchant.UserStoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.pojo.ContractStoreDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindStoreService {
    private final PermissionHelper permissionHelper;
    private final StoreRepository storeRepository;
    private final UserStoreRepository userStoreRepository;
	private final ContractMapper contractMapper;


    public ResponseDto<List<ContractStoreDto>> start(UserDto userDto, Integer merchantId) {
        permissionHelper.checkPermission(userDto, merchantId);
		List<ContractStoreDto> contractStoreList = contractMapper.findContractAndStore(ConstantType.PLATFORM_CODE_HKTV, merchantId);
		List<Integer> userStoreList = userStoreRepository.findStoreIdByUserId(userDto.getUserId());
		if (CollectionUtil.isNotEmpty(userStoreList)) {
			contractStoreList = contractStoreList.stream().filter(contractStoreDto -> userStoreList.contains(contractStoreDto.getStoreId())).collect(Collectors.toList());
		}
        return ResponseDto.<List<ContractStoreDto>>builder().data(contractStoreList).status(1).build();
    }
}
