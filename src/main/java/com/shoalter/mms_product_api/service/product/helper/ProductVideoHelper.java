package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.product.ProductVideoSettingRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductVideoSettingDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadVideoDataDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadVideoResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadVideoResultDto;
import com.shoalter.mms_product_api.service.product.pojo.VideoStatusRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.EncryptUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductVideoHelper {
    private static final String SERVICE_NAME = "Video Service";
    public static final String THUMBNAIL_VIDEO = "thumbnailVideo";
    public static final String GALLERY_VIDEO = "galleryVideo";

    private static final List<String> fileExtensionList = Arrays.asList("mov", "mp4", "avi");

    private static final String VIDEO_NAME_REGEX ="[\\w!@#$%^&{}\\[\\]()+\\-=,.~'`]*";

    @Value("${mms.video.private.key}")
    private String videoPrivateKey;

    @Value("${video.upload.url}")
    private String videoUploadUrl;

    @Value("${video.status.url}")
    private String videoStatusUrl;

    private final HttpRequestHelper httpRequestHelper;

    private final ProductVideoSettingRepository productVideoSettingRepository;
    private final MessageSource messageSource;


    public boolean requestUploadVideoStatus(String userCode, List<String> videoFileNameList) {
        if (CollectionUtil.isEmpty(videoFileNameList)) {
            return true;
        }
        HttpHeaders headers = generateHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        return HttpStatus.OK == httpRequestHelper.requestForStatus(HttpRequestDto.<VideoStatusRequestDto, Void>builder()
                .serviceName(SERVICE_NAME)
                .url(videoStatusUrl)
                .method(HttpMethod.PUT)
                .customHeaders(headers)
                .body(VideoStatusRequestDto.builder().status("commit").filenames(videoFileNameList).build())
                .user(UserDto.builder().userCode(userCode).build())
                .build());
    }

    public void requestDeleteVideoByFileName(String userCode, String fileName) {
        HttpHeaders headers = generateHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String,String> body= new LinkedMultiValueMap<>();
		body.add("filename", fileName);

        httpRequestHelper.requestForStatus(HttpRequestDto.<MultiValueMap<String,String>, Void>builder()
                .serviceName(SERVICE_NAME)
                .url(videoUploadUrl)
                .method(HttpMethod.DELETE)
                .customHeaders(headers)
                .body(body)
                .user(UserDto.builder().userCode(userCode).build())
                .build());
    }

    public List<UploadVideoResponseDto> requestUploadVideo(UserDto userDto, String skuCode, MultipartFile file) {
        List<UploadVideoResponseDto> responseList = new ArrayList<>();

        UploadVideoResultDto thumbnailVideoResult = uploadVideoFileServer(file, THUMBNAIL_VIDEO, userDto);
        if (thumbnailVideoResult != null && thumbnailVideoResult.getData() != null) {
            responseList.add(jsonToUploadVideoResponseDto(thumbnailVideoResult.getData(), skuCode, THUMBNAIL_VIDEO));
        }else{
            throw new SystemException("Video system is busy, try again later.");
        }

        UploadVideoResultDto galleryVideoResult = uploadVideoFileServer(file, GALLERY_VIDEO, userDto);
        if (galleryVideoResult != null && galleryVideoResult.getData() != null) {
            UploadVideoResponseDto uploadVideoResponseDto = jsonToUploadVideoResponseDto(galleryVideoResult.getData(), skuCode, GALLERY_VIDEO);
            uploadVideoResponseDto.setOriginalFile(file.getName());
            responseList.add(uploadVideoResponseDto);
        }else{
            throw new SystemException("Video system is busy, try again later.");
        }

        return responseList;
    }

    private UploadVideoResultDto uploadVideoFileServer(MultipartFile file, String fileType, UserDto userDto) {
        String uploadVideoUrl = videoUploadUrl + "?systemName=mms&functionName=productUploadVideo&status=upload";
        return httpRequestHelper.requestForBody(HttpRequestDto.<MultiValueMap<String, Object>, UploadVideoResultDto>builder()
                .serviceName(SERVICE_NAME)
                .url(uploadVideoUrl)
                .method(HttpMethod.POST)
                .customHeaders(generateFileHeaders())
                .resultClass(UploadVideoResultDto.class)
                .body(getUploadVideoBody(fileType, file))
                .user(userDto)
                .build());
    }

    private MultiValueMap<String, Object> getUploadVideoBody(String fileType, MultipartFile file) {
        ProductVideoSettingDo videoSetting = productVideoSettingRepository.findByFileType(fileType);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("startAt", videoSetting.getStartAt());
        body.add("endAt", videoSetting.getEndAt());
        body.add("bitrate", videoSetting.getBitrate());
        body.add("isEnableAudio", videoSetting.getIsEnableaudio());
        body.add("height", videoSetting.getHeight());
        body.add("file", file.getResource());
        return body;
    }

    public List<String> checkVideo(MultipartFile file) throws IOException {
        List<String> errorList = new ArrayList<>();

        try (FFmpegFrameGrabber frameGrabber = new FFmpegFrameGrabber(file.getInputStream())) {
			String fileName = file.getOriginalFilename();
            long size = file.getSize();
            frameGrabber.start();

            ProductVideoSettingDo videoSetting = productVideoSettingRepository.findByFileType(GALLERY_VIDEO);
            if (frameGrabber.getImageWidth() < videoSetting.getLimitWeight() || frameGrabber.getImageHeight() < videoSetting.getLimitHeight()) {
                errorList.add("  The minimum video dimension ratio should be (1920*1080).");
            }

            if (frameGrabber.getLengthInTime() < videoSetting.getMinDuration() || frameGrabber.getLengthInTime() > videoSetting.getMaxDuration()) {
                errorList.add("  video duration takes 5-20 seconds.");
            }

            if (size > videoSetting.getLimitSize()) {
                errorList.add("  The video size should not be larger than 50 MB.");
            }

            String fileExtension = StringUtils.getFilenameExtension(fileName);
            if (!fileExtensionList.contains(fileExtension)) {
                errorList.add("  Unable to upload more than video.");
            }

			if (fileName != null && (fileName.length() > ConstantType.MAX_FILE_LENGTH || !fileName.matches(VIDEO_NAME_REGEX))) {
				errorList.add("  Please ensure your file name contains only letters, numbers, and the following special characters: ! @ # $ % ^ & { } [ ] ( ) _ + - = , . ~ ' `. The file name should be between 1 and 255 characters long");
			}
        }

        return errorList;
    }

    private HttpHeaders generateHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("System-Name", "mms");
        headers.add("Server-Token", createJwtToken(videoPrivateKey));
        return headers;
    }

    private HttpHeaders generateFileHeaders() {
        HttpHeaders headers = generateHeaders();
        MediaType type = MediaType.parseMediaType(MediaType.MULTIPART_FORM_DATA_VALUE);
        headers.setContentType(type);
        return headers;
    }

    public String createJwtToken(String privateKey) {
        return Jwts.builder()
                .setHeaderParam("alg", "RS256")
                .setHeaderParam("typ", "JWT")
                .setSubject(ConstantType.PLATFORM_CODE_LOWER_CASE_HKTV)
                .setIssuedAt(new Date())
                .signWith(SignatureAlgorithm.RS256, EncryptUtil.getPrivateKey(privateKey, "RSA"))
                .compact();
    }

    private UploadVideoResponseDto jsonToUploadVideoResponseDto(UploadVideoDataDto uploadVideoDataDto, String skuCode, String fileType) {
        UploadVideoResponseDto response = new UploadVideoResponseDto();
        response.setSkuCode(skuCode);
        response.setFileName(uploadVideoDataDto.getFilename());
        response.setFilePath(uploadVideoDataDto.getUrl());
        response.setFileType(fileType);
        response.setHref(getHref(uploadVideoDataDto.getUrl()));
        return response;
    }

    private String getHref(String filePath) {
        return StringUtil.isNotNullOrBlank(filePath) ? filePath.trim() : null;
    }
}
