package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EditInvisibleProductRequestDto {

	@JsonProperty("storefront_store_code")
	@NotNull
	private String storefrontStoreCode;

	@JsonProperty("product_ids")
	@NotEmpty
	private List<@NotNull String> productCodes;
}
