package com.shoalter.mms_product_api.service.product.pojo.constant;

import java.util.List;

/**
 * Constants for image validation.
 * This class contains all the constants used for validating product images.
 */
public class ImageValidationConstants {
	// Image size constants
	public static final long MAX_IMAGE_SIZE_MB = 8L;
	public static final long MAX_IMAGE_SIZE_BYTES = MAX_IMAGE_SIZE_MB * 1048576L;

	// Image dimension constants
	public static final int MIN_IMAGE_WIDTH = 300;
	public static final int MIN_IMAGE_HEIGHT = 200;

	// Filename constants
	public static final int MAX_FILENAME_LENGTH = 255;
	public static final String IMAGE_NAME_REGEX = "[\\w!@#$%^&{}\\[\\]()+\\-=,.~'`]*";

	// Supported file extensions
	public static final List<String> SUPPORTED_FILE_EXTENSIONS = List.of("jpg", "jpeg", "png", "gif");

	private ImageValidationConstants() {
	}
}
