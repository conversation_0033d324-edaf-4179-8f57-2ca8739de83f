package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProductMasterResultDto extends ProductMasterProductDto implements Serializable {
	private String id;
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
	@JsonProperty("create_time")
	@SerializedName("create_time")
	private Date createTime;
	@JsonProperty("create_user")
	@SerializedName("create_user")
	private String createUser;
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
	@JsonProperty("modified_time")
	@SerializedName("modified_time")
	private Date modifiedTime;
	@JsonProperty("modified_user")
	@SerializedName("modified_user")
	private String modifiedUser;
	private Integer version;
}
