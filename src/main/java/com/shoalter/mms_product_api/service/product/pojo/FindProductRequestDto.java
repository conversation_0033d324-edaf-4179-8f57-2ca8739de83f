package com.shoalter.mms_product_api.service.product.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class FindProductRequestDto {
	private Integer page;
	private Integer size;
	private String productName;
	private String skuId;
	private String barcode;
	private String productId;
	private List<String> buCode;
	private String productType;
	private List<Integer> storeId;
	private List<Integer> brand;
	private String lastUpdateFrom;
	private String lastUpdateTo;
	private String onlineStatus;
	private List<Integer> merchantId;
	private Boolean forceOffline;

	@Schema(example = "[\"bu_code asc\",\n" +
		"    \"product_type desc\",\n" +
		"    \"product_id asc\",\n" +
		"    \"sku_id asc\",\n" +
		"    \"product_name desc\",\n" +
		"    \"product_name_ch desc\",\n" +
		"    \"online_status asc\",\n" +
		"    \"store_id asc\",\n" +
		"    \"visibility asc\",\n" +
		"    \"product_ready_method asc\",\n" +
		"    \"modified_time desc\",\n" +
		"    \"merchant_name asc\"]")
	private List<String> orderBy;
	private Boolean disableInventory;
	private String merchantName;
	private List<String> productReadyMethod;
	private String visibility;
}
