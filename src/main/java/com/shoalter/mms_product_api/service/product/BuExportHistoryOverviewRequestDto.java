package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuExportHistoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BuExportHistoryOverviewRequestDto implements Serializable {
    private Integer page;
    private Integer size;
	private List<BuExportHistoryEnum> buCodes;
	private String startDate;
	private String endDate;
    private String orderBy;
}
