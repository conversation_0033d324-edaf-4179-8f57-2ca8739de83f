package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.ProductMasterActionEnum;
import com.shoalter.mms_product_api.config.product.ProductMasterQueueTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.pojo.request.BundleChildProductSearchRequestDto;
import com.shoalter.mms_product_api.service.bundle.pojo.response.BundleChildProductResponseDto;
import com.shoalter.mms_product_api.service.bundle.pojo.response.BundleChildProductResultDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.EwBindingOverviewResponseDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.EwBindingOverviewResultDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.EwSearchBindingRequestDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.UpdateEwSkuBindingRequestDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.UpdateEwSkuBindingResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckBarcodeRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckBarcodeResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductIdByLittleMallStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByHktvStoreProductMasterRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByLittleMallStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ExistsBarcodeResponseDataDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductLittleMallFalttenSkusRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductLittleMallSearchFlattenStoresRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStatusResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStoreSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchLittleMallFalttenSkuRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchUuidRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductUuidResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.QueryBundlesByChildUuidsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallPromotionProductsSearchRequestData;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationResultDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.SearchProductIdsRequestData;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterLightweightProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterRelationSettingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterUpdateVisibilityRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterLightweightProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingMainResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterSearchVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterUpdateVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.EwSkuBindingHybrisAndPmCombineResponse;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallFlattenSkuResponse;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallQueryFlattenStoresResponse;
import com.shoalter.mms_product_api.util.CollectionUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductMasterHelper {

	private static final String SERVICE_NAME = "Product Master";
	@Value("${product.master.api.url}")
	private String productMasterUrl;

	@Value("${product.master.ew.update.binding.endpoint}")
	private String updateEwBindingEndpoint;

	@Value("${product.master.ew.search.binding.endpoint}")
	private String updateEwSearchBindingEndpoint;

    public static final int SEARCH_MAX_SIZE = 1000;

	private final TokenHelper tokenHelper;
	private final HttpRequestHelper httpRequestHelper;
	private final Gson gson;
	private final MessageSource messageSource;

	private static final Set<Integer> BATCH_CREATE_LIST = Set.of(SaveProductType.BATCH_CREATE_PRODUCT, SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL, SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL ,SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT);

    public SaveProductResultDto requestSaveProduct(UserDto userDto, List<ProductMasterDto> productList, String identifier, SaveProductRecordDo saveProductRecordDo) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<List<ProductMasterDto>, SaveProductResultDto>builder()
                .serviceName(SERVICE_NAME)
                .url(getProductMasterWithParamUrl(saveProductRecordDo))
                .method(HttpMethod.POST)
                .customHeaders(generateCustomHeaders(userDto))
                .body(productList)
                .resultClass(SaveProductResultDto.class)
                .user(userDto)
		        .identifier(identifier)
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR)
                .build());
    }

    public SaveProductResultDto requestEditProduct(UserDto userDto, List<ProductMasterDto> editProductList, String identifier,HttpMethod httpMethod, SaveProductRecordDo saveProductRecordDo) {
		return httpRequestHelper.requestForBody(HttpRequestDto.<List<ProductMasterDto>, SaveProductResultDto>builder()
                .serviceName(SERVICE_NAME)
                .url(getProductMasterWithParamUrl(saveProductRecordDo))
                .method(httpMethod)
                .customHeaders(generateCustomHeaders(userDto))
                .body(editProductList)
                .resultClass(SaveProductResultDto.class)
                .user(userDto)
		        .identifier(identifier)
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_PATCH_PRODUCTS_ERROR)
                .build());
    }

	private String getProductMasterWithParamUrl(SaveProductRecordDo saveProductRecordDo) {
		UriComponentsBuilder builder = UriComponentsBuilder
			.fromUriString(productMasterUrl + "/v1/api/products")
			.queryParam("queue_type", ProductMasterQueueTypeEnum.getAction(saveProductRecordDo).name());

		ProductMasterActionEnum action = ProductMasterActionEnum.getAction(saveProductRecordDo);
		if (action != null) {
			builder.queryParam("action", action.name());
		}

		return builder.build().toUriString();
	}

    public ProductOverviewResultDto requestProductsByParams(UserDto userDto, ProductSearchRequestDto productSearchRequestDto, String queryType) {
		productSearchRequestDto.setQueryType(queryType);
        ProductOverviewResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<ProductSearchRequestDto, ProductOverviewResponseDto>builder()
                .serviceName(SERVICE_NAME)
                .url(productMasterUrl + "/v1/api/products/search")
                .method(HttpMethod.POST)
                .customHeaders(generateHeaders(userDto))
                .body(productSearchRequestDto)
                .resultClass(ProductOverviewResponseDto.class)
                .user(userDto)
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
                .build());

        return result == null ? null : result.getData();
    }

	public BundleChildProductResultDto requestBundleChildProductsByParams(UserDto userDto, BundleChildProductSearchRequestDto bundleChildProductSearchRequestDto) {

		BundleChildProductResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<BundleChildProductSearchRequestDto, BundleChildProductResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + "/v1/api/products/search_bundle_child")
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(bundleChildProductSearchRequestDto)
				.resultClass(BundleChildProductResponseDto.class)
				.user(userDto)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_GET_CHILD_PRODUCT_ERROR)
				.build());

		return result == null ? null : result.getData();
	}

	public ProductOverviewResultDto requestLittleMallProductsByParams(UserDto userDto, LittleMallProductSearchRequestDto littleMallProductSearchRequestDto) {
        ProductOverviewResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<LittleMallProductSearchRequestDto, ProductOverviewResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(getLittleMallProductsUrl(littleMallProductSearchRequestDto))
				.method(HttpMethod.GET)
				.customHeaders(generateHeaders(userDto))
				.resultClass(ProductOverviewResponseDto.class)
				.user(userDto)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_LITTLE_MALL_PRODUCTS_ERROR)
				.build());

		return result == null ? null : result.getData();
	}

    private String getLittleMallProductsUrl(LittleMallProductSearchRequestDto littleMallProductSearchRequestDto) {
        return UriComponentsBuilder
                .fromUriString(productMasterUrl + "/v1/api/little_mall/products")
                .queryParam("page", littleMallProductSearchRequestDto.getPage())
                .queryParam("size", littleMallProductSearchRequestDto.getSize())
                .queryParamIfPresent("merchant_id", Optional.ofNullable(littleMallProductSearchRequestDto.getMerchantId()))
				.queryParamIfPresent("store_code", Optional.ofNullable(littleMallProductSearchRequestDto.getStoreCode()))
                .queryParamIfPresent("category", Optional.ofNullable(littleMallProductSearchRequestDto.getCategory()))
                .queryParamIfPresent("sku_id", Optional.ofNullable(littleMallProductSearchRequestDto.getSkuIds()))
                .queryParamIfPresent("product_name", Optional.ofNullable(littleMallProductSearchRequestDto.getProductName()))
				.queryParamIfPresent("query_type", Optional.ofNullable(littleMallProductSearchRequestDto.getQueryType()))
                .build()
                .toUriString()
                ;
    }

	public List<String> requestProductIds(UserDto userDto, SearchProductIdsRequestData requestData) {
		ProductMasterBaseResponseDto<List<String>> result = httpRequestHelper.requestForBody(HttpRequestDto.<SearchProductIdsRequestData, ProductMasterBaseResponseDto<List<String>>>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/search/product_ids")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestData)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<ProductMasterBaseResponseDto<List<String>>>(){}.getType()))
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
			.build());
		return result == null ? null : result.getData();
	}

	public List<ProductMasterRelationSettingResponseDto> requestLittleMallRelationSettingByParams(UserDto userDto, List<ProductMasterRelationSettingRequestDto> requestList) {
		ProductMasterRelationSettingMainResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<List<ProductMasterRelationSettingRequestDto>, ProductMasterRelationSettingMainResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + "/v1/api/little_mall/search/variant_relations")
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(requestList)
				.user(userDto)
				.resultClass(ProductMasterRelationSettingMainResponseDto.class)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR)
				.build());
		return result == null ? null : result.getData();
    }

	public ProductOverviewResultDto requestLittleMallPromotionProductsByParams(UserDto userDto,
																			   LittleMallPromotionProductsSearchRequestData littleMallPromotionProductsSearchRequestData) {
		ProductOverviewResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<LittleMallPromotionProductsSearchRequestData, ProductOverviewResponseDto>builder()
			.serviceName(SERVICE_NAME)
			.url(getLittleMallPromotionProductsUrl(littleMallPromotionProductsSearchRequestData))
			.method(HttpMethod.GET)
			.customHeaders(generateHeaders(userDto))
			.user(userDto)
			.resultClass(ProductOverviewResponseDto.class)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_GET_LITTLE_MALL_PRODUCTS_ERROR)
			.build());

		return result == null ? null : result.getData();
	}

	private String getLittleMallPromotionProductsUrl(LittleMallPromotionProductsSearchRequestData littleMallPromotionProductsSearchRequestData) {
		return UriComponentsBuilder
			.fromUriString(productMasterUrl + "/v1/api/little_mall/promotion_products")
			.queryParam("page", littleMallPromotionProductsSearchRequestData.getPage())
			.queryParam("size", littleMallPromotionProductsSearchRequestData.getSize())
			.queryParamIfPresent("merchant_id", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getMerchantId()))
			.queryParamIfPresent("store_code", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getStoreCode()))
			.queryParamIfPresent("category", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getCategory()))
			.queryParamIfPresent("sku_id", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getSkuIds()))
			.queryParamIfPresent("product_name_ch", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getProductNameCh()))
			.queryParamIfPresent("product_id", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getProductId()))
			.queryParamIfPresent("order_by", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getOrderBy()))
			.queryParamIfPresent("query_type", Optional.ofNullable(littleMallPromotionProductsSearchRequestData.getQueryType()))
			.build()
			.toUriString();
	}

    public List<ProductMasterResultDto> requestProductsByUuid(UserDto userDto, ProductMasterSearchRequestDto productMasterSearchRequestDto) {
		if(productMasterSearchRequestDto == null || CollectionUtil.isEmpty(productMasterSearchRequestDto.getUuids())){
			return Collections.emptyList();
		}

        String url = productMasterUrl + "/v1/api/products/batch_products";
        ProductUuidResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<ProductMasterSearchRequestDto, ProductUuidResponseDto>builder()
                .serviceName(SERVICE_NAME)
                .url(url)
                .method(HttpMethod.POST)
                .customHeaders(generateHeaders(userDto))
                .body(productMasterSearchRequestDto)
                .user(userDto)
                .resultClass(ProductUuidResponseDto.class)
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR)
                .build());

        return result == null ? Collections.emptyList() : result.getData();
    }

	public ProductMasterStoreSkuIdResponseDto requestProductByStoreSkuId(UserDto userDto, FindStoreSkuIdProductRequestDto request) {
		String url = String.format("%s/v1/api/products/search_by_store_sku_ids", productMasterUrl);

		return httpRequestHelper.requestForBody(HttpRequestDto.<FindStoreSkuIdProductRequestDto, ProductMasterStoreSkuIdResponseDto>builder()
			.serviceName(SERVICE_NAME)
			.url(url)
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(request)
			.user(userDto)
			.resultClass(ProductMasterStoreSkuIdResponseDto.class)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
			.build());
	}

    public ProductMasterStatusResultDto requestTraceProduct(UserDto userDto, String traceId) {
        String url = productMasterUrl + "/v1/api/products/product_status?trace_id=" + traceId;

        // request api
        return httpRequestHelper.requestForBody(HttpRequestDto.<Void, ProductMasterStatusResultDto>builder()
                .serviceName(SERVICE_NAME)
                .url(url)
                .method(HttpMethod.GET)
                .customHeaders(generateHeaders(userDto))
                .user(userDto)
                .resultClass(ProductMasterStatusResultDto.class)
		        .identifier(traceId)
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR)
                .build());
    }

    public CheckSkuIdResultDto requestCheckSkuIdByHktvStore(UserDto userDto, CheckSkuIdByHktvStoreProductMasterRequestDto checkSkuIdRequestDto) {
        String url = productMasterUrl + "/v1/api/products/check_hktv_stores_sku";

        // request api
        CheckSkuIdResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<CheckSkuIdByHktvStoreProductMasterRequestDto, CheckSkuIdResponseDto>builder()
                .serviceName(SERVICE_NAME)
                .url(url)
                .method(HttpMethod.POST)
                .customHeaders(generateHeaders(userDto))
                .body(checkSkuIdRequestDto)
                .user(userDto)
                .resultClass(CheckSkuIdResponseDto.class)
		        .identifier(checkSkuIdRequestDto.getSkuId().toString())
		        .systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_SKU_ERROR)
                .build());

        return result == null ? null : result.getData();
    }

	public CheckSkuIdResultDto requestCheckSkuIdByLittleMallStore(UserDto userDto, CheckSkuIdByLittleMallStoreRequestDto checkSkuIdByLittleMallStoreRequestDto) {
		String url = productMasterUrl + "/v1/api/products/check_little_mall_store_sku";

		// request api
		CheckSkuIdResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<CheckSkuIdByLittleMallStoreRequestDto, CheckSkuIdResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(url)
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(checkSkuIdByLittleMallStoreRequestDto)
				.user(userDto)
				.resultClass(CheckSkuIdResponseDto.class)
				.identifier(checkSkuIdByLittleMallStoreRequestDto.getSkuId().toString())
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_SKU_ERROR)
				.build());

		return result == null ? null : result.getData();
	}

	public List<String> requestCheckExistingProductIdByLittleMallStore(UserDto userDto, CheckProductIdByLittleMallStoreRequestDto checkProductIdByLittleMallStoreRequestDto) {
		String url = productMasterUrl + "/v1/api/little_mall/search/duplicate_product_id";

		// request api
		ProductMasterBaseResponseDto<List<String>> result = httpRequestHelper.requestForBody(HttpRequestDto.<CheckProductIdByLittleMallStoreRequestDto, ProductMasterBaseResponseDto<List<String>>>builder()
			.serviceName(SERVICE_NAME)
			.url(url)
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(checkProductIdByLittleMallStoreRequestDto)
			.user(userDto)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<ProductMasterBaseResponseDto<List<String>>>() {
				}.getType()))
			.identifier(checkProductIdByLittleMallStoreRequestDto.getProductIds().toString())
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_SKU_ERROR)
			.build());

		return result == null ? null : result.getData();
	}

	public ExistsBarcodeResponseDataDto requestCheckBarcode(UserDto userDto, CheckBarcodeRequestDto checkBarcodeRequestDto) {
		String url = productMasterUrl + "/v1/api/products/check_merchant_barcode";

		// request api
		CheckBarcodeResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<CheckBarcodeRequestDto, CheckBarcodeResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(url)
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(checkBarcodeRequestDto)
				.user(userDto)
				.resultClass(CheckBarcodeResponseDto.class)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_BARCODE_ERROR)
				.build());

		return result == null ? null : result.getData();
	}

	public List<ProductMasterResultDto> requestPmForGetBundlesByChildUuids(UserDto userDto, String identifier, List<String> childUuids) {
		String url = productMasterUrl + "/v1/api/products/batch_bundle_parent_products";

		ProductUuidResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<QueryBundlesByChildUuidsRequestDto, ProductUuidResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(url)
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(QueryBundlesByChildUuidsRequestDto.builder().uuids(childUuids).build())
				.resultClass(ProductUuidResponseDto.class)
				.user(userDto)
				.identifier(identifier)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR)
				.build());

		return result == null ? Collections.emptyList() : result.getData();
	}

	public EwBindingOverviewResultDto requestExtendedWarrantyBindingList(UserDto userDto, EwSearchBindingRequestDto ewSearchBindingRequestDto) {

		EwBindingOverviewResponseDto result = httpRequestHelper.requestForBody(HttpRequestDto.<EwSearchBindingRequestDto, EwBindingOverviewResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + updateEwSearchBindingEndpoint)
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(userDto))
				.body(ewSearchBindingRequestDto)
				.resultClass(EwBindingOverviewResponseDto.class)
				.user(userDto)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
				.build());

		return result == null ? null : result.getData();
	}

	public ResponseDto<List<EwSkuBindingHybrisAndPmCombineResponse>> requestUpdateEwSkuBinding(UserDto userDto, UpdateEwSkuBindingRequestDto updateEwSkuBindingDto, HttpMethod httpMethod) {
		// request api
		UpdateEwSkuBindingResponseDto requestResult = httpRequestHelper.requestForBody(HttpRequestDto.<UpdateEwSkuBindingRequestDto, UpdateEwSkuBindingResponseDto>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + updateEwBindingEndpoint)
				.method(httpMethod)
				.customHeaders(generateHeaders(userDto))
				.body(updateEwSkuBindingDto)
				.user(userDto)
				.resultClass(UpdateEwSkuBindingResponseDto.class)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_UPDATE_EW_BINDING_ERROR)
				.build());

		if (requestResult == null) {
			log.error("Request product master update ew binding fail");
			return ResponseDto.fail(List.of("request product master update ew binding fail"));
		}
		if (requestResult.getStatus() != null && !requestResult.getStatus().equalsIgnoreCase(StatusCodeEnum.SUCCESS.name())) {
			log.error("Request product master update ew binding fail error message : {}, error code : {}", requestResult.getMessage(), requestResult.getCode());
			return ResponseDto.fail(List.of("Product master response error: " + requestResult.getMessage()));
		}

		return ResponseDto.success(EwSkuBindingHybrisAndPmCombineResponse.convertFromProductMasterResponse(requestResult.getData().getFailCases()));
	}

	public ProductMasterBaseResponseDto<List<String>> requestProductUuidsByParams(UserDto userDto, ProductSearchUuidRequestDto requestDto) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<ProductSearchUuidRequestDto, ProductMasterBaseResponseDto<List<String>>>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/products/search_uuid")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<ProductMasterBaseResponseDto<List<String>>>(){}.getType()))
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
			.build());
	}

	public ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>> requestSearchLittleMallFlattenSkusByStores(UserDto userDto, ProductSearchLittleMallFalttenSkuRequestDto requestDto) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<ProductSearchLittleMallFalttenSkuRequestDto, ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>>>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/little_mall/search/flatten_products")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>>>(){}.getType()))
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
			.build());
	}

	public SaveProductResultDto requestLittleMallFlattenSkusByStores(UserDto userDto, List<ProductLittleMallFalttenSkusRequestDto> requestDto) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<List<ProductLittleMallFalttenSkusRequestDto>, SaveProductResultDto>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/little_mall/flatten")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.resultClass(SaveProductResultDto.class)
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR)
			.build());
	}

	public LittleMallQueryFlattenStoresResponse requestQueryLittleMallFlattenStores(UserDto userDto, ProductLittleMallSearchFlattenStoresRequestDto requestDto) {
        return httpRequestHelper.requestForBody(HttpRequestDto.<ProductLittleMallSearchFlattenStoresRequestDto, LittleMallQueryFlattenStoresResponse>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/little_mall/search/flatten_stores")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.resultClass(LittleMallQueryFlattenStoresResponse.class)
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR)
			.build());
	}

	public LittleMallRelationResultDto requestLittleMallPutRelation(UserDto userDto, List<LittleMallRelationDto> requestDto) {
		return httpRequestHelper.requestForBody(HttpRequestDto.<List<LittleMallRelationDto>, LittleMallRelationResultDto>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/little_mall/variant_relations")
			.method(HttpMethod.PUT)
			.customHeaders(generateHeaders(userDto))
			.body(requestDto)
			.resultClass(LittleMallRelationResultDto.class)
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_PUT_VARIANT_PRODUCT_RELATION_ERROR)
			.build());
	}


    private HttpHeaders generateHeaders(UserDto userDto) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-jwt-auth", tokenHelper.generateToken(userDto));
        return headers;
    }

    private HttpHeaders generateCustomHeaders(UserDto userDto){
        HttpHeaders headers = generateHeaders(userDto);
        headers.add("x-user-code", userDto.getUserCode());
        return headers;
    }

	public List<String> handleProductCreateResponse(SaveProductResultDto productMasterResult, Integer uploadType){
		List<String> errorMessageList = new ArrayList<>();

		if (productMasterResult == null) {
			errorMessageList.add(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR}, null));
		} else if ("FAIL".equalsIgnoreCase(productMasterResult.getStatus())) {
			String errorMessagePrefix = "ProductMaster: ";
			errorMessageList.add(errorMessagePrefix + productMasterResult.getMessage());
			if (CollectionUtil.isNotEmpty(productMasterResult.getErrorDetails())) {
				errorMessageList.add(gson.toJson(productMasterResult.getErrorDetails()));
			}
		}
		return errorMessageList;
	}

	public List<String> handleProductCreateOrEditMasterResponse(SaveProductResultDto productMasterResult, Integer uploadType){
		List<String> errorMessageList = new ArrayList<>();

		if (productMasterResult == null) {
			errorMessageList.add(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR}, null));
		} else if ("FAIL".equalsIgnoreCase(productMasterResult.getStatus())) {
			String errorMessagePrefix = BATCH_CREATE_LIST.contains(uploadType) ? "[whole file was failed]\nProductMaster: " : "ProductMaster: ";
			errorMessageList.add(errorMessagePrefix + productMasterResult.getMessage());
			if (CollectionUtil.isNotEmpty(productMasterResult.getErrorDetails())) {
				errorMessageList.add(gson.toJson(productMasterResult.getErrorDetails()));
			}
		}
		return errorMessageList;
	}

	public ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> requestUuidsByStorefrontStoreCodeAndProductIds(
		List<EditInvisibleProductRequestDto> request) {

		UserDto systemUser = tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM);

		return httpRequestHelper.requestForBody(
			HttpRequestDto.<List<EditInvisibleProductRequestDto>, ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>>>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + "/v1/api/search/visibility")
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(systemUser))
				.body(request)
				.resultTypeReference(ParameterizedTypeReference.forType(
					new TypeToken<ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>>>() {
					}.getType()))
				.user(systemUser)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
				.build());
	}

	public ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>> requestUpdateVisibility(
		ProductMasterUpdateVisibilityRequestDto request) {

		UserDto systemUser = tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM);

		return httpRequestHelper.requestForBody(
			HttpRequestDto.<ProductMasterUpdateVisibilityRequestDto, ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>>>builder()
				.serviceName(SERVICE_NAME)
				.url(productMasterUrl + "/v1/api/products/visibility")
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders(systemUser))
				.body(request)
				.resultTypeReference(ParameterizedTypeReference.forType(
					new TypeToken<ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>>>() {
					}.getType()))
				.user(systemUser)
				.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_EDIT_VISIBILITY_ERROR)
				.build());
	}

	public List<ProductMasterLightweightProductResponseDto> requestLightweightProduct(UserDto userDto, ProductMasterLightweightProductRequestDto requestData) {
		ProductMasterBaseResponseDto<List<ProductMasterLightweightProductResponseDto>> result = httpRequestHelper.requestForBody(HttpRequestDto.<ProductMasterLightweightProductRequestDto, ProductMasterBaseResponseDto<List<ProductMasterLightweightProductResponseDto>>>builder()
			.serviceName(SERVICE_NAME)
			.url(productMasterUrl + "/v1/api/search/by_product_ids/lightweight")
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders(userDto))
			.body(requestData)
			.resultTypeReference(ParameterizedTypeReference.forType(
				new TypeToken<ProductMasterBaseResponseDto<List<ProductMasterLightweightProductResponseDto>>>(){}.getType()))
			.user(userDto)
			.systemErrorCode(ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR)
			.build());
		return result == null ? null : result.getData();
	}
}
