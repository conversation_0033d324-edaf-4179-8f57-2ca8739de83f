package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BusinessMapper;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.ITemplateService;
import com.shoalter.mms_product_api.service.product.pojo.IContractStoreDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.template.IProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCol;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCols;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.shoalter.mms_product_api.config.type.ConstantType.IS_ERROR_REPORT;
import static com.shoalter.mms_product_api.config.type.ContractType.MAINLAND_MERCHANT_CONTRACT_SET;
import static com.shoalter.mms_product_api.util.ExcelUtil.EXPIRY_TYPE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.GOODS_TYPE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.OLD_YES_NO_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.PACK_DIMENSION_UNIT_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.PRODUCT_STATUS_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.RETURN_DAYS_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.STYLE_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.WARRANTY_PERIOD_UNIT_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.WEIGHT_UNIT_LIST;

@RequiredArgsConstructor
@Service
@Slf4j
public class EditProductTemplateHelper extends AbstractReport implements ITemplateService {

	private static final String PRODUCT_SHEET_NAME = "Product Template";

	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final BrandRepository brandRepository;
	private final StoreRepository storeRepository;
	private final MerchantStoreRepository merchantStoreRepository;
	private final BusinessMapper businessMapper;
	private final MessageSource messageSource;

	public Pair<BusinessPlatformDo, List<SysParmDo>> getTemplateNecessaryData() {
		BusinessPlatformDo businessPlatformDo =
			businessMapper.findByBusinessCode(ProductMasterBusinessUnitType.HKTV).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));
		List<SysParmDo> sysParmList = searchBySegments(SEGMENT_PARENT_LIST);
		return Pair.of(businessPlatformDo, sysParmList);
	}

	public Pair<Workbook, List<SysParmDo>> generateEditTemplate(Pair<BusinessPlatformDo, List<SysParmDo>> necessaryDataPair, TemplateTypeEnum templateType, Integer storeId, Integer contractId, boolean isErrorReport) {
		long startTime = System.currentTimeMillis();
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		setHeaderColumn(templateType, workbook, isErrorReport);
		switch (templateType) {
			case EXTENDED_WARRANTY:
				break;
			default:
				addLovSheet(workbook, storeId, contractId, necessaryDataPair, templateType);
		}
		long endTime = System.currentTimeMillis();
		log.info("Time taken by generate hktv template {} milliseconds", (endTime - startTime));
		return Pair.of(workbook, necessaryDataPair.getRight());
	}

	public Workbook generateEditTemplateUnIncludeSysParameter(TemplateTypeEnum templateType, boolean isErrorReport) {
		long startTime = System.currentTimeMillis();
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		setHeaderColumn(templateType, workbook, isErrorReport);
		addLovSheet(workbook, templateType);
		long endTime = System.currentTimeMillis();
		log.info("Time taken by generate hktv template {} milliseconds", (endTime - startTime));
		return workbook;
	}

	public Workbook generateEditProductErrorReport(TemplateTypeEnum templateTypeEnum, SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}
		switch (templateTypeEnum) {
			case FORCE_OFFLINE:
				Workbook workbook = generateEditTemplateUnIncludeSysParameter(templateTypeEnum, IS_ERROR_REPORT);
				return TemplateTypeEnum.getService(templateTypeEnum).setErrorReportBodyColumn(workbook, rowList, record);
			default:
				Pair<BusinessPlatformDo, List<SysParmDo>> templateNecessaryDataPair = getTemplateNecessaryData();
				Pair<Workbook, List<SysParmDo>> pair = generateEditTemplate(templateNecessaryDataPair, templateTypeEnum, null, null, true);
				return TemplateTypeEnum.getService(templateTypeEnum).setErrorReportBodyColumn(pair.getLeft(), rowList, pair.getRight(), record);
		}
	}

	public Pair<String, ByteArrayOutputStream> generateEditProductFile(TemplateTypeEnum templateTypeEnum, List<SingleEditProductDto> singleEditProductDtoList, String dateString) {
		//set store code to storefront store code
		Set<String> storeCodes = singleEditProductDtoList.stream().map(product -> product.getProduct().getAdditional().getHktv().getStores()).collect(Collectors.toSet());
		Map<String, String> storeCodeToStorefrontStoreCodeMap = storeRepository.findByStoreCodes(storeCodes).stream().collect(Collectors.toMap(StoreDo::getStoreCode, StoreDo::getStorefrontStoreCode));
		singleEditProductDtoList.forEach(product -> product.getProduct().getAdditional().getHktv().setStores(storeCodeToStorefrontStoreCodeMap.get(product.getProduct().getAdditional().getHktv().getStores())));

		//generate template
		Pair<BusinessPlatformDo, List<SysParmDo>> templateNecessaryDataPair = getTemplateNecessaryData();
		ByteArrayOutputStream os = new ByteArrayOutputStream();

		//other than ALL_COLUMN type:
		if (templateTypeEnum != TemplateTypeEnum.ALL_COLUMN) {
			//if number of products <= 10000, product Excel
			if (singleEditProductDtoList.size() <= ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
				produceExcelFile(os, templateTypeEnum, singleEditProductDtoList, templateNecessaryDataPair);
				return Pair.of(StringUtil.FILE_EXTENSION_EXCEL, os);

			//if number of products > 10000, product Zip
			} else {
				produceOtherTemplateTypeZipFile(os, templateTypeEnum, templateNecessaryDataPair, singleEditProductDtoList, dateString);
				return Pair.of(StringUtil.FILE_EXTENSION_ZIP, os);
			}
		}

		//ALL_COLUMN type: separate products by different store
		Map<String, List<SingleEditProductDto>> storeCodeToProductListMap = singleEditProductDtoList.stream()
			.collect(Collectors.groupingBy(data -> data.getProduct().getAdditional().getHktv().getStores()));
		Map<String, IContractStoreDto> storeToContractMap = new HashMap<>();
		List<IContractStoreDto> contractTypeDtoList = contractRepository.findLastMainContractByBuCodeAndStorefrontStoreCodes(BuCodeEnum.HKTV.name(), storeCodeToProductListMap.keySet());
		contractTypeDtoList.forEach(data -> storeToContractMap.put(data.getStorefrontStoreCode(), data));

		// if only one store
		if (storeCodeToProductListMap.size() == 1) {
			String storeCode = storeCodeToProductListMap.keySet().iterator().next();
			if (!storeToContractMap.containsKey(storeCode)) {
				throw new BadRequestException(messageSource.getMessage("message184", new String[]{storeCode}, null));
			}
			//if number of products <= 10000, product Excel
			if (singleEditProductDtoList.size() <= ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
				IContractStoreDto contractStoreDto = storeToContractMap.get(storeCode);
				Integer storeId = contractStoreDto == null ? null : contractStoreDto.getStoreId();
				Integer contractId = contractStoreDto == null ? null : contractStoreDto.getContractId();
				produceAllColumnExcelFile(os, templateTypeEnum, singleEditProductDtoList, templateNecessaryDataPair, storeId, contractId);
				return Pair.of(StringUtil.FILE_EXTENSION_EXCEL, os);

			//if number of products > 10000, product Zip
			} else {
				produceAllColumnTypeZipFile(os, templateTypeEnum, templateNecessaryDataPair, storeCodeToProductListMap, storeToContractMap, dateString);
				return Pair.of(StringUtil.FILE_EXTENSION_ZIP, os);
			}
		// if more than one store, product Zip
		} else {
			produceAllColumnTypeZipFile(os, templateTypeEnum, templateNecessaryDataPair, storeCodeToProductListMap, storeToContractMap, dateString);
			return Pair.of(StringUtil.FILE_EXTENSION_ZIP, os);
		}
	}

	private void produceExcelFile(ByteArrayOutputStream os, TemplateTypeEnum templateTypeEnum,
								  List<SingleEditProductDto> singleEditProductDtoList, Pair<BusinessPlatformDo, List<SysParmDo>> getTemplateNecessaryDataPair) {
		Pair<Workbook, List<SysParmDo>> pair = generateEditTemplate(getTemplateNecessaryDataPair, templateTypeEnum,
			null, null, false);
		Workbook workbook = TemplateTypeEnum.getService(templateTypeEnum).setTemplateBodyColumn(pair.getLeft(), singleEditProductDtoList, pair.getRight());
		try {
			workbook.write(os);
			workbook.close();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void produceAllColumnExcelFile(ByteArrayOutputStream os, TemplateTypeEnum templateTypeEnum,
										   List<SingleEditProductDto> singleEditProductDtoList, Pair<BusinessPlatformDo, List<SysParmDo>> getTemplateNecessaryDataPair, Integer storeId, Integer contractId) {

		Pair<Workbook, List<SysParmDo>> pair = generateEditTemplate(getTemplateNecessaryDataPair, templateTypeEnum, storeId, contractId, false);
		Workbook workbook = TemplateTypeEnum.getService(templateTypeEnum).setTemplateBodyColumn(pair.getLeft(), singleEditProductDtoList, pair.getRight());
		try {
			workbook.write(os);
			workbook.close();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void produceAllColumnTypeZipFile(ByteArrayOutputStream os, TemplateTypeEnum templateTypeEnum, Pair<BusinessPlatformDo, List<SysParmDo>> templateNecessaryDataPair,
											 Map<String, List<SingleEditProductDto>> storeCodeToProductListMap, Map<String, IContractStoreDto> storeCodeToContractMap, String dateString) {
		byte[] buf = new byte[1024];
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(os)) {
			for (Map.Entry<String, List<SingleEditProductDto>> entry : storeCodeToProductListMap.entrySet()) {
				String storeCode = entry.getKey();
				String fileNamePostfix = dateString + StringUtil.UNDERLINE;
				if (templateTypeEnum == TemplateTypeEnum.ALL_COLUMN) {
					fileNamePostfix = storeCode + StringUtil.UNDERLINE + fileNamePostfix;
				}
				List<SingleEditProductDto> singleEditProductDtoList = entry.getValue();

				if (!storeCodeToContractMap.containsKey(storeCode)) {
					throw new BadRequestException(messageSource.getMessage("message184", new String[]{storeCode}, null));
				}

				int index = 1;
				for (List<SingleEditProductDto> partitionProductList : ListUtils.partition(singleEditProductDtoList, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
					String fileName = singleEditProductDtoList.size() > ConstantType.PRODUCT_UPLOAD_MAX_SIZE ?
						generateEditProductFileName(templateTypeEnum, fileNamePostfix + index++, StringUtil.FILE_EXTENSION_EXCEL) :
						generateEditProductFileName(templateTypeEnum, fileNamePostfix, StringUtil.FILE_EXTENSION_EXCEL);

					IContractStoreDto contractStoreDto = storeCodeToContractMap.get(storeCode);
					Pair<Workbook, List<SysParmDo>> pair = generateEditTemplate(templateNecessaryDataPair, templateTypeEnum, contractStoreDto.getStoreId(),
						contractStoreDto.getContractId(), false);
					writeZip(templateTypeEnum, buf, zipOutputStream, partitionProductList, fileName, pair);
				}
			}
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}

	}

	private void produceOtherTemplateTypeZipFile(ByteArrayOutputStream os, TemplateTypeEnum templateTypeEnum, Pair<BusinessPlatformDo, List<SysParmDo>> templateNecessaryDataPair,
												 List<SingleEditProductDto> singleEditProductDtoList, String dateString) {
		byte[] buf = new byte[1024];
		int index = 1;
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(os)) {
			String fileNamePostfix = dateString + StringUtil.UNDERLINE;
			for (List<SingleEditProductDto> partitionProductList : ListUtils.partition(singleEditProductDtoList, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
				String fileName = singleEditProductDtoList.size() > ConstantType.PRODUCT_UPLOAD_MAX_SIZE ?
					generateEditProductFileName(templateTypeEnum, fileNamePostfix + index++, StringUtil.FILE_EXTENSION_EXCEL) :
					generateEditProductFileName(templateTypeEnum, fileNamePostfix, StringUtil.FILE_EXTENSION_EXCEL);
				Pair<Workbook, List<SysParmDo>> pair = generateEditTemplate(templateNecessaryDataPair, templateTypeEnum, null,
					null, false);
				writeZip(templateTypeEnum, buf, zipOutputStream, partitionProductList, fileName, pair);
			}

		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}

	}

	private void writeZip(TemplateTypeEnum templateTypeEnum, byte[] buf, ZipOutputStream zipOutputStream, List<SingleEditProductDto> partitionProductList, String fileName, Pair<Workbook, List<SysParmDo>> pair) throws IOException {
		Workbook workbook = pair.getLeft();
		workbook = TemplateTypeEnum.getService(templateTypeEnum).setTemplateBodyColumn(workbook, partitionProductList, pair.getRight());

		FileOutputStream fileOutputStream = null;
		FileInputStream fileInputStream = null;
		//create Excel
		File tempFile = null;
		try {
			tempFile = File.createTempFile(UUID.randomUUID().toString(), null);
			fileOutputStream = new FileOutputStream(tempFile);
			workbook.write(fileOutputStream);
			workbook.close();

			//put Excel in Zip
			fileInputStream = new FileInputStream(tempFile);
			zipOutputStream.putNextEntry(new ZipEntry(fileName));
			int len;
			while ((len = fileInputStream.read(buf)) > 0) {
				zipOutputStream.write(buf, 0, len);
			}
		} finally {
			zipOutputStream.closeEntry();
			if (fileInputStream != null) {
				fileInputStream.close();
			}
			if (fileOutputStream != null) {
				fileOutputStream.close();
			}
			// delete temp file
			if (tempFile!= null && tempFile.exists()) {
				Files.delete(tempFile.toPath());
			}
		}
	}

	public String generateEditProductFileName(TemplateTypeEnum templateType, String postfix, String fileExtension) {
		return String.format("Product_export_%s_%s.%s", templateType.name().toLowerCase(), postfix, fileExtension);
	}

	private void setHeaderColumn(TemplateTypeEnum templateType, XSSFWorkbook workbook, boolean isErrorReport) {
		XSSFSheet dataSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(true);
		CellStyle notLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		notLockStyle.setLocked(false);

		CTCol col = dataSheet.getCTWorksheet().getColsArray(0).addNewCol();
		col.setMin(1);
		col.setMax(16384);
		col.setWidth(9.15);
		col.setStyle(notLockStyle.getIndex());

		int rowNum = 0;
		int colNum = 0;
		Row row = CellUtil.getRow(rowNum, dataSheet);
		row.setHeight((short) (30 * 20));

		IProductTemplateHelper IProductTemplateHelper = TemplateTypeEnum.getService(templateType);
		for (TemplateInterface columnEnum : templateType.getTemplateColumnEnum().getEnumConstants()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());
			int columnWidth = IProductTemplateHelper.getColumnWidth(columnEnum);
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), columnWidth);
			cell.setCellStyle(headerStyle);
			colNum++;
		}

		if (isErrorReport) {
			Cell cell = CellUtil.getCell(row, templateType.getTemplateColumnEnum().getEnumConstants().length);
			cell.setCellValue("Error Reason");
			cell.setCellStyle(headerStyle);
			dataSheet.protectSheet(LOV_PASSWORD);
		} else {
			if (!TemplateTypeEnum.ALL_COLUMN_ENUM_SET.contains(templateType)) {
				dataSheet.protectSheet(LOV_PASSWORD);
			} else {
				// lock all column header cell
				setRowDataLockValidation(dataSheet, rowNum, rowNum);
			}
		}

		switch (templateType) {
			case EXTENDED_WARRANTY:
				CellStyle ewNotLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, false);
				ewNotLockStyle.setLocked(false);
				CTCols ctCols = dataSheet.getCTWorksheet().getColsArray(0);
				ctCols.getColList().forEach(ctCol -> ctCol.setStyle(ewNotLockStyle.getIndex()));
				break;
			case FORCE_OFFLINE:
				break;
			default:
				dataSheet.createFreezePane(2, 1, 2, 1);//凍結窗格
		}
	}


	private void addLovSheet(Workbook workbook, Integer storeId, Integer contractId, Pair<BusinessPlatformDo, List<SysParmDo>> necessaryDataPair, TemplateTypeEnum templateType) {
		Sheet lovSheet = workbook.createSheet(AbstractReport.LOV_SHEET_NAME);
		Sheet dataSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		Map<String, Map<String, Object>> map = new HashMap<>();
		Num theColNum = new Num(0);
		switch (templateType) {
			case SKU_PRICE:
			case ONLINE_STATUS:
			case VISIBILITY:
				TemplateTypeEnum.getService(templateType).setLoveSheetValues(lovSheet, map, theColNum);
				break;
			case PACKING_DIMENSION:
				TemplateTypeEnum.getService(templateType).setLoveSheetValues(lovSheet, map, theColNum, necessaryDataPair);
				break;
			default:
				setLovSheetValues(lovSheet, map, theColNum, necessaryDataPair.getLeft(), storeId, contractId, necessaryDataPair.getRight());
				break;
		}
		if (contractId == null) {
			setValidations(templateType, workbook, dataSheet, theColNum, map, necessaryDataPair.getLeft().getPlatformId());
		} else {
			setValidations(templateType, workbook, dataSheet, theColNum, map, necessaryDataPair.getLeft().getPlatformId(), contractId);
		}
		//保護選項內容
		lovSheet.protectSheet(LOV_PASSWORD);
	}

	private void addLovSheet(Workbook workbook, TemplateTypeEnum templateType) {
		Sheet lovSheet = workbook.createSheet(AbstractReport.LOV_SHEET_NAME);
		Sheet dataSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		Map<String, Map<String, Object>> map = new HashMap<>();
		Num theColNum = new Num(0);

		TemplateTypeEnum.getService(templateType).setLoveSheetValues(lovSheet, map, theColNum);
		setValidationsUnIncludeParent(dataSheet, templateType, map);
		lovSheet.protectSheet(LOV_PASSWORD);
	}

	private void setLovSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum, BusinessPlatformDo businessPlatformDo, Integer storeId, Integer contractId, List<SysParmDo> sysParmList) {
		Integer platformId = businessPlatformDo.getPlatformId();
		String buCode = businessPlatformDo.getBusinessCode();
		Integer buId = businessPlatformDo.getBusinessId();

		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CONTRACT_NO, List.of(String.valueOf(contractId)), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORE_ID, List.of(String.valueOf(storeId)), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_YES_NO, OLD_YES_NO_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WEIGHT_UNIT, WEIGHT_UNIT_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, PACK_DIMENSION_UNIT_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_EXPIRY_TYPE, EXPIRY_TYPE_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_RETURN_DAYS, RETURN_DAYS_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_STATUS, PRODUCT_STATUS_LIST, theColNum, map);

		//MMS-146
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_GOODS_TYPE, GOODS_TYPE_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WARRANTY_PERIOD_UNIT, WARRANTY_PERIOD_UNIT_LIST, theColNum, map);

		List<String> manuCountryList = SysParmUtil.getParamDescList(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_MANU_COUNTRY, manuCountryList, theColNum, map);

		List<String> currencyList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.CURRENCY, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CURRENCY, currencyList, theColNum, map);

		List<String> packBoxTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
		List<String> packBoxThirdTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE_3PL_SKU, platformId);
		packBoxTypeList.addAll(packBoxThirdTypeList);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_BOX_TYPE, packBoxTypeList, theColNum, map);

		List<String> storageTemperatureList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.STORAGE_TEMPERATURE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TEMPERATURE, storageTemperatureList, theColNum, map);

		List<String> deliveryMethodList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.DELIVERY_METHOD, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_METHOD, deliveryMethodList, theColNum, map);

		List<String> deliComDaysList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.DELIVERY_COMPLETION_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_COMPLETION_DAYS, deliComDaysList, theColNum, map);

		List<String> pickupTimeslotList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.PICKUP_TIMESLOT, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_TIMESLOT, pickupTimeslotList, theColNum, map);

		List<String> proReadyDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_READY_DAYS, proReadyDaysList, theColNum, map);

		List<String> voucherTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TYPE, voucherTypeList, theColNum, map);

		List<String> voucherDisplayTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_DISPLAY_TYPE, voucherDisplayTypeList, theColNum, map);

		List<String> voucherTemplateTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TEMPLATE_TYPE, voucherTemplateTypeList, theColNum, map);


		Integer merchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);
		List<String> virtualStoreMerchantList = sysParmRepository.findBySegmentAndBuCode(SysParmSegment.VIRTUAL_STORE_MERCHANT, ProductMasterBusinessUnitType.HKTV)
			.stream().filter(sysParmDo -> isVirtualStoreMerchant(sysParmDo, merchantId))
			.map(SysParmUtil::getCodeAndShortDescString)
			.collect(Collectors.toList());setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VIRTUAL_STORE_MERCHANT, virtualStoreMerchantList, theColNum, map);

		List<String> storageTypeList = SysParmUtil.getParamValueList(sysParmList, SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TYPE, storageTypeList, theColNum, map);

		List<String> warehouseList = storeWarehouseRepository.findOldDropListByStoreId(storeId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WAREHOUSE, warehouseList, theColNum, map);

		//获取 product ready method list并设置lov
		List<SysParmDo> productReadyMethodList = searchProductReadyMethodByContractId(contractId, buCode);
		List<String> validationProductReadyMethodList = productReadyMethodList.stream()
			.map(SysParmUtil::getCodeAndShortDescString)
			.collect(Collectors.toList());
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRO_READY_METHOD, validationProductReadyMethodList, theColNum, map);

		List<String> pickupDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_DAYS, pickupDaysList, theColNum, map);

		List<String> brandNameEng = brandRepository.findListByStatusAndBuId("A", buId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_BRAND_NAME_ENG, brandNameEng, theColNum, map);

		List<String> payTermsList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PAYMENT_TERM, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PAY_TERMS, payTermsList, theColNum, map);

		List<String> sizeSystemList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_SIZE_SYSTEM, sizeSystemList, theColNum, map);

		List<String> colorFamiliesList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_COLOR_FAMILIES, colorFamiliesList, theColNum, map);

		List<String> termNameList = contractProdTermsRepository.getContractTermsNameList(contractId, storeId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_TERM_NAME, termNameList, theColNum, map);

		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_FIELD, setProductFieldLovSheetValue(sysParmList, platformId, contractId), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STYLE, STYLE_LIST, theColNum, map);
	}

	private void setValidationsUnIncludeParent(Sheet dataSheet, TemplateTypeEnum templateType, Map<String, Map<String, Object>> map) {
		for (TemplateInterface columnEnum : templateType.getTemplateColumnEnum().getEnumConstants()) {
			if (null != columnEnum.getValidationName()) {
				setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));//设置一级菜单
			}
		}
	}

	private void setValidations(TemplateTypeEnum templateType, Workbook workbook, Sheet dataSheet, Num theColNum, Map<String, Map<String, Object>> map, Integer platformId) {
		List<String> parentSegments = Arrays.stream(templateType.getTemplateColumnEnum().getEnumConstants())
			.filter(columnEnum -> null == columnEnum.getValidationName() && null != columnEnum.getParent())
			.map(TemplateInterface::getParentSegment)
			.collect(Collectors.toList());
		List<SysParmDo> sysParmDoList = searchByParentSegmentsAndPlatformId(parentSegments, platformId);

		for (TemplateInterface columnEnum : templateType.getTemplateColumnEnum().getEnumConstants()) {
			if (null != columnEnum.getValidationName() && null != columnEnum.getParent()) {
				createVLOOKUP(PRODUCT_SHEET_NAME, workbook, columnEnum.getValidationName(), columnEnum.getParentColumnNumber(), columnEnum.getColumnNumber(), map);//連動下拉選單
			} else if (null != columnEnum.getValidationName()) {
				setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));//设置一级菜单
			} else if (null != columnEnum.getParent()) {
				createXSSFName(PRODUCT_SHEET_NAME, workbook, columnEnum.getParentSegment(), columnEnum.getParentColumnNumber(), columnEnum.getColumnNumber(), theColNum, map, false, platformId, sysParmDoList);//设置二级菜单
			}
		}
	}

	private void setValidations(TemplateTypeEnum templateType, Workbook workbook, Sheet dataSheet, Num theColNum, Map<String, Map<String, Object>> map, Integer platformId, Integer contractId) {
		List<String> parentSegments = Arrays.stream(templateType.getTemplateColumnEnum().getEnumConstants())
			.filter(columnEnum -> null == columnEnum.getValidationName() && null != columnEnum.getParent())
			.map(TemplateInterface::getParentSegment)
			.collect(Collectors.toList());
		String contractType = contractRepository.findMainContractTypeInContract(contractId);
		List<SysParmDo> sysParmDoList = !MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)
			? searchByParentSegmentsAndPlatformId(parentSegments, platformId).stream()
				.filter(sysParmDo -> !SysParmCodeEnum.PRODUCT_FIELD_MAINLAND_CODE_SET.contains(sysParmDo.getParentCode()))
				.collect(Collectors.toList())
			: searchByParentSegmentsAndPlatformId(parentSegments, platformId);

		for (TemplateInterface columnEnum : templateType.getTemplateColumnEnum().getEnumConstants()) {
			if (null != columnEnum.getValidationName() && null != columnEnum.getParent()) {
				createVLOOKUP(PRODUCT_SHEET_NAME, workbook, columnEnum.getValidationName(), columnEnum.getParentColumnNumber(), columnEnum.getColumnNumber(), map);//連動下拉選單
			} else if (null != columnEnum.getValidationName()) {
				setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));//设置一级菜单
			} else if (null != columnEnum.getParent()) {
				createXSSFName(PRODUCT_SHEET_NAME, workbook, columnEnum.getParentSegment(), columnEnum.getParentColumnNumber(), columnEnum.getColumnNumber(), theColNum, map, false, platformId, sysParmDoList);//设置二级菜单
			}
		}
	}

	@Override
	public String getFileName() {
		return String.format("product_upload_update_%d.xlsx", System.currentTimeMillis());
	}

	@Override
	public HttpHeaders getResponseHeader() {
		HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("application", "octet-stream"));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + getFileName());
		return header;
	}
}
