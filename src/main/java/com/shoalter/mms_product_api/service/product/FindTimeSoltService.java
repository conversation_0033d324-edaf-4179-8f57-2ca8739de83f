package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.CONSIGNMENT;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.HYBRID_DELIVERY_CONSOLIDATED;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.MAINLAND_DELIVERY;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.THIRD_PARTY;

@RequiredArgsConstructor
@Service
public class FindTimeSoltService {

    private final SysParmRepository sysParmRepository;

    public ResponseDto<List<SysParmDo>> start(String buCode, String productReadyMethodCode) {
        String code = generateTimeSlotCode(productReadyMethodCode);
        List<SysParmDo> sysParmDoList =
                sysParmRepository.findBySegmentAndBuCodeAndCode("PICKUP_TIMESLOT", buCode, code);
        return ResponseDto.<List<SysParmDo>>builder().data(sysParmDoList).status(1).build();
    }

    private String generateTimeSlotCode(String productReadyMethodCode) {
        switch (productReadyMethodCode) {
			case STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE:
			case STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY:
                return "AM/PM";
			case CONSIGNMENT:
			case THIRD_PARTY:
			case STANDARD_DELIVERY_SAME_DAY_IN_HUB:
			case MAINLAND_DELIVERY:
			case HYBRID_DELIVERY_CONSOLIDATED:
				return "AM/PM/EV";
        }
        return null;
    }
}
