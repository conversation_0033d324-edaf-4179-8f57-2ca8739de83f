package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class FindPackingBoxTypeService {

	private final SysParmRepository sysParmRepository;

	public ResponseDto<List<SysParmDo>> start(
			String buCode, String categoryCode, String productReadyMethodCode) {
		List<SysParmDo> packingBoxTypeList;
		String segment = ProductReadyMethodType.THIRD_PARTY.equalsIgnoreCase(productReadyMethodCode) ?
				SysParmSegment.PACK_BOX_TYPE_3PL_SKU :
				SysParmSegment.PACK_BOX_TYPE;

		if (ProductReadyMethodType.E_VOUCHER.equalsIgnoreCase(productReadyMethodCode)) {
			packingBoxTypeList = sysParmRepository.findBySegmentAndCode(SysParmSegment.PACK_BOX_TYPE, "L");
		} else {
			packingBoxTypeList = generatePackingBoxTypeList(categoryCode, segment, buCode);
		}

		return ResponseDto.<List<SysParmDo>>builder().status(1).data(packingBoxTypeList).build();
	}

	private List<SysParmDo> generatePackingBoxTypeList(String categoryCode, String segment, String buCode) {
		List<SysParmDo> packingBoxTypeList = sysParmRepository.findBySegment(segment);
		SysParmUtil.checkSysParmExists(packingBoxTypeList, segment);
		filterPackingBoxTypeByCategoryCode(packingBoxTypeList, categoryCode, buCode);
		return packingBoxTypeList;
	}

	private void filterPackingBoxTypeByCategoryCode(List<SysParmDo> packingBoxTypeList, String categoryCode, String buCode) {
		List<SysParmDo> categoryRestrictionSysParmDoList = sysParmRepository.findBySegmentAndBuCode("CATEGORY_RESTRICTION", buCode);
		Set<String> matchCategoryRestrictionSet = generateMatchCategoryRestriction(categoryRestrictionSysParmDoList, categoryCode);
		// 如果primary category code沒有在category restriction限制裡，就將限制的packing box type都移除。
		// 反之，若是primary category code有在限制裡只保留限制的packing box type
		if (CollectionUtil.isEmpty(matchCategoryRestrictionSet)) {
			Set<String> categoryRestrictionSet = categoryRestrictionSysParmDoList.stream().map(SysParmDo::getCode).collect(Collectors.toSet());
			packingBoxTypeList.removeIf(s -> categoryRestrictionSet.contains(s.getCode()));
		} else {
			packingBoxTypeList.removeIf(s -> !matchCategoryRestrictionSet.contains(s.getCode()));
		}
	}

	private Set<String> generateMatchCategoryRestriction(List<SysParmDo> categoryRestrictionSysParmDoList, String categoryCode) {
		Set<String> categoryRestrictionMatchSet = new HashSet<>();
		if (CollectionUtil.isNotEmpty(categoryRestrictionSysParmDoList)) {
			for (SysParmDo categoryRestriction : categoryRestrictionSysParmDoList) {
				String[] checkCategoryCodes = categoryRestriction.getParmValue().split(",");
				for (String checkCategoryCode : checkCategoryCodes) {
					if (categoryCode.startsWith(checkCategoryCode)) {
						categoryRestrictionMatchSet.add(categoryRestriction.getCode());
						break;
					}
				}

			}
		}
		return categoryRestrictionMatchSet;
	}

}
