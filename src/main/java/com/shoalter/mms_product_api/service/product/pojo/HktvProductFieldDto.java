package com.shoalter.mms_product_api.service.product.pojo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HktvProductFieldDto {

	//only for tmall data
	@JsonProperty("shop_title")
	@SerializedName("shop_title")
	private String shopTitle;

	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storefrontStoreCode;

	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;

	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;

	@JsonProperty("product_type_code")
	@SerializedName("product_type_code")
	private List<String> productTypeCode;

	@JsonProperty("primary_category_code")
	@SerializedName("primary_category_code")
	private String primaryCategoryCode;

	@JsonProperty("brand_id")
	@SerializedName("brand_id")
	private Integer brandId;

	@JsonProperty("product_ready_method")
	@SerializedName("product_ready_method")
	private String productReadyMethod;

	@JsonProperty("delivery_method")
	@SerializedName("delivery_method")
	private String deliveryMethod;

	@JsonProperty("sku_status")
	@SerializedName("sku_status")
	private String skuStatus;

	private String warehouse;

	@JsonProperty("term_name")
	@SerializedName("term_name")
	private String termName;

	@JsonProperty("is_primary_sku")
	@SerializedName("is_primary_sku")
	private String isPrimarySku;

	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	private String skuNameEn;

	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	private String skuNameCh;

	@JsonProperty("sku_name_sc")
	@SerializedName("sku_name_sc")
	private String skuNameSc;

	@JsonProperty("sku_short_description_title_en")
	@SerializedName("sku_short_description_title_en")
	private String skuShortDescriptionTitleEn;

	@JsonProperty("sku_short_description_title_ch")
	@SerializedName("sku_short_description_title_ch")
	private String skuShortDescriptionTitleCh;

	@JsonProperty("sku_short_description_en")
	@SerializedName("sku_short_description_en")
	private String skuShortDescriptionEn;

	@JsonProperty("sku_short_description_ch")
	@SerializedName("sku_short_description_ch")
	private String skuShortDescriptionCh;

	@JsonProperty("sku_short_description_sc")
	@SerializedName("sku_short_description_sc")
	private String skuShortDescriptionSc;

	@JsonProperty("sku_long_description_title_en")
	@SerializedName("sku_long_description_title_en")
	private String skuLongDescriptionTitleEn;

	@JsonProperty("sku_long_description_title_ch")
	@SerializedName("sku_long_description_title_ch")
	private String skuLongDescriptionTitleCh;

	@JsonProperty("sku_long_description_en")
	@SerializedName("sku_long_description_en")
	private String skuLongDescriptionEn;

	@JsonProperty("sku_long_description_ch")
	@SerializedName("sku_long_description_ch")
	private String skuLongDescriptionCh;

	@JsonProperty("sku_long_description_sc")
	@SerializedName("sku_long_description_sc")
	private String skuLongDescriptionSc;

	@JsonProperty("main_photo")
	@SerializedName("main_photo")
	private String mainPhoto;

	@JsonProperty("main_video")
	@SerializedName("main_video")
	private String mainVideo;

	@JsonProperty("other_product_photo")
	@SerializedName("other_product_photo")
	private List<String> otherProductPhoto;

	@JsonProperty("other_photo")
	@SerializedName("other_photo")
	private List<String> otherPhoto;

	@JsonProperty("advertising_photo")
	@SerializedName("advertising_photo")
	private String advertisingPhoto;

	@JsonProperty("video_link_1")
	@SerializedName("video_link_1")
	private String videoLink1;

	@JsonProperty("video_link_1_text_en")
	@SerializedName("video_link_1_text_en")
	private String videoLink1TextEn;

	@JsonProperty("video_link_1_text_ch")
	@SerializedName("video_link_1_text_ch")
	private String videoLink1TextCh;

	@JsonProperty("video_link_1_text_sc")
	@SerializedName("video_link_1_text_sc")
	private String videoLink1TextSc;

	@JsonProperty("video_link_2")
	@SerializedName("video_link_2")
	private String videoLink2;

	@JsonProperty("video_link_2_text_en")
	@SerializedName("video_link_2_text_en")
	private String videoLink2TextEn;

	@JsonProperty("video_link_2_text_ch")
	@SerializedName("video_link_2_text_ch")
	private String videoLink2TextCh;

	@JsonProperty("video_link_2_text_sc")
	@SerializedName("video_link_2_text_sc")
	private String videoLink2TextSc;

	@JsonProperty("video_link_3")
	@SerializedName("video_link_3")
	private String videoLink3;

	@JsonProperty("video_link_3_text_en")
	@SerializedName("video_link_3_text_en")
	private String videoLink3TextEn;

	@JsonProperty("video_link_3_text_ch")
	@SerializedName("video_link_3_text_ch")
	private String videoLink3TextCh;

	@JsonProperty("video_link_3_text_sc")
	@SerializedName("video_link_3_text_sc")
	private String videoLink3TextSc;

	@JsonProperty("video_link_4")
	@SerializedName("video_link_4")
	private String videoLink4;

	@JsonProperty("video_link_4_text_en")
	@SerializedName("video_link_4_text_en")
	private String videoLink4TextEn;

	@JsonProperty("video_link_4_text_ch")
	@SerializedName("video_link_4_text_ch")
	private String videoLink4TextCh;

	@JsonProperty("video_link_4_text_sc")
	@SerializedName("video_link_4_text_sc")
	private String videoLink4TextSc;

	@JsonProperty("video_link_5")
	@SerializedName("video_link_5")
	private String videoLink5;

	@JsonProperty("video_link_5_text_en")
	@SerializedName("video_link_5_text_en")
	private String videoLink5TextEn;

	@JsonProperty("video_link_5_text_ch")
	@SerializedName("video_link_5_text_ch")
	private String videoLink5TextCh;

	@JsonProperty("video_link_5_text_sc")
	@SerializedName("video_link_5_text_sc")
	private String videoLink5TextSc;

	@JsonProperty("manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;

	@JsonProperty("colour_families")
	@SerializedName("colour_families")
	private String colourFamilies;

	@JsonProperty("color_en")
	@SerializedName("color_en")
	private String colorEn;

	@JsonProperty("color_ch")
	@SerializedName("color_ch")
	private String colorCh;

	@JsonProperty("size_system")
	@SerializedName("size_system")
	private String sizeSystem;

	private String size;

	private String currency;

	private BigDecimal cost;

	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;

	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;

	@JsonProperty("mall_dollar")
	@SerializedName("mall_dollar")
	private BigDecimal mallDollar;

	@JsonProperty("vip_mall_dollar")
	@SerializedName("vip_mall_dollar")
	private BigDecimal vipMallDollar;

	@JsonProperty("user_max")
	@SerializedName("user_max")
	private Integer userMax;

	private String style;

	@JsonProperty("discount_text_en")
	@SerializedName("discount_text_en")
	private String discountTextEn;

	@JsonProperty("discount_text_ch")
	@SerializedName("discount_text_ch")
	private String discountTextCh;

	@JsonProperty("discount_text_sc")
	@SerializedName("discount_text_sc")
	private String discountTextSc;

	@JsonProperty("packing_spec_en")
	@SerializedName("packing_spec_en")
	private String packingSpecEn;

	@JsonProperty("packing_spec_ch")
	@SerializedName("packing_spec_ch")
	private String packingSpecCh;

	@JsonProperty("packing_spec_sc")
	@SerializedName("packing_spec_sc")
	private String packingSpecSc;

	@JsonProperty("packing_height")
	@SerializedName("packing_height")
	private BigDecimal packingHeight;

	@JsonProperty("packing_length")
	@SerializedName("packing_length")
	private BigDecimal packingLength;

	@JsonProperty("packing_depth")
	@SerializedName("packing_depth")
	private BigDecimal packingDepth;

	@JsonProperty("packing_dimension_unit")
	@SerializedName("packing_dimension_unit")
	private String packingDimensionUnit;

	private BigDecimal weight;

	@JsonProperty("weight_unit")
	@SerializedName("weight_unit")
	private String weightUnit;

	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;

	@JsonProperty("carton_height")
	@SerializedName("carton_height")
	private Integer cartonHeight;

	@JsonProperty("carton_length")
	@SerializedName("carton_length")
	private Integer cartonLength;

	@JsonProperty("carton_depth")
	@SerializedName("carton_depth")
	private Integer cartonDepth;

	@JsonProperty("invisible_flag")
	@SerializedName("invisible_flag")
	private String invisibleFlag;

	private String barcode;

	@JsonProperty("feature_start_date")
	@SerializedName("feature_start_date")
	private String featureStartDate;

	@JsonProperty("feature_end_date")
	@SerializedName("feature_end_date")
	private String featureEndDate;

	@JsonProperty("voucher_type")
	@SerializedName("voucher_type")
	private String voucherType;

	@JsonProperty("voucher_display_type")
	@SerializedName("voucher_display_type")
	private String voucherDisplayType;

	@JsonProperty("voucher_template_type")
	@SerializedName("voucher_template_type")
	private String voucherTemplateType;

	@JsonProperty("expiry_type")
	@SerializedName("expiry_type")
	private String expiryType;

	private String consumable;

	private Integer priority;

	@JsonProperty("field_1")
	@SerializedName("field_1")
	private String field1;

	@JsonProperty("value_1")
	@SerializedName("value_1")
	private String value1;

	@JsonProperty("field_2")
	@SerializedName("field_2")
	private String field2;

	@JsonProperty("value_2")
	@SerializedName("value_2")
	private String value2;

	@JsonProperty("field_3")
	@SerializedName("field_3")
	private String field3;

	@JsonProperty("value_3")
	@SerializedName("value_3")
	private String value3;

	@JsonProperty("redeem_start_date")
	@SerializedName("redeem_start_date")
	private String redeemStartDate;

	@JsonProperty("fixed_redemption_date")
	@SerializedName("fixed_redemption_date")
	private String fixedRedemptionDate;

	@JsonProperty("upon_purchase_date")
	@SerializedName("upon_purchase_date")
	private String uponPurchaseDate;

	@JsonProperty("fine_prInteger_title_en")
	@SerializedName("fine_prInteger_title_en")
	private String finePrIntegerTitleEn;

	@JsonProperty("fine_prInteger_title_ch")
	@SerializedName("fine_prInteger_title_ch")
	private String finePrIntegerTitleCh;

	@JsonProperty("fine_prInteger_en")
	@SerializedName("fine_prInteger_en")
	private String finePrIntegerEn;

	@JsonProperty("fine_prInteger_ch")
	@SerializedName("fine_prInteger_ch")
	private String finePrIntegerCh;

	@JsonProperty("fine_prInteger_sc")
	@SerializedName("fine_prInteger_sc")
	private String finePrIntegerSc;

	@JsonProperty("need_removal_services")
	@SerializedName("need_removal_services")
	private String needRemovalServices;

	@JsonProperty("goods_type")
	@SerializedName("goods_type")
	private String goodsType;

	@JsonProperty("warranty_period_unit")
	@SerializedName("warranty_period_unit")
	private String warrantyPeriodUnit;

	@JsonProperty("warranty_period")
	@SerializedName("warranty_period")
	private Integer warrantyPeriod;

	@JsonProperty("warranty_supplier_en")
	@SerializedName("warranty_supplier_en")
	private String warrantySupplierEn;

	@JsonProperty("warranty_supplier_ch")
	@SerializedName("warranty_supplier_ch")
	private String warrantySupplierCh;

	@JsonProperty("warranty_supplier_sc")
	@SerializedName("warranty_supplier_sc")
	private String warrantySupplierSc;

	@JsonProperty("service_centre_address_en")
	@SerializedName("service_centre_address_en")
	private String serviceCentreAddressEn;

	@JsonProperty("service_centre_address_ch")
	@SerializedName("service_centre_address_ch")
	private String serviceCentreAddressCh;

	@JsonProperty("service_centre_address_sc")
	@SerializedName("service_centre_address_sc")
	private String serviceCentreAddressSc;

	@JsonProperty("service_centre_email")
	@SerializedName("service_centre_email")
	private String serviceCentreEmail;

	@JsonProperty("service_centre_contact")
	@SerializedName("service_centre_contact")
	private String serviceCentreContact;

	@JsonProperty("warranty_remark_en")
	@SerializedName("warranty_remark_en")
	private String warrantyRemarkEn;

	@JsonProperty("warranty_remark_ch")
	@SerializedName("warranty_remark_ch")
	private String warrantyRemarkCh;

	@JsonProperty("warranty_remark_sc")
	@SerializedName("warranty_remark_sc")
	private String warrantyRemarkSc;

	@JsonProperty("invoice_remark_en")
	@SerializedName("invoice_remark_en")
	private String invoiceRemarkEn;

	@JsonProperty("invoice_remark_ch")
	@SerializedName("invoice_remark_ch")
	private String invoiceRemarkCh;

	@JsonProperty("invoice_remark_sc")
	@SerializedName("invoice_remark_sc")
	private String invoiceRemarkSc;

	@JsonProperty("return_days")
	@SerializedName("return_days")
	private Integer returnDays;

	@JsonProperty("product_ready_days")
	@SerializedName("product_ready_days")
	private String productReadyDays;

	@JsonProperty("pickup_days")
	@SerializedName("pickup_days")
	private String pickupDays;

	@JsonProperty("pickup_timeslot")
	@SerializedName("pickup_timeslot")
	private String pickupTimeslot;

	@JsonProperty("oversea_delivery")
	@SerializedName("oversea_delivery")
	private String overseaDelivery;

	private String urgent;

	@JsonProperty("delivery_title_en")
	@SerializedName("delivery_title_en")
	private String deliveryTitleEn;

	@JsonProperty("delivery_title_ch")
	@SerializedName("delivery_title_ch")
	private String deliveryTitleCh;

	@JsonProperty("delivery_detail_en")
	@SerializedName("delivery_detail_en")
	private String deliveryDetailEn;

	@JsonProperty("delivery_detail_ch")
	@SerializedName("delivery_detail_ch")
	private String deliveryDetailCh;

	@JsonProperty("delivery_completion_days")
	@SerializedName("delivery_completion_days")
	private Integer deliveryCompletionDays;

	@JsonProperty("minimum_shelf_life")
	@SerializedName("minimum_shelf_life")
	private Integer minimumShelfLife;

	@JsonProperty("virtual_store")
	@SerializedName("virtual_store")
	private String virtualStore;

	@JsonProperty("rm_code")
	@SerializedName("rm_code")
	private String rmCode;

    @JsonProperty("storage_type")
    @SerializedName("storage_type")
	private String storageType;

    @JsonProperty("pre_sell_fruit")
    @SerializedName("pre_sell_fruit")
	private String preSellFruit;

    @JsonProperty("physical_store")
    @SerializedName("physical_store")
	private List<String> physicalStore;

	@JsonProperty("affiliate_url")
	@SerializedName("affiliate_url")
	private String affiliateUrl;

	@JsonProperty("external_platform")
	@SerializedName("external_platform")
	private ExternalPlatform externalPlatform;

	//only for EW Sku
	@JsonProperty("extended_warranty_percentage_setting")
	@SerializedName("extended_warranty_percentage_setting")
	private BigDecimal ewPercentageSetting;

	//only for EW Sku
	@JsonProperty("claim_link_en")
	@SerializedName("claim_link_en")
	private String claimLinkEn;

	//only for EW Sku
	@JsonProperty("claim_link_ch")
	@SerializedName("claim_link_ch")
	private String claimLinkCh;

	//only for EW Sku
	@JsonProperty("claim_link_sc")
	@SerializedName("claim_link_sc")
	private String claimLinkSc;

	// only for affiliate network display in excel
	private String brandName;

	// only for open api
	private String brandCode;

	// only for tmall data
	@JsonProperty("converted_main_photo")
	@SerializedName("converted_main_photo")
	private String convertedMainPhoto;

	// only for tmall data
	@JsonProperty("converted_other_product_photo")
	@SerializedName("converted_other_product_photo")
	private List<String> convertedOtherProductPhoto;

	// only for tmall data
	@JsonProperty("converted_other_photo")
	@SerializedName("converted_other_photo")
	private List<String> convertedOtherPhoto;

	/**
	 * SaveProductType = BATCH_EDIT_PRODUCT_PRICE
	 */
	public static HktvProductFieldDto generate(BatchEditPriceDto dto) {
		return HktvProductFieldDto.builder()
			.storefrontStoreCode(dto.getStorefrontStoreCode())
			.skuId(dto.getSkuCode())
			.sellingPrice(dto.getSellingPrice())
			.originalPrice(dto.getOriginalPrice())
			.style(dto.getStyle())
			.discountTextEn(dto.getDiscountTextEn())
			.discountTextCh(dto.getDiscountTextCh())
			.discountTextSc(dto.getDiscountTextSc())
			.build();
	}

	/**
	 * SaveProductType = BATCH_EDIT_PRODUCT_READY_DAYS
	 */
	public static HktvProductFieldDto generate(BatchEditProductReadyDaysDto dto) {
		return HktvProductFieldDto.builder()
			.storefrontStoreCode(dto.getStorefrontStoreCode())
			.skuId(dto.getSkuCode())
			.productReadyDays(dto.getProductReadyDays())
			.build();
	}
}
