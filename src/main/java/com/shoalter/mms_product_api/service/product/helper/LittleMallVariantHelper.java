package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.QueryTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallVariantProcessData;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper.SEARCH_MAX_SIZE;

@RequiredArgsConstructor
@Service
@Slf4j
public class LittleMallVariantHelper {

	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	private final ProductMasterHelper productMasterHelper;
	private final LittleMallRelationHelper littleMallRelationHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final UserHelper userHelper;
	private final CheckBuHelper checkBuHelper;

	private final MessageSource messageSource;
	private final Gson gson;

	public Pair<List<SaveProductRecordRowDo>, List<SaveProductRecordRowDo>> checkAndUpdateFailRecordRow(List<SaveProductRecordRowDo> waitSendPmList, SaveProductRecordDo record) {
		if (CollectionUtil.isEmpty(waitSendPmList) || record == null) {
			return Pair.of(new ArrayList<>(), new ArrayList<>());
		}

		UserDto userDto = userHelper.generateUserDtoByRecord(record);
		LittleMallVariantProcessData processData = null;
		if (record.getUploadType() == SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT) {
			processData = processBatchCreateProduct(userDto, waitSendPmList);
		} else if (record.getUploadType() == SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT) {
			processData = processBatchEditProduct(userDto, waitSendPmList, record.getId());
		}
		return processRequestRelation(userDto, processData);
	}

	private LittleMallVariantProcessData processBatchCreateProduct(UserDto userDto, List<SaveProductRecordRowDo> waitSendPmList) {
		List<SaveProductRecordRowDo> failedList = new ArrayList<>();
		List<SaveProductRecordRowDo> keepProcessingList = new ArrayList<>();

		Map<String, List<SingleEditProductDto>> successPrimaryWithVariantSkuMap = generatePrimaryWithVariantSkuMap(waitSendPmList);
		// little mall only one store for batch product
		String storefrontStoreCode = gson.fromJson(waitSendPmList.get(0).getContent(), SingleEditProductDto.class).getProduct().getAdditional().getLittleMall().getStoreCode();
		Map<String, SaveProductRecordRowDo> skuRowMap = waitSendPmList.stream().collect(Collectors.toMap(SaveProductRecordRowDo::getSku, Function.identity()));
		Map<String, ProductMasterResultDto> primarySkusMapFromProductMaster = new HashMap<>();
		if (storefrontStoreCode != null) {
			primarySkusMapFromProductMaster = findPrimarySkusMap(userDto, successPrimaryWithVariantSkuMap.keySet(), storefrontStoreCode);
		}

		Set<String> relationSendProductIdSet = successPrimaryWithVariantSkuMap.keySet();
		for (Map.Entry<String, List<SingleEditProductDto>> entry : successPrimaryWithVariantSkuMap.entrySet()) {
			String productId = entry.getKey();
			Pair<SingleEditProductDto, List<SingleEditProductDto>> primaryAndVariantSku = convertPrimaryAndVariantSku(entry.getValue());
			SingleEditProductDto primarySku = primaryAndVariantSku.getLeft();
			List<SingleEditProductDto> variantSkus = primaryAndVariantSku.getRight();

			ProductMasterResultDto primarySkuFromProductMaster = primarySkusMapFromProductMaster.getOrDefault(productId, null);
			boolean excelPrimaryExistsAndValid = processPrimarySku(primarySku, primarySkuFromProductMaster, skuRowMap, failedList, keepProcessingList, relationSendProductIdSet);
			for (SingleEditProductDto variantSku : variantSkus) {
				processVariantSku(excelPrimaryExistsAndValid, primarySku, primarySkuFromProductMaster, variantSku, skuRowMap, failedList, keepProcessingList, relationSendProductIdSet);
			}
		}
		Map<String, ProductMasterRelationSettingResponseDto> queryRelationProductIdMap = littleMallRelationHelper.queryRelationSettingMap(userDto, storefrontStoreCode, relationSendProductIdSet);

		return LittleMallVariantProcessData.builder()
			.failedList(failedList)
			.keepProcessingList(keepProcessingList)
			.relationSendProductIdSet(relationSendProductIdSet)
			.queryRelationProductIdMap(queryRelationProductIdMap)
			.build();
	}

	private LittleMallVariantProcessData processBatchEditProduct(UserDto userDto, List<SaveProductRecordRowDo> waitSendPmList, Long recordId) {
		List<SaveProductRecordRowDo> failedList = new ArrayList<>();
		List<SaveProductRecordRowDo> keepProcessingList = new ArrayList<>();

		Map<String, List<SingleEditProductDto>> successPrimaryWithVariantSkuMap = generatePrimaryWithVariantSkuMap(waitSendPmList);
		// little mall only one store for batch product
		String storefrontStoreCode = gson.fromJson(waitSendPmList.get(0).getContent(), SingleEditProductDto.class).getProduct().getAdditional().getLittleMall().getStoreCode();
		Map<String, SaveProductRecordRowDo> uuidRowMap = waitSendPmList.stream().collect(Collectors.toMap(SaveProductRecordRowDo::getUuid, Function.identity()));

		Map<String, List<ProductMasterResultDto>> skusMapFromProductMaster = new HashMap<>();
		if (storefrontStoreCode != null) {
			skusMapFromProductMaster = findProductIdSkusMap(userDto, successPrimaryWithVariantSkuMap.keySet(), storefrontStoreCode);
		}

		int processVariantCountFromProductMaster = 0;
		for (Map.Entry<String, List<SingleEditProductDto>> entry : successPrimaryWithVariantSkuMap.entrySet()) {
			String productId = entry.getKey();
			Pair<SingleEditProductDto, List<SingleEditProductDto>> primaryAndVariantSku = convertPrimaryAndVariantSku(entry.getValue());
			SingleEditProductDto primarySku = primaryAndVariantSku.getLeft();
			List<SingleEditProductDto> variantSkus = primaryAndVariantSku.getRight();

			processVariantCountFromProductMaster += processEditProduct(primarySku, variantSkus, uuidRowMap, failedList, keepProcessingList, skusMapFromProductMaster.getOrDefault(productId, Collections.emptyList()), recordId);
		}
		int totalCount = saveProductRecordRowRepository.countByRecordId(recordId);
		// If there are more than 10,000 skus, row set fail
		if (totalCount + processVariantCountFromProductMaster > ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
			for (SaveProductRecordRowDo recordRow : keepProcessingList) {
				generateFailRow(recordRow, failedList, "message354");
			}
			keepProcessingList = new ArrayList<>();
		}

		Set<String> relationSendProductIdSet = keepProcessingList.stream().map(row -> {
			SingleEditProductDto product = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			return product.getProduct().getProductId();
		}).collect(Collectors.toSet());

		Map<String, ProductMasterRelationSettingResponseDto> queryRelationProductIdMap = littleMallRelationHelper.queryRelationSettingMap(userDto, storefrontStoreCode, relationSendProductIdSet);

		return LittleMallVariantProcessData.builder()
			.failedList(failedList)
			.keepProcessingList(keepProcessingList)
			.relationSendProductIdSet(relationSendProductIdSet)
			.queryRelationProductIdMap(queryRelationProductIdMap)
			.build();
	}

	private int processEditProduct(
		SingleEditProductDto primarySku,
		List<SingleEditProductDto> variantSkus,
		Map<String, SaveProductRecordRowDo> uuidRowMap,
		List<SaveProductRecordRowDo> failedList,
		List<SaveProductRecordRowDo> keepProcessingList,
		List<ProductMasterResultDto> skusFromProductMaster,
		Long recordId
	) {
		ProductMasterResultDto primarySkuFromProductMaster = null;
		List<ProductMasterResultDto> variantSkusFromProductMaster = new ArrayList<>();
		for (ProductMasterResultDto skuFromProductMaster : skusFromProductMaster) {
			if (skuFromProductMaster.getAdditional() == null || skuFromProductMaster.getAdditional().getLittleMall() == null) {
				continue;
			}
			LittleMallProductDto littleMallProductDto = skuFromProductMaster.getAdditional().getLittleMall();
			if (Boolean.TRUE.equals(littleMallProductDto.getIsPrimarySku())) {
				primarySkuFromProductMaster = skuFromProductMaster;
			} else if (Boolean.FALSE.equals(littleMallProductDto.getIsPrimarySku())) {
				variantSkusFromProductMaster.add(skuFromProductMaster);
			}
		}

		int processVariantCountFromProductMaster = 0;
		SaveProductRecordRowDo primaryRow = null;
		if (primarySku != null) {
			primaryRow = uuidRowMap.get(primarySku.getProduct().getUuid());
		}
		if (primarySkuFromProductMaster == null) {
			if (primaryRow != null) {
				generateFailRow(primaryRow, failedList, "message34");
			}
			if (CollectionUtil.isNotEmpty(variantSkus)) {
				variantSkus.forEach(variantSku -> generateFailRow(uuidRowMap.get(variantSku.getProduct().getUuid()), failedList, "message34"));
			}
		} else {
			// primarySku : product master的skuId如果與excel input的skuId不同，Y + N 整組set fail
			if (primarySku != null && primaryRow != null && !primarySkuFromProductMaster.getSkuId().equals(primarySku.getProduct().getSkuId())) {
				generateFailRow(primaryRow, failedList, "message348");
				if (CollectionUtil.isNotEmpty(variantSkus)) {
					variantSkus.forEach(variantSku -> generateFailRow(uuidRowMap.get(variantSku.getProduct().getUuid()), failedList, "message348"));
				}
				// 判斷Y的共用欄位是否有改，若有改送excel input Y + 找出所有product master的N + excel input 的 N set共用欄位一起送
			} else if (primarySku != null && primaryRow != null && !isPrimarySkuGlobalFieldsEquals(primarySku, primarySkuFromProductMaster)) {
				if (CollectionUtil.isNotEmpty(variantSkusFromProductMaster)) {
					variantSkusFromProductMaster.forEach(variantProduct -> {
						if (uuidRowMap.containsKey(variantProduct.getUuid())) {
							return;
						}
						ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(ProductMasterDto.convertFromProductMasterResultDto(variantProduct), ProductMasterDto.class), ProductMasterDto.class);
						SingleEditProductDto variantProductFromProductMaster = new SingleEditProductDto();
						variantProductFromProductMaster.setProduct(productMasterDto);
						variantProductFromProductMaster.setRelation(primarySku.getRelation());
						setPrimarySkuGlobalFieldsToVariant(primarySku, variantProductFromProductMaster);
						checkBuHelper.addBuToSend(variantProductFromProductMaster);

						SaveProductRecordRowDo createVariantRowFromProductMaster = saveProductRecordRowHelper.createProductRecordRowDo(recordId, variantProductFromProductMaster, SaveProductStatus.REQUESTING_PM, null);
						keepProcessingList.add(createVariantRowFromProductMaster);
					});
					processVariantCountFromProductMaster = variantSkusFromProductMaster.size();
				}
				if (CollectionUtil.isNotEmpty(variantSkus)) {
					variantSkus.forEach(processVariantSku -> {
						setPrimarySkuGlobalFieldsToVariant(primarySku, processVariantSku);
						SaveProductRecordRowDo variantRow = uuidRowMap.get(processVariantSku.getProduct().getUuid());
						variantRow.setContent(gson.toJson(processVariantSku));
						keepProcessingList.add(variantRow);
					});
				}
				// excel input Y
				keepProcessingList.add(primaryRow);
				// excel input Y 沒有改共用欄位
			} else if (primarySku != null && primaryRow != null) {
				keepProcessingList.add(primaryRow);
				// Y success，N set Y的共用欄位,送Y + N 給Product master
				if (CollectionUtil.isNotEmpty(variantSkus)) {
					variantSkus.forEach(processVariantSku -> {
						setPrimarySkuGlobalFieldsToVariant(primarySku, processVariantSku);
						SaveProductRecordRowDo variantRow = uuidRowMap.get(processVariantSku.getProduct().getUuid());
						variantRow.setContent(gson.toJson(processVariantSku));
						keepProcessingList.add(variantRow);
					});
				}
				//  Y fail, 或是Excel沒有送入Y， product master有Y, 送N過去(這邊共用欄位不用再set，因為原本就是從product master找來的原本的值)
			} else if (primarySku == null && CollectionUtil.isNotEmpty(variantSkus)) {
				variantSkus.forEach(processVariantSku -> {
					SaveProductRecordRowDo variantRow = uuidRowMap.get(processVariantSku.getProduct().getUuid());
					keepProcessingList.add(variantRow);
				});
			}
		}
		return processVariantCountFromProductMaster;
	}

	private boolean processPrimarySku(SingleEditProductDto primarySku,
									  ProductMasterResultDto primarySkuFromProductMaster,
									  Map<String, SaveProductRecordRowDo> skuRowMap,
									  List<SaveProductRecordRowDo> failedList,
									  List<SaveProductRecordRowDo> keepProcessingList,
									  Set<String> relationSendProductIdSet) {
		if (primarySku == null) {
			return false;
		}

		String productId = primarySku.getProduct().getProductId();
		String primarySkuId = primarySku.getProduct().getSkuId();
		SaveProductRecordRowDo primaryRow = skuRowMap.get(primarySkuId);
		if (primarySkuFromProductMaster != null) {
			if (!primarySkuFromProductMaster.getSkuId().equals(primarySku.getProduct().getSkuId())) {
				generateFailRow(primaryRow, failedList, "message348", relationSendProductIdSet, productId);
			} else {
				generateFailRow(primaryRow, failedList, "message48", relationSendProductIdSet, productId);
			}
			return false;
		}

		keepProcessingList.add(primaryRow);
		return true;
	}

	private void processVariantSku(boolean excelPrimaryExistsAndValid, SingleEditProductDto primarySku,
								   ProductMasterResultDto primarySkuFromProductMaster, SingleEditProductDto variantSku,
								   Map<String, SaveProductRecordRowDo> skuRowMap,
								   List<SaveProductRecordRowDo> failedList,
								   List<SaveProductRecordRowDo> keepProcessingList,
								   Set<String> relationSendProductIdSet) {

		SaveProductRecordRowDo variantRow = skuRowMap.get(variantSku.getProduct().getSkuId());
		String productId = variantSku.getProduct().getProductId();

		// primarySku exist, excel variant isPrimarySku change to Y
		if (primarySkuFromProductMaster != null && primarySku != null && !primarySkuFromProductMaster.getSkuId().equals(primarySku.getProduct().getSkuId())) {
			generateFailRow(variantRow, failedList, "message348", relationSendProductIdSet, productId);
			return;
		}
		LittleMallProductDto variant = variantSku.getProduct().getAdditional().getLittleMall();
		// excel primarySku success
		if (excelPrimaryExistsAndValid && primarySku != null) {
			LittleMallProductDto primary = primarySku.getProduct().getAdditional().getLittleMall();
			if (checkLittleMallProductHelper.isPrimarySkuNotEnabledVariant(primary)) {
				generateFailRow(variantRow, failedList, "message351", relationSendProductIdSet, productId);
			} else if (!checkLittleMallProductHelper.isSameRelationWithPrimarySku(variant, primary)) {
				generateFailRow(variantRow, failedList, "message349", relationSendProductIdSet, productId);
			} else {
				setPrimarySkuGlobalFieldsToVariant(primarySku, variantSku);
				variantRow.setContent(gson.toJson(variantSku));
				keepProcessingList.add(variantRow);
			}
			// excel primarySku fail, or not have excel primarySku
		} else if (primarySkuFromProductMaster != null) {
			LittleMallProductDto primary = primarySkuFromProductMaster.getAdditional().getLittleMall();
			if (checkLittleMallProductHelper.isPrimarySkuNotEnabledVariant(primary)) {
				generateFailRow(variantRow, failedList, "message351", relationSendProductIdSet, productId);
			} else if (!checkLittleMallProductHelper.isSameRelationWithPrimarySku(variant, primary)) {
				generateFailRow(variantRow, failedList, "message349", relationSendProductIdSet, productId);
			} else {
				setPrimarySkuGlobalFieldsToVariant(primarySkuFromProductMaster, variantSku);
				variantRow.setContent(gson.toJson(variantSku));
				keepProcessingList.add(variantRow);
			}
			// excel and product master all not have primarySku
		} else {
			generateFailRow(variantRow, failedList, "message34", relationSendProductIdSet, productId);
		}
	}

	private Pair<List<SaveProductRecordRowDo>, List<SaveProductRecordRowDo>> processRequestRelation(UserDto userDto, LittleMallVariantProcessData processData) {
		if (processData == null) {
			log.warn("processData is null");
			return Pair.of(new ArrayList<>(), new ArrayList<>());
		}

		List<SaveProductRecordRowDo> finalProcessingRows = new ArrayList<>();
		Map<String, LittleMallRelationDto> finalSentRelationMap = new HashMap<>();

		List<SaveProductRecordRowDo> failedList = processData.getFailedList();
		Set<String> relationSendProductIdSet = processData.getRelationSendProductIdSet();
		Map<String, ProductMasterRelationSettingResponseDto> queryRelationProductIdMap = processData.getQueryRelationProductIdMap();

		// find all sku success from same productId relations
		for (SaveProductRecordRowDo skuRow : processData.getKeepProcessingList()) {
			SingleEditProductDto keepProcessingDto = gson.fromJson(skuRow.getContent(), SingleEditProductDto.class);
			String productId = keepProcessingDto.getProduct().getProductId();

			// if any sku fail, same productId all set fail and relation could not send
			if (!relationSendProductIdSet.contains(productId)) {
				log.warn("cannot find productId:{}, record row id:{}", productId, skuRow.getId());
				failedList.add(skuRow);
				continue;
			}

			ProductMasterRelationSettingResponseDto queryRelation = queryRelationProductIdMap.get(productId);
			Pair<Boolean, String> checkRelationResult = littleMallRelationHelper.checkRelationSetting(queryRelation, productId, keepProcessingDto.getRelation());
			if (Boolean.TRUE.equals(checkRelationResult.getLeft())) {
				finalProcessingRows.add(skuRow);
				if (keepProcessingDto.getRelation() != null) {
					finalSentRelationMap.putIfAbsent(productId, keepProcessingDto.getRelation());
				}
			} else {
				generateFailRowErrorMessage(skuRow, failedList, checkRelationResult.getRight());
			}
		}

		List<LittleMallRelationDto> finalSentRelations = new ArrayList<>(finalSentRelationMap.values());
		List<String> putVariantError = littleMallRelationHelper.requestProductMasterPutRelation(userDto, finalSentRelations);
		// if relation error, could not create sku
		if (CollectionUtil.isNotEmpty(putVariantError)) {
			log.info("put variant relation error:{}", putVariantError);
			finalProcessingRows.forEach(processRow -> {
				processRow.setStatus(StatusCodeEnum.FAIL.getCode());
				processRow.setErrorMessage(putVariantError.toString());
				failedList.add(processRow);
			});
			finalProcessingRows = new ArrayList<>();
		}
		return Pair.of(failedList, finalProcessingRows);
	}

	public Map<String, ProductMasterResultDto> findPrimarySkusMap(UserDto userDto, Set<String> productIds, String storefrontStoreCode) {
		Map<String, ProductMasterResultDto> primarySkusMap = new HashMap<>();
		for (String productId : productIds) {
			ProductSearchRequestDto productSearchRequestDto = ProductSearchRequestDto.builder()
				.page(1)
				.size(SEARCH_MAX_SIZE)
				.productId(productId)
				.littleMallStorefrontStoreCodes(List.of(storefrontStoreCode))
				.littleMallIsPrimary(true)
				.build();
			ProductOverviewResultDto productOverviewResult = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.EXACT.name());
			if (productOverviewResult == null) {
				log.info("call product master error, productOverviewResult = null");
				throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR);
			}
			if (CollectionUtil.isEmpty(productOverviewResult.getContent())) {
				continue;
			}
			if (productOverviewResult.getContent().size() > 1) {
				log.warn("Little Mall findPrimarySku size > 1, size:{}, store:{}, productId:{}",
					productOverviewResult.getContent().size(), storefrontStoreCode, productId);
			}
			primarySkusMap.putIfAbsent(productId, productOverviewResult.getContent().get(0));
		}
		return primarySkusMap;
	}

	public Map<String, List<ProductMasterResultDto>> findProductIdSkusMap(UserDto userDto, Set<String> productIds, String storefrontStoreCode) {
		Map<String, List<ProductMasterResultDto>> skusMap = new HashMap<>();
		for (String productId : productIds) {
			ProductSearchRequestDto productSearchRequestDto = ProductSearchRequestDto.builder()
				.page(1)
				.size(SEARCH_MAX_SIZE)
				.productId(productId)
				.littleMallStorefrontStoreCodes(List.of(storefrontStoreCode))
				.build();
			ProductOverviewResultDto productOverviewResult = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.EXACT.name());
			if (productOverviewResult == null) {
				log.info("call product master error, productOverviewResult = null");
				throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR);
			}
			if (productOverviewResult.getTotalElements() == 0 || CollectionUtil.isEmpty(productOverviewResult.getContent())) {
				continue;
			}
			skusMap.computeIfAbsent(productId, skus -> new ArrayList<>()).addAll(productOverviewResult.getContent());

			int totalPage = productOverviewResult.getTotalPages();
			for (int i = productSearchRequestDto.getPage() + 1; i <= totalPage; i++) {
				productSearchRequestDto.setPage(i);
				ProductOverviewResultDto iteratorResultDto = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.EXACT.name());
				if (iteratorResultDto == null) {
					throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR);
				}
				if (CollectionUtil.isNotEmpty(iteratorResultDto.getContent())) {
					skusMap.computeIfAbsent(productId, skus -> new ArrayList<>()).addAll(iteratorResultDto.getContent());
				}
			}
		}
		return skusMap;
	}

	private Pair<SingleEditProductDto, List<SingleEditProductDto>> convertPrimaryAndVariantSku(List<SingleEditProductDto> productList) {
		if (CollectionUtil.isEmpty(productList)) {
			return Pair.of(null, Collections.emptyList());
		}
		SingleEditProductDto primarySku = productList.stream()
			.filter(product -> {
				if (product.getProduct() == null || product.getProduct().getAdditional() == null ||
					product.getProduct().getAdditional().getLittleMall() == null) {
					return false;
				} else {
					return Boolean.TRUE.equals(product.getProduct().getAdditional().getLittleMall().getIsPrimarySku());
				}
			})
			.findFirst()
			.orElse(null);

		List<SingleEditProductDto> variantSkus = productList.stream()
			.filter(product -> {
				if (product.getProduct() == null || product.getProduct().getAdditional() == null ||
					product.getProduct().getAdditional().getLittleMall() == null) {
					return false;
				} else {
					return Boolean.FALSE.equals(product.getProduct().getAdditional().getLittleMall().getIsPrimarySku());
				}
			})
			.collect(Collectors.toList());
		return Pair.of(primarySku, variantSkus);
	}

	public boolean isPrimarySkuGlobalFieldsEquals(SingleEditProductDto primarySku, ProductMasterResultDto primarySkuMaster) {
		LittleMallProductDto primaryDetail = primarySku.getProduct().getAdditional().getLittleMall();
		LittleMallProductDto primarySkuMasterDetail = primarySkuMaster.getAdditional().getLittleMall();

		return StringUtil.equalsIgnoreNullWithEmpty(primaryDetail.getProductReadyMethod(), primarySkuMasterDetail.getProductReadyMethod())
			&& Objects.equals(primaryDetail.getDisplayInHktvmallCategory(), primarySkuMasterDetail.getDisplayInHktvmallCategory())
			&& StringUtil.equalsIgnoreNullWithEmpty(primaryDetail.getSkuLongDescriptionCh(), primarySkuMasterDetail.getSkuLongDescriptionCh())
			&& Objects.equals(primaryDetail.getOtherPhoto(), primarySkuMasterDetail.getOtherPhoto())
			&& StringUtil.equalsIgnoreNullWithEmpty(primarySku.getProduct().getSkuNameCh(), primarySkuMaster.getSkuNameCh());
	}

	public void setPrimarySkuGlobalFieldsToVariant(SingleEditProductDto primarySku, SingleEditProductDto variantSku) {
		if (primarySku == null || variantSku == null) {
			return;
		}
		LittleMallProductDto primaryDetails = primarySku.getProduct().getAdditional().getLittleMall();
		LittleMallProductDto variantDetails = variantSku.getProduct().getAdditional().getLittleMall();

		variantDetails.setProductReadyMethod(primaryDetails.getProductReadyMethod());
		variantDetails.setDisplayInHktvmallCategory(primaryDetails.getDisplayInHktvmallCategory());
		variantDetails.setSkuLongDescriptionCh(primaryDetails.getSkuLongDescriptionCh());
		variantDetails.setOtherPhoto(primaryDetails.getOtherPhoto());
		variantSku.getProduct().setSkuNameCh(primarySku.getProduct().getSkuNameCh());
	}

	private void setPrimarySkuGlobalFieldsToVariant(ProductMasterResultDto primarySku, SingleEditProductDto variantSku) {
		LittleMallProductDto primaryDetails = primarySku.getAdditional().getLittleMall();
		LittleMallProductDto variantDetails = variantSku.getProduct().getAdditional().getLittleMall();

		variantDetails.setProductReadyMethod(primaryDetails.getProductReadyMethod());
		variantDetails.setDisplayInHktvmallCategory(primaryDetails.getDisplayInHktvmallCategory());
		variantDetails.setSkuLongDescriptionCh(primaryDetails.getSkuLongDescriptionCh());
		variantDetails.setOtherPhoto(primaryDetails.getOtherPhoto());
		variantSku.getProduct().setSkuNameCh(primarySku.getSkuNameCh());
	}

	private Map<String, List<SingleEditProductDto>> generatePrimaryWithVariantSkuMap(List<SaveProductRecordRowDo> rows) {
		if (CollectionUtil.isEmpty(rows)) {
			return new HashMap<>();
		}
		return rows.stream()
			.map(row -> {
				if (row.getContent() == null) return null;
				SingleEditProductDto dto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
				if (dto == null || dto.getProduct() == null ||
					dto.getProduct().getAdditional() == null ||
					dto.getProduct().getAdditional().getLittleMall() == null) {
					return null;
				}
				return dto;
			})
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(product -> product.getProduct().getProductId()));
	}

	private void generateFailRow(SaveProductRecordRowDo row, List<SaveProductRecordRowDo> failedList, String messageKey) {
		if (row == null || StringUtil.isEmpty(messageKey)) {
			return;
		}
		row.setStatus(StatusCodeEnum.FAIL.getCode());
		row.setErrorMessage(messageSource.getMessage(messageKey, null, null));
		failedList.add(row);
	}

	private void generateFailRowErrorMessage(SaveProductRecordRowDo row, List<SaveProductRecordRowDo> failedList, String errorMessage) {
		if (row == null || StringUtil.isEmpty(errorMessage)) {
			return;
		}
		row.setStatus(StatusCodeEnum.FAIL.getCode());
		row.setErrorMessage(errorMessage);
		failedList.add(row);
	}

	/**
	 * create sku need to check send relation by same productId
	 */
	private void generateFailRow(SaveProductRecordRowDo row, List<SaveProductRecordRowDo> failedList, String messageKey, Set<String> relationSendProductIdSet, String productId) {
		if (row == null || StringUtil.isEmpty(messageKey)) {
			return;
		}
		row.setStatus(StatusCodeEnum.FAIL.getCode());
		row.setErrorMessage(messageSource.getMessage(messageKey, null, null));
		failedList.add(row);
		relationSendProductIdSet.remove(productId);
	}
}
