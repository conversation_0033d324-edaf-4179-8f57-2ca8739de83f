package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class BatchEditPriceDto extends BatchEditProductBaseDto {
	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;
	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;
	private String style;
	@JsonProperty("discount_text_en")
	@SerializedName("discount_text_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextEn;
	@JsonProperty("discount_text_ch")
	@SerializedName("discount_text_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextCh;
	@JsonProperty("discount_text_sc")
	@SerializedName("discount_text_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextSc;
}
