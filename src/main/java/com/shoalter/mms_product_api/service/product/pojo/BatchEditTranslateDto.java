package com.shoalter.mms_product_api.service.product.pojo;

import com.shoalter.mms_product_api.helper.TranslationHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class BatchEditTranslateDto extends BatchEditProductBaseDto {
	private String uuid;
	private String skuNameCh;
	private String skuNameSc;
	// additional.hktv
	private HktvField hktvField;

	public boolean needsScTranslation() {
		if (StringUtils.isNotEmpty(skuNameCh) && StringUtils.isEmpty(skuNameSc)) {
			return true;
		}
		return hktvField != null && hktvField.needsScTranslation();
	}

	public void translateChToSc(TranslationHelper translationHelper) {
		if (StringUtils.isEmpty(this.skuNameSc)) this.skuNameSc = translationHelper.translateIfNotEmpty(this.skuNameCh);

		if (this.hktvField != null) {
			if (StringUtils.isEmpty(this.hktvField.getSkuShortDescriptionSc())) this.hktvField.setSkuShortDescriptionSc(translationHelper.translateIfNotEmpty(this.hktvField.getSkuShortDescriptionCh()));
			if (StringUtils.isEmpty(this.hktvField.getSkuLongDescriptionSc())) this.hktvField.setSkuLongDescriptionSc(translationHelper.translateIfNotEmpty(this.hktvField.getSkuLongDescriptionCh()));
			if (StringUtils.isEmpty(this.hktvField.getPackingSpecSc())) this.hktvField.setPackingSpecSc(translationHelper.translateIfNotEmpty(this.hktvField.getPackingSpecCh()));
			if (StringUtils.isEmpty(this.hktvField.getInvoiceRemarksSc())) this.hktvField.setInvoiceRemarksSc(translationHelper.translateIfNotEmpty(this.hktvField.getInvoiceRemarksCh()));
			if (StringUtils.isEmpty(this.hktvField.getFinePrintSc())) this.hktvField.setFinePrintSc(translationHelper.translateIfNotEmpty(this.hktvField.getFinePrintCh()));
			if (StringUtils.isEmpty(this.hktvField.getVideoLinkTextSc())) this.hktvField.setVideoLinkTextSc(translationHelper.translateIfNotEmpty(this.hktvField.getVideoLinkTextCh()));
			if (StringUtils.isEmpty(this.hktvField.getVideoLinkTextSc2())) this.hktvField.setVideoLinkTextSc2(translationHelper.translateIfNotEmpty(this.hktvField.getVideoLinkTextCh2()));
			if (StringUtils.isEmpty(this.hktvField.getVideoLinkTextSc3())) this.hktvField.setVideoLinkTextSc3(translationHelper.translateIfNotEmpty(this.hktvField.getVideoLinkTextCh3()));
			if (StringUtils.isEmpty(this.hktvField.getVideoLinkTextSc4())) this.hktvField.setVideoLinkTextSc4(translationHelper.translateIfNotEmpty(this.hktvField.getVideoLinkTextCh4()));
			if (StringUtils.isEmpty(this.hktvField.getVideoLinkTextSc5())) this.hktvField.setVideoLinkTextSc5(translationHelper.translateIfNotEmpty(this.hktvField.getVideoLinkTextCh5()));
			if (StringUtils.isEmpty(this.hktvField.getClaimLinkSc())) this.hktvField.setClaimLinkSc(translationHelper.translateIfNotEmpty(this.hktvField.getClaimLinkCh()));
			if (StringUtils.isEmpty(this.hktvField.getWarrantySupplierSc())) this.hktvField.setWarrantySupplierSc(translationHelper.translateIfNotEmpty(this.hktvField.getWarrantySupplierCh()));
			if (StringUtils.isEmpty(this.hktvField.getWarrantyRemarkSc())) this.hktvField.setWarrantyRemarkSc(translationHelper.translateIfNotEmpty(this.hktvField.getWarrantyRemarkCh()));
			if (StringUtils.isEmpty(this.hktvField.getServiceCentreAddressSc())) this.hktvField.setServiceCentreAddressSc(translationHelper.translateIfNotEmpty(this.hktvField.getServiceCentreAddressCh()));
		}
	}

	@Data
	public static class HktvField {
		private String 	skuShortDescriptionCh;
		private String 	skuShortDescriptionSc;
		private String 	skuLongDescriptionCh;
		private String 	skuLongDescriptionSc;
		private String 	packingSpecCh;
		private String 	packingSpecSc;
		private String 	invoiceRemarksCh;
		private String 	invoiceRemarksSc;
		private String 	finePrintCh;
		private String 	finePrintSc;
		private String 	videoLinkTextCh;
		private String 	videoLinkTextSc;
		private String 	videoLinkTextCh2;
		private String 	videoLinkTextSc2;
		private String 	videoLinkTextCh3;
		private String 	videoLinkTextSc3;
		private String 	videoLinkTextCh4;
		private String 	videoLinkTextSc4;
		private String 	videoLinkTextCh5;
		private String 	videoLinkTextSc5;
		private String 	claimLinkCh;
		private String 	claimLinkSc;
		private String 	warrantySupplierCh;
		private String 	warrantySupplierSc;
		private String 	warrantyRemarkCh;
		private String 	warrantyRemarkSc;
		private String 	serviceCentreAddressCh;
		private String 	serviceCentreAddressSc;

		public boolean needsScTranslation() {
			return (StringUtils.isNotEmpty(skuShortDescriptionCh) && StringUtils.isEmpty(skuShortDescriptionSc)) ||
				(StringUtils.isNotEmpty(skuLongDescriptionCh) && StringUtils.isEmpty(skuLongDescriptionSc)) ||
				(StringUtils.isNotEmpty(packingSpecCh) && StringUtils.isEmpty(packingSpecSc)) ||
				(StringUtils.isNotEmpty(invoiceRemarksCh) && StringUtils.isEmpty(invoiceRemarksSc)) ||
				(StringUtils.isNotEmpty(finePrintCh) && StringUtils.isEmpty(finePrintSc)) ||
				(StringUtils.isNotEmpty(videoLinkTextCh) && StringUtils.isEmpty(videoLinkTextSc)) ||
				(StringUtils.isNotEmpty(videoLinkTextCh2) && StringUtils.isEmpty(videoLinkTextSc2)) ||
				(StringUtils.isNotEmpty(videoLinkTextCh3) && StringUtils.isEmpty(videoLinkTextSc3)) ||
				(StringUtils.isNotEmpty(videoLinkTextCh4) && StringUtils.isEmpty(videoLinkTextSc4)) ||
				(StringUtils.isNotEmpty(videoLinkTextCh5) && StringUtils.isEmpty(videoLinkTextSc5)) ||
				(StringUtils.isNotEmpty(claimLinkCh) && StringUtils.isEmpty(claimLinkSc)) ||
				(StringUtils.isNotEmpty(warrantySupplierCh) && StringUtils.isEmpty(warrantySupplierSc)) ||
				(StringUtils.isNotEmpty(warrantyRemarkCh) && StringUtils.isEmpty(warrantyRemarkSc)) ||
				(StringUtils.isNotEmpty(serviceCentreAddressCh) && StringUtils.isEmpty(serviceCentreAddressSc));
		}
	}
}
