package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.BrandIdMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.BrandMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.BrandNameMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.response.BrandDto;
import com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class FindBrandService {

	public final BrandRepository brandRepository;

	public ResponseDto<FindBrandDto> start(Integer id) {

		return ResponseDto.success(brandRepository.findBrandById(id));

	}

	public ResponseDto<Page<BrandDto>> start(BrandMainRequestData request) {
		Pageable pageable = PageRequest.of(request.getPage(), request.getSize(),
			Sort.Direction.fromString(request.getDirection()), request.getSort());

		return ResponseDto.success(brandRepository.findBrandPage(request, pageable));
	}

	public ResponseDto<List<BrandDto>> start(BrandNameMainRequestData request) {
		return ResponseDto.success(brandRepository.findBrandByNames(request));
	}

	public ResponseDto<List<BrandDto>> start(BrandIdMainRequestData request) {
		return ResponseDto.success(brandRepository.findBrandByIds(request));
	}
}

