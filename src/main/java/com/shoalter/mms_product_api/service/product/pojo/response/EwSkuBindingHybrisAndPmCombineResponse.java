package com.shoalter.mms_product_api.service.product.pojo.response;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.UpdateEwSkuBindingFailDataDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateEwSkuBindingResultDataDto;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
public class EwSkuBindingHybrisAndPmCombineResponse {
	private String status;
	private String message;
	private String electronicStoreSkuCode;
	private String ewStoreSkuCode;

	public static List<EwSkuBindingHybrisAndPmCombineResponse> convertFromHybrisResponse(List<HybrisUpdateEwSkuBindingResultDataDto> hybrisUpdateEwSkuBindingResultDataDtos) {
		List<EwSkuBindingHybrisAndPmCombineResponse> ewSkuBindingHybrisAndPmCombineResponses = new ArrayList<>();
		for (HybrisUpdateEwSkuBindingResultDataDto hybrisResDto : hybrisUpdateEwSkuBindingResultDataDtos) {
			ewSkuBindingHybrisAndPmCombineResponses.add(EwSkuBindingHybrisAndPmCombineResponse.builder()
					.status(hybrisResDto.getStatus())
					.message(hybrisResDto.getMessage())
					.electronicStoreSkuCode(hybrisResDto.getParentProductCode())
					.ewStoreSkuCode(hybrisResDto.getDpProductCode())
					.build());
		}
		return ewSkuBindingHybrisAndPmCombineResponses;
	}

	public static List<EwSkuBindingHybrisAndPmCombineResponse> convertFromProductMasterResponse(List<UpdateEwSkuBindingFailDataDto> updateEwSkuBindingFailDataDto) {
		List<EwSkuBindingHybrisAndPmCombineResponse> ewSkuBindingHybrisAndPmCombineResponses = new ArrayList<>();
		for (UpdateEwSkuBindingFailDataDto productMasterResDto : updateEwSkuBindingFailDataDto) {
			ewSkuBindingHybrisAndPmCombineResponses.add(EwSkuBindingHybrisAndPmCombineResponse.builder()
					.status(ConstantType.ERROR_RESPONSE_FAILED)
					.message(productMasterResDto.getMessage())
					.electronicStoreSkuCode(productMasterResDto.getElectronicProductStoreSkuId())
					.ewStoreSkuCode(productMasterResDto.getExtendedWarrantyProductStoreSkuId())
					.build());
		}
		return ewSkuBindingHybrisAndPmCombineResponses;
	}
}
