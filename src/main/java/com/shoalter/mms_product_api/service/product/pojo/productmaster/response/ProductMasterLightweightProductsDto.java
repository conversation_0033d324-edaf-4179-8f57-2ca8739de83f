package com.shoalter.mms_product_api.service.product.pojo.productmaster.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductMasterLightweightProductsDto {

	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuCode;
	@JsonProperty("store_sku_id")
	@SerializedName("store_sku_id")
	private String storeSkuId;
}
