package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.projection.CheckSaveProductRecordsStatusProjection;
import com.shoalter.mms_product_api.service.product.pojo.response.CheckSaveProductRecordRowStatusResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.CheckSaveProductRecordsStatusMainResponseData;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CheckSaveProductRecordsStatusService {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	public ResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> start(List<Long> recordIds) {
		if (CollectionUtil.isEmpty(recordIds)) {
			throw new NoDataException();
		}

		List<CheckSaveProductRecordsStatusProjection> records = findByRecordIds(recordIds);

		if (records.isEmpty()) {
			return generateResponse(StatusCodeEnum.SUCCESS, new ArrayList<>(), null);
		}

		return generateResponse(StatusCodeEnum.SUCCESS, generateCheckSaveProductRecordsData(records), null);
	}

	private ResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> generateResponse(StatusCodeEnum status,
			List<CheckSaveProductRecordsStatusMainResponseData> data, List<String> errorMessageList) {
		return ResponseDto.<List<CheckSaveProductRecordsStatusMainResponseData>>builder()
			.status(status.getCode())
			.data(data)
			.errorMessageList(errorMessageList)
			.build();
	}

	private List<CheckSaveProductRecordsStatusProjection> findByRecordIds(List<Long> recordIds) {
		return saveProductRecordRepository.findByRecordIds(recordIds);
	}

	private List<CheckSaveProductRecordsStatusMainResponseData> generateCheckSaveProductRecordsData(List<CheckSaveProductRecordsStatusProjection> recordList) {
		Map<Long, List<CheckSaveProductRecordsStatusProjection>> recordMap = recordList.stream()
			.collect(Collectors.groupingBy(CheckSaveProductRecordsStatusProjection::getRecordId));

		return recordMap.entrySet().stream()
			.filter(Objects::nonNull)
			.map(entry -> {
				List<CheckSaveProductRecordRowStatusResponseData> rows = entry.getValue().stream()
					.map(this::generateCheckSaveProductRecordRowData)
					.collect(Collectors.toList());

				boolean hasUpdating = entry.getValue().stream().anyMatch(row -> SaveProductStatusEnum.UPDATING_LIST.contains(row.getRowStatus()));
				boolean hasError = entry.getValue().stream().anyMatch(row -> row.getRowStatus().equals(SaveProductStatusEnum.FAIL.getCode()));
				String finalRecordStatus = SaveProductStatusEnum.SUCCESS.getName();
				if (hasUpdating) {
					finalRecordStatus = SaveProductStatusEnum.UPDATING.getName();
				} else if (!hasUpdating && hasError) {
					finalRecordStatus = SaveProductStatusEnum.FAIL.getName();
				}

				return new CheckSaveProductRecordsStatusMainResponseData(entry.getKey(), finalRecordStatus, rows);

			})
			.collect(Collectors.toList());
	}

	private CheckSaveProductRecordRowStatusResponseData generateCheckSaveProductRecordRowData(CheckSaveProductRecordsStatusProjection row) {
		CheckSaveProductRecordRowStatusResponseData rowResponse = new CheckSaveProductRecordRowStatusResponseData();
		rowResponse.setUuid(row.getUuid());
		rowResponse.setSkuId(row.getSku());
		rowResponse.setErrorMessage(row.getErrorMessage());
		rowResponse.setStatus(SaveProductStatusEnum.UPDATING_LIST.contains(row.getRowStatus())
			? SaveProductStatusEnum.UPDATING.getName()
			: SaveProductStatusEnum.getProductStatusName(row.getRowStatus()));

		return rowResponse;
	}
}
