package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductCommonlyUsedRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.EditProductCommonlyUsedInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.SkuMapUuidDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BatchEditProductCommonlyUsedService {

    private final PermissionHelper permissionHelper;
    private final ProductMapper productMapper;
    private final BatchCheckHelper batchCheckHelper;
	private final SaveProductRecordGroupHelper saveProductRecordGroupHelper;

    public ResponseDto<Long> start(UserDto userDto, BatchEditProductCommonlyUsedRequestDto batchEditProductCommonlyUsedRequestDto, String clientIp) {
        checkRequest(userDto, batchEditProductCommonlyUsedRequestDto);

        ResponseDto<Long> checkCountResult = batchCheckHelper.checkCount(userDto, SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED,
				batchEditProductCommonlyUsedRequestDto, batchEditProductCommonlyUsedRequestDto.getProductTemplate(), BatchCheckHelper.MAXIMUM_10000, clientIp);
        if (checkCountResult.getStatus() == -1) {
            return ResponseDto.<Long>builder().data(checkCountResult.getData()).status(1).build();
        }

        List<String> skuCodeList = batchEditProductCommonlyUsedRequestDto.getProductTemplate().stream().map(EditProductCommonlyUsedInfoDto::getSkuCode).collect(Collectors.toList());
        List<SkuMapUuidDto> skuAndUuid = productMapper.getUuidByStoreIdAndSkuList(batchEditProductCommonlyUsedRequestDto.getStoreId(), skuCodeList);
        Map<String, String> skuMapUuid = skuAndUuid.stream().collect(Collectors.toMap(SkuMapUuidDto::getSkuCode, SkuMapUuidDto::getUuid));

		Long recordId = saveProductRecordGroupHelper.saveProductRecordMethod(
			userDto, batchEditProductCommonlyUsedRequestDto, skuMapUuid, clientIp);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", recordId, SaveProductTypeEnum.getProductTypeName(SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED), batchEditProductCommonlyUsedRequestDto.getProductTemplate().size(), userDto.getUserId(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.PROCESSING));
        return ResponseDto.<Long>builder().data(recordId).status(1).build();
    }

    private void checkRequest(UserDto userDto, BatchEditProductCommonlyUsedRequestDto batchEditProductCommonlyUsedRequestDto) {
        permissionHelper.checkPermission(userDto, batchEditProductCommonlyUsedRequestDto.getMerchantId());
        for (EditProductCommonlyUsedInfoDto info : batchEditProductCommonlyUsedRequestDto.getProductTemplate()) {
            if (StringUtil.isEmpty(info.getSkuCode()) ||
                    (StringUtil.isEmpty(info.getInvisible()) && Objects.isNull(info.getPrice()) && Objects.isNull(info.getOnline()))) {
                throw new SystemI18nException("message32");
            }
        }
    }
}
