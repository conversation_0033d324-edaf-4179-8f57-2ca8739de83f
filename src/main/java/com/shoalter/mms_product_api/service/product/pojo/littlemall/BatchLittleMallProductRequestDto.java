package com.shoalter.mms_product_api.service.product.pojo.littlemall;

import com.shoalter.mms_product_api.service.base.pojo.BatchRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class BatchLittleMallProductRequestDto extends BatchRequestDto {
	//SHOPLINE,EXCEL ,LITTLE_MALL(default)
	private String source;
	private List<LittleMallBatchDto> littleMallBatchDtoList;
	private List<LittleMallRelationDto> relations;
}
