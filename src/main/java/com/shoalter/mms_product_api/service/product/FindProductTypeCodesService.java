package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.CategoryCodeDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class FindProductTypeCodesService {

	private final BuProductCategoryRepository buProductCategoryRepository;

	public ResponseDto<List<CategoryCodeDto>> start(List<String> productTypeCodes) {
		if (productTypeCodes.isEmpty()) {
			return ResponseDto.success(new ArrayList<>());
		}
		List<BuProductCategoryDo> buProductCategoryDoList =
			buProductCategoryRepository.findByProductCatCodes(ConstantType.PRODUCT_CATEGORY_MMS, BuCodeEnum.HKTV.name(), productTypeCodes);
		return ResponseDto.success(convertToDtoList(buProductCategoryDoList));
	}

	private List<CategoryCodeDto> convertToDtoList(List<BuProductCategoryDo> buProductCategoryDoList) {
		List<CategoryCodeDto> list = new ArrayList<>();
		for (BuProductCategoryDo buProductCategoryDo : buProductCategoryDoList) {
			CategoryCodeDto categoryCodeDto = new CategoryCodeDto();
			categoryCodeDto.setId(buProductCategoryDo.getId());
			categoryCodeDto.setParentId(buProductCategoryDo.getParentId());
			categoryCodeDto.setProductCatCode(buProductCategoryDo.getProductCatCode());
			categoryCodeDto.setProductCatName(buProductCategoryDo.getProductCatName());
			categoryCodeDto.setProductCatNameSchi(buProductCategoryDo.getProductCatNameSchi());
			categoryCodeDto.setProductCatNameTchi(buProductCategoryDo.getProductCatNameTchi());
			list.add(categoryCodeDto);
		}
		return list;
	}
}
