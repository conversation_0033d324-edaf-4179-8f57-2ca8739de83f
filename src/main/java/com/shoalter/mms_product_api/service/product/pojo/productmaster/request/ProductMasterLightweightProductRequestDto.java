package com.shoalter.mms_product_api.service.product.pojo.productmaster.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ProductMasterLightweightProductRequestDto {

	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storefrontStoreCode;

	@JsonProperty("product_ids")
	@SerializedName("product_ids")
	private List<String> productIds;
}
