package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductStatusDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class FindProductStatusService {

    private final ProductStorePromotionMapper productStorePromotionMapper;
    public ResponseDto<ProductStatusDto> start(String uuid) {
        ProductStatusDto productStatusDto = productStorePromotionMapper.findProductStatusByUuid(uuid);
        return ResponseDto.<ProductStatusDto>builder().data(productStatusDto).status(1).build();
    }
}
