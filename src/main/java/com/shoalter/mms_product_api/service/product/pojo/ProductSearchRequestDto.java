package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSearchRequestDto implements Serializable {
    private Integer page;
    private Integer size;
    @JsonProperty("product_name")
	@SerializedName("product_name")
    private String productName;
    @JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;
    private String barcode;
    @JsonProperty("product_id")
	@SerializedName("product_id")
    private String productId;
    @JsonProperty("bu_code")
	@SerializedName("bu_code")
	private List<String> buCode;
    @JsonProperty("product_type")
	@SerializedName("product_type")
	private String productType;
    @JsonProperty("store_id")
	@SerializedName("store_id")
    private List<String> storeId;
	@JsonProperty("little_mall_is_primary")
	@SerializedName("little_mall_is_primary")
	private Boolean littleMallIsPrimary;
	@JsonProperty("little_mall_store_id")
	@SerializedName("little_mall_store_id")
	private List<String> littleMallStorefrontStoreCodes;

    // hktv, the place use this together
    @JsonProperty("storefront_store_codes")
    @SerializedName("storefront_store_codes")
    private List<String> storefrontStoreCodes;
    private List<Integer> brand;
    @JsonProperty("last_update_from")
	@SerializedName("last_update_from")
	private String lastUpdateFrom;
    @JsonProperty("last_update_to")
	@SerializedName("last_update_to")
	private String lastUpdateTo;
    @JsonProperty("online_status")
	@SerializedName("online_status")
	private String onlineStatus;
    @JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private List<Integer> merchantId;
	@JsonProperty("iids_warehouse_seq_no")
	@SerializedName("iids_warehouse_seq_no")
    private List<String> iidsWarehouseSeqNo;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@JsonProperty("order_by")
	@SerializedName("order_by")
	private List<String> orderBy;
	@JsonProperty("is_bundle")
	@SerializedName("is_bundle")
	private Boolean isBundle;
	private Integer priority;
	@JsonProperty("storage_temperature")
	@SerializedName("storage_temperature")
	private List<String> storageTemperature;
	@JsonProperty("product_ready_method")
	@SerializedName("product_ready_method")
	private List<String> productReadyMethod;
	private String visibility;
	@JsonProperty("query_type")
	@SerializedName("query_type")
	private String queryType;
	
	@JsonProperty("force_offline")
	@SerializedName("force_offline")
	private Boolean forceOffline;

	public ProductSearchRequestDto(Integer page, Integer size, String productName, String skuId, String barcode, String productId,
								   List<String> buCode, String productType, List<Integer> brand, String lastUpdateFrom,
								   String lastUpdateTo, String onlineStatus, List<Integer> merchantId, List<String> orderBy, List<String> productReadyMethod,
								   String visibility, Boolean isBundle, Boolean forceOffline) {
		this.page = page;
		this.size = size;
		this.productName = productName;
		this.skuId = skuId;
		this.barcode = barcode;
		this.productId = productId;
		this.buCode = buCode;
		this.productType = productType;
		this.brand = brand;
		this.lastUpdateFrom = lastUpdateFrom;
		this.lastUpdateTo = lastUpdateTo;
		this.onlineStatus = onlineStatus;
		this.merchantId = merchantId;
		this.orderBy = orderBy;
		this.productReadyMethod = productReadyMethod;
		this.visibility = visibility;
		this.isBundle = isBundle;
		this.forceOffline = forceOffline;
	}
}
