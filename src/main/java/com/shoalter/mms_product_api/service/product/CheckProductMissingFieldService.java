package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.CategoryConfig;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.product.VoucherType;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.product.template.HktvUploadProductExcelReportEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.helper.SysParamHelper;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckErrorResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMissingFieldCheckRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.ValidationCheckUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckProductMissingFieldService {

	private final MessageSource messageSource;
	private final SysParamHelper sysParamHelper;
	private final StoreRepository storeRepository;

	public ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> start(ProductMissingFieldCheckRequestDto requestDto) {
		if (CollectionUtil.isEmpty(requestDto.getProducts()) || requestDto.getTemplateType() == null) {
			throw new NoDataException();
		}

		List<String> categoryForRemovalServiceList = new ArrayList<>();
		Map<String, Boolean> storeIsBuysellMap = new HashMap<>();
		if (TemplateTypeEnum.ALL_COLUMN_ENUM_SET.contains(requestDto.getTemplateType())) {

			Set<String> storefrontStoreCodes = requestDto.getProducts().stream()
				.map(HktvProductFieldDto::getStorefrontStoreCode)
				.filter(StringUtil::isNotEmpty)
				.collect(Collectors.toSet());

			//get needed data
			List<String> buysellMerchantIds = sysParamHelper.getSplitSystemParamsBySegmentAndBuCode(SysParmSegmentEnum.BUYSELL_MERCHANT, BuCodeEnum.HKTV.name());
			categoryForRemovalServiceList.addAll(sysParamHelper.getSplitSystemParamsBySegmentAndBuCode(SysParmSegmentEnum.CATEGORY_FOR_REMOVAL_SERVICE, BuCodeEnum.HKTV.name()));

			if (CollectionUtil.isNotEmpty(storefrontStoreCodes)) {
				List<StoreMerchantViewDo> storeMerchantViewDos = storeRepository.findStoreMerchantViewDoByStorefrontStoreCodes(storefrontStoreCodes);
				for (StoreMerchantViewDo storeMerchantViewDo : storeMerchantViewDos) {
					storeIsBuysellMap.put(storeMerchantViewDo.getStorefrontStoreCode(), buysellMerchantIds.contains(storeMerchantViewDo.getMerchantId().toString()));
				}
			}
		}

		DataWrapper dataWrapper = generateCheckingProductMap(requestDto.getProducts());
		Map<Map.Entry<String, String>, Long> skuIdCountMap = dataWrapper.getSkuIdCountMap();
		Map<String, Map<String, DataChecker>> variantSkuCheckMap = dataWrapper.getVariantSkuCheckMap();

		//start checking
		List<ProductMissingFieldCheckErrorResponseDto> errorResults = new ArrayList<>();
		int rowIndex = 0;
		for (HktvProductFieldDto hktvProductFieldDto : requestDto.getProducts()) {
			rowIndex++;
			Set<String> errors = new HashSet<>();
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getStorefrontStoreCode(), getErrorMessage("message187"));
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuId(), getErrorMessage("message170"));

			if (hktvProductFieldDto.getSkuId() != null &&
				skuIdCountMap.containsKey(Map.entry(hktvProductFieldDto.getStorefrontStoreCode(), hktvProductFieldDto.getSkuId())) &&
				skuIdCountMap.get(Map.entry(hktvProductFieldDto.getStorefrontStoreCode(), hktvProductFieldDto.getSkuId())) > 1) {
				errors.add(getErrorMessage("message137"));
			}

			checkVariantProductFields(errors, hktvProductFieldDto, variantSkuCheckMap);

			switch (requestDto.getTemplateType()) {
				case ONLINE_STATUS:
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuStatus(), getErrorMessage("message191"));
					break;
				case VISIBILITY:
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getInvisibleFlag(), getErrorMessage("message198"));
					break;
				case SKU_PRICE:
					ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getOriginalPrice(), getErrorMessage("message175"));
					break;
				case PACKING_DIMENSION:
					checkPackingDimension(errors, hktvProductFieldDto);
					break;
				case ALL_COLUMN:
					checkHktvField(errors, hktvProductFieldDto);
					checkAllColumnField(errors, hktvProductFieldDto, categoryForRemovalServiceList, storeIsBuysellMap);
					break;
				case ALL_COLUMN_OPEN_API:
					checkOapiField(errors, hktvProductFieldDto);
					checkAllColumnField(errors, hktvProductFieldDto, categoryForRemovalServiceList, storeIsBuysellMap);
					break;
			}

			if (!errors.isEmpty()) {
				errorResults.add(new ProductMissingFieldCheckErrorResponseDto(rowIndex, new ArrayList<>(errors)));
			}
		}

		if (errorResults.isEmpty()) {
			return ResponseDto.success(null);
		}

		return ResponseDto.<List<ProductMissingFieldCheckErrorResponseDto>>builder()
			.status(StatusCodeEnum.FAIL.getCode())
			.data(errorResults)
			.build();
	}

	private String getErrorMessage(String messageCode, String... args) {
		return messageSource.getMessage(messageCode, args, null);
	}

	private void checkGlobalFields(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getProductId(), getErrorMessage("message171"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getProductTypeCode(), getErrorMessage("message188"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPrimaryCategoryCode(), getErrorMessage("message189"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getProductReadyMethod(), getErrorMessage("message190"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getDeliveryMethod(), getErrorMessage("message228"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuStatus(), getErrorMessage("message191"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getIsPrimarySku(), getErrorMessage("message180"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuNameEn(), getErrorMessage("message193"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuNameCh(), getErrorMessage("message172"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuNameSc(), getErrorMessage("message307", "SKU Name (SC)"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuShortDescriptionEn(), getErrorMessage("message194"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuShortDescriptionCh(), getErrorMessage("message195"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getSkuShortDescriptionSc(), getErrorMessage("message307", "SKU Short Description (SC)"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getManufacturedCountry(), getErrorMessage("message196"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getCurrency(), getErrorMessage("message197"));
		ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getOriginalPrice(), getErrorMessage("message175"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getInvisibleFlag(), getErrorMessage("message198"));
		ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getReturnDays(), getErrorMessage("message199"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getProductReadyDays(), getErrorMessage("message200"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPickupDays(), getErrorMessage("message201"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPickupTimeslot(), getErrorMessage("message202"));

		if (ConstantType.CONSTANT_YES.equals(hktvProductFieldDto.getPreSellFruit())) {
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPhysicalStore(), getErrorMessage("message229"));
		}

	}

	private void checkGoodsTypeAndWarrantyPeriodRelativeFields(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		if (StringUtil.isNotEmpty(hktvProductFieldDto.getGoodsType())) {
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getWarrantyPeriodUnit(), getErrorMessage("message213"));
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getWarrantyPeriod(), getErrorMessage("message214"));
			if (hktvProductFieldDto.getWarrantyPeriod() != null && hktvProductFieldDto.getWarrantyPeriod() != 0) {
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getWarrantySupplierCh(), getErrorMessage("message307", "Warranty supplier(CH)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getWarrantySupplierEn(), getErrorMessage("message307", "Warranty supplier(EN)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getWarrantySupplierSc(), getErrorMessage("message307", "Warranty supplier(SC)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getServiceCentreAddressEn(), getErrorMessage("message307", "Service Centre Address(Eng)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getServiceCentreAddressCh(), getErrorMessage("message307", "Service Centre Address(Chi)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getServiceCentreAddressSc(), getErrorMessage("message307", "Service Centre Address(SC)"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getServiceCentreEmail(), getErrorMessage("message307", "Service Centre Email"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getServiceCentreContact(), getErrorMessage("message307", "Service Centre Contact"));
			}
		}
	}

	private void checkEwSku(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		if (CategoryConfig.EW_SKU_CATEGORY.equals(hktvProductFieldDto.getPrimaryCategoryCode())) {
			ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getEwPercentageSetting(), getErrorMessage("message307", "EW Percentage Setting"));
		}
	}

	public void checkExternalPlatform(Collection<String> errors, ExternalPlatform externalPlatform) {
		if (externalPlatform == null) {
			return;
		}

		boolean hasSource = CollectionUtils.isNotEmpty(externalPlatform.getSource());
		boolean hasProductId = StringUtils.isNotEmpty(externalPlatform.getProductId());
		boolean hasSkuId = StringUtils.isNotEmpty(externalPlatform.getSkuId());

		// success 條件一：全部沒填
		if (!hasSource && !hasProductId && !hasSkuId) {
			return;
		}

		// success 條件二：source & product id 有填
		if (hasSource && hasProductId) {
			return;
		}

		errors.add(getErrorMessage("message328"));
	}

	private void checkPackingDimension(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPackingBoxType(), getErrorMessage("message142"));
		if ((hktvProductFieldDto.getCartonDepth() != null || hktvProductFieldDto.getCartonLength() != null || hktvProductFieldDto.getCartonHeight() != null) &&
			(hktvProductFieldDto.getCartonDepth() == null || hktvProductFieldDto.getCartonLength() == null || hktvProductFieldDto.getCartonHeight() == null)) {
			ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getCartonHeight(), getErrorMessage("message218"));
			ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getCartonLength(), getErrorMessage("message219"));
			ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getCartonDepth(), getErrorMessage("message220"));
		}
	}

	private void checkRemovalService(Set<String> errors, List<String> categoryForRemovalServiceList, HktvProductFieldDto hktvProductFieldDto) {
		if (CollectionUtil.isEmpty(hktvProductFieldDto.getProductTypeCode())) {
			return;
		}

		for (String productTypeCode : hktvProductFieldDto.getProductTypeCode()) {
			for (String categoryForRemovalService : categoryForRemovalServiceList) {
				if (productTypeCode.startsWith(categoryForRemovalService)) {
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getNeedRemovalServices(), getErrorMessage("message216"));
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getGoodsType(), getErrorMessage("message217"));
				}
			}
		}
	}

	private void checkProductReadyMethod(Set<String> errors, Map<String, Boolean> storeIsBuysellMap, HktvProductFieldDto hktvProductFieldDto) {
		if (StringUtil.isEmpty(hktvProductFieldDto.getProductReadyMethod())) {
			return;
		}

		switch (hktvProductFieldDto.getProductReadyMethod()) {
			case ProductReadyMethodType.OVERSEA_DELIVERY:
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getCost(), getErrorMessage("message230"));
				break;
			case ProductReadyMethodType.DISPLAY_STORE:
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getAffiliateUrl(), getErrorMessage("message215"));
				break;
			case ProductReadyMethodType.CONSIGNMENT:
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getStorageType(), getErrorMessage("message38"));
				if (storeIsBuysellMap.get(hktvProductFieldDto.getStorefrontStoreCode())) {
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getRmCode(), getErrorMessage("message37"));
				}
				break;
			case ProductReadyMethodType.E_VOUCHER:
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getUserMax(), getErrorMessage("message203"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getFeatureStartDate(), getErrorMessage("message204"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getFeatureEndDate(), getErrorMessage("message205"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getVoucherType(), getErrorMessage("message206"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getVoucherDisplayType(), getErrorMessage("message207"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getVoucherTemplateType(), getErrorMessage("message208"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getExpiryType(), getErrorMessage("message209"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getRedeemStartDate(), getErrorMessage("message210"));
				if (hktvProductFieldDto.getExpiryType() != null) {
					if (VoucherType.FIXED.name().equalsIgnoreCase(hktvProductFieldDto.getExpiryType())) {
						ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getFixedRedemptionDate(), getErrorMessage("message211"));
					} else {
						ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getUponPurchaseDate(), getErrorMessage("message212"));
					}
				}
				break;
			case ProductReadyMethodType.THIRD_PARTY:
			case ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB:
			case ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY:
			case ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE:
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getPackingHeight(), getErrorMessage("message84"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getPackingLength(), getErrorMessage("message86"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getPackingDepth(), getErrorMessage("message85"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getPackingDimensionUnit(), getErrorMessage("message143"));
				ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getWeight(), getErrorMessage("message92"));
				ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getWeightUnit(), getErrorMessage("message144"));
				if (ProductReadyMethodType.THIRD_PARTY.equals(hktvProductFieldDto.getProductReadyMethod())) {
					ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getBarcode(), getErrorMessage("message139"));
				}
				break;
		}
	}

	private void checkHktvField(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getBrandId(), getErrorMessage("message8"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getWarehouse(), getErrorMessage("message192"));
	}

	private void checkOapiField(Set<String> errors, HktvProductFieldDto hktvProductFieldDto) {
		ValidationCheckUtil.checkObjectNonNull(errors, hktvProductFieldDto.getBrandCode(), getErrorMessage("message8"));
		ValidationCheckUtil.checkNullOrEmpty(errors, hktvProductFieldDto.getMainPhoto(), getErrorMessage("message307", "Main photo"));
	}

	private void checkAllColumnField(Set<String> errors, HktvProductFieldDto hktvProductFieldDto, List<String> categoryForRemovalServiceList, Map<String, Boolean> storeIsBuysellMap) {
		checkGlobalFields(errors, hktvProductFieldDto);
		checkGoodsTypeAndWarrantyPeriodRelativeFields(errors, hktvProductFieldDto);
		checkPackingDimension(errors, hktvProductFieldDto);
		checkRemovalService(errors, categoryForRemovalServiceList, hktvProductFieldDto);
		checkProductReadyMethod(errors, storeIsBuysellMap, hktvProductFieldDto);
		checkEwSku(errors, hktvProductFieldDto);
		checkExternalPlatform(errors, hktvProductFieldDto.getExternalPlatform());
	}

	private void checkVariantProductFields(Set<String> errors, HktvProductFieldDto hktvProductFieldDto, Map<String, Map<String, DataChecker>> variantSkuCheckMap) {
		if (hktvProductFieldDto.getProductId() != null && hktvProductFieldDto.getStorefrontStoreCode() != null) {
			String productAndStore = hktvProductFieldDto.getProductId() + hktvProductFieldDto.getStorefrontStoreCode();
			if (variantSkuCheckMap.containsKey(productAndStore)) {
				List<String> differentVariantFields = variantSkuCheckMap.get(productAndStore).entrySet().stream()
					.filter(data -> data.getValue().isDifferent())
					.map(Map.Entry::getKey)
					.collect(Collectors.toList());
				if (!differentVariantFields.isEmpty()) {
					errors.add(getErrorMessage("message339", String.join(",", differentVariantFields)));
				}
			}
		}
	}

	private DataWrapper generateCheckingProductMap(List<HktvProductFieldDto> products) {

		Map<Map.Entry<String, String>, Long> skuIdCountMap = new HashMap<>();
		Map<String, Map<String, DataChecker>> variantSkuCheckMap = new HashMap<>();
		products.stream()
			.filter(data -> data.getSkuId() != null)
			.forEach(data -> {
				skuIdCountMap.merge(Map.entry(data.getStorefrontStoreCode(), data.getSkuId()), 1L, Long::sum);
				if (data.getProductId() != null && data.getStorefrontStoreCode() != null) {
					String productAndStore = data.getProductId() + data.getStorefrontStoreCode();
					if (variantSkuCheckMap.containsKey(productAndStore)) {
						Map<String, DataChecker> checkMap = variantSkuCheckMap.get(productAndStore);
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.BRAND_ID.getColumnName()).getValue(), data.getBrandId() == null ? null : data.getBrandId().toString())) {
							checkMap.get(HktvUploadProductExcelReportEnum.BRAND_ID.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.VOUCHER_TYPE.getColumnName()).getValue(), data.getVoucherType())) {
							checkMap.get(HktvUploadProductExcelReportEnum.VOUCHER_TYPE.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.VOUCHER_DISPLAY_TYPE.getColumnName()).getValue(), data.getVoucherDisplayType())) {
							checkMap.get(HktvUploadProductExcelReportEnum.VOUCHER_DISPLAY_TYPE.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.FEATURE_START_TIME.getColumnName()).getValue(), data.getFeatureStartDate())) {
							checkMap.get(HktvUploadProductExcelReportEnum.FEATURE_START_TIME.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.FEATURE_END_TIME.getColumnName()).getValue(), data.getFeatureEndDate())) {
							checkMap.get(HktvUploadProductExcelReportEnum.FEATURE_END_TIME.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_EN.getColumnName()).getValue(), data.getInvoiceRemarkEn())) {
							checkMap.get(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_EN.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_CH.getColumnName()).getValue(), data.getInvoiceRemarkCh())) {
							checkMap.get(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_CH.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.INVISIBLE_FLAG.getColumnName()).getValue(), data.getInvisibleFlag())) {
							checkMap.get(HktvUploadProductExcelReportEnum.INVISIBLE_FLAG.getColumnName()).setDifferent(true);
						}
						if (!Objects.equals(checkMap.get(HktvUploadProductExcelReportEnum.URGENT.getColumnName()).getValue(), data.getUrgent())) {
							checkMap.get(HktvUploadProductExcelReportEnum.URGENT.getColumnName()).setDifferent(true);
						}
					} else {
						variantSkuCheckMap.put(productAndStore, generateVariantFieldMap(data));
					}
				}
			});

		return DataWrapper.builder()
			.skuIdCountMap(skuIdCountMap)
			.variantSkuCheckMap(variantSkuCheckMap)
			.build();
	}

	Map<String, DataChecker> generateVariantFieldMap(HktvProductFieldDto productFieldDto) {
		Map<String, DataChecker> map = new HashMap<>();
		map.put(HktvUploadProductExcelReportEnum.BRAND_ID.getColumnName(), new DataChecker(false, productFieldDto.getBrandId() == null ? null : productFieldDto.getBrandId().toString()));
		map.put(HktvUploadProductExcelReportEnum.VOUCHER_TYPE.getColumnName(), new DataChecker(false, productFieldDto.getVoucherType()));
		map.put(HktvUploadProductExcelReportEnum.VOUCHER_DISPLAY_TYPE.getColumnName(), new DataChecker(false, productFieldDto.getVoucherDisplayType()));
		map.put(HktvUploadProductExcelReportEnum.FEATURE_START_TIME.getColumnName(), new DataChecker(false, productFieldDto.getFeatureStartDate()));
		map.put(HktvUploadProductExcelReportEnum.FEATURE_END_TIME.getColumnName(), new DataChecker(false, productFieldDto.getFeatureEndDate()));
		map.put(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_EN.getColumnName(), new DataChecker(false, productFieldDto.getInvoiceRemarkEn()));
		map.put(HktvUploadProductExcelReportEnum.INVOICE_REMARKS_CH.getColumnName(), new DataChecker(false, productFieldDto.getInvoiceRemarkCh()));
		map.put(HktvUploadProductExcelReportEnum.INVISIBLE_FLAG.getColumnName(), new DataChecker(false, productFieldDto.getInvisibleFlag()));
		map.put(HktvUploadProductExcelReportEnum.URGENT.getColumnName(), new DataChecker(false, productFieldDto.getUrgent()));
		return map;
	}

	@Data
	@Builder
	private static class DataWrapper {
		Map<Map.Entry<String, String>, Long> skuIdCountMap;
		Map<String, Map<String, DataChecker>> variantSkuCheckMap;
	}

	@Data
	@AllArgsConstructor
	private static class DataChecker {
		boolean isDifferent;
		String value;
	}

}
