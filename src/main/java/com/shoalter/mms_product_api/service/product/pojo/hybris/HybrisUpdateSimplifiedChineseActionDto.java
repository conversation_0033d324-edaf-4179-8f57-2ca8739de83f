package com.shoalter.mms_product_api.service.product.pojo.hybris;

import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DTO for Hybris Update Simplified Chinese Action
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HybrisUpdateSimplifiedChineseActionDto extends SaveHybrisProductRequestDto {

	@SerializedName("merchantId")
	private String merchantId;

	@SerializedName("skuCode")
	private String skuCode;

	@SerializedName("action")
	private String action;

	@SerializedName("skuNameZhCN")
	private String skuNameZhCN;

	@SerializedName("skuSDescHktvZhCN")
	private String skuSDescHktvZhCN;

	@SerializedName("skuLDescHktvZhCN")
	private String skuLDescHktvZhCN;

	@SerializedName("invoiceRemarksZhCN")
	private String invoiceRemarksZhCN;

	@SerializedName("videoLinkZhCN")
	private String videoLinkZhCN;

	@SerializedName("manuCountryZhCN")
	private String manuCountryZhCN;

	@SerializedName("packSpecZhCN")
	private String packSpecZhCN;

	@SerializedName("finePrintZhCN")
	private String finePrintZhCN;

	@SerializedName("warrantySupplierZhCN")
	private String warrantySupplierZhCN;

	@SerializedName("serviceCentreAddressZhCN")
	private String serviceCentreAddressZhCN;

	@SerializedName("warrantyRemarkZhCN")
	private String warrantyRemarkZhCN;

	@SerializedName("claimLinkZhCN")
	private String claimLinkZhCN;

	@SerializedName("videoLinkList")
	private String videoLinkList;
}
