package com.shoalter.mms_product_api.service.product.pojo.littlemall;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SearchProductIdsRequestData {

	@JsonProperty("hktv_storefront_store_code")
	@SerializedName("hktv_storefront_store_code")
	private String hktvStorefrontStoreCode;

	@JsonProperty("little_mall_storefront_store_code")
	@SerializedName("little_mall_storefront_store_code")
	private String littleMallStorefrontStoreCode;

	public static SearchProductIdsRequestData generateLittleMallStore(String littleMallStorefrontStoreCode) {
		return SearchProductIdsRequestData.builder()
			.littleMallStorefrontStoreCode(littleMallStorefrontStoreCode)
			.build();
	}

	public static SearchProductIdsRequestData generateHktvStore(String hktvStorefrontStoreCode) {
		return SearchProductIdsRequestData.builder()
			.hktvStorefrontStoreCode(hktvStorefrontStoreCode)
			.build();
	}
}
