package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class ViewBuHistoryDto {
	private Integer id;
	private String buCode;
	private String exportType;
	private String status;
	private Date createTime;
	private String createBy;
	private Integer productCounts;
	private boolean isLatestData;
	private boolean isExpiredData;
	private List<String> failReasons;
	private String informProductToSyncProgress;
}
