package com.shoalter.mms_product_api.service.product.pojo.onebound;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OneBoundDetailResponseDataDto {
	private String productCode;
	private String detailUrl;
	private BigDecimal price;
	private BigDecimal originalPrice;
	private List<OneBoundDetailSkuDto> skus;
}
