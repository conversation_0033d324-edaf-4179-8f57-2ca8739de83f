package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@Builder
public class LittleMallProductSearchRequestDto implements Serializable {
	private static final long serialVersionUID = -1946312624894306968L;

	private Integer page;
	private Integer size;
	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;
	@JsonProperty("store_code")
	@SerializedName("store_code")
	private String storeCode;
	private List<String> category;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private List<String> skuIds;
	@JsonProperty("product_name")
	@SerializedName("product_name")
	private String productName;
	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;
	private List<String> orderBy;
	@JsonProperty("query_type")
	@SerializedName("query_type")
	private String queryType;
}
