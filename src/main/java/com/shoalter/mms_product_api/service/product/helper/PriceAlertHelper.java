package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductPriceAlertStatus;
import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreAffiliateDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceAlertDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceAlertRepository;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PriceAlertHelper {
    private final StoreRepository storeRepository;
    private final ProductPriceAlertRepository productPriceAlertRepository;
	private final ContractRepository contractRepository;
	private final Gson gson;

    /**
     * Creates a price alert for a product when it's first created
     * @param rowContent JSON string containing the product data (SingleEditProductDto)
     * @param userDto The user information who is creating the product
     */
	public void createProductPriceAlert(String rowContent, UserDto userDto) {
		process(userDto, null, "CREATE", rowContent);
	}

    /**
     * Creates a price alert for a product when its price is modified
     * @param rowContent JSON string containing the updated product data (SingleEditProductDto)
     * @param beforeProduct The original product data before the update
     * @param userDto The user information who is updating the product
     */
	public void updateProductPriceAlert(String rowContent,ProductMasterResultDto beforeProduct, UserDto userDto) {
		process(userDto, beforeProduct, "UPDATE", rowContent);
	}

	/**
	 * This method deserializes the rowContent into a SingleEditProductDto object and retrieves the product details.
	 * If the product's additional information or store information is null, it logs a message and skips the price alert check.
	 * Otherwise, it proceeds to perform the price alert check.
	 */
	private void process(UserDto userDto, ProductMasterResultDto beforeProductFromPM, String action, String rowContent) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(rowContent, SingleEditProductDto.class);

		ProductMasterDto productMasterDto = singleEditProductDto.getProduct();
		if (singleEditProductDto.getProduct() == null || productMasterDto.getAdditional().getHktv() == null) {
			return;
		} else if (productMasterDto.getAdditional().getHktv().getStores() == null) {
			String skuId = singleEditProductDto.getProduct().getSkuId();
			String productId = singleEditProductDto.getProduct().getProductId();
			log.info("price alert check skip, product code: {}, skuId: {}, store code is null, action: {}", productId, skuId, action);
			return;
		}

		productPriceAlertCheck(singleEditProductDto, beforeProductFromPM, action, userDto);
	}

	private void productPriceAlertCheck(SingleEditProductDto singleEditProductDto, ProductMasterResultDto originDto, String action, UserDto userDto) {
		try {
			String storeCode = singleEditProductDto.getProduct().getAdditional().getHktv().getStores();
			// check if the store is affiliate store
			List<StoreAffiliateDo> storeAffiliateInfos = storeRepository.findStoreAffiliateByPlatformCodeAndStoreCode(ThirdPartySourceEnum.TMALL.getValue(), storeCode, BuCodeEnum.HKTV.name());
			if (CollectionUtils.isNotEmpty(storeAffiliateInfos)) {
				StoreAffiliateDo storeAffiliateDo = storeAffiliateInfos.get(0);
				log.info("store affiliate store found, store code: {}, store id: {}", storeCode, storeAffiliateDo.getStoreId());
				switch (action) {
					// when CREATE, productDo is null, only check if the store is affiliate store
					case "CREATE":
						if (checkProductCurrency(singleEditProductDto.getProduct().getAdditional().getHktv(), CurrencyEnum.RMB.name())
							&& isMainlandContract(singleEditProductDto.getProduct().getAdditional().getHktv())) {
							triggerProductPriceAlert(singleEditProductDto, userDto, storeAffiliateDo);
						}
						break;
					// when UPDATE, check if the product price is increased and the store is affiliate store
					// special case: if the selling price is canceled, also trigger the product price alert
					case "UPDATE":
						if (checkProductCurrency(originDto.getAdditional().getHktv(), CurrencyEnum.RMB.name())
							&& (isProductPriceIncreased(singleEditProductDto, originDto) || isCancelSellingPrice(singleEditProductDto, originDto))
							&& isMainlandContract(originDto.getAdditional().getHktv())) {
							triggerProductPriceAlert(singleEditProductDto, userDto, storeAffiliateDo);
						}
						break;
					default:
						break;
				}
			}
		} catch (Exception e) {
			// catch exception to avoid affecting the main create/update product process
			log.error("product price alert check error, productMasterMqDto: {}, action: {}, error: {}", singleEditProductDto, action, e.getMessage());
		}
	}

	private boolean checkProductCurrency(HktvProductDto hktvProductDto, String currencyCode) {
		String currency = hktvProductDto.getCurrency();
		if (currency == null) return false;
		return currency.equalsIgnoreCase(currencyCode);
	}

	private boolean isMainlandContract(HktvProductDto hktvProductDto) {
		Integer contractId = hktvProductDto.getContractNo();
		if (contractId == null) return false;

		String contractType = contractRepository.findMainContractTypeInContract(contractId);
		return ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType);
	}

	private void triggerProductPriceAlert(SingleEditProductDto singleEditProductDto, UserDto userDto, StoreAffiliateDo storeAffiliateDo) {
		String storeCode = singleEditProductDto.getProduct().getAdditional().getHktv().getStores();
		String skuId = singleEditProductDto.getProduct().getSkuId();
		String productId = singleEditProductDto.getProduct().getProductId();
		log.info("add product price alert check, product code: {}, skuId: {}, store code: {}", productId, skuId, storeCode);

		String storeSkuId = storeAffiliateDo.getStorefrontStoreCode() + StringUtil.PRODUCT_SEPARATOR + skuId;
		String sourcePlatform = BuCodeEnum.HKTV.name();
		String targetPlatform = ThirdPartySourceEnum.TMALL.getValue();

		// Check for existing pending alert
		boolean existingAlert = productPriceAlertRepository.existsByStoreSkuIdAndBusUnitIdAndSourcePlatformAndTargetPlatformAndStatus(
			storeSkuId,
			storeAffiliateDo.getBusUnitId(),
			sourcePlatform,
			targetPlatform,
			ProductPriceAlertStatus.PENDING.getStatusCode()
		);

		if (existingAlert) {
			log.info("Skipping price alert creation - existing pending alert found for busUnitId: {}, storeSkuId: {}, sourcePlatform: {}, targetPlatform: {}",
				storeAffiliateDo.getBusUnitId(), storeSkuId, sourcePlatform, targetPlatform);
			return;
		}

		ProductPriceAlertDo productPriceAlertDo = ProductPriceAlertDo.builder()
			.busUnitId(storeAffiliateDo.getBusUnitId())
			.status(ProductPriceAlertStatus.PENDING.getStatusCode())
			.merchantId(singleEditProductDto.getProduct().getMerchantId())
			.storeId(storeAffiliateDo.getStoreId())
			.productCode(productId)
			.storeSkuId(storeSkuId)
			.skuCode(skuId)
			.skuNameTchi(singleEditProductDto.getProduct().getSkuNameCh())
			.sourcePlatform(sourcePlatform)
			.sourceCurrencyCode(CurrencyEnum.RMB.name())
			.targetPlatform(targetPlatform)
			.targetCurrencyCode(CurrencyEnum.RMB.name())
			.createdBy(userDto.getUserCode())
			.createdDate(LocalDateTime.now())
			.lastUpdatedBy(userDto.getUserCode())
			.lastUpdatedDate(LocalDateTime.now())
			.build();
		productPriceAlertRepository.save(productPriceAlertDo);
	}

    boolean isCancelSellingPrice(SingleEditProductDto singleEditProductDto, ProductMasterResultDto originalProductDo) {
        BigDecimal updateSellingPrice = singleEditProductDto.getProduct().getAdditional().getHktv().getSellingPrice();
        BigDecimal beforeSellingPrice = originalProductDo.getAdditional().getHktv().getSellingPrice() == null ? BigDecimal.ZERO : originalProductDo.getAdditional().getHktv().getSellingPrice();

        if (updateSellingPrice == null) return false;

        // if before selling price is greater than 0 and update selling price is 0, which means the selling price is canceled
        return beforeSellingPrice.compareTo(BigDecimal.ZERO) > 0 && updateSellingPrice.compareTo(BigDecimal.ZERO) == 0;
    }

    boolean isProductPriceIncreased(SingleEditProductDto singleEditProductDto, ProductMasterResultDto originalProductDo) {
        // if selling price exists, compare the selling price, else compare the original price
        BigDecimal updateSellingPrice = singleEditProductDto.getProduct().getAdditional().getHktv().getSellingPrice();
        BigDecimal updateOriginalPrice = singleEditProductDto.getProduct().getOriginalPrice();

        if (updateSellingPrice != null && updateSellingPrice.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal beforeSellingPrice = originalProductDo.getAdditional().getHktv().getSellingPrice() == null ? BigDecimal.ZERO : originalProductDo.getAdditional().getHktv().getSellingPrice();
            return updateSellingPrice.compareTo(beforeSellingPrice) > 0;
        } else if (updateOriginalPrice != null) {
            BigDecimal beforeOriginalPrice = originalProductDo.getOriginalPrice() == null ? BigDecimal.ZERO : originalProductDo.getOriginalPrice();
            return updateOriginalPrice.compareTo(beforeOriginalPrice) > 0;
        } else {
            return false;
        }
    }
}
