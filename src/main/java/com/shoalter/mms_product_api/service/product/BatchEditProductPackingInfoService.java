package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductPackingInfoRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.EditProductPackingInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SkuMapUuidDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BatchEditProductPackingInfoService {

	private final PermissionHelper permissionHelper;
	private final ProductMapper productMapper;
	private final BatchCheckHelper batchCheckHelper;

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	private final Gson gson;

	@Transactional
	public ResponseDto<Long> start(UserDto userDto, BatchEditProductPackingInfoRequestDto batchEditProductPackingInfoRequestDto, String clientIp) {
		checkRequest(userDto, batchEditProductPackingInfoRequestDto);

		ResponseDto<Long> checkCountResult = batchCheckHelper.checkCount(userDto, SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO
				, batchEditProductPackingInfoRequestDto, batchEditProductPackingInfoRequestDto.getProductTemplate(), BatchCheckHelper.MAXIMUM_3000);
		if (checkCountResult.getStatus() == -1) {
			return ResponseDto.<Long>builder().data(checkCountResult.getData()).status(1).build();
		}

		List<String> skuCodeList = batchEditProductPackingInfoRequestDto.getProductTemplate().stream().map(EditProductPackingInfoDto::getSkuCode).collect(Collectors.toList());
		List<SkuMapUuidDto> skuAndUuid = productMapper.getUuidByStoreIdAndSkuList(batchEditProductPackingInfoRequestDto.getStoreId(), skuCodeList);
		Map<String, String> skuMapUuid = skuAndUuid.stream().collect(Collectors.toMap(SkuMapUuidDto::getSkuCode, SkuMapUuidDto::getUuid));

		SaveProductRecordDo saveProductRecordDo = saveRecord(userDto, batchEditProductPackingInfoRequestDto, clientIp);
		for (EditProductPackingInfoDto info : batchEditProductPackingInfoRequestDto.getProductTemplate()) {
			saveRecordRow(saveProductRecordDo.getId(), skuMapUuid.get(info.getSkuCode()), info);
		}
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), batchEditProductPackingInfoRequestDto.getProductTemplate().size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.<Long>builder().data(saveProductRecordDo.getId()).status(1).build();
	}

	private void checkRequest(UserDto userDto, BatchEditProductPackingInfoRequestDto batchEditProductPackingInfoRequestDto) {
		permissionHelper.checkPermission(userDto, batchEditProductPackingInfoRequestDto.getMerchantId());
		checkUserRole(userDto);
	}

	private SaveProductRecordDo saveRecord(UserDto userDto, BatchEditProductPackingInfoRequestDto batchEditProductPackingInfoRequestDto, String clientIp) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setMerchantId(batchEditProductPackingInfoRequestDto.getMerchantId());
		saveProductRecordDo.setUploadType(SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO);
		saveProductRecordDo.setFileName(batchEditProductPackingInfoRequestDto.getFileName());
		saveProductRecordDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setUserIp(clientIp);
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	private void saveRecordRow(Long recordId, String uuid, EditProductPackingInfoDto info) {
		SingleEditProductDto singleEditProductDto = convertSingleEditProductDto(uuid, info);

		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordRowDo.setSku(info.getSkuCode());
		saveProductRecordRowDo.setUuid(uuid);
		saveProductRecordRowDo.setContent(gson.toJson(singleEditProductDto));
		saveProductRecordRowRepository.save(saveProductRecordRowDo);
	}

	private SingleEditProductDto convertSingleEditProductDto(String uuid, EditProductPackingInfoDto info) {
		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		ProductMasterDto product = new ProductMasterDto();
		BuProductDto additional = new BuProductDto();
		HktvProductDto hktvProductDto = new HktvProductDto();

		product.setUuid(uuid);
		product.setSkuId(info.getSkuCode());
		product.setPackingBoxType(info.getPackingBoxType());
		product.setPackingDimensionUnit(info.getPackingDimensionUnit());
		product.setWeightUnit(info.getWeightUnit());
		product.setWeight(info.getWeight());
		product.setPackingHeight(info.getPackingHeight());
		product.setPackingLength(info.getPackingLength());
		product.setPackingDepth(info.getPackingDepth());
		product.setCartonSizeList(generateCartonSizeDto(info));
		hktvProductDto.setPackingSpecEn(info.getPackingSpecEn());
		hktvProductDto.setPackingSpecCh(info.getPackingSpecCh());
		hktvProductDto.setPackingSpecSc(info.getPackingSpecSc());

		additional.setHktv(hktvProductDto);
		product.setAdditional(additional);
		singleEditProductDto.setProduct(product);

		return singleEditProductDto;
	}

	private List<CartonSizeDto> generateCartonSizeDto(EditProductPackingInfoDto info) {
		CartonSizeDto cartonSizeDto = null;

		if(info.getCartonHeight() != null || info.getCartonDepth() != null || info.getCartonLength() != null){
			cartonSizeDto = new CartonSizeDto();
			cartonSizeDto.setHeight(info.getCartonHeight());
			cartonSizeDto.setWidth(info.getCartonDepth());
			cartonSizeDto.setLength(info.getCartonLength());
		}

		return cartonSizeDto == null ? null : List.of(cartonSizeDto);
	}

	private void checkUserRole(UserDto userDto) {
		if (RoleCode.ALLOW_PRODUCT_PACKING_INFO_ROLES.contains(userDto.getRoleCode())) {
			return;
		}
		throw new SystemI18nException("message28", userDto.getRoleCode());
	}
}
