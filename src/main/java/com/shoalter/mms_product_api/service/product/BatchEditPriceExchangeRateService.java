package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.asynctask.CreateSaveProductRecordTask;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BatchEditPriceExchangeRateService {

	public static final String BATCH_EDIT_PRICE_EXCHANGE_RATE_FILE_NAME = "SynchronizeRMBPrice_%s_%s.xlsx";
	public static final String BATCH_EDIT_PRICE_EXCHANGE_RATE_FILE_NAME_INDEX = "SynchronizeRMBPrice_%s_%s_%s.xlsx";

	private final CreateSaveProductRecordTask createSaveProductRecordTask;
	private final ExchangeRateHelper exchangeRateHelper;
	private final MessageSource messageSource;

	public ResponseDto<Void> batchEditCurrencyRmbSkuPrice(UserDto userDto) {
		checkPermission(userDto);
		BigDecimal exchangeRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);
		if (exchangeRate == null) {
			log.error("[batchEditCurrencyRmbSkuPrice] Error, RMB exchangeRate is null");
			return ResponseDto.fail(List.of(messageSource.getMessage("message360", null, null)));
		}

		log.info("[batchEditCurrencyRmbSkuPrice] RMB exchangeRate: {}", exchangeRate);
		createSaveProductRecordTask.createBatchCurrencyRmbSkuPriceRecord(userDto);

		return ResponseDto.success(null);
	}

	private void checkPermission(UserDto userDto) {
		if (!RoleCode.ALLOW_BATCH_EDIT_PRICE_EXCHANGE_RATE_ROLES.contains(userDto.getRoleCode())) {
			log.error("User does not have permission to access this merchant, user id : {}, merchant id : {}", userDto.getUserId(), userDto.getMerchantId());
			throw new SystemI18nException("message28", userDto.getRoleCode());
		}
	}
}
