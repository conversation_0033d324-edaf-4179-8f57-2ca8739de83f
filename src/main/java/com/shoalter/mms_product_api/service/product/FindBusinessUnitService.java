package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.business.BusUnitRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BusUnitDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class FindBusinessUnitService {

    private final BusUnitRepository busUnitRepository;

    public ResponseDto<List<BusUnitDo>> start() {
        List<BusUnitDo> busUnitDoList = busUnitRepository.findAll();
        return ResponseDto.<List<BusUnitDo>>builder().data(busUnitDoList).status(1).build();
    }
}
