package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.mapper.ProductResponseDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.MerchantHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.StoreApiHelper;
import com.shoalter.mms_product_api.service.product.pojo.FindProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterIIDSBuDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterIIDSBuWarehouseDetailDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.request.StoreApiFindStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreApiFindStoreResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.GetProductOverviewResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.type.ApplicableServiceType.STANDARD_DELIVERY;
import static com.shoalter.mms_product_api.config.type.ApplicableServiceType.THIRD_PL;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.THIRD_PARTY;

@Service
@RequiredArgsConstructor
@Slf4j
public class GetProductOverviewService {

    private final PermissionHelper permissionHelper;
    private final MerchantHelper merchantHelper;
    private final StoreApiHelper storeApiHelper;
    private final StoreRepository storeRepository;
    private final ProductMasterHelper productMasterHelper;
    private final MerchantRepository merchantRepository;
    private final ProductImageHelper productImageHelper;
    private final SysParmRepository sysParmRepository;

    public ResponseDto<GetProductOverviewResponseDto> start(UserDto userDto, FindProductRequestDto request) {

        ProductSearchRequestDto productSearchRequestDto = genProductSearchRequestDto(request);

        if (CollectionUtil.isNotEmpty(productSearchRequestDto.getMerchantId())) {
            for (Integer merchantId : productSearchRequestDto.getMerchantId()) {
                permissionHelper.checkPermission(userDto, merchantId);
            }
        } else {
            productSearchRequestDto.setMerchantId(merchantHelper.findMerchantIdByRole(userDto));
        }

        merchantHelper.convertMerchantListByMerchantName(productSearchRequestDto, request.getMerchantName());

        if (CollectionUtil.isEmpty(productSearchRequestDto.getMerchantId()) || CollectionUtil.isEmpty(productSearchRequestDto.getBuCode())) {
            return ResponseDto.<GetProductOverviewResponseDto>builder()
                .data(genEmptyResponse(productSearchRequestDto.getSize()))
                .status(StatusCodeEnum.SUCCESS.getCode())
                .build();
        }

        List<String> storefrontStoreCodes = genStorefrontStoreCodesByBuCode(userDto, productSearchRequestDto, request.getStoreId());

        if (CollectionUtil.isEmpty(storefrontStoreCodes)) {
            return ResponseDto.<GetProductOverviewResponseDto>builder()
                .data(genEmptyResponse(productSearchRequestDto.getSize()))
                .status(StatusCodeEnum.SUCCESS.getCode())
                .build();
        }

        productSearchRequestDto.setStorefrontStoreCodes(storefrontStoreCodes);

        ProductOverviewResultDto result = getProductFromProductMaster(userDto, productSearchRequestDto);
        GetProductOverviewResponseDto products = genGetProductOverviewResponse(result);

        Map<String, List<String>> readyMethodWarehouseSeqNoMap = generateReadyMethodWarehouseSeqNo();
        generateApplicableServiceResult(products, readyMethodWarehouseSeqNoMap);

        return ResponseDto.<GetProductOverviewResponseDto>builder().data(products).status(StatusCodeEnum.SUCCESS.getCode()).build();
    }

    private GetProductOverviewResponseDto genGetProductOverviewResponse(ProductOverviewResultDto productOverviewResult) {

        GetProductOverviewResponseDto response = GetProductOverviewResponseDto.convertFromProductOverviewResultDto(productOverviewResult);

        if (CollectionUtil.isEmpty(productOverviewResult.getContent())) {
            return response;
        }

        List<Integer> merchantIds = productOverviewResult.getContent().stream().map(ProductMasterResultDto::getMerchantId).collect(Collectors.toList());
        Set<String> storeCodeSet = productOverviewResult.getContent().stream()
            .filter(productMasterResultDto -> productMasterResultDto.getAdditional().getHktv() != null)
            .map(productMasterResultDto -> productMasterResultDto.getAdditional().getHktv().getStores())
            .collect(Collectors.toSet());

        Map<Integer, String> merchantNameByIdMap = merchantRepository.findByIdIn(merchantIds).stream().collect(Collectors.toMap(MerchantDo::getId, MerchantDo::getMerchantName));
        Map<String, Integer> storeCodeMap = fetchStoreCodeIn(storeCodeSet);

        List<ProductResponseDto> productResponseList = productOverviewResult.getContent().stream()
            .map(productMasterResult -> {
                productMasterResult.setMerchantName(merchantNameByIdMap.get(productMasterResult.getMerchantId()));
                if (productMasterResult.getAdditional().getHktv() != null) {
                    HktvProductDto hktvProductDto = productMasterResult.getAdditional().getHktv();
                    hktvProductDto.setStoreId(storeCodeMap.get(hktvProductDto.getStores()));
                    productImageHelper.convertPhotoUrl(hktvProductDto);
                    hktvProductDto.setStorefrontStoreCode(hktvProductDto.getStoreSkuId().split(StringUtil.PRODUCT_SEPARATOR)[0]);
                }
                return ProductResponseDataMapper.INSTANCE.toResponseDto(productMasterResult);
            })
            .collect(Collectors.toList());

        response.setProducts(productResponseList);
        return response;
    }

    private Map<String, Integer> fetchStoreCodeIn(Set<String> storeCodeSet) {
        if (CollectionUtil.isNotEmpty(storeCodeSet)) {
            return storeRepository.findByStoreCodeIn(storeCodeSet)
                .stream().collect(Collectors.toMap(StoreDo::getStoreCode, StoreDo::getId));
        }
        return new HashMap<>();
    }

    private ProductOverviewResultDto getProductFromProductMaster(UserDto userDto, ProductSearchRequestDto request) {
        ProductOverviewResultDto result = productMasterHelper.requestProductsByParams(userDto, request, merchantHelper.getQueryType(userDto).name());
        if (result == null) {
            throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR);
        }
        return result;
    }

    private ProductSearchRequestDto genProductSearchRequestDto(FindProductRequestDto request) {
        return new ProductSearchRequestDto(
            request.getPage(),
            request.getSize(),
            request.getProductName(),
            request.getSkuId(),
            request.getBarcode(),
            request.getProductId(),
            request.getBuCode(),
            request.getProductType(),
            request.getBrand(),
            request.getLastUpdateFrom(),
            request.getLastUpdateTo(),
            request.getOnlineStatus(),
            request.getMerchantId(),
            request.getOrderBy(),
            request.getProductReadyMethod(),
            request.getVisibility(),
            false,
            request.getForceOffline()
        );
    }

    private GetProductOverviewResponseDto genEmptyResponse(Integer size) {
        GetProductOverviewResponseDto response = new GetProductOverviewResponseDto();
        response.setTotalPages(0);
        response.setTotalElements(0);
        response.setNumberOfElements(0);
        response.setSize(size);
        return response;
    }

    private List<String> genStorefrontStoreCodesByBuCode(UserDto userDto, ProductSearchRequestDto request, List<Integer> storeIds) {
        if (CollectionUtil.isEmpty(storeIds)) {
            Set<String> buCodeSet = request.getBuCode().stream().map(String::toUpperCase).collect(Collectors.toSet());
            List<StoreApiFindStoreResponseDto> storeResult = storeApiHelper.requestFindStore(userDto,
                StoreApiFindStoreRequestDto.builder().buCodes(buCodeSet).build());
            storeIds = storeResult.stream().map(StoreApiFindStoreResponseDto::getStoreId).collect(Collectors.toList());
        }
        return storeRepository.findStorefrontStoreCodesByIds(storeIds);
    }

    private Map<String, List<String>> generateReadyMethodWarehouseSeqNo() {

        Map<String, List<String>> readyMethodWarehouseSeqNoMap = new HashMap<>();
        Set<String> standardWarehouseSeqNoSet = generateWarehouseSeqNoSet(List.of(STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE, STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY));
        Set<String> tplWarehouseSeqNoSet = generateWarehouseSeqNoSet(List.of(THIRD_PARTY));

        readyMethodWarehouseSeqNoMap.put(STANDARD_DELIVERY, new ArrayList<>(standardWarehouseSeqNoSet));
        readyMethodWarehouseSeqNoMap.put(THIRD_PL, new ArrayList<>(tplWarehouseSeqNoSet));

        return readyMethodWarehouseSeqNoMap;
    }

    private Set<String> generateWarehouseSeqNoSet(List<String> productReadyMethodCodeList) {

        Set<String> warehouseSeqNoSet = new HashSet<>();
        List<SysParmDo> productReadyMethodWarehouseList =
            sysParmRepository.findBySegmentAndBuCodeAndCodeList("PRODUCT_READY_METHOD_WAREHOUSE", null, productReadyMethodCodeList);

        for (SysParmDo productReadyMethodWarehouse : productReadyMethodWarehouseList) {
            if (StringUtil.isNotEmpty(productReadyMethodWarehouse.getLongDesc())) {
                String[] warehouseSeqNoArray = productReadyMethodWarehouse.getLongDesc().split(",");
                for (String warehouseSeqNo : warehouseSeqNoArray) {
                    warehouseSeqNoSet.add(String.format("%02d", Integer.valueOf(warehouseSeqNo)));
                }
            }
        }

        return warehouseSeqNoSet;
    }

    private void generateApplicableServiceResult(GetProductOverviewResponseDto productOverviewResponse, Map<String, List<String>> readyMethodWarehouseSeqNoMap) {

        List<ProductResponseDto> products = productOverviewResponse.getProducts();

        if (CollectionUtil.isEmpty(products)) {
            return;
        }

        for (ProductResponseDto product : products) {

            ProductMasterIIDSBuDto iidsBuDto = product.getAdditional().getIids();

            if (iidsBuDto != null && CollectionUtil.isNotEmpty(iidsBuDto.getWarehouseDetail())) {

                List<String> warehouseSeqNoList = iidsBuDto.getWarehouseDetail().stream().map(ProductMasterIIDSBuWarehouseDetailDto::getWarehouseSeqNo).collect(Collectors.toList());
                List<String> applicableServiceList = new ArrayList<>();

                if (readyMethodWarehouseSeqNoMap.get(STANDARD_DELIVERY) != null && CollectionUtil.isNotEmpty(warehouseSeqNoList)
                    && !Collections.disjoint(readyMethodWarehouseSeqNoMap.get(STANDARD_DELIVERY), warehouseSeqNoList)) {
                    applicableServiceList.add(STANDARD_DELIVERY);
                }

                if (readyMethodWarehouseSeqNoMap.get(THIRD_PL) != null && CollectionUtil.isNotEmpty(warehouseSeqNoList)
                    && !Collections.disjoint(readyMethodWarehouseSeqNoMap.get(THIRD_PL), warehouseSeqNoList)) {
                    applicableServiceList.add(THIRD_PL);
                }

                product.setApplicableService(applicableServiceList);
            }
        }
    }

}
