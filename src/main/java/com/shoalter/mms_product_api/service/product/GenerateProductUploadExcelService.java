package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.old_flow.helper.ProductOldFlowUploadExcelHelper;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper.SEARCH_MAX_SIZE;

@RequiredArgsConstructor
@Service
@Slf4j
public class GenerateProductUploadExcelService extends AbstractReport implements ITemplateService {

	private final ProductOldFlowUploadExcelHelper productOldFlowUploadExcelHelper;
    private final ProductMasterHelper productMasterHelper;

    private final MessageSource messageSource;

    public HttpEntity<ByteArrayResource> start(UserDto userDto, GenerateProductExcelRequestDto requestDto) {
        checkRequest(requestDto);
		List<SingleEditProductDto> singleEditProductDtoList = null;
		// If uuid list is null or empty is means download create product template
		if(!CollectionUtil.isEmpty(requestDto.getUuidList()) ){
			List<ProductMasterResultDto> productList = checkProductExist(userDto, requestDto.getUuidList());
			singleEditProductDtoList = productList.stream().map(product -> {
				SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
				singleEditProductDto.setProduct(ProductMasterDto.convertFromProductMasterResultDto(product));
				return singleEditProductDto;
			}).collect(Collectors.toList());
		}

        Workbook workbook = productOldFlowUploadExcelHelper.start(userDto, requestDto.getStoreId(), requestDto.getContractId(), requestDto.getUuidList(),
				singleEditProductDtoList, requestDto.getBrandIdList(), false);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(), HttpStatus.OK);
    }

    private void checkRequest(GenerateProductExcelRequestDto requestDto) {
        if (requestDto.getStoreId() == null || requestDto.getContractId() == null) {
            throw new BadRequestException("Missing required parameter");
        }
    }

    private List<ProductMasterResultDto> checkProductExist(UserDto userDto, List<String> uuidList) {
        List<ProductMasterSearchRequestDto> requestList =
                IntStream.range(0, (uuidList.size() + SEARCH_MAX_SIZE - 1) / SEARCH_MAX_SIZE)
                        .mapToObj(i -> {
                            List<String> subList = uuidList.subList(i * SEARCH_MAX_SIZE, Math.min((i + 1) * SEARCH_MAX_SIZE, uuidList.size()));
                            return ProductMasterSearchRequestDto.builder().uuids(subList).build();
                        })
                        .collect(Collectors.toList());

        long startTime = System.currentTimeMillis();
        List<ProductMasterResultDto> productList = requestList.stream()
                .map(request -> {
                    List<ProductMasterResultDto> resultList = productMasterHelper.requestProductsByUuid(userDto, request);
                    if (resultList.isEmpty()) {
                        throw new SystemException(messageSource.getMessage("message51", null, null));
                    }
                    return resultList;
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (productList.size() < uuidList.size()) {
            List<String> notExistUuidList = new ArrayList<>();
            Set<String> resultSet = productList.stream().map(ProductMasterResultDto::getUuid).collect(Collectors.toSet());
            uuidList.forEach(uuid -> {
                if (resultSet.add(uuid)) {
                    notExistUuidList.add(uuid);
                }
            });
            throw new BadRequestException("These UUID do not exist Product Master uuid: " + notExistUuidList);
        }
        long endTime = System.currentTimeMillis();
        log.info("Time taken by get Product data for Product Master {} milliseconds", (endTime - startTime));
        return productList;
    }

    @Override
    public String getFileName() {
        return String.format("product_upload_update_%d.xlsx", System.currentTimeMillis());
    }

    @Override
    public HttpHeaders getResponseHeader() {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(new MediaType("application", "octet-stream"));
        header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + getFileName());
        return header;
    }
}
