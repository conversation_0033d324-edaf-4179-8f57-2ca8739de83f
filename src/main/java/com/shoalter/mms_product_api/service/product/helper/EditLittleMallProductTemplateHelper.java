package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.edit_column.LittleMallFlattenTemplateColumnEnum;
import com.shoalter.mms_product_api.config.product.template.LittleMallProductColumnEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.ProductFieldsOptionsPidDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallExportDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCol;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
@RequiredArgsConstructor
public class EditLittleMallProductTemplateHelper extends AbstractReport {

	private final LittleMallProductTemplateHelper littleMallProductTemplateHelper;
	private final LittleMallRelationHelper littleMallRelationHelper;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final Gson gson;

	public static final String PRODUCT_SHEET_NAME = "Product Information";
	public static final int BODY_START_ROW = 3;

	public Pair<String, ByteArrayOutputStream> generateEditProductFile(UserDto userDto, List<LittleMallExportDto> littleMallExportDtoList, String fileDateTime) {
		if (CollectionUtil.isEmpty(littleMallExportDtoList)) {
			throw new NoDataException();
		}
		ByteArrayOutputStream os = new ByteArrayOutputStream();

		Map<String, List<LittleMallExportDto>> littleMallExportStoreMap = littleMallExportDtoList.stream()
			.collect(Collectors.groupingBy(LittleMallExportDto::getStoreFrontStoreCode));
		if (littleMallExportStoreMap.size() == 1) {
			Map.Entry<String, List<LittleMallExportDto>> littleMallExportDtoListByStore = littleMallExportStoreMap.entrySet().iterator().next();
			if (littleMallExportDtoListByStore.getValue().size() <= ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
				produceEditExcelFile(userDto, littleMallExportDtoListByStore.getKey(), os, littleMallExportDtoListByStore.getValue());
				return Pair.of(StringUtil.FILE_EXTENSION_EXCEL, os);
			} else {
				produceEditZipFile(userDto, os, littleMallExportStoreMap, fileDateTime);
				return Pair.of(StringUtil.FILE_EXTENSION_ZIP, os);
			}
		} else {
			produceEditZipFile(userDto, os, littleMallExportStoreMap, fileDateTime);
			return Pair.of(StringUtil.FILE_EXTENSION_ZIP, os);
		}
	}

	private void produceEditExcelFile(UserDto userDto, String storefrontStoreCode, ByteArrayOutputStream os, List<LittleMallExportDto> littleMallExportDtoList) {
		if (CollectionUtil.isEmpty(littleMallExportDtoList)) {
			return;
		}
		// Using try-with-resources to ensure workbook is closed
		try (XSSFWorkbook workbook = new XSSFWorkbook()) {
			addDefaultStyle(workbook);
			addEditHeaderColumn(workbook);
			// find store productFieldOptions
			List<ProductFieldsOptionsPidDto> productFieldOptions = littleMallRelationHelper.getProductFieldOptionsByStore(userDto, storefrontStoreCode);
			littleMallProductTemplateHelper.addLovWithCustomLovSheet(productFieldOptions, workbook);
			addEditBodyColumn(workbook, littleMallExportDtoList, productFieldOptions);
			workbook.write(os);
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	private void produceEditZipFile(UserDto userDto, ByteArrayOutputStream os, Map<String, List<LittleMallExportDto>> littleMallExportStoreMap, String dateString) {
		byte[] buf = new byte[1024];
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(os)) {
			for (Map.Entry<String, List<LittleMallExportDto>> entry : littleMallExportStoreMap.entrySet()) {
				String storefrontStoreCode = entry.getKey();
				String fileNamePostfix = storefrontStoreCode + StringUtil.UNDERLINE + dateString;
				List<LittleMallExportDto> littleMallExportDtoList = entry.getValue();

				int index = 1;
				for (List<LittleMallExportDto> partitionProductList : ListUtils.partition(littleMallExportDtoList, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
					// if count > 10000, file name : Product_export_all_column_{{storeFrontStoreCode}}_yyyyMMddhhmmss_{index}.xlsx
					String fileName = littleMallExportDtoList.size() <= ConstantType.PRODUCT_UPLOAD_MAX_SIZE ?
						String.format("Product_export_all_column_%s.%s", fileNamePostfix, StringUtil.FILE_EXTENSION_EXCEL) :
						String.format("Product_export_all_column_%s_%s.%s", fileNamePostfix, index++, StringUtil.FILE_EXTENSION_EXCEL);

					ByteArrayOutputStream tempOs = new ByteArrayOutputStream();
					produceEditExcelFile(userDto, storefrontStoreCode, tempOs, partitionProductList);
					writeZip(tempOs.toByteArray(), buf, zipOutputStream, fileName);
				}
			}
		} catch (Exception e) {
			log.error("produceEditZipFile error:{}", e.getMessage(), e);
		}
	}

	private void writeZip(byte[] content, byte[] buf, ZipOutputStream zipOutputStream, String fileName) throws IOException {
		File tempFile = null;
		try {
			tempFile = File.createTempFile(UUID.randomUUID().toString(), null);
			try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
				fileOutputStream.write(content);
			}
			try (FileInputStream fileInputStream = new FileInputStream(tempFile)) {
				zipOutputStream.putNextEntry(new ZipEntry(fileName));
				int len;
				while ((len = fileInputStream.read(buf)) > 0) {
					zipOutputStream.write(buf, 0, len);
				}
			}
		} finally {
			zipOutputStream.closeEntry();
			// delete temp file
			if (tempFile != null && tempFile.exists()) {
				Files.delete(tempFile.toPath());
			}
		}
	}

	private void addEditHeaderColumn(XSSFWorkbook workbook) {
		XSSFSheet productSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(false);
		CellStyle instructionsStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		instructionsStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		instructionsStyle.setLocked(false);
		CellStyle notLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, false);
		notLockStyle.setLocked(false);

		// init style all cells are not locked
		CTCol col = productSheet.getCTWorksheet().getColsArray(0).addNewCol();
		col.setMin(1);
		col.setMax(16384);
		col.setWidth(9.15);
		col.setStyle(notLockStyle.getIndex());

		Font redFont = ExcelUtil.getRedFont(workbook);

		int colNum = 0;
		Row englishNameRow = CellUtil.getRow(0, productSheet);
		Row chineseNameRow = CellUtil.getRow(1, productSheet);
		Row instructionsRow = CellUtil.getRow(2, productSheet);
		englishNameRow.setHeight((short) (30 * 20));
		chineseNameRow.setHeight((short) (30 * 20));
		instructionsRow.setHeight((short) (100 * 20));

		for (LittleMallProductColumnEnum columnEnum : LittleMallProductColumnEnum.values()) {
			productSheet.setColumnWidth(columnEnum.getColumnNumber(), (short) (columnEnum.getWeight() * 256 + 184));
			Cell englishNameCell = CellUtil.getCell(englishNameRow, colNum);
			Cell chineseNameCell = CellUtil.getCell(chineseNameRow, colNum);
			Cell instructionsCell = CellUtil.getCell(instructionsRow, colNum);
			instructionsCell.setCellValue(columnEnum.getInstructions());
			if (columnEnum.isRequired()) {
				littleMallProductTemplateHelper.setLastTextRed(chineseNameCell, redFont, columnEnum.getColNameChinese());
				littleMallProductTemplateHelper.setLastTextRed(englishNameCell, redFont, columnEnum.getColNameEnglish());
			} else {
				englishNameCell.setCellValue(columnEnum.getColNameEnglish());
				chineseNameCell.setCellValue(columnEnum.getColNameChinese());
			}
			englishNameCell.setCellStyle(headerStyle);
			chineseNameCell.setCellStyle(headerStyle);
			instructionsCell.setCellStyle(instructionsStyle);
			colNum++;
		}
		// lock header cell
		setRowDataLockValidation(productSheet, 0, 2);
	}

	private void addEditBodyColumn(Workbook workbook, List<LittleMallExportDto> littleMallExportDtoList, List<ProductFieldsOptionsPidDto> productFieldOptions) {
		Sheet productSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle unlockStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false, false, false);

		AtomicInteger rowIndex = new AtomicInteger(BODY_START_ROW);
		Map<Integer, ProductFieldsOptionsPidDto> productFieldOptionsMap = productFieldOptions.stream().collect(Collectors.toMap(ProductFieldsOptionsPidDto::getPid, productFieldOptionsPidDto -> productFieldOptionsPidDto));

		littleMallExportDtoList.forEach(product -> {
			// convert product field and option ID to description
			Pair<String, String> fieldAndOptionDescription1 = littleMallRelationHelper.processFieldOptionToExcelDescription(product.getProductFieldCategory1(), product.getProductFieldOption1(), productFieldOptionsMap.get(product.getProductField1()));
			Pair<String, String> fieldAndOptionDescription2 = littleMallRelationHelper.processFieldOptionToExcelDescription(product.getProductFieldCategory2(), product.getProductFieldOption2(), productFieldOptionsMap.get(product.getProductField2()));
			Pair<String, String> fieldAndOptionDescription3 = littleMallRelationHelper.processFieldOptionToExcelDescription(product.getProductFieldCategory3(), product.getProductFieldOption3(), productFieldOptionsMap.get(product.getProductField3()));
			Pair<String, String> fieldAndOptionDescription4 = littleMallRelationHelper.processFieldOptionToExcelDescription(product.getProductFieldCategory4(), product.getProductFieldOption4(), productFieldOptionsMap.get(product.getProductField4()));

			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_ID.getColumnNumber(), product.getSkuId());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_ID.getColumnNumber(), product.getProductId());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_READY_METHOD.getColumnNumber(), product.getProductReadyMethod());
			setCellBooleanValueToYN(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.IS_PRIMARY_SKU.getColumnNumber(), product.getIsPrimarySku(), ExcelUtil.YN_LIST);
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_NAME_CHI.getColumnNumber(), product.getSkuNameCh());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.ORIGINAL_PRICE.getColumnNumber(), product.getOriginalPrice());
			setNumberCellValueDefaultString(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.SELLING_PRICE.getColumnNumber(), product.getSellingPrice());
			setCellBooleanValueToYN(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.VISIBILITY.getColumnNumber(), product.getVisibility(), ExcelUtil.YN_LIST);
			setCellBooleanValueToYN(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.ONLINE_STATUS.getColumnNumber(), product.getOnlineStatus(), ExcelUtil.YN_LIST);
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.DISPLAY_IN_HKTVMALL_CATEGORY.getColumnNumber(), product.getDisplayInHktvmallCategory());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.SKU_LONG_DESCRIPTION_CHI.getColumnNumber(), product.getSkuLongDescriptionCh());
			// edit template no need to set mainPhoto & otherPhoto value
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.MAIN_PHOTO.getColumnNumber(), "");
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.OTHER_PHOTO.getColumnNumber(), "");
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_1.getColumnNumber(), fieldAndOptionDescription1.getLeft());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_1.getColumnNumber(), fieldAndOptionDescription1.getRight());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_2.getColumnNumber(), fieldAndOptionDescription2.getLeft());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_2.getColumnNumber(), fieldAndOptionDescription2.getRight());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_3.getColumnNumber(), fieldAndOptionDescription3.getLeft());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_3.getColumnNumber(), fieldAndOptionDescription3.getRight());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_4.getColumnNumber(), fieldAndOptionDescription4.getLeft());
			setCellValue(productSheet, unlockStyle, rowIndex.get(), LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_4.getColumnNumber(), fieldAndOptionDescription4.getRight());

			rowIndex.addAndGet(1);
		});
		// export lock cell value
		Set<Integer> lockColumns = Set.of(
			LittleMallProductColumnEnum.SKU_ID.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_ID.getColumnNumber(),
			LittleMallProductColumnEnum.IS_PRIMARY_SKU.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_1.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_1.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_2.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_2.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_3.getColumnNumber(),
			LittleMallProductColumnEnum.PRODUCT_FIELD_OPTION_3.getColumnNumber()
			);
		setColumnsDataLockValidation(productSheet, BODY_START_ROW, rowIndex.get() - 1, lockColumns);
	}

	public Workbook generateEditFlattenErrorReport(SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		addEditFlattenHeaderColumn(workbook);
		addErrorColumn(workbook, LittleMallFlattenTemplateColumnEnum.values().length, false, false);
		addEditFlattenBodyColumn(workbook, rowList);
		return workbook;
	}

	private void addEditFlattenHeaderColumn(XSSFWorkbook workbook) {
		XSSFSheet dataSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(true);

		int rowNum = 0;
		int colNum = 0;
		Row row = CellUtil.getRow(rowNum, dataSheet);
		row.setHeight((short) (30 * 20));

		for (LittleMallFlattenTemplateColumnEnum columnEnum : LittleMallFlattenTemplateColumnEnum.values()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());
			cell.setCellStyle(headerStyle);
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), (short) (columnEnum.getWeight() * 256 + 184));
			colNum++;
		}
		dataSheet.protectSheet(LOV_PASSWORD);
	}

	private void addEditFlattenBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> rows) {
		Sheet dataSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle lockStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.LEFT, false, false, false, false, true);
		AtomicInteger rowIndex = new AtomicInteger(1);

		rows.forEach(row -> {
			SingleEditProductDto rowEditProduct = checkLittleMallFieldsIsPresent(row);
			setCellValue(dataSheet, lockStyle, rowIndex.get(), LittleMallFlattenTemplateColumnEnum.PRODUCT_CODE.getColumnNumber(), rowEditProduct.getProduct().getProductId());
			setCellValue(dataSheet, lockStyle, rowIndex.get(), LittleMallFlattenTemplateColumnEnum.SKU_CODE.getColumnNumber(), rowEditProduct.getProduct().getSkuId());
			setCellBooleanValueToYN(dataSheet, lockStyle, rowIndex.get(), LittleMallFlattenTemplateColumnEnum.IS_PRIMARY_SKU.getColumnNumber(), rowEditProduct.getProduct().getAdditional().getLittleMall().getIsPrimarySku(), ExcelUtil.YN_LIST);
			setCellValue(dataSheet, lockStyle, rowIndex.get(), LittleMallFlattenTemplateColumnEnum.values().length, row.getErrorMessage());
			rowIndex.addAndGet(1);
		});
	}

	protected SingleEditProductDto checkLittleMallFieldsIsPresent(SaveProductRecordRowDo row) {
		if (row == null) {
			log.warn("row is null");
			throw new NoDataException();
		}
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		if (singleEditProductDto == null
			|| singleEditProductDto.getProduct() == null
			|| singleEditProductDto.getProduct().getAdditional() == null
			|| singleEditProductDto.getProduct().getAdditional().getLittleMall() == null) {
			log.warn("field is null, row id:{}", row.getId());
			throw new NoDataException();
		}
		return singleEditProductDto;
	}
}
