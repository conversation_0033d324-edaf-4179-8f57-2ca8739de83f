package com.shoalter.mms_product_api.service.product.pojo.productmaster.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductMasterUpdateVisibilityResponseDto {

	@JsonProperty("uuid")
	private String uuid;

	@JsonProperty("store_sku_id")
	private String storeSkuId;

	@JsonProperty("product_id")
	private String productId;

	@JsonProperty("message")
	private String message;
}
