package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuExportHistoryEnum;
import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.LittleMallExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.LittleMallExportInfoContentData;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.LittleMallExportInfoDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.DownloadLittleMallAllProductsRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class DownloadLittleMallAllProductsService {

	private final LittleMallExportInfoRepository littleMallExportInfoRepository;
	private final StoreRepository storeRepository;
	private final MessageSource messageSource;
	private final ProductMasterHelper productMasterHelper;
	private final PermissionHelper permissionHelper;

	private static final String ERROR_CODE = "ERROR_CODE";
	private static final String ERROR_ARGS = "ERROR_ARGS";

	@Transactional
	public ResponseDto<Void> start(UserDto userDto, DownloadLittleMallAllProductsRequestDto requestDto) {
		// check
		Pair<Boolean, Map<String, String>> checkErrorResult = checkError(userDto, requestDto);
		if (Boolean.FALSE.equals(checkErrorResult.getLeft())) {
			return ResponseDto.fail(List.of(messageSource.getMessage(checkErrorResult.getRight().get(ERROR_CODE), new String[]{checkErrorResult.getRight().get(ERROR_ARGS)}, null)));
		}

		LittleMallExportInfoDo littleMallExportInfoDo = littleMallExportInfoRepository.save(LittleMallExportInfoDo.builder()
			.exportType("Product")
			.bu(BuExportHistoryEnum.LITTLE_MALL)
			.status(ExportStatusEnum.PROCESSING)
			.skuCount(0)
			.merchantId(userDto.getMerchantId())
			.content(new LittleMallExportInfoContentData(null, requestDto))
			.createdBy(userDto.getUserCode())
			.lastUpdatedBy(userDto.getUserCode())
			.build());

		log.info("LittleMall export all products info created: {}", littleMallExportInfoDo.getId());

		return ResponseDto.success(null);
	}

	private Pair<Boolean, Map<String, String>> checkError(UserDto userDto, DownloadLittleMallAllProductsRequestDto requestDto) {
		// store limit 1~5
		if (CollectionUtil.isEmpty(requestDto.getStoreIds()) || requestDto.getStoreIds().size() > 5) {
			return Pair.of(false, new HashMap<>(Map.of(ERROR_CODE, "message315")));
		}

		List<StoreMerchantViewDo> requestMerchantStores = storeRepository.findStoreMerchantViewDoByStoreIds(requestDto.getStoreIds());
		if (CollectionUtil.isEmpty(requestMerchantStores)) {
			return Pair.of(false, new HashMap<>(Map.of(ERROR_CODE, "message69")));
		}

		// check user have permission to access request store
		for (StoreMerchantViewDo storeMerchantView : requestMerchantStores) {
			try {
				permissionHelper.checkPermission(userDto, storeMerchantView.getMerchantId());
			} catch (SystemI18nException e) {
				return Pair.of(false, new HashMap<>(Map.of(ERROR_CODE, "message314", ERROR_ARGS, String.valueOf(storeMerchantView.getStorefrontStoreCode()))));
			}
		}

		//check if there is any sku in product master
		if (!checkLittleMallProductIsExist(userDto, requestMerchantStores)) {
			return Pair.of(false, new HashMap<>(Map.of(ERROR_CODE, "message51")));
		}

		return Pair.of(true, new HashMap<>());
	}

	private boolean checkLittleMallProductIsExist(UserDto userDto, List<StoreMerchantViewDo> requestMerchantStores) {
		for (StoreMerchantViewDo storeMerchantView : requestMerchantStores) {
			LittleMallProductSearchRequestDto littleMallProductRequest = LittleMallProductSearchRequestDto.builder()
				.page(1)
				.size(1)
				.merchantId(storeMerchantView.getMerchantId())
				.storeCode(storeMerchantView.getStorefrontStoreCode())
				.build();
			ProductOverviewResultDto resultDto = productMasterHelper.requestLittleMallProductsByParams(userDto, littleMallProductRequest);
			if (resultDto != null && resultDto.getTotalElements() > 0) {
				return true;
			}
		}
		return false;
	}
}
