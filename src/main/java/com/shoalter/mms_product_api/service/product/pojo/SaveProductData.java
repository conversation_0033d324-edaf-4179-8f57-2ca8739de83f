package com.shoalter.mms_product_api.service.product.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveProductData {

    private BigDecimal rmbToHkdExchangeRate;
    private Set<String> hkdToRmbWhiteListSkuIds = new HashSet<>();

}
