package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ContractMapper;
import com.shoalter.mms_product_api.dao.repository.rm.RmTeamRepository;
import com.shoalter.mms_product_api.dao.repository.rm.pojo.RmTeamDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.pojo.ContractTypeDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@RequiredArgsConstructor
@Service
@Slf4j
public class FindContractService {

	private final PermissionHelper permissionHelper;
	private final ContractMapper contractMapper;
	private final RmTeamRepository rmTeamRepository;

	public ResponseDto<List<ContractTypeDto>> start(UserDto userDto, Integer merchantId, String buCode) {
		permissionHelper.checkPermission(userDto, merchantId);
		List<ContractTypeDto> contractList = contractMapper.findContractAndContractType(buCode, merchantId);
		contractList = filterDataByUserRole(contractList, userDto.getUserId(), userDto.getRoleCode());
		return ResponseDto.<List<ContractTypeDto>>builder().status(1).data(contractList).build();
	}

	private List<ContractTypeDto> filterDataByUserRole(List<ContractTypeDto> contractList, Integer userId, String roleCode) {
		switch (roleCode.toUpperCase()) {
			case RoleCode.RM:
				return filterDataByRm(contractList, userId);
			case RoleCode.RML:
				return filterDataByRml(contractList, userId);
		}
		return contractList;
	}

	private List<ContractTypeDto> filterDataByRm(List<ContractTypeDto> contractList, Integer userId) {
		List<ContractTypeDto> tempContractDoList = new ArrayList<>();
		for (ContractTypeDto contractDto : contractList) {
			if (contractDto.getRmId().equals(userId)) tempContractDoList.add(contractDto);
		}
		return tempContractDoList;
	}

	private List<ContractTypeDto> filterDataByRml(List<ContractTypeDto> contractList, Integer userId) {
		List<ContractTypeDto> tempContractDoList = new ArrayList<>();
		List<RmTeamDo> rmTeamDoList = rmTeamRepository.findRmlByUserIdAndDeptCode(userId);
		for (RmTeamDo rmTeamDo : rmTeamDoList) {
			for (ContractTypeDto contractDto : contractList) {
				if (contractDto.getRmId().equals(rmTeamDo.getUserId())) tempContractDoList.add(contractDto);
			}
		}
		return tempContractDoList;
	}
}
