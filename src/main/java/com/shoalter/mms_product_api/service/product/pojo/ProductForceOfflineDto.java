package com.shoalter.mms_product_api.service.product.pojo;

import com.shoalter.mms_product_api.config.product.ForceOfflineStatusEnum;
import com.shoalter.mms_product_api.service.force_offline.ForceOfflineRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.lang.Nullable;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProductForceOfflineDto extends BatchEditProductBaseDto {
	private String skuStatus;
	private String caseNumber;

	public static ProductForceOfflineDto map(ForceOfflineRequestDto requestDto) {
		ProductForceOfflineDto dto = new ProductForceOfflineDto();
		dto.setStorefrontStoreCode(requestDto.getStorefrontStoreCode());
		dto.setSkuCode(requestDto.getSkuCode());
		dto.setSkuStatus(requestDto.getSkuStatus());
		dto.setCaseNumber(requestDto.getCaseNumber());
		return dto;
	}

	public boolean isForceOffline() {
		return ForceOfflineStatusEnum.FORCE_OFFLINE.getStatus().equalsIgnoreCase(skuStatus);
	}

	@Nullable
	public Boolean getForceOffline() {
		if (skuStatus == null) {
			return null;
		}

		return isForceOffline();
	}
}
