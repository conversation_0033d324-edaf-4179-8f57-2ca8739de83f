package com.shoalter.mms_product_api.service.product.template;


import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.edit_column.AllColumnTemplateColumnEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BusinessMapper;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.dao.mapper.store.StoreWarehouseMapper;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameEnViewDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductUploadExcelDataDto;
import com.shoalter.mms_product_api.service.product.pojo.SetCellValueDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.StoreWarehouseIdViewDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateAllColumnHelper extends AbstractReport implements IProductTemplateHelper<AllColumnTemplateColumnEnum> {

	private final BrandRepository brandRepository;
	private final BusinessMapper businessMapper;
	private final StoreWarehouseMapper storeWarehouseMapper;
	private final ProductMapper productMapper;

	public Workbook setTemplateBodyColumn(Workbook workbook, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		List<String> uuidList = productList.stream().map(data -> data.getProduct().getUuid()).collect(Collectors.toList());
		List<ProductUploadExcelDataDto> productUploadExcelDataDtoList = productMapper.findProductUploadExcelDataList(uuidList);
		Map<String, ProductUploadExcelDataDto> uuidMapProductData = productUploadExcelDataDtoList.stream().collect(Collectors.toMap(ProductUploadExcelDataDto::getUuid, product -> product));

		List<Integer> brandIdList = new ArrayList<>();
		List<Integer> warehouseIdList = new ArrayList<>();
		productList.forEach(singleEditProductDto -> {
				brandIdList.add(singleEditProductDto.getProduct().getBrandId());
				warehouseIdList.add(singleEditProductDto.getProduct().getAdditional().getHktv().getWarehouseId());
			}
		);
		BusinessPlatformDo businessPlatformDo =
			businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElse(new BusinessPlatformDo());
		Integer platformId = businessPlatformDo.getPlatformId();

		Map<String, String> manufacturedCountryMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmDo::getShortDesc, (oldValue, newValue) -> oldValue));

		Map<String, String> colorEnglishMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COLOR, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));

		Map<String, String> voucherTypeMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));
		Map<String, String> sizeSystemMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
		Map<String, String> sizeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE, platformId);
		Map<String, String> colorFamiliesMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
		Map<String, String> productFieldMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD, platformId);
		Map<String, String> productFieldValueMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD_VALUE, platformId);
		Map<String, String> packBoxTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
		Map<String, String> productReadyMethodMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_METHOD, platformId);
		Map<String, String> voucherTemplateTypeMap = getDirtyDataVoucherTemplateTypeMap(addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId));
		Map<String, String> voucherDisplayTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
		Map<String, String> currencyMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.CURRENCY, platformId);
		Map<String, String> productReadyDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
		Map<String, String> pickupDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);

		Map<Integer, String> brandNameByIdMap = brandRepository.findBrandNameEnByIdIn(brandIdList).stream().collect(Collectors.toMap(BrandNameEnViewDo::getId, BrandNameEnViewDo::getBrandNameEn));
		Map<Integer, String> warehouseIdByIdMap = storeWarehouseMapper.findWarehouseIdHaveDelimiter(warehouseIdList).stream().collect(Collectors.toMap(StoreWarehouseIdViewDto::getId, StoreWarehouseIdViewDto::getWarehouseId));

		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle lockBodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		lockBodyStyle.setLocked(true);
		CellStyle dateSimpleWithTimeStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR_M, tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);
		SetCellValueDto setCellValueDto = SetCellValueDto.builder()
			.sheet(dataSheet)
			.lockStyle(lockBodyStyle)
			.notLockStyle(bodyStyle)
			.build();
		productList.forEach(singleEditProductDto -> {
			ProductMasterDto product = singleEditProductDto.getProduct();
			HktvProductDto hktvProductDto = product.getAdditional().getHktv();
			ProductUploadExcelDataDto productUploadExcelDataDto = Optional.ofNullable(uuidMapProductData.get(product.getUuid())).orElse(new ProductUploadExcelDataDto());
			String colorEnglish = null;
			String colorChinese = null;
			String color = product.getColor();
			if (StringUtil.isNotEmpty(product.getColor())) {
				int index = product.getColor().indexOf("    ");
				if (index > 0) {
					colorEnglish = color.substring(0, index);
					colorChinese = color.substring(index + 4);
				} else {
					colorEnglish = product.getColor();
				}
			}

			setCellValueDto.setRowNumber(rowIndex.get());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRODUCT_CODE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PRODUCT_CODE), product.getProductId());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_CODE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_CODE), product.getSkuId());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRODUCT_HKTV_CAT_LIST.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PRODUCT_HKTV_CAT_LIST), hktvProductDto.getProductTypeCode());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRODUCT_HKTV_CAT_CODE_PRI.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PRODUCT_HKTV_CAT_CODE_PRI), hktvProductDto.getPrimaryCategoryCode());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.BRAND_ID.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.BRAND_ID), brandNameByIdMap.get(product.getBrandId()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRODUCT_READY_METHOD.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PRODUCT_READY_METHOD), productReadyMethodMap.get(hktvProductDto.getProductReadyMethod()));
			//online status is empty
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.STORE_STATUS.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.STORE_STATUS), hktvProductDto.getOnlineStatus().name());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WAREHOUSE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WAREHOUSE), warehouseIdByIdMap.get(hktvProductDto.getWarehouseId()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.TERM_NAME.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.TERM_NAME), hktvProductDto.getTermName());
			setYesNoCellValue(setCellValueDto, AllColumnTemplateColumnEnum.IS_PRIMARY_SKU.getColumnNumber(),isLockColumn(AllColumnTemplateColumnEnum.IS_PRIMARY_SKU), hktvProductDto.getIsPrimarySku());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_NAME.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_NAME), product.getSkuNameEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_NAME_TCHI.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_NAME_TCHI), product.getSkuNameCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_NAME_SCHI.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_NAME_SCHI), product.getSkuNameSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_EN), hktvProductDto.getSkuShortDescriptionEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_CH), hktvProductDto.getSkuShortDescriptionCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_S_DESC_HKTV_SC), hktvProductDto.getSkuShortDescriptionSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_EN), hktvProductDto.getSkuLongDescriptionEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_CH), hktvProductDto.getSkuLongDescriptionCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SKU_L_DESC_HKTV_SC), hktvProductDto.getSkuLongDescriptionSc());
			//photo & video not set
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MAIN_PHOTO_HKTVMALL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MAIN_PHOTO_HKTVMALL), "");
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MAIN_VIDEO.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MAIN_VIDEO), "");
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.OTHER_PRODUCT_PHOTO_HKTVMALL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.OTHER_PRODUCT_PHOTO_HKTVMALL), "");
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.OTHER_PHOTO_HKTVMALL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.OTHER_PHOTO_HKTVMALL), "");
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.ADVERTISING_PHOTO_HKTVMALL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.ADVERTISING_PHOTO_HKTVMALL), "");
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK), hktvProductDto.getVideoLink());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_EN), hktvProductDto.getVideoLinkTextEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_CH), hktvProductDto.getVideoLinkTextCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_SC), hktvProductDto.getVideoLinkTextSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK2), hktvProductDto.getVideoLink2());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_EN2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_EN2), hktvProductDto.getVideoLinkTextEn2());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_CH2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_CH2), hktvProductDto.getVideoLinkTextCh2());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_SC2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_SC2), hktvProductDto.getVideoLinkTextSc2());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK3), hktvProductDto.getVideoLink3());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_EN3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_EN3), hktvProductDto.getVideoLinkTextEn3());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_CH3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_CH3), hktvProductDto.getVideoLinkTextCh3());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_SC3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_SC3), hktvProductDto.getVideoLinkTextSc3());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK4.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK4), hktvProductDto.getVideoLink4());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_EN4.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_EN4), hktvProductDto.getVideoLinkTextEn4());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_CH4.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_CH4), hktvProductDto.getVideoLinkTextCh4());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_SC4.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_SC4), hktvProductDto.getVideoLinkTextSc4());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK5.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK5), hktvProductDto.getVideoLink5());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_EN5.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_EN5), hktvProductDto.getVideoLinkTextEn5());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_CH5.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_CH5), hktvProductDto.getVideoLinkTextCh5());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIDEO_LINK_SC5.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIDEO_LINK_SC5), hktvProductDto.getVideoLinkTextSc5());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MANU_COUNTRY.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MANU_COUNTRY), manufacturedCountryMap.get(product.getManufacturedCountry()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.COLOR_FAMILIAR.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.COLOR_FAMILIAR), colorFamiliesMap.get(product.getColourFamilies()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.COLOR_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.COLOR_EN), colorEnglishMap.get(colorEnglish));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.COLOR_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.COLOR_CH), colorChinese);
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SIZE_SYSTEM.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SIZE_SYSTEM), sizeSystemMap.get(product.getSizeSystem()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SIZE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SIZE), sizeMap.get(product.getSize()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CURRENCY_CODE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CURRENCY_CODE), currencyMap.get(hktvProductDto.getCurrency()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.COST.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.COST), convertDecimalToDouble(hktvProductDto.getCost()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.ORIGINAL_PRICE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.ORIGINAL_PRICE), convertDecimalToDouble(product.getOriginalPrice()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SELLING_PRICE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SELLING_PRICE), convertDecimalToDouble(hktvProductDto.getSellingPrice()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MALL_DOLLAR.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MALL_DOLLAR), convertDecimalToDouble(hktvProductDto.getMallDollar()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MALL_DOLLAR_VIP.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MALL_DOLLAR_VIP), convertDecimalToDouble(hktvProductDto.getVipMallDollar()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.USER_MAX.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.USER_MAX), zeroToNull(hktvProductDto.getUserMax()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.STYLE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.STYLE), hktvProductDto.getStyle());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.DISCOUNT_TEXT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.DISCOUNT_TEXT), hktvProductDto.getDiscountTextEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.DISCOUNT_TEXT_ZH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.DISCOUNT_TEXT_ZH), hktvProductDto.getDiscountTextCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.DISCOUNT_TEXT_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.DISCOUNT_TEXT_SC), hktvProductDto.getDiscountTextSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_SPEC_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_SPEC_EN), hktvProductDto.getPackingSpecEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_SPEC_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_SPEC_CH), hktvProductDto.getPackingSpecCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_SPEC_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_SPEC_SC), hktvProductDto.getPackingSpecSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_HEIGHT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_HEIGHT), convertDecimalToDouble(product.getPackingHeight()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_LENGTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_LENGTH), convertDecimalToDouble(product.getPackingLength()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_DEPTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_DEPTH), convertDecimalToDouble(product.getPackingDepth()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_DIMENSION_UNIT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_DIMENSION_UNIT), product.getPackingDimensionUnit());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WEIGHT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WEIGHT), convertDecimalToDouble(product.getWeight()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WEIGHT_UNIT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WEIGHT_UNIT), product.getWeightUnit());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PACK_BOX_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PACK_BOX_TYPE), packBoxTypeMap.get(product.getPackingBoxType()));

			if (CollectionUtil.isNotEmpty(product.getCartonSizeList())) {
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_HEIGHT), product.getCartonSizeList().get(0).getHeight());
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_DEPTH), product.getCartonSizeList().get(0).getWidth());
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_LENGTH), product.getCartonSizeList().get(0).getLength());
			} else {
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_HEIGHT), "");
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_DEPTH), "");
				setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CARTON_LENGTH), "");
			}
			setYesNoCellValue(setCellValueDto, AllColumnTemplateColumnEnum.INVISIBLE_FLAG.getColumnNumber(),isLockColumn(AllColumnTemplateColumnEnum.INVISIBLE_FLAG), reverseYN(hktvProductDto.getVisibility()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.BARCODE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.BARCODE), Optional.ofNullable(product.getBarcodes()).orElse(new ArrayList<>()).stream().filter(productBarcodeDto -> 1 == productBarcodeDto.getSequenceNo()).map(ProductBarcodeDto::getEan).findFirst().orElse(""));
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), AllColumnTemplateColumnEnum.FEATURE_START_TIME.getColumnNumber(), hktvProductDto.getFeatureStartTime());
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), AllColumnTemplateColumnEnum.FEATURE_END_TIME.getColumnNumber(), hktvProductDto.getFeatureEndTime());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VOUCHER_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VOUCHER_TYPE), voucherTypeMap.get(hktvProductDto.getVoucherType()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VOUCHER_DISPLAY_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VOUCHER_DISPLAY_TYPE), voucherDisplayTypeMap.get(hktvProductDto.getVoucherDisplayType()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VOUCHER_TEMPLATE_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VOUCHER_TEMPLATE_TYPE), voucherTemplateTypeMap.get(hktvProductDto.getVoucherTemplateType()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.EXPIRY_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.EXPIRY_TYPE), hktvProductDto.getExpiryType());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FIELD1.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FIELD1), productFieldMap.get(product.getOption1()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VALUE1.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VALUE1), productFieldValueMap.get(product.getOption1Value()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FIELD2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FIELD2), productFieldMap.get(product.getOption2()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VALUE2.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VALUE2), productFieldValueMap.get(product.getOption2Value()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FIELD3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FIELD3), productFieldMap.get(product.getOption3()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VALUE3.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VALUE3), productFieldValueMap.get(product.getOption3Value()));
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY, rowIndex.get(), AllColumnTemplateColumnEnum.REDEEM_START_DATE.getColumnNumber(), hktvProductDto.getRedeemStartDate());
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  rowIndex.get(), AllColumnTemplateColumnEnum.REDEEM_END_DATE.getColumnNumber(), hktvProductDto.getFixedRedemptionDate());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.UPON_PURCHASE_DATE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.UPON_PURCHASE_DATE), zeroToNull(hktvProductDto.getUponPurchaseDate()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FINE_PRINT_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FINE_PRINT_EN), hktvProductDto.getFinePrintEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FINE_PRINT_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FINE_PRINT_CH), hktvProductDto.getFinePrintCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.FINE_PRINT_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.FINE_PRINT_SC), hktvProductDto.getFinePrintSc());
			setYesNoCellValue(setCellValueDto, AllColumnTemplateColumnEnum.REMOVAL_SERVICES.getColumnNumber(),isLockColumn(AllColumnTemplateColumnEnum.REMOVAL_SERVICES), hktvProductDto.getNeedRemovalServices());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.GOODS_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.GOODS_TYPE), hktvProductDto.getGoodsType());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_PERIOD_UNIT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_PERIOD_UNIT), hktvProductDto.getWarrantyPeriodUnit());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_PERIOD.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_PERIOD), zeroToNull(hktvProductDto.getWarrantyPeriod()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_EN), hktvProductDto.getWarrantySupplierEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_CH), hktvProductDto.getWarrantySupplierCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_SUPPLIER_SC), hktvProductDto.getWarrantySupplierSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_EN), hktvProductDto.getServiceCentreAddressEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_CH), hktvProductDto.getServiceCentreAddressCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SERVICE_CENTRE_ADDRESS_SC), hktvProductDto.getServiceCentreAddressSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SERVICE_CENTRE_EMAIL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SERVICE_CENTRE_EMAIL), hktvProductDto.getServiceCentreEmail());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.SERVICE_CENTRE_CONTACT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.SERVICE_CENTRE_CONTACT), hktvProductDto.getServiceCentreContact());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_REMARK_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_REMARK_EN), hktvProductDto.getWarrantyRemarkEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_REMARK_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_REMARK_CH), hktvProductDto.getWarrantyRemarkCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.WARRANTY_REMARK_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.WARRANTY_REMARK_SC), hktvProductDto.getWarrantyRemarkSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.INVOICE_REMARKS_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.INVOICE_REMARKS_EN), hktvProductDto.getInvoiceRemarksEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.INVOICE_REMARKS_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.INVOICE_REMARKS_CH), hktvProductDto.getInvoiceRemarksCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.INVOICE_REMARKS_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.INVOICE_REMARKS_SC), hktvProductDto.getInvoiceRemarksSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.RETURN_DAYS.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.RETURN_DAYS), hktvProductDto.getReturnDays());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRODUCT_READY_DAYS.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PRODUCT_READY_DAYS), productReadyDaysMap.get(hktvProductDto.getProductReadyDays()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PICKUP_DAYS.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PICKUP_DAYS), pickupDaysMap.get(hktvProductDto.getPickupDays()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PICKUP_TIMESLOT.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PICKUP_TIMESLOT), hktvProductDto.getPickupTimeslot());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.OVERSEA_DELIVERY.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.OVERSEA_DELIVERY), productUploadExcelDataDto.getOverseaDelivery());
			setYesNoCellValue(setCellValueDto, AllColumnTemplateColumnEnum.URGENT.getColumnNumber(),isLockColumn(AllColumnTemplateColumnEnum.URGENT), hktvProductDto.getUrgent());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.MINIMUM_SHELF_LIFE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.MINIMUM_SHELF_LIFE), zeroToNull(productUploadExcelDataDto.getMinimumShelfLife()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.VIRTUAL_STORE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.VIRTUAL_STORE), hktvProductDto.getVirtualStore());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.RM_CODE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.RM_CODE), hktvProductDto.getRmCode());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.STORAGE_TYPE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.STORAGE_TYPE), hktvProductDto.getStorageType());
			setYesNoCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PRE_SELL_FRUIT.getColumnNumber(),isLockColumn(AllColumnTemplateColumnEnum.PRE_SELL_FRUIT), hktvProductDto.getPreSellFruit());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PHYSICAL_STORE.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PHYSICAL_STORE), hktvProductDto.getPhysicalStore());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.AFFILIATE_URL.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.AFFILIATE_URL), hktvProductDto.getAffiliateUrl());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.EW_PERCENTAGE_SETTING.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.EW_PERCENTAGE_SETTING), convertDecimalToDouble(hktvProductDto.getEwPercentageSetting()));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CLAIM_LINK_EN.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CLAIM_LINK_EN), hktvProductDto.getClaimLinkEn());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CLAIM_LINK_CH.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CLAIM_LINK_CH), hktvProductDto.getClaimLinkCh());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.CLAIM_LINK_SC.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.CLAIM_LINK_SC), hktvProductDto.getClaimLinkSc());
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PARTNER_PLATFORM.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PARTNER_PLATFORM),
				ofNullable(hktvProductDto.getExternalPlatform())
					.map(ExternalPlatform::getSource)
					.map(source -> StringUtils.join(source, ","))
					.orElse(""));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PARTNER_PRODUCT_ID.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PARTNER_PRODUCT_ID), ofNullable(hktvProductDto.getExternalPlatform()).map(ExternalPlatform::getProductId).orElse(""));
			setCellValue(setCellValueDto, AllColumnTemplateColumnEnum.PARTNER_SKU_ID.getColumnNumber(), isLockColumn(AllColumnTemplateColumnEnum.PARTNER_SKU_ID), ofNullable(hktvProductDto.getExternalPlatform()).map(ExternalPlatform::getSkuId).orElse(""));
			rowIndex.addAndGet(1);
		});
		setLockCellValidation(dataSheet, rowIndex.get() - 1);
		return tempWorkbook;
	}

	private void setLockCellValidation(Sheet dataSheet, int rowSize) {
		Set<Integer> lockColumns = Set.of(
			AllColumnTemplateColumnEnum.PRODUCT_CODE.getColumnNumber(),
			AllColumnTemplateColumnEnum.SKU_CODE.getColumnNumber(),
			AllColumnTemplateColumnEnum.COLOR_CH.getColumnNumber(),
			AllColumnTemplateColumnEnum.MALL_DOLLAR.getColumnNumber(),
			AllColumnTemplateColumnEnum.MALL_DOLLAR_VIP.getColumnNumber());
		setColumnsDataLockValidation(dataSheet, 1, rowSize, lockColumns);
	}

	@Override
	public Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> productList, List<SysParmDo> sysParmList, SaveProductRecordDo record) {
		return null;
	}

	@Override
	public int getColumnWidth(TemplateInterface<AllColumnTemplateColumnEnum> columnEnum) {
		if (columnEnum == AllColumnTemplateColumnEnum.COLOR_CH ||
			columnEnum == AllColumnTemplateColumnEnum.MALL_DOLLAR_VIP ||
			columnEnum == AllColumnTemplateColumnEnum.MALL_DOLLAR) {
			return 0;
		} else if (columnEnum == AllColumnTemplateColumnEnum.BRAND_ID) {
			return 5000;
		} else if (columnEnum == AllColumnTemplateColumnEnum.PRODUCT_CODE ||
			columnEnum == AllColumnTemplateColumnEnum.WAREHOUSE) {
			return 6000;
		} else if (columnEnum == AllColumnTemplateColumnEnum.SKU_CODE ||
			columnEnum == AllColumnTemplateColumnEnum.FEATURE_START_TIME ||
			columnEnum == AllColumnTemplateColumnEnum.FEATURE_END_TIME ||
			columnEnum == AllColumnTemplateColumnEnum.TERM_NAME) {
			return 7000;
		}
		return columnEnum.getColumnName().length() * 400;
	}

	/** MS-7835 HKTV All column template unLock all columns */
	@Override
	public boolean isLockColumn(TemplateInterface columnEnum) {
		return false;
	}
}
