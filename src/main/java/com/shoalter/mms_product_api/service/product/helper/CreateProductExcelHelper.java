package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.template.HktvUploadProductExcelReportEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.business.BusUnitRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.ITemplateService;
import com.shoalter.mms_product_api.service.product.pojo.BusinessPlatformViewDo;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.type.ContractType.MAINLAND_MERCHANT_CONTRACT_SET;


@Service
@RequiredArgsConstructor
@Slf4j
public class CreateProductExcelHelper extends AbstractReport implements ITemplateService {

	private static final String PRODUCT_SHEET_NAME = "Product Template";

	private final StoreWarehouseRepository storeWarehouseRepository;
	private final BrandRepository brandRepository;

	private final BusUnitRepository busUnitRepository;
	private final SysParmRepository sysParmRepository;

	@Value("${hktvmall.external.base.url}")
	private String hktvmallExternalBaseUrl;

	@Value("${hktvmall.external.affiliate.endpoint}")
	private String hktvmallExternalAffiliateEndpoint;

	public Workbook generateExcel(UserDto userDto, List<HktvProductFieldDto> productList, Integer storeId, Integer contractId, boolean isEditReport) {
		Workbook workbook = new XSSFWorkbook();
		List<SysParmDo> sysParmList = searchBySegments(SEGMENT_PARENT_LIST);
		addDefaultStyle(workbook);
		setHeaderColumn(userDto, workbook, isEditReport);
		addLovSheet(workbook, isEditReport, storeId, contractId, sysParmList);
		if (CollectionUtils.isNotEmpty(productList)) {
			workbook = setBodyColumn(workbook, productList, sysParmList);
		}

		return workbook;
	}

	private void setHeaderColumn(UserDto userDto, Workbook workbook, boolean isEditReport) {
		Sheet dataSheet = workbook.createSheet(PRODUCT_SHEET_NAME);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(false);
		CellStyle lockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);

		int rowNum = 0;
		int colNum = 0;
		Row row = CellUtil.getRow(rowNum, dataSheet);
		row.setHeight((short) (30 * 20));

		boolean isMerchantRole = RoleCode.MERCHANT.equals(userDto.getRoleCode()) || RoleCode.MERCHANT_ADMIN.equals(userDto.getRoleCode());

		for (HktvUploadProductExcelReportEnum columnEnum : HktvUploadProductExcelReportEnum.values()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());

			int columnWidth = getColumnWidth(columnEnum, isMerchantRole, isEditReport);
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), columnWidth);

			boolean isLock = isLockColumn(columnEnum, isMerchantRole, isEditReport);
			if (isLock) {
				cell.setCellStyle(lockStyle);
			} else {
				cell.setCellStyle(headerStyle);
			}

			colNum++;
		}

		if (isEditReport) {
			dataSheet.protectSheet(LOV_PASSWORD);
		}

		dataSheet.createFreezePane(2, 1, 2, 1);//凍結窗格
	}

	private int getColumnWidth(HktvUploadProductExcelReportEnum columnEnum, boolean isMerchantRole, boolean isEditReport) {
		if (!isEditReport && isMerchantRole && columnEnum == HktvUploadProductExcelReportEnum.URGENT) {
			return 0;
		}

		switch (columnEnum) {
			case ID:
			case CONSUMABLE:
			case PRIORITY:
			case DELIVERY_TITLE_EN:
			case DELIVERY_TITLE_CH:
			case DELIVERY_DETAILS_EN:
			case DELIVERY_DETAILS_CH:
			case DELIVERY_COMPLETION_DAYS:
			case COLOR_CH:
			case SKU_S_TITLE_HKTV_EN:
			case SKU_S_TITLE_HKTV_CH:
			case SKU_L_TITLE_HKTV_EN:
			case SKU_L_TITLE_HKTV_CH:
			case FINE_PRINT_TITLE_EN:
			case FINE_PRINT_TITLE_CH:
				return 0;
			case BRAND_ID:
				return 5000;
			case PRODUCT_CODE:
			case WAREHOUSE:
				return 6000;
			case SKU_CODE:
			case FEATURE_START_TIME:
			case FEATURE_END_TIME:
			case TERM_NAME:
				return 7000;
			default:
				return columnEnum.getColumnName().length() * 400;
		}
	}

	private boolean isLockColumn(HktvUploadProductExcelReportEnum columnEnum, boolean isMerchantRole, boolean isEditReport) {
		if (columnEnum == HktvUploadProductExcelReportEnum.ID) {
			return true;
		}

		if (isEditReport) {
			switch (columnEnum) {
				case PRODUCT_CODE:
				case SKU_CODE:
				case VOUCHER_TYPE:
				case VOUCHER_DISPLAY_TYPE:
				case VOUCHER_TEMPLATE_TYPE:
				case EXPIRY_TYPE:
					return true;
				default:
			}
		}
		if (isMerchantRole) {
			switch (columnEnum) {
				case DELIVERY_DETAILS_EN:
				case DELIVERY_DETAILS_CH:
				case DELIVERY_COMPLETION_DAYS:
				case CONSUMABLE:
				case PRIORITY:
				case DELIVERY_TITLE_EN:
				case DELIVERY_TITLE_CH:
					return true;
				default:
			}
		}

		return false;
	}

	private void addLovSheet(Workbook workbook, boolean isEditReport, Integer storeId, Integer contractId, List<SysParmDo> sysParmList) {
		Sheet lovSheet = workbook.createSheet(AbstractReport.LOV_SHEET_NAME);
		Sheet dataSheet = workbook.getSheet(PRODUCT_SHEET_NAME);
		Map<String, Map<String, Object>> map = new HashMap<>();
		Num theColNum = new Num(0);
		BusinessPlatformViewDo businessPlatformDo =
			busUnitRepository.findByBusinessCode(ProductMasterBusinessUnitType.HKTV).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));

		setLovSheetValues(lovSheet, map, theColNum, businessPlatformDo, storeId, contractId, sysParmList);
		setValidations(workbook, dataSheet, theColNum, map, businessPlatformDo.getPlatformId(), contractId);
		//保護選項內容
		if (isEditReport) {
			lovSheet.protectSheet(LOV_PASSWORD);
		}
	}

	private void setLovSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum, BusinessPlatformViewDo businessPlatformViewDo, Integer storeId, Integer contractId, List<SysParmDo> sysParmList) {
		Integer platformId = businessPlatformViewDo.getPlatformId();
		String buCode = businessPlatformViewDo.getBusinessCode();
		Integer buId = businessPlatformViewDo.getBusinessId();

		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CONTRACT_NO, List.of(String.valueOf(contractId)), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORE_ID, List.of(String.valueOf(storeId)), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_YES_NO, ExcelUtil.OLD_YES_NO_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WEIGHT_UNIT, ExcelUtil.WEIGHT_UNIT_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, ExcelUtil.PACK_DIMENSION_UNIT_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_EXPIRY_TYPE, ExcelUtil.EXPIRY_TYPE_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_RETURN_DAYS, ExcelUtil.RETURN_DAYS_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_STATUS, ExcelUtil.PRODUCT_STATUS_LIST, theColNum, map);

		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_GOODS_TYPE, ExcelUtil.GOODS_TYPE_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WARRANTY_PERIOD_UNIT, ExcelUtil.WARRANTY_PERIOD_UNIT_LIST, theColNum, map);

		List<String> manuCountryList = SysParmUtil.getParamDescList(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_MANU_COUNTRY, manuCountryList, theColNum, map);

		List<String> currencyList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.CURRENCY, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_CURRENCY, currencyList, theColNum, map);

		List<String> packBoxTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
		List<String> packBoxThirdTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE_3PL_SKU, platformId);
		packBoxTypeList.addAll(packBoxThirdTypeList);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_BOX_TYPE, packBoxTypeList, theColNum, map);

		List<String> storageTemperatureList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.STORAGE_TEMPERATURE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TEMPERATURE, storageTemperatureList, theColNum, map);

		List<String> deliveryMethodList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.DELIVERY_METHOD, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_METHOD, deliveryMethodList, theColNum, map);

		List<String> deliComDaysList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.DELIVERY_COMPLETION_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_DELIVERY_COMPLETION_DAYS, deliComDaysList, theColNum, map);

		List<String> pickupTimeslotList = SysParmUtil.getParamCodeList(sysParmList, SysParmSegment.PICKUP_TIMESLOT, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_TIMESLOT, pickupTimeslotList, theColNum, map);

		List<String> proReadyDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_READY_DAYS, proReadyDaysList, theColNum, map);

		List<String> voucherTypeList = SysParmUtil.getShortDescAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TYPE, voucherTypeList, theColNum, map);

		List<String> voucherDisplayTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_DISPLAY_TYPE, voucherDisplayTypeList, theColNum, map);

		List<String> voucherTemplateTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VOUCHER_TEMPLATE_TYPE, voucherTemplateTypeList, theColNum, map);

		List<String> virtualStoreMerchantList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.VIRTUAL_STORE_MERCHANT, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_VIRTUAL_STORE_MERCHANT, virtualStoreMerchantList, theColNum, map);

		List<String> storageTypeList = SysParmUtil.getParamValueList(sysParmList, SysParmSegment.WH_ID_STORAGE_TYPE_MAPPING, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STORAGE_TYPE, storageTypeList, theColNum, map);

		List<String> warehouseList = storeWarehouseRepository.findOldDropListByStoreId(storeId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WAREHOUSE, warehouseList, theColNum, map);


		List<SysParmDo> productReadyMethodList = searchProductReadyMethodByContractId(contractId, buCode);
		List<String> validationProductReadyMethodList = productReadyMethodList.stream()
			.map(SysParmUtil::getCodeAndShortDescString)
			.collect(Collectors.toList());
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRO_READY_METHOD, validationProductReadyMethodList, theColNum, map);

		List<String> pickupDaysList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PICKUP_DAYS, pickupDaysList, theColNum, map);

		List<String> brandNameEng = brandRepository.findListByStatusAndBuId("A", buId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_BRAND_NAME_ENG, brandNameEng, theColNum, map);

		List<String> payTermsList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PAYMENT_TERM, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PAY_TERMS, payTermsList, theColNum, map);

		List<String> sizeSystemList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_SIZE_SYSTEM, sizeSystemList, theColNum, map);

		List<String> colorFamiliesList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_COLOR_FAMILIES, colorFamiliesList, theColNum, map);

		List<String> termNameList = contractProdTermsRepository.getContractTermsNameList(contractId, storeId);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_TERM_NAME, termNameList, theColNum, map);

		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_FIELD, setProductFieldLovSheetValue(sysParmList, platformId, contractId), theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_STYLE, ExcelUtil.STYLE_LIST, theColNum, map);
	}

	private void setValidations(Workbook workbook, Sheet dataSheet, Num theColNum, Map<String, Map<String, Object>> map, Integer platformId, Integer contractId) {
		List<String> parentSegments = Arrays.stream(HktvUploadProductExcelReportEnum.values())
			.filter(columnEnum -> null == columnEnum.getValidationName() && null != columnEnum.getParent())
			.map(HktvUploadProductExcelReportEnum::getParentSegment)
			.collect(Collectors.toList());
		String contractType = contractRepository.findMainContractTypeInContract(contractId);
		List<SysParmDo> sysParmDoList = !MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)
			? sysParmRepository.findChildBySegmentsAndPlatformId(parentSegments, platformId).stream()
				.filter(sysParmDo -> !SysParmCodeEnum.PRODUCT_FIELD_MAINLAND_CODE_SET.contains(sysParmDo.getParentCode()))
				.collect(Collectors.toList())
			: sysParmRepository.findChildBySegmentsAndPlatformId(parentSegments, platformId);

		for (HktvUploadProductExcelReportEnum columnEnum : HktvUploadProductExcelReportEnum.values()) {
			if (null != columnEnum.getValidationName() && null != columnEnum.getParent()) {
				createVLOOKUP(PRODUCT_SHEET_NAME, workbook, columnEnum.getValidationName(), columnEnum.getParent().getColumnNumber(), columnEnum.getColumnNumber(), map);//連動下拉選單
			} else if (null != columnEnum.getValidationName()) {
				setXSSFValidation(dataSheet, columnEnum.getColumnNumber(), map.get(columnEnum.getValidationName()));//设置一级菜单
			} else if (null != columnEnum.getParent()) {
				createXSSFName(PRODUCT_SHEET_NAME, workbook, columnEnum.getParentSegment(), columnEnum.getParent().getColumnNumber(), columnEnum.getColumnNumber(), theColNum, map, false, platformId, sysParmDoList);//设置二级菜单
			}
		}
	}

	private Workbook setBodyColumn(Workbook workbook, List<HktvProductFieldDto> productList, List<SysParmDo> sysParmList) {
		long startTime = System.currentTimeMillis();
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook,100);

		BusinessPlatformViewDo businessPlatformViewDo =
			busUnitRepository.findByBusinessCode("HKTV").orElseThrow(() -> new SystemException("Business or platform doesn't exist"));
		Integer platformId = businessPlatformViewDo.getPlatformId();

		Map<String, String> manufacturedCountryMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COUNTRY_OF_ORIGIN, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmDo::getShortDesc, (oldValue, newValue) -> oldValue));

		Map<String, String> colorEnglishMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.COLOR, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));

		Map<String, String> voucherTypeMap = SysParmUtil.filterSysParmBySegmentAndPlatformId(sysParmList, SysParmSegment.VOUCHER_TYPE, platformId)
			.stream()
			.collect(Collectors.toMap(SysParmDo::getCode, SysParmUtil::getCodeAndShortDescString, (oldValue, newValue) -> oldValue));
		Map<String, String> sizeSystemMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE_SYSTEM, platformId);
		Map<String, String> sizeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.SIZE, platformId);
		Map<String, String> colorFamiliesMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.COLOR_FAMILIES, platformId);
		Map<String, String> productFieldMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD, platformId);
		Map<String, String> productFieldValueMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_FIELD_VALUE, platformId);
		Map<String, String> packBoxTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
		Map<String, String> productReadyMethodMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_METHOD, platformId);
		Map<String, String> voucherTemplateTypeMap = getDirtyDataVoucherTemplateTypeMap(addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_TEMPLATE_TYPE, platformId));
		Map<String, String> voucherDisplayTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.VOUCHER_DISPLAY_TYPE, platformId);
		Map<String, String> currencyMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.CURRENCY, platformId);
		Map<String, String> productReadyDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PRODUCT_READY_DAYS, platformId);
		Map<String, String> pickupDaysMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PICKUP_DAYS, platformId);

		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleWithTimeStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR_M, tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle dateSimpleStyle = ExcelUtil.createDateFormatStyle(DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);

		productList.forEach(product -> {
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_CODE.getColumnNumber(), product.getProductId());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_CODE.getColumnNumber(), product.getSkuId());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_HKTV_CAT_LIST.getColumnNumber(), product.getProductTypeCode());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_HKTV_CAT_CODE_PRI.getColumnNumber(), product.getPrimaryCategoryCode());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.BRAND_ID.getColumnNumber(), product.getBrandName());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_READY_METHOD.getColumnNumber(), productReadyMethodMap.get(product.getProductReadyMethod()));
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ID.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_STATUS.getColumnNumber(), OnlineStatusEnum.ONLINE.name());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WAREHOUSE.getColumnNumber(), product.getWarehouse());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.TERM_NAME.getColumnNumber(), product.getTermName());
			setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.IS_PRIMARY_SKU.getColumnNumber(), product.getIsPrimarySku());

			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME.getColumnNumber(), product.getSkuNameEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME_TCHI.getColumnNumber(), product.getSkuNameCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_NAME_SCHI.getColumnNumber(), product.getSkuNameSc());
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_TITLE_HKTV_EN.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_TITLE_HKTV_CH.getColumnNumber(), "");

			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_EN.getColumnNumber(), product.getSkuShortDescriptionEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_CH.getColumnNumber(), product.getSkuShortDescriptionCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_S_DESC_HKTV_SC.getColumnNumber(), product.getSkuShortDescriptionSc());
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_TITLE_HKTV_EN.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_TITLE_HKTV_CH.getColumnNumber(), "");

			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_EN.getColumnNumber(), product.getSkuLongDescriptionEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_CH.getColumnNumber(), product.getSkuLongDescriptionCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SKU_L_DESC_HKTV_SC.getColumnNumber(), product.getSkuLongDescriptionSc());
			//photo not set
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MAIN_PHOTO_HKTVMALL.getColumnNumber(), product.getMainPhoto());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MAIN_VIDEO.getColumnNumber(), product.getMainVideo());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OTHER_PRODUCT_PHOTO_HKTVMALL.getColumnNumber(), joinNonEmptyStrings(product.getOtherProductPhoto()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OTHER_PHOTO_HKTVMALL.getColumnNumber(), product.getOtherPhoto());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ADVERTISING_PHOTO_HKTVMALL.getColumnNumber(), product.getAdvertisingPhoto());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK.getColumnNumber(), product.getVideoLink1());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN.getColumnNumber(), product.getVideoLink1TextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH.getColumnNumber(), product.getVideoLink1TextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC.getColumnNumber(), product.getVideoLink1TextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK2.getColumnNumber(), product.getVideoLink2());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN2.getColumnNumber(), product.getVideoLink2TextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH2.getColumnNumber(), product.getVideoLink2TextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC2.getColumnNumber(), product.getVideoLink2TextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK3.getColumnNumber(), product.getVideoLink3());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN3.getColumnNumber(), product.getVideoLink3TextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH3.getColumnNumber(), product.getVideoLink3TextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC3.getColumnNumber(), product.getVideoLink3TextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK4.getColumnNumber(), product.getVideoLink4());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN4.getColumnNumber(), product.getVideoLink4TextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH4.getColumnNumber(), product.getVideoLink4TextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC4.getColumnNumber(), product.getVideoLink4TextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK5.getColumnNumber(), product.getVideoLink5());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_EN5.getColumnNumber(), product.getVideoLink5TextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_CH5.getColumnNumber(), product.getVideoLink5TextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIDEO_LINK_SC5.getColumnNumber(), product.getVideoLink5TextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MANU_COUNTRY.getColumnNumber(), manufacturedCountryMap.get(product.getManufacturedCountry()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_FAMILIAR.getColumnNumber(), colorFamiliesMap.get(product.getColourFamilies()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_EN.getColumnNumber(), colorEnglishMap.get(product.getColorEn()));
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COLOR_CH.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SIZE_SYSTEM.getColumnNumber(), sizeSystemMap.get(product.getSizeSystem()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SIZE.getColumnNumber(), sizeMap.get(product.getSize()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CURRENCY_CODE.getColumnNumber(), currencyMap.get(product.getCurrency()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.COST.getColumnNumber(), convertDecimalToDouble(product.getCost()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.ORIGINAL_PRICE.getColumnNumber(), convertDecimalToDouble(product.getOriginalPrice()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SELLING_PRICE.getColumnNumber(), convertDecimalToDouble(product.getSellingPrice()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MALL_DOLLAR.getColumnNumber(), convertDecimalToDouble(product.getMallDollar()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MALL_DOLLAR_VIP.getColumnNumber(), convertDecimalToDouble(product.getVipMallDollar()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.USER_MAX.getColumnNumber(), zeroToNull(product.getUserMax()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.STYLE.getColumnNumber(), product.getStyle());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT.getColumnNumber(), product.getDiscountTextEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT_ZH.getColumnNumber(), product.getDiscountTextCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DISCOUNT_TEXT_SC.getColumnNumber(), product.getDiscountTextSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_EN.getColumnNumber(), product.getPackingSpecEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_CH.getColumnNumber(), product.getPackingSpecCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_SPEC_SC.getColumnNumber(), product.getPackingSpecSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_HEIGHT.getColumnNumber(), convertDecimalToDouble(product.getPackingHeight()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_LENGTH.getColumnNumber(), convertDecimalToDouble(product.getPackingLength()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_DEPTH.getColumnNumber(), convertDecimalToDouble(product.getPackingDepth()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_DIMENSION_UNIT.getColumnNumber(), product.getPackingDimensionUnit());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WEIGHT.getColumnNumber(), convertDecimalToDouble(product.getWeight()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WEIGHT_UNIT.getColumnNumber(), product.getWeightUnit());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PACK_BOX_TYPE.getColumnNumber(), packBoxTypeMap.get(product.getPackingBoxType()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_HEIGHT.getColumnNumber(), zeroToNull(product.getCartonHeight()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_DEPTH.getColumnNumber(), zeroToNull(product.getCartonDepth()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CARTON_LENGTH.getColumnNumber(), zeroToNull(product.getCartonLength()));

			setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVISIBLE_FLAG.getColumnNumber(), product.getInvisibleFlag());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.BARCODE.getColumnNumber(), product.getBarcode());
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, com.shoalter.mms_product_api.util.DateUtil.ISO8601_UTC_WITHOUT_SSSZ, com.shoalter.mms_product_api.util.DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), HktvUploadProductExcelReportEnum.FEATURE_START_TIME.getColumnNumber(), product.getFeatureStartDate());
			setDateCellValue(dataSheet, dateSimpleWithTimeStyle, com.shoalter.mms_product_api.util.DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY_HOUR, rowIndex.get(), HktvUploadProductExcelReportEnum.FEATURE_END_TIME.getColumnNumber(), product.getFeatureEndDate());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_TYPE.getColumnNumber(), voucherTypeMap.get(product.getVoucherType()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_DISPLAY_TYPE.getColumnNumber(), voucherDisplayTypeMap.get(product.getVoucherDisplayType()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VOUCHER_TEMPLATE_TYPE.getColumnNumber(), voucherTemplateTypeMap.get(product.getVoucherTemplateType()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.EXPIRY_TYPE.getColumnNumber(), product.getExpiryType());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.CONSUMABLE.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRIORITY.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD1.getColumnNumber(), productFieldMap.get(product.getField1()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE1.getColumnNumber(), productFieldValueMap.get(product.getValue1()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD2.getColumnNumber(), productFieldMap.get(product.getField2()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE2.getColumnNumber(), productFieldValueMap.get(product.getValue2()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FIELD3.getColumnNumber(), productFieldMap.get(product.getField3()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VALUE3.getColumnNumber(), productFieldValueMap.get(product.getValue3()));
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY, rowIndex.get(), HktvUploadProductExcelReportEnum.REDEEM_START_DATE.getColumnNumber(), product.getRedeemStartDate());
			setDateCellValue(dataSheet, dateSimpleStyle, DateUtil.ISO8601_UTC_WITHOUT_SSSZ, DateUtil.DATE_FORMAT_YEAR_MONTH_DAY,  rowIndex.get(), HktvUploadProductExcelReportEnum.REDEEM_END_DATE.getColumnNumber(), product.getFixedRedemptionDate());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.UPON_PURCHASE_DATE.getColumnNumber(), product.getUponPurchaseDate());
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_TITLE_EN.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_TITLE_CH.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_EN.getColumnNumber(), product.getFinePrIntegerEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_CH.getColumnNumber(), product.getFinePrIntegerCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.FINE_PRINT_SC.getColumnNumber(), product.getFinePrIntegerSc());
			setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.REMOVAL_SERVICES.getColumnNumber(), product.getNeedRemovalServices());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.GOODS_TYPE.getColumnNumber(), product.getGoodsType());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_PERIOD_UNIT.getColumnNumber(), product.getWarrantyPeriodUnit());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_PERIOD.getColumnNumber(), zeroToNull(product.getWarrantyPeriod()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_EN.getColumnNumber(), product.getWarrantySupplierEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_CH.getColumnNumber(), product.getWarrantySupplierCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_SUPPLIER_SC.getColumnNumber(), product.getWarrantySupplierSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_EN.getColumnNumber(), product.getServiceCentreAddressEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_CH.getColumnNumber(), product.getServiceCentreAddressCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_ADDRESS_SC.getColumnNumber(), product.getServiceCentreAddressSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_EMAIL.getColumnNumber(), product.getServiceCentreEmail());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.SERVICE_CENTRE_CONTACT.getColumnNumber(), product.getServiceCentreContact());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_EN.getColumnNumber(), product.getWarrantyRemarkEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_CH.getColumnNumber(), product.getWarrantyRemarkCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.WARRANTY_REMARK_SC.getColumnNumber(), product.getWarrantyRemarkSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_EN.getColumnNumber(), product.getInvoiceRemarkEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_CH.getColumnNumber(), product.getInvoiceRemarkCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.INVOICE_REMARKS_SC.getColumnNumber(), product.getInvoiceRemarkSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.RETURN_DAYS.getColumnNumber(), product.getReturnDays());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRODUCT_READY_DAYS.getColumnNumber(), productReadyDaysMap.get(product.getProductReadyDays()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PICKUP_DAYS.getColumnNumber(), pickupDaysMap.get(product.getPickupDays()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PICKUP_TIMESLOT.getColumnNumber(), product.getPickupTimeslot());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.OVERSEA_DELIVERY.getColumnNumber(), product.getOverseaDelivery());
			setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.URGENT.getColumnNumber(), product.getUrgent());
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_TITLE_EN.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_TITLE_CH.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_DETAILS_EN.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_DETAILS_CH.getColumnNumber(), "");
			// excel column hide
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.DELIVERY_COMPLETION_DAYS.getColumnNumber(), "");
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.MINIMUM_SHELF_LIFE.getColumnNumber(), zeroToNull(product.getMinimumShelfLife()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.VIRTUAL_STORE.getColumnNumber(), product.getVirtualStore());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.RM_CODE.getColumnNumber(), product.getRmCode());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.STORAGE_TYPE.getColumnNumber(), product.getStorageType());
			setYseNoCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PRE_SELL_FRUIT.getColumnNumber(), product.getPreSellFruit());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.PHYSICAL_STORE.getColumnNumber(), product.getPhysicalStore());

			String affiliateUrl = StringUtils.hasText(product.getAffiliateUrl())
				? hktvmallExternalBaseUrl + hktvmallExternalAffiliateEndpoint + product.getAffiliateUrl()
				: "";
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), HktvUploadProductExcelReportEnum.AFFILIATE_URL.getColumnNumber(), affiliateUrl);
			rowIndex.addAndGet(1);
		});


		long endTime = System.currentTimeMillis();
		log.info("Time taken by generate affiliate product for hktv upload template {} milliseconds", (endTime - startTime));
		return tempWorkbook;
	}

	@Override
	public String getFileName() {
		return null;
	}
}
