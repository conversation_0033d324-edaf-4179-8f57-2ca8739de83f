package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallProductDataProjection;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueRequestData;
import com.shoalter.mms_product_api.service.mms_product.BatchExportAndSaveAllService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreateTmallVariantOptionValueService {

	private final BatchExportAndSaveAllService batchExportAndSaveAllService;
	private final BuExportHistoryRepository buExportHistoryRepository;
	private final TokenHelper tokenHelper;
	private final TmallVariantOptionValueHelper tmallVariantOptionValueHelper;


	@Async
	public void start(Integer historyId) {

		log.info("CreateTmallVariantOptionValueService start, historyId: {}", historyId);

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		try {
			List<TmallProductDataProjection> tmallDataProjections =
				buExportHistoryRepository.findTmallProductDataByIdAndStatus(
					historyId,
					ExportStatusEnum.SUCCESS.name()
				);

			List<Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>>> needSaveAndCreateList = new ArrayList<>();

			batchExportAndSaveAllService.generateHktvProductFieldDtoList(
					tmallDataProjections,
					needSaveAndCreateList);

			ResponseDto<Void> saveOptionValueResult = batchExportAndSaveAllService.saveOptionValue(
				tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM),
				historyId,
				null,
				needSaveAndCreateList);

			if (StatusCodeEnum.SUCCESS.getCode() == saveOptionValueResult.getStatus()) {
				completeJobSuccessfully(historyId,stopWatch);
				return;
			}
			completeJobWithFailure(historyId,stopWatch);
		} catch (Exception e) {

			log.error("CreateTmallVariantOptionValueService error, stackTrace: {}",
				ExceptionUtils.getStackTrace(e));
			completeJobWithFailure(historyId,stopWatch);
		}
	}

	private void completeJobSuccessfully(Integer historyId, StopWatch stopWatch){
		tmallVariantOptionValueHelper.markTmallExportHistoryAsCompleteById(historyId);
		logExecutionTime(historyId,stopWatch);
	}

	private void completeJobWithFailure(Integer historyId, StopWatch stopWatch){
		tmallVariantOptionValueHelper.markTmallExportHistoryAsFailedById(historyId);
		logExecutionTime(historyId,stopWatch);
	}

	private void logExecutionTime(Integer historyId, StopWatch stopWatch) {
		stopWatch.stop();
		log.info(
			"CreateTmallVariantOptionValueService finished, historyId: {}, total use time: {} seconds",
			historyId, stopWatch.getTotalTimeSeconds());
	}
}
