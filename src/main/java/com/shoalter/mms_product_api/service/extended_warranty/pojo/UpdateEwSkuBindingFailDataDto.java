package com.shoalter.mms_product_api.service.extended_warranty.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateEwSkuBindingFailDataDto {
	private String message;
	@JsonProperty("electronic_product_store_sku_id")
	@SerializedName("electronic_product_store_sku_id")
	private String electronicProductStoreSkuId;
	@JsonProperty("extended_warranty_product_store_sku_id")
	@SerializedName("extended_warranty_product_store_sku_id")
	private String extendedWarrantyProductStoreSkuId;
}
