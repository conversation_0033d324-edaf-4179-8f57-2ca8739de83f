package com.shoalter.mms_product_api.service.extended_warranty.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class EwBindingOverviewResultDetailDto {
	@JsonProperty("store_id")
	@SerializedName("store_id")
	private String storeCode;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuCode;
	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	private String skuNameCh;
	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	private String skuNameEn;
	@JsonProperty("extended_warranty_store_id")
	@SerializedName("extended_warranty_store_id")
	private String extendedWarrantyStoreCode;
	@JsonProperty("extended_warranty_sku_id")
	@SerializedName("extended_warranty_sku_id")
	private String extendedWarrantySkuCode;
	@JsonProperty("extended_warranty_sku_name_ch")
	@SerializedName("extended_warranty_sku_name_ch")
	private String extendedWarrantySkuNameCh;
	@JsonProperty("extended_warranty_sku_name_en")
	@SerializedName("extended_warranty_sku_name_en")
	private String extendedWarrantySkuNameEn;
}
