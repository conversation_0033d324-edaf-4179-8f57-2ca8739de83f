package com.shoalter.mms_product_api.service.extended_warranty.pojo;

import com.shoalter.mms_product_api.service.base.pojo.ProductMasterBasePageResultDto;
import lombok.Data;

import java.util.List;

@Data
public class EwBindingsOverviewDto extends ProductMasterBasePageResultDto {
    private List<EwBindingInfoDto> bindingList;

	public static EwBindingsOverviewDto convertFromProductOverviewResultDto(EwBindingOverviewResultDto ewBindingOverviewResultDto) {
		EwBindingsOverviewDto ewBindingsOverviewDto = new EwBindingsOverviewDto();
		ewBindingsOverviewDto.setEmpty(ewBindingOverviewResultDto.getEmpty());
		ewBindingsOverviewDto.setFirst(ewBindingOverviewResultDto.getFirst());
		ewBindingsOverviewDto.setLast(ewBindingOverviewResultDto.getLast());
		ewBindingsOverviewDto.setNumber(ewBindingOverviewResultDto.getNumber());
		ewBindingsOverviewDto.setPageable(ewBindingOverviewResultDto.getPageable());
		ewBindingsOverviewDto.setNumberOfElements(ewBindingOverviewResultDto.getNumberOfElements());
		ewBindingsOverviewDto.setSort(ewBindingOverviewResultDto.getSort());
		ewBindingsOverviewDto.setTotalPages(ewBindingOverviewResultDto.getTotalPages());
		ewBindingsOverviewDto.setTotalElements(ewBindingOverviewResultDto.getTotalElements());
		ewBindingsOverviewDto.setSize(ewBindingOverviewResultDto.getSize());
		return ewBindingsOverviewDto;
	}
}
