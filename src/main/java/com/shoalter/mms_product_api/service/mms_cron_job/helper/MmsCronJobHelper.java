package com.shoalter.mms_product_api.service.mms_cron_job.helper;

import com.shoalter.mms_product_api.dao.repository.mms_cron_job.pojo.MmsCronJobDo;
import com.shoalter.mms_product_api.dao.repository.mms_cron_job.pojo.MmsCronJobRepository;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.ActiveEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobNameEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobStatusEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsServiceNameEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.pojo.MmsCronJobData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class MmsCronJobHelper {

	private final MmsCronJobRepository mmsCronJobRepository;

	public MmsCronJobData canProcessCronJob(MmsCronJobNameEnum cronJobNameEnum) {
		Optional<MmsCronJobDo> mmsCronJobDoOptional = mmsCronJobRepository.findByServiceNameAndJobName(MmsServiceNameEnum.MMS_PRODUCT_API, cronJobNameEnum);
		if (mmsCronJobDoOptional.isEmpty()) {
			log.info("cron job {} not exist", cronJobNameEnum);
			return MmsCronJobData.builder()
				.isCanProcessCronJob(true)
				.mmsCronJobName(cronJobNameEnum)
				.build();
		}
		MmsCronJobDo mmsCronJobDo = mmsCronJobDoOptional.get();
		log.info("cron job name :{}, activeInd :{}, status: {}", mmsCronJobDo.getJobName(), mmsCronJobDo.getActiveInd(), mmsCronJobDo.getStatus());
		boolean isTimeOut = isCronJobTimeOut(mmsCronJobDo);
		boolean isCanProcess = ActiveEnum.ENABLE.getValue() == mmsCronJobDo.getActiveInd()
			&& (MmsCronJobStatusEnum.PROCESSING != mmsCronJobDo.getStatus() || isTimeOut);

		return MmsCronJobData.builder()
			.mmsCronJobDo(mmsCronJobDo)
			.isCanProcessCronJob(isCanProcess)
			.mmsCronJobName(cronJobNameEnum)
			.build();
	}

	public boolean isCronJobTimeOut(MmsCronJobDo job) {
		boolean isTimeOut = job.getTimeOut() != null &&
			job.getLastUpdateDate() != null &&
			LocalDateTime.now().isAfter(job.getLastUpdateDate().plusMinutes(job.getTimeOut()));

		if (isTimeOut) {
			log.warn("cron job {} is timeout, lastUpdate: {}, timeout: {} min", job.getJobName(), job.getLastUpdateDate(), job.getTimeOut());
		}
		return isTimeOut;
	}

	@Transactional
	public MmsCronJobData createOrUpdateProcessingCronJob(MmsCronJobData mmsCronJobData, Integer timeOutMinutes) {
		MmsCronJobDo job = Optional.ofNullable(mmsCronJobData.getMmsCronJobDo())
			.orElseGet(() -> {
				MmsCronJobDo newJob = new MmsCronJobDo();
				newJob.setActiveInd(ActiveEnum.ENABLE.getValue());
				newJob.setServiceName(MmsServiceNameEnum.MMS_PRODUCT_API);
				newJob.setJobName(mmsCronJobData.getMmsCronJobName());
				newJob.setTimeOut(timeOutMinutes);
				return newJob;
			});

		job.setStatus(MmsCronJobStatusEnum.PROCESSING);
		mmsCronJobData.setMmsCronJobDo(mmsCronJobRepository.save(job));
		return mmsCronJobData;
	}

	@Transactional
	public void updateCronJobStatus(MmsCronJobData mmsCronJobData, MmsCronJobStatusEnum statusEnum) {
		log.info("cron job {} update status: {}", mmsCronJobData.getMmsCronJobName(), statusEnum);
		MmsCronJobDo mmsCronJobDo = mmsCronJobData.getMmsCronJobDo();
		if (mmsCronJobDo == null) {
			log.warn("cron job is null, cannot update status: {}", statusEnum);
			return;
		}

		mmsCronJobDo.setStatus(statusEnum);
		mmsCronJobRepository.save(mmsCronJobDo);
	}
}
