package com.shoalter.mms_product_api.service.hybris.pojo;

import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildSkuDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HybrisBundleChildSkuDto {

	//required
	private String skuCode;

	//required, > 0
	private Integer qty;

	//not required when created (default 0)
	private Integer ceilingQty;

	//not required when created (default false)
	private Boolean isLoop;

	public static HybrisBundleChildSkuDto generateHybrisBundleChildSkuDto(BundleChildSkuDto bundleChildSkuDto) {
		return HybrisBundleChildSkuDto.builder()
			.skuCode(bundleChildSkuDto.getStorefrontStoreCode() + StringUtil.PRODUCT_SEPARATOR + bundleChildSkuDto.getChildSkuId())
			.qty(bundleChildSkuDto.getChildSkuSettingQuantity())
			.isLoop(bundleChildSkuDto.getIsLoop())
			.ceilingQty(bundleChildSkuDto.getChildCeilingQuantity())
			.build();
	}
}
