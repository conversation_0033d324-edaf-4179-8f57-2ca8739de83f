package com.shoalter.mms_product_api.service.hybris.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.HybrisStatusEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.properties.HybrisProperties;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.SyncResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.SyncService;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisBaseResultStatusDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisBundleCreateDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisBundleEditRequestDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateBundleResultDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueMainRequestData;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueMainResponseData;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisEditBundleResultDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateEwSkuBindingDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateEwSkuBindingResultDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateMainlandSamePriceRequestSkuDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateMainlandSamePriceMainResultDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpsertEverutsBuyerResultDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiCreateEverutsBuyerMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.HybrisCreateDiscountRuleResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HybrisResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HybrisSaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisCreatePromotionalDiscountRuleDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.response.EwSkuBindingHybrisAndPmCombineResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class HybrisHelper {

	public static final String SERVICE_NAME = "hybris";

	private final HybrisProperties hktvProperties;
	private final HttpRequestHelper httpRequestHelper;
	private final TokenHelper tokenHelper;
	private final Gson gson;

    public ResponseDto<Void> requestCreateBundle(UserDto userDto, HybrisBundleCreateDto requestData, String identifier) {
			HybrisCreateBundleResultDto requestResult = httpRequestHelper.requestForBody(
				HttpRequestDto.<HybrisBundleCreateDto, HybrisCreateBundleResultDto>builder()
					.serviceName(SERVICE_NAME)
					.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getCreateBundle())
					.method(HttpMethod.POST)
					.customHeaders(generateHeaders())
					.body(requestData)
					.resultClass(HybrisCreateBundleResultDto.class)
					.user(userDto)
					.identifier(identifier)
					.systemErrorCode(ErrorMessageTypeCode.HYBRIS_POST_BUNDLE_ERROR)
					.build());

		if (requestResult == null) {
			return ResponseDto.fail(List.of("request hybris create bundle fail"));
		}

		if (!requestResult.getStatus().getCode().equalsIgnoreCase(StatusCodeEnum.SUCCESS.name())) {
			return ResponseDto.fail(List.of("hybris response error: " + requestResult.getStatus().getMessage()));
		}

		return ResponseDto.success(null);
    }

	public ResponseDto<Void> requestUpdateBundle(UserDto userDto, HybrisBundleEditRequestDto requestData, String identifier) {
		HybrisEditBundleResultDto requestResult =  httpRequestHelper.requestForBody(HttpRequestDto.<HybrisBundleEditRequestDto, HybrisEditBundleResultDto>builder()
				.serviceName(SERVICE_NAME)
				.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getUpdateBundle())
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders())
				.body(requestData)
				.resultClass(HybrisEditBundleResultDto.class)
				.user(userDto)
				.identifier(identifier)
				.systemErrorCode(ErrorMessageTypeCode.HYBRIS_POST_BUNDLE_ERROR)
				.build());

		if (requestResult == null) {
			return ResponseDto.fail(List.of("request hybris edit bundle fail"));
		}

		if (requestResult.getStatus().getCode().equalsIgnoreCase(StatusCodeEnum.FAIL.name())) {
			return ResponseDto.fail(List.of("hybris response error: " + requestResult.getStatus().getMessage()));
		}

		return ResponseDto.success(null);
	}

	public ResponseDto<List<EwSkuBindingHybrisAndPmCombineResponse>> requestUpdateEwProductBinding(UserDto userDto, HybrisUpdateEwSkuBindingDto requestData, String identifier) {
		HybrisUpdateEwSkuBindingResultDto requestResult =  httpRequestHelper.requestForBody(HttpRequestDto.<HybrisUpdateEwSkuBindingDto, HybrisUpdateEwSkuBindingResultDto>builder()
				.serviceName(SERVICE_NAME)
				.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getUpdateEwBinding())
				.method(HttpMethod.POST)
				.customHeaders(generateHeaders())
				.body(requestData)
				.resultClass(HybrisUpdateEwSkuBindingResultDto.class)
				.user(userDto)
				.identifier(identifier)
				.systemErrorCode(ErrorMessageTypeCode.HYBRIS_POST_BUNDLE_ERROR)
				.build());

		if (requestResult == null) {
			log.error("Request hybris update ew binding fail");
			return ResponseDto.fail(List.of("request hybris update ew binding fail"));
		}
		if (requestResult.getStatus() != null && requestResult.getStatus().getCode() != null && !requestResult.getStatus().getCode().equalsIgnoreCase(StatusCodeEnum.SUCCESS.name())) {
			log.error("Request hybris update ew binding fail error message : {}", requestResult.getStatus().getMessage());
			return ResponseDto.fail(List.of("Hybris response error: " + requestResult.getStatus().getMessage()));
		}

		return ResponseDto.success(EwSkuBindingHybrisAndPmCombineResponse.convertFromHybrisResponse(requestResult.getData()));
	}

	public ResponseDto<HybrisUpsertEverutsBuyerResultDto> requestUpsertEverutsBuyer(UserDto userDto, OapiCreateEverutsBuyerMainRequestData requestData, String identifier) {
		HybrisUpsertEverutsBuyerResultDto requestResult =  httpRequestHelper.requestForBody(HttpRequestDto.<OapiCreateEverutsBuyerMainRequestData, HybrisUpsertEverutsBuyerResultDto>builder()
				.serviceName(SERVICE_NAME)
				.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getUpsertEverutsBuyer())
				.method(HttpMethod.PUT)
				.customHeaders(generateOapiHeaders())
				.body(requestData)
				.resultClass(HybrisUpsertEverutsBuyerResultDto.class)
				.user(userDto)
				.identifier(identifier)
				.systemErrorCode(ErrorMessageTypeCode.HYBRIS_POST_EVERUTS_BUYER_ERROR)
				.build());

		if (requestResult == null) {
			log.error("Request hybris upsert everuts buyer");
			return ResponseDto.fail(List.of("request hybris upsert everuts buyer fail"));
		}
		if (requestResult.getStatus() != null && !requestResult.getStatus().equalsIgnoreCase(StatusCodeEnum.SUCCESS.name())) {
			log.error("Request hybris upsert everuts buyer fail error message : {}", requestResult.getResult());
			return ResponseDto.fail(List.of("Hybris response error: " + requestResult.getResult()));
		}

		return ResponseDto.success(requestResult);
	}

	public ResponseDto<HybrisCreateUpdateOptionValueMainResponseData> requestCreateUpdateOptionValue(UserDto userDto, HybrisCreateUpdateOptionValueMainRequestData requestData, String identifier) {
		HybrisCreateUpdateOptionValueMainResponseData requestResult = httpRequestHelper.requestForBody(HttpRequestDto.<HybrisCreateUpdateOptionValueMainRequestData, HybrisCreateUpdateOptionValueMainResponseData>builder()
			.serviceName(SERVICE_NAME)
			.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getCreateUpdateOptionValue())
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders())
			.body(requestData)
			.resultClass(HybrisCreateUpdateOptionValueMainResponseData.class)
			.user(userDto)
			.identifier(identifier)
			.systemErrorCode(ErrorMessageTypeCode.HYBRIS_POST_OPTION_VALUE_ERROR)
			.build());

		if (requestResult == null) {
			log.error("Request hybris create update option value fail");
			return ResponseDto.fail(List.of("request hybris create update option value fail"));
		}

		HybrisBaseResultStatusDto status = requestResult.getStatus();
		boolean notSuccess = Optional.ofNullable(status)
			.map(HybrisBaseResultStatusDto::getCode)
			.map(code -> !code.equalsIgnoreCase(StatusCodeEnum.SUCCESS.name()))
			.orElse(false);

		if (notSuccess) {
			log.error("Request hybris create update option value fail error message : {}", requestResult.getStatus().getMessage());
			return ResponseDto.fail(List.of("Hybris response error: " + requestResult.getStatus().getMessage()));
		}

		return ResponseDto.success(requestResult);
	}

	/**
	 * Request to update the mainland same price flag for products in Hybris
	 * Note: Maximum batch size is 1000 products per request.
	 */
	public ResponseDto<HybrisUpdateMainlandSamePriceMainResultDto> requestUpdateProductMainlandSamePrice(UserDto userDto, List<HybrisUpdateMainlandSamePriceRequestSkuDto> skuInfoList, String identifier) {
		log.info("Requesting Hybris to update mainland same price flag for {} products", skuInfoList.size());

		HybrisUpdateMainlandSamePriceMainResultDto requestResult = httpRequestHelper.requestForBody(HttpRequestDto.<List<HybrisUpdateMainlandSamePriceRequestSkuDto>, HybrisUpdateMainlandSamePriceMainResultDto>builder()
			.serviceName(SERVICE_NAME)
			.url(hktvProperties.getApiUrl() + hktvProperties.getEndpoint().getUpdateProductMainlandSamePrice())
			.method(HttpMethod.POST)
			.customHeaders(generateHeaders())
			.body(skuInfoList)
			.resultClass(HybrisUpdateMainlandSamePriceMainResultDto.class)
			.user(userDto)
			.identifier(identifier)
			.build());

		if (requestResult == null) {
			log.error("Request to Hybris to update mainland same price flag failed - null response");
			return ResponseDto.fail(List.of("Request to Hybris to update mainland same price flag failed - null response"));
		}

		if (requestResult.getStatus() == null || requestResult.getStatus().getCode() == null || !requestResult.getStatus().getCode().equalsIgnoreCase(StatusCodeEnum.SUCCESS.name())) {
			String errorMessage = requestResult.getStatus() != null ? requestResult.getStatus().getMessage() : "Unknown error";
			log.error("Request to Hybris to update mainland same price flag failed: {}", errorMessage);
			return ResponseDto.fail(List.of("Hybris response error: " + errorMessage));
		}

		return ResponseDto.success(requestResult);
	}

	public SyncResponseDto<HybrisSaveProductResultDto> requestCreateProduct(UserDto userDto,
		SaveHybrisProductRequestDto request, boolean isOpenApi) {
		return requestSaveProduct(userDto, hktvProperties.getEndpoint().getCreateProduct(), request,
			isOpenApi);
	}

	public SyncResponseDto<HybrisSaveProductResultDto> requestUpdateProduct(UserDto userDto,
		SaveHybrisProductRequestDto request, boolean isOpenApi) {
		return requestSaveProduct(userDto, hktvProperties.getEndpoint().getUpdateProduct(), request,
			isOpenApi);
	}

	private SyncResponseDto<HybrisSaveProductResultDto> requestSaveProduct(UserDto userDto,
		String saveProductEndpoint, SaveHybrisProductRequestDto request, boolean isOpenApi) {

		String requestString = gson.toJson(request);

		HybrisSaveProductResultDto requestResult = httpRequestHelper.requestForBody(
			HttpRequestDto.<String, HybrisSaveProductResultDto>builder()
				.serviceName(SERVICE_NAME)
				.url(hktvProperties.getApiUrl() + saveProductEndpoint)
				.method(HttpMethod.PUT)
				.customHeaders(isOpenApi ? generateOapiHeaders() : generateHeaders())
				.body(requestString)
				.resultClass(HybrisSaveProductResultDto.class)
				.user(userDto)
				.build());

		return buildSyncResponseFromHybrisResult(requestResult, saveProductEndpoint);
	}

	public SyncResponseDto<HybrisCreateDiscountRuleResultDto> requestCreateDiscountRule(
		UserDto userDto, SaveHybrisCreatePromotionalDiscountRuleDto request, boolean isOpenApi) {

		String requestString = gson.toJson(request);
		String endpoint = hktvProperties.getEndpoint().getCreateDiscountRule();

		HybrisCreateDiscountRuleResultDto requestResult = httpRequestHelper.requestForBody(
			HttpRequestDto.<String, HybrisCreateDiscountRuleResultDto>builder()
				.serviceName(SERVICE_NAME)
				.url(hktvProperties.getApiUrl() + endpoint)
				.method(HttpMethod.POST)
				.customHeaders(isOpenApi ? generateOapiHeaders() : generateHeaders())
				.body(requestString)
				.resultClass(HybrisCreateDiscountRuleResultDto.class)
				.user(userDto)
				.build());

		return buildSyncResponseFromHybrisResult(requestResult, endpoint);
	}

	private <T extends HybrisResultDto> SyncResponseDto<T> buildSyncResponseFromHybrisResult(
		T requestResult, String endpoint) {
		SyncResponseDto<T> syncResponseDto;
		List<String> errorMessages = new ArrayList<>();

		if (requestResult == null) {
			errorMessages.add("Call hybris response null, endpoint: " + endpoint);
			syncResponseDto = SyncResponseDto.from(ResponseDto.fail(errorMessages));
			syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.FALSE);
			return syncResponseDto;
		}

		if (HybrisStatusEnum.SUCCESS.getValue().equals(requestResult.getStatus())) {
			syncResponseDto = SyncResponseDto.from(ResponseDto.success(requestResult));
			syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.TRUE);
		} else {
			errorMessages.add(requestResult.getResult());
			syncResponseDto = SyncResponseDto.from(ResponseDto.generate(requestResult, errorMessages));
			syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.FALSE);
		}

		return syncResponseDto;
	}

	private HttpHeaders generateOapiHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Server-Token", tokenHelper.generateHybrisToken());
		headers.set("System-Name", "mms.product");
		headers.set("oapi", "true");
		return headers;
	}


	private HttpHeaders generateHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		return headers;
	}
}
