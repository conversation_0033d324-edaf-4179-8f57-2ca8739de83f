package com.shoalter.mms_product_api.service.partner_product_price.enums;

import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum TooniesSkuNotFoundColumnEnum implements TemplateInterface<TooniesSkuNotFoundColumnEnum> {
	STORE_CODE(0, "Store Code", null, null, null),
	SKU_ID(1, "SKU ID", null, null, null);

	private final Integer columnNumber;
	private final String columnName;
	private final String validationName;
	private final TooniesSkuNotFoundColumnEnum parent;
	private final String parentSegment;

	TooniesSkuNotFoundColumnEnum(Integer columnNumber, String columnName, String validationName, TooniesSkuNotFoundColumnEnum parent, String parentSegment) {
		this.columnNumber = columnNumber;
		this.columnName = columnName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}
}
