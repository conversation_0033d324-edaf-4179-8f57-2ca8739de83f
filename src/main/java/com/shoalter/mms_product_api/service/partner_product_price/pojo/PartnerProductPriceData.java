package com.shoalter.mms_product_api.service.partner_product_price.pojo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class PartnerProductPriceData {

	private String source;
	private Integer status;
	private BigDecimal exchangeRate;
	private String storefrontStoreCode;
	private String productCode;
	private String skuCode;
	private String storeSkuId;
	private BigDecimal chargePrice;
}
