package com.shoalter.mms_product_api.service.force_offline;

import com.shoalter.mms_product_api.asynctask.CheckRequestPMRecordProductTask;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.mapper.ProductMasterDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductForceOfflineDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * Service for handling force offline functionality
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ForceOfflineService {

	public static final String FORCE_OFFLINE_REPORT_PARTIAL_FILE_NAME = "ForceOffline_.xlsx";
	private final PermissionHelper permissionHelper;
	private final BatchEditHelper batchEditHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
    private final StoreRepository storeRepository;
    private final ProductMasterHelper productMasterHelper;
    private final MessageSource messageSource;
	private final ProductMasterDtoMapper productMasterDtoMapper;
	private final CheckRequestPMRecordProductTask checkRequestPMRecordProductTask;

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	public ResponseDto<ForceOfflineResponseDto> startSingleProcess(UserDto userDto, ForceOfflineRequestDto requestDto) {
		ResponseDto<ForceOfflineResponseDto> responseDto = SpringBeanProvider.getBean(ForceOfflineService.class).processing(userDto, requestDto);
		if (StatusCodeEnum.SUCCESS.getCode() == responseDto.getStatus()) {
			SpringBeanProvider.getBean(ForceOfflineService.class).callProductMasterAndUpdateRecordRow(responseDto.getData().getRecordId());
		}
		return responseDto;
	}

	public ResponseDto<Void> startBatchProcess(UserDto userDto, List<ForceOfflineRequestDto> list) {
		log.info("Batch processing force offline request with {} items, user: {}", list.size(), userDto);

		permissionHelper.checkForceOfflinePermission(userDto);

		if (CollectionUtils.isEmpty(list)) {
			return ResponseDto.fail(List.of("Request batch list is empty"));
		}

		BatchEditProductRequestDto<ProductForceOfflineDto> batchEditDtos = new BatchEditProductRequestDto<>();
		batchEditDtos.setFileName(FORCE_OFFLINE_REPORT_PARTIAL_FILE_NAME);
		batchEditDtos.setData(list.stream().map(ProductForceOfflineDto::map).collect(Collectors.toList()));

		batchEditHelper.createBatchEditRecord(userDto, batchEditDtos, SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE, ClientIpHolder.getClientIp());

		return ResponseDto.success(null);
	}

    /**
     * Process a single SKU for force offline or remove force offline
     */
	@Transactional
	public ResponseDto<ForceOfflineResponseDto> processing(UserDto userDto, ForceOfflineRequestDto requestDto) {
		try {
			log.info("Processing single force offline request: {}, user: {}", requestDto, userDto);

			// 0. Permission check
			permissionHelper.checkForceOfflinePermission(userDto);

			// 1. Validate request & collect errors
			List<String> errorMessageList = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(basicValidation(requestDto, errorMessageList, ConstantType.HKTV))) {
				return ResponseDto.fail(errorMessageList);
			}

			// find current SKU via product master
			String storeSkuId = getStoreSkuId(requestDto);
			ProductMasterResultDto productMasterProductResult = findProductFromPm(userDto, storeSkuId);
			if (CollectionUtils.isNotEmpty(validateProductResult(productMasterProductResult, storeSkuId))) {
				return ResponseDto.fail(errorMessageList);
			}

			// Validate force offline request
			List<String> forceOfflineErrorMessageList = validateForceOfflineRequest(requestDto);

			// 2. Create record for force offline
			SingleEditProductDto singleEditProductDto = generateForceOfflineSingleEditProductDto(requestDto, productMasterProductResult);
			Long recordId = createRecord(userDto, singleEditProductDto, forceOfflineErrorMessageList);

			// 3. Create response
			ForceOfflineResponseDto responseDto = new ForceOfflineResponseDto();
			responseDto.setRecordId(recordId);
			log.info("Create Force offline record with recordId: {}, storeSkuId: {}, forceOffline: {}, caseNumber: {}", recordId, storeSkuId, requestDto.getSkuStatus(), requestDto.getCaseNumber());

			return ResponseDto.<ForceOfflineResponseDto>builder()
				.status(StatusCodeEnum.SUCCESS.getCode())
				.errorMessageList(forceOfflineErrorMessageList)
				.data(responseDto)
				.build();
		} catch (Exception e) {
			log.error("Error force offline for single processing, request: {}", requestDto, e);
			return ResponseDto.fail(null);
		}
	}

	private static String getStoreSkuId(ForceOfflineRequestDto requestDto) {
		return requestDto.getStorefrontStoreCode() + StringUtil.PRODUCT_SEPARATOR + requestDto.getSkuCode();
	}

	private List<String> basicValidation(ForceOfflineRequestDto requestDto, List<String> errorMessageList, String buCode) {
		if (requestDto.getStorefrontStoreCode() == null || requestDto.getStorefrontStoreCode().isEmpty()) {
			errorMessageList.add(messageSource.getMessage("message187", null, Locale.getDefault()));
		}

		if (requestDto.getSkuCode() == null || requestDto.getSkuCode().isEmpty()) {
			errorMessageList.add(messageSource.getMessage("message170", null, Locale.getDefault()));
		}

		// Store existence check
		boolean storeExists = storeRepository.findByStorefrontStoreCodeAndBuCode(requestDto.getStorefrontStoreCode(), buCode).isPresent();
		if (!storeExists) {
			errorMessageList.add(messageSource.getMessage("message134", null, Locale.getDefault()));
		}

		return errorMessageList;
	}

	private SingleEditProductDto generateForceOfflineSingleEditProductDto(ForceOfflineRequestDto requestDto, ProductMasterResultDto productMasterProductResult) {
		// set update fields based on requestDto: force_offline, case_number
		boolean forceOffline = ForceOfflineStatusEnum.FORCE_OFFLINE.getStatus().equalsIgnoreCase(requestDto.getSkuStatus());

		productMasterProductResult.getAdditional().getHktv().setForceOffline(forceOffline);
		productMasterProductResult.getAdditional().getHktv().setCaseNumber(requestDto.getCaseNumber());

		// set online status based on forceOffline
		if (forceOffline) {
			productMasterProductResult.getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
		}

		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDtoMapper.toProductMasterDto(productMasterProductResult));
		return singleEditProductDto;
	}

	protected Long createRecord(UserDto userDto, SingleEditProductDto singleEditProductDto, List<String> errorMessageList) {
		Integer merchantId = singleEditProductDto.getProduct().getMerchantId();
		String clientIp = ClientIpHolder.getClientIp();

		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(
			userDto,
			merchantId,
			SaveProductType.SINGLE_EDIT_PRODUCT_FORCE_OFFLINE,
			String.format(SaveProductRecordHelper.FORCE_OFFLINE_PRODUCT_FILE_NAME, merchantId, System.currentTimeMillis()),
			SaveProductStatus.WAIT_START,
			clientIp);

		SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(
			saveProductRecordDo.getId(),
			singleEditProductDto,
			SaveProductStatus.WAIT_START,
			null);

		log.info("Create Single Force offline record row with recordId: {}, rowId: {}, storeSkuId: {}, forceOffline: {}, caseNumber: {}, onlineStatus: {}",
			saveProductRecordDo.getId(), row.getId(),
			singleEditProductDto.getProduct().getAdditional().getHktv().getStoreSkuId(),
			singleEditProductDto.getProduct().getAdditional().getHktv().getForceOffline(),
			singleEditProductDto.getProduct().getAdditional().getHktv().getCaseNumber(),
			singleEditProductDto.getProduct().getAdditional().getHktv().getOnlineStatus());

		if (CollectionUtils.isNotEmpty(errorMessageList)) {
			row.setErrorMessage(StringUtil.generateErrorMessage(errorMessageList));
			row.setStatus(SaveProductStatus.FAIL);
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		}

		return saveProductRecordDo.getId();
	}

	protected List<String> validateProductResult(ProductMasterResultDto productMasterProductResult, String storeSkuId) {
		List<String> errorMessageList = new ArrayList<>();
		if (productMasterProductResult == null) {
			errorMessageList.add(messageSource.getMessage("message127", null, Locale.getDefault()));
			log.error("SKU {} not exist in Product Master", storeSkuId);
		} else if (productMasterProductResult.getAdditional() == null || productMasterProductResult.getAdditional().getHktv() == null) {
			log.error("SKU {} hktv data not found", storeSkuId);
			errorMessageList.add(messageSource.getMessage("message227", null, Locale.getDefault()));
		}
		return errorMessageList;
	}

	@Transactional
	public void callProductMasterAndUpdateRecordRow(Long recordId) {
		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
		List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.WAIT_START);
		if (rows.isEmpty()) {
			log.error("No rows found for recordId: {}", recordId);
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return;
		}

		ProductMasterResponseDto productMasterResponseDto = checkRequestPMRecordProductTask.requestSendProductToProductMaster(saveProductRecordDo, rows);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterResponseDto, saveProductRecordDo, rows);
	}

	protected ProductMasterResultDto findProductFromPm(UserDto userDto, String storeSkuId) {
		var response = productMasterHelper.requestProductByStoreSkuId(userDto, FindStoreSkuIdProductRequestDto.generate(ConstantType.HKTV, List.of(storeSkuId)));

		if (response == null || response.getData() == null || response.getData().isEmpty()) {
			return null;
		} else {
			return response.getData().get(0);
		}
	}


    /**
     * Validate request data
     */
    protected List<String> validateForceOfflineRequest(ForceOfflineRequestDto requestDto) {
        List<String> errorList = new ArrayList<>();

        // 1. Required field validation
        if (requestDto.getSkuStatus() == null || requestDto.getSkuStatus().isEmpty()) {
            errorList.add(messageSource.getMessage("message191", null, Locale.getDefault()));
        }

        // 2. Case number validation (optional)
        if (requestDto.getCaseNumber() != null && !requestDto.getCaseNumber().isEmpty()) {
            if (requestDto.getCaseNumber().length() > 50) {
                errorList.add(messageSource.getMessage("message373", null, Locale.getDefault()));
            }
        }

        // stop further existence check if required fields missing
        if (!errorList.isEmpty()) {
            return errorList;
        }

        // 3. skuStatus validation value
        ForceOfflineStatusEnum statusEnum = ForceOfflineStatusEnum.getEnum(requestDto.getSkuStatus());
        if (statusEnum == null) {
            errorList.add("Invalid skuStatus: " + requestDto.getSkuStatus());
        }

        return errorList;
    }

}
