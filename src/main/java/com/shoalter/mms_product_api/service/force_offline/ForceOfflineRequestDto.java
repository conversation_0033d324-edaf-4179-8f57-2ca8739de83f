package com.shoalter.mms_product_api.service.force_offline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for force offline request
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ForceOfflineRequestDto {

    @Schema(description = "UNIQUE STORE CODE")
    private String storefrontStoreCode;

    @Schema(description = "SKU CODE/SKU ID")
    private String skuCode;

    @Schema(description = "SKU STATUS, 'Suspended' means set to force offline, 'Offline' means remove force offline")
    private String skuStatus;

    @Schema(description = "CASE NUMBER")
    private String caseNumber;
}
