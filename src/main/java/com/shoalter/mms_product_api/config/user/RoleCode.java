package com.shoalter.mms_product_api.config.user;

import com.shoalter.mms_product_api.config.product.SystemUserEnum;

import java.util.Set;

public class RoleCode {
    private RoleCode() {}

    public static final String DATABANK_USER = "DATABANK_USER";
    public static final String FOOD_BRAND_ADMIN = "FOOD_BRAND_ADMIN";
    public static final String FOOD_SUPER_MERCHANT = "FOOD_SUPER_MERCHANT";
    public static final String MERCHANT = "MERCHANT";
    public static final String MERCHANT_ADMIN = "MERCHANT_ADMIN";
	public static final String MERCHANT_OPERATOR = "MERCHANT_OPERATOR";
    public static final String MERCHANT_MABS = "MERCHANT_MABS";
	public static final String MERCHANT_BASIC = "MERCHANT_BASIC";
	public static final String SELLER_CUSTOMER_SERVICE_AGENT = "SELLER_CUSTOMER_SERVICE_AGENT";
    public static final String PHY_STORE_MERCHANT = "PHY_STORE_MERCHANT";
    public static final String SUPPLIERS = "SUPPLIERS";
    public static final String ADMIN = "ADMIN";
    public static final String APPROVER = "APPROVER";
    public static final String AUDITOR = "AUDITOR";
    public static final String CP = "CP";
    public static final String DEPT_HEAD = "DEPT_HEAD";
    public static final String EDITORIAL = "EDITORIAL";
    public static final String EMR = "EMR";
    public static final String FINANCE = "FINANCE";
    public static final String FINBUSER = "FINBUSER";
    public static final String FMR = "FMR";
    public static final String FOOD_INTERN = "FOOD_INTERN";
    public static final String HKTVSHOPS_DOCUMENT_CHECKER = "HKTVSHOPS_DOCUMENT_CHECKER";
    public static final String KPMG = "KPMG";
    public static final String LEGAL = "LEGAL";
    public static final String MARKETING_LEADER = "MARKETING_LEADER";
    public static final String MARKETING_STAFF = "MARKETING_STAFF";
    public static final String MR = "MR";
    public static final String OPERATION_TEAM = "OPERATION TEAM";
    public static final String OPERATION_ADMIN = "OPERATION_ADMIN";
    public static final String PM = "PM";
    public static final String PMR = "PMR";
    public static final String PMVIEWER = "PMVIEWER";
    public static final String PRODUCT_ADMIN = "PRODUCT_ADMIN";
    public static final String QA = "QA";
    public static final String RC = "RC";
    public static final String REPORTING = "REPORTING";
    public static final String RM = "RM";
    public static final String RM_ADMIN = "RM ADMIN";
    public static final String RM_ADMIN2 = "RM ADMIN2";
    public static final String RML = "RML";
    public static final String RMO = "RMO";
    public static final String ROLE_MABS_HKTV_ADMIN = "ROLE_MABS_HKTV_ADMIN";
    public static final String ROLE_MABS_HKTV_USER = "ROLE_MABS_HKTV_USER";
    public static final String SUPER_SYSTEM_ADMIN = "SUPER_SYSTEM_ADMIN";
	public static final String OPS_DEPT_HEAD = "OPS_DEPT_HEAD";
	public static final String CQA = "CQA";
	public static final String CQA_LEADER = "CQA_LEADER";
	public static final String MCS = "MCS";
	public static final String MCSTL = "MCSTL";
	public static final String MCS_APPROVER = "MCS_APPROVER";

	public static final Set<String> ALLOW_PRODUCT_IMPORT_ROLES = Set.of(ADMIN, OPERATION_ADMIN);
	// can view bu history Tmall and Affiliate all data
	public static final Set<String> THIRD_PARTY_ROLES = Set.of(RM, RML, RMO, RM_ADMIN, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_PRODUCT_IMPORT_FROM_SHOPLINE_ROLES = Set.of(ADMIN, OPERATION_ADMIN, MERCHANT_ADMIN, MERCHANT);
	public static final Set<String> ALLOW_PRODUCT_IMPORT_FROM_HKTV_TO_LITTLE_MALL_ROLES = Set.of(ADMIN, OPERATION_ADMIN, MERCHANT_ADMIN, MERCHANT, PM);
	public static final Set<String> EW_SKU_ALLOW_ROLE_SET = Set.of(DEPT_HEAD, RM_ADMIN, ADMIN, SUPER_SYSTEM_ADMIN, PM, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_OVERSEA_DELIVERY_ROLES = Set.of(RM, RMO, RML, RM_ADMIN, OPERATION_ADMIN, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_ADD_OVERSEA_DELIVERY_ROLES = Set.of(OPERATION_ADMIN, RM_ADMIN, ADMIN, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_APPROVAL_DEAL_AUDIT_ROLE_SET = Set.of(RM, RML);
	public static final Set<String> ALLOW_QUERY_APPROVAL_DEAL_ROLES = Set.of(RM, RML, RM_ADMIN, OPERATION_ADMIN, ADMIN, SUPER_SYSTEM_ADMIN, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_PRODUCT_PACKING_INFO_ROLES = Set.of(RM, RMO, RM_ADMIN, RML, DEPT_HEAD, ADMIN, MERCHANT_ADMIN, MERCHANT, OPS_DEPT_HEAD);
	public static final Set<String> ALLOW_BATCH_EDIT_PRICE_EXCHANGE_RATE_ROLES = Set.of(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemRoleCode(), ADMIN, SUPER_SYSTEM_ADMIN);
}
