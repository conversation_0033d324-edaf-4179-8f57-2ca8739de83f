package com.shoalter.mms_product_api.config.impact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum GenderEnum {

	MALE("Male"),
	FEMALE("Female"),
	UNISEX("Unisex");

	private final String field;

	@JsonValue
	public String value() {
		return field;
	}

	@JsonCreator
	public static GenderEnum of(String value) {
		for (GenderEnum target : GenderEnum.values()) {
			if (Objects.equals(target.value(), value)) {
				return target;
			}
		}
		return null;
	}

	public static boolean contains(String value) {
		if (value == null) {
			return false;
		}
		return Arrays.stream(GenderEnum.values()).anyMatch(target -> Objects.equals(target.field, value));
	}
}

