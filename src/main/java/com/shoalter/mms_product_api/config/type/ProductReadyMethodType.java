package com.shoalter.mms_product_api.config.type;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.shoalter.mms_product_api.config.type.ContractType.*;

public class ProductReadyMethodType {
	private ProductReadyMethodType(){}

	public static final String THIRD_PARTY = "3PL";
	public static final String CONSIGNMENT = "C";
	public static final String E_VOUCHER = "E";
	public static final String STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY = "G";
    public static final String MERCHANT_DELIVERY = "M";
    public static final String STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE = "N";
	public static final String NON_STANDARD_DELIVERY = "NS";
    public static final String OVERSEA_DELIVERY = "O";
    public static final String STANDARD_DELIVERY_SAME_DAY_IN_HUB = "S";
    public static final String MAINLAND_DELIVERY = "ML";
    public static final String DISPLAY_STORE = "DS";
    public static final String HYBRID_DELIVERY_CONSOLIDATED = "MLC";

	public static final Set<String> RESTRICT_SERVICE_DEALS_CATEGORY_SET = Set.of(E_VOUCHER, DISPLAY_STORE);

	public static final Set<String> RESTRICT_INSURANCE_CATEGORY_SET = Set.of(MERCHANT_DELIVERY, DISPLAY_STORE);

	public static final String DEFAULT_LITTLE_MALL_PRODUCT_READY_METHOD = MERCHANT_DELIVERY;
	public static final List<String> LITTLE_MALL_PRODUCT_READY_METHOD = List.of(DEFAULT_LITTLE_MALL_PRODUCT_READY_METHOD);
	public static final List<String> MAINLAND_PRODUCT_READY_METHOD = List.of(MAINLAND_DELIVERY, HYBRID_DELIVERY_CONSOLIDATED);


	// move from MMS 1.0 api searchProductReadyMethod
	public static List<String> generateRestrictedProductReadyCodeList(String contractTypeCode) {
		List<String> productReadyCodeList = new ArrayList<>();
		switch (contractTypeCode) {
			case SERVICE_DEAL:
				productReadyCodeList.add(E_VOUCHER);
				break;
			case E_COMMERCE_MAIN_CONTRACT:
			case O2O_PARTNERSHIP_PROGRAM:
				productReadyCodeList.addAll(List.of(
						CONSIGNMENT,
						STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE,
						STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY,
						MERCHANT_DELIVERY,
						NON_STANDARD_DELIVERY,
						THIRD_PARTY));
				break;
			case FIXED_COST_CONTRACT:
			case HKTV_FOOD_CONTRACT_TYPE_CODE:
			case HKTV_PAY_CONTRACT_TYPE_CODE:
			case HKTV_EXPRESS_CONTRACT_TYPE_CODE:
				productReadyCodeList.addAll(List.of(
						CONSIGNMENT,
						STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE,
						STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY,
						MERCHANT_DELIVERY,
						NON_STANDARD_DELIVERY));
				break;
			case SMALL_MERCHANT_CONTRACT_30K:
			case SMALL_MERCHANT_CONTRACT_50K:
			case SMALL_MERCHANT_CONTRACT_100K:
			case SMALL_MERCHANT_CONTRACT_15K:
			case SMALL_MERCHANT_CONTRACT_250K:
				productReadyCodeList.addAll(List.of(
						CONSIGNMENT,
						STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE,
						STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY,
						MERCHANT_DELIVERY,
						THIRD_PARTY));
				break;
			case KOREA_MAIN_CONTRACT:
			case JAPAN_MAIN_CONTRACT:
			case TAIWAN_MERCHANT_CONTRACT:
				productReadyCodeList.addAll(List.of(
						OVERSEA_DELIVERY,
						MERCHANT_DELIVERY));
				break;
			case HOKOBUY_MAIN_CONTRACT:
				productReadyCodeList.addAll(List.of(
						CONSIGNMENT,
						STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE,
						MERCHANT_DELIVERY));
				break;
			case ANNUAL_INSURANCE_CONTRACT:
				productReadyCodeList.add(MERCHANT_DELIVERY);
				break;
			case OVERSEAS_CONTRACT:
				productReadyCodeList.addAll(List.of(
						THIRD_PARTY,
						MERCHANT_DELIVERY));
				break;
			case MAINLAND_MERCHANT_CONTRACT:
			case MAINLAND_MERCHANT_CONTRACT_3:
				productReadyCodeList.addAll(MAINLAND_PRODUCT_READY_METHOD);
				break;
			case DISPLAY_STORE_CONTRACT:
				productReadyCodeList.add(DISPLAY_STORE);
				break;
			default:
		}

		return productReadyCodeList;
	}
}
