package com.shoalter.mms_product_api.config.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "hybris")
public class HybrisProperties {

	private String apiUrl;

	private Endpoint endpoint;

	@Data
	public static class Endpoint {

		private String createBundle;
		private String updateBundle;
		private String updateEwBinding;
		private String upsertEverutsBuyer;
		private String createUpdateOptionValue;
		private String updateProductMainlandSamePrice;
		private String createProduct;
		private String updateProduct;
		private String createDiscountRule;
	}
}
