package com.shoalter.mms_product_api.config.properties;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
@ConfigurationProperties(prefix = "product.async")
@Data
public class AsyncTaskProperties {

	private ThreadProperties ecomEngine;

	@Data
	public static class ThreadProperties {
		private int corePoolSize;
		private int maxPoolSize;
	}
}
