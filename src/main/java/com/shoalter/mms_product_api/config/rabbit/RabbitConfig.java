package com.shoalter.mms_product_api.config.rabbit;

import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Correlation;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {

    @Bean
    public ConnectionFactory productMasterConnectionFactory(
            @Value("${product.master.rabbitmq.addresses}") String addresses,
            @Value("${product.master.rabbitmq.port}") int port,
            @Value("${product.master.rabbitmq.username}") String username,
            @Value("${product.master.rabbitmq.password}") String password) {
        return generateCachingConnectionFactory(addresses, port, username, password);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory productMasterConnectionFactory) {
        var rabbitTemplate = new RabbitTemplate(productMasterConnectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory productMasterContainerFactory(ConnectionFactory productMasterConnectionFactory) {
        var factory = new SimpleRabbitListenerContainerFactory();
        factory.setPrefetchCount(1);
		factory.setConcurrentConsumers(2);
        factory.setConnectionFactory(productMasterConnectionFactory);
        factory.setMessageConverter(new Jackson2JsonMessageConverter());
        return factory;
    }

	@Bean
	public SimpleRabbitListenerContainerFactory productMasterSingleContainerFactory(ConnectionFactory productMasterConnectionFactory) {
		var factory = new SimpleRabbitListenerContainerFactory();
		factory.setPrefetchCount(5);
		factory.setConcurrentConsumers(5);
		factory.setConnectionFactory(productMasterConnectionFactory);
		factory.setMessageConverter(new Jackson2JsonMessageConverter());
		return factory;
	}

    /**
     * NOTE: Naming of this default connectionFactory CANNOT be changed!!<br>
     * Because <code>RabbitAnnotationDrivenConfiguration.simpleRabbitListenerContainerFactory()</code> needs a bean of <code>ConnectionFactory</code> with this particular name.
     */
    @Bean
    public ConnectionFactory connectionFactory(
            @Value("${mpps.rabbitmq.addresses}") String addresses,
            @Value("${mpps.rabbitmq.port}") int port,
            @Value("${mpps.rabbitmq.username}") String username,
            @Value("${mpps.rabbitmq.password}") String password) {
        return generateCachingConnectionFactory(addresses, port, username, password);
    }

    @Bean
    public RabbitTemplate mppsRabbitTemplate(ConnectionFactory connectionFactory) {
        var rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory mppsContainerFactory(ConnectionFactory connectionFactory) {
        var factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setPrefetchCount(1);
        factory.setMessageConverter(new Jackson2JsonMessageConverter());
        return factory;
    }

    @Bean
    public MessagePostProcessor productMasterMessagePostProcessor() {
        return new MessagePostProcessor() {
            @Override
            public Message postProcessMessage(Message message, Correlation correlation) throws AmqpException {
                MessageProperties messageProperties = message.getMessageProperties();
                if(correlation instanceof CorrelationData){
                    String correlationId = ((CorrelationData) correlation).getId();
                    messageProperties.setCorrelationId(correlationId);
                }
                return message;
            }
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                return message;
            }
        };
    }

    private static ConnectionFactory generateCachingConnectionFactory(String addresses, int port, String username, String password) {
        var connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(addresses);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost("/");
        return connectionFactory;
    }
}
