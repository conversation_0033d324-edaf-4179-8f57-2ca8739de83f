package com.shoalter.mms_product_api.config.product;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum MonitorProductCheckStatus {
	ERROR(-1),
	PENDING(0),
	PROCESSING(1),
	OFFLINE(2),
	OFFLINE_FAIL(3),
	CHECKED(4),
	NOT_FOUND(5),
	OFFLINE_RECORD_CREATE(11),
	OFFLINE_RECORD_CREATE_FAIL(12);

	private final int value;

	public static final Set<Integer> PRICE_CHECK_FINAL_STATUSES = Set.of(
		MonitorProductCheckStatus.ERROR.getValue(),
		MonitorProductCheckStatus.CHECKED.getValue(),
		MonitorProductCheckStatus.NOT_FOUND.getValue(),
		MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue()
	);

	public static final Set<Integer> OFFLINE_CHECK_FINAL_STATUSES = Set.of(
		MonitorProductCheckStatus.OFFLINE.getValue(),
		MonitorProductCheckStatus.OFFLINE_FAIL.getValue()
	);

	// NEED OFFLINE NOTIFICATION STATUS
	public static final Set<Integer> OFFLINE_NOTIFICATION_STATUSES = Set.of(
		MonitorProductCheckStatus.OFFLINE.getValue(),
		MonitorProductCheckStatus.OFFLINE_FAIL.getValue(),
		MonitorProductCheckStatus.OFFLINE_RECORD_CREATE.getValue(),
		MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue()
	);
}
