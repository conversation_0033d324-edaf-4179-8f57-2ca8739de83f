package com.shoalter.mms_product_api.config.product;

import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;

public enum ProductMasterActionEnum {
	UPDATE_IS_PRIMARY;
	public static ProductMasterActionEnum getAction(SaveProductRecordDo saveProductRecordDo) {
		if (SaveProductType.THE_PLACE_SWITCH_PRIMARY == saveProductRecordDo.getUploadType()) {
			return UPDATE_IS_PRIMARY;
		}
        return null;
    }
}
