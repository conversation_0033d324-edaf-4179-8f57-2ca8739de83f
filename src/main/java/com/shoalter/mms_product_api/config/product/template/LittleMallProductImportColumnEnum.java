package com.shoalter.mms_product_api.config.product.template;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import lombok.Getter;

@Getter
public enum LittleMallProductImportColumnEnum {
	SKU_ID(0, "SKU ID*", "SKU ID*", " 每件商品自己獨有的ID，用於識別產品規格、顏色、款式等\n\n建議輸入英文, 數字或_，不能輸入中文或特別符號\n(E.g. double_chocolate_cookies)\n\n註: 上傳後不能更改此欄位", null, null, null, 34, true),
	PRODUCT_ID(1, "Product ID*", "產品ID*", " 主商品ID，用於歸納其他子商品。\n(E.g. cookies)", null, null, null, 30, true),
	PRODUCT_READY_METHOD(2, "Product Ready Method*", "貨品準備方式*", "每件商品的貨品出貨方式，預設選取商戶出貨", ExcelValidationName.VALIDATION_PRO_READY_METHOD, null, null, 30, true),
	IS_PRIMARY_SKU(3, "Is Primary SKU (Y/N)*", "主要SKU (Y/N)*", " 同一產品ID只能指定一個主要SKU", ExcelValidationName.VALIDATION_YES_NO, null, null, 20, true),
	SKU_NAME_CHI(4, "SKU Name (Chi)*", "SKU名稱 (中文)*", "只支援純文字", null, null, null, 17, true),
	ORIGINAL_PRICE(5, "Original Price*", "原價*", " ", null, null, null, 17, true),
	SELLING_PRICE(6, "Selling Price", "售價", "不可大於原價", null, null, null, 16, false),
	VISIBILITY(7, "Visibility*", "可被搜尋*", "如果選擇 Y, 代表此商品可在 ThePlace 被搜尋到。\n\n如果選擇 N, 代表此商品不可在 ThePlace 被搜尋到。", ExcelValidationName.VALIDATION_YES_NO, null, null, 28, true),
	ONLINE_STATUS(8, "Online Status (Y/N)*", "上架狀態 (Y/N)*", " ", ExcelValidationName.VALIDATION_YES_NO, null, null, 19, true),
	DISPLAY_IN_HKTVMALL_CATEGORY(9, "Display in HKTVmall Category*", "可顯示在HKTVmall分類*", "請輸入你希望你的商品會顯示在什麼HKTVmall分類的分類編號\n(E.g. AA11031500001)\n\n\n分類代碼可參考單筆建立之下拉選單", null, null, null, 39, true),
	SKU_LONG_DESCRIPTION_CHI(10, "SKU Long Description (Chi)*", "商品詳細介紹 (中文)*", " 只支援純文字", null, null, null, 29, true),
	MAIN_PHOTO(11, "Main Photo*", "主要照片*", " 請輸入照片的名稱 連同照片格式 (E.g. sneaker01.jpg)\n並把照片壓縮在同一個檔案\n\n建議上傳 1125 x 1125px ; 上限 8 MB 的照片\n只支援 JPG, PNG, JPEG, GIF\n\n只能上傳1張照片", null, null, null, 40, true),
	OTHER_PHOTO(12, "Other Photo", "其他照片", " 請輸入照片的名稱 連同照片格式 (E.g. sneaker01.jpg)\n並把照片壓縮在同一個檔案\n\n建議上傳 1125 x 1125px ; 上限 8 MB 的照片\n只支援 JPG, PNG, JPEG, GIF\n\n最多可上傳30張照片，如果有多張，請使用「,」分隔。", null, null, null, 41, false),
	;


	private final Integer columnNumber;
	private final String colNameEnglish;
	private final String colNameChinese;
	private final String instructions;
	private final String validationName;
	private final ProductTemplateProductMasterInfoColumnEnum parent;
	private final String parentSegment;
	private final Integer weight;
	private final boolean required;


	LittleMallProductImportColumnEnum(Integer colNum, String colNameEnglish, String colNameChinese, String instructions, String validationName,
									  ProductTemplateProductMasterInfoColumnEnum parent, String parentSegment, Integer weight, boolean required) {
		this.columnNumber = colNum;
		this.colNameEnglish = colNameEnglish;
		this.colNameChinese = colNameChinese;
		this.instructions = instructions;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
		this.weight = weight;
		this.required = required;
	}
}
