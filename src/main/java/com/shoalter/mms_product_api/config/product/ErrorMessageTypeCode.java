package com.shoalter.mms_product_api.config.product;

public final class ErrorMessageTypeCode {
	public static final String PRODUCT_MASTER_POST_PRODUCTS_ERROR = "PMSA-001";
	public static final String PRODUCT_MASTER_PATCH_PRODUCTS_ERROR = "PMSA-002";
	public static final String PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR = "PMSA-003";
	public static final String PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR = "PMSA-004";
	public static final String PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR = "PMSA-005";
	public static final String PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_SKU_ERROR = "PMSA-006";
	public static final String PRODUCT_MASTER_TRACE_RESULT_NOT_FOUND_ROW = "PMSA-007";
	public static final String PRODUCT_MASTER_POST_LITTLE_MALL_PRODUCTS_ERROR = "PMSA-011";
	public static final String PRODUCT_MASTER_POST_PRODUCTS_CHECK_MERCHANT_BARCODE_ERROR = "PMSA-012";
	public static final String PRODUCT_MASTER_GET_CHILD_PRODUCT_ERROR = "PMSA-013";
	public static final String PRODUCT_MASTER_UPDATE_EW_BINDING_ERROR = "PMSA-014";
	public static final String PRODUCT_MASTER_PUT_VARIANT_PRODUCT_RELATION_ERROR = "PMSA-015";

	public static final String PRODUCT_MASTER_GET_LITTLE_MALL_PRODUCTS_ERROR = "PMSA-015";
	public static final String PRODUCT_MASTER_EDIT_VISIBILITY_ERROR = "PMSA-016";

    public static final String INVENTORY_POST_PRODUCT_INVENTORY_ERROR = "ISA-001";
    public static final String INVENTORY_PUT_PRODUCT_INVENTORY_ERROR = "ISA-002";
    public static final String INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_ERROR = "ISA-003";
    public static final String INVENTORY_GET_PRODUCT_INVENTORY_BATCH_HISTORY_ERROR = "ISA-004";
    public static final String INVENTORY_CREATE_EDIT_FAIL = "ISA-005";
    public static final String INVENTORY_SHARE_STATUS_NOT_MAP_BU_FAIL = "ISA-006";
    public static final String INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_BUNDLE_SELLING_QTY_ERROR = "ISA-007";
    public static final String INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_CHILD_SKU_QTY_ERROR = "ISA-008";

    public static final String THIRD_PARTY_SKU_VALIDATE_ERROR = "TPLSA_001";

    public static final String FILE_HANDLE_ERROR = "SL-001";
    public static final String UPLOAD_TYPE_NO_IMPLEMENT = "SL-002";
    public static final String NULL_POINTER_EXCEPTION = "SL-003";
    public static final String CHECK_TASK_EXCEPTION = "SL-004";
    public static final String SERVICE_EXCEPTION = "SL-005";
	public static final String MISSING_SYS_PARM_ERROR = "SL-006";
	public static final String STORE_AFFILIATE_SETTING_ERROR = "SL-007";
	public static final String HYBRIS_POST_BUNDLE_ERROR = "HBA-001";
	public static final String HYBRIS_POST_EVERUTS_BUYER_ERROR = "HBA-002";
	public static final String HYBRIS_POST_OPTION_VALUE_ERROR = "HBA-003";
	public static final String IMPACT_GET_CATALOG_ITEMS_ERROR = "IMP-001";
	public static final String AFFILIATE_EXPORT_ERROR = "AFA-001";

	public static final String RAKUTEN_GET_PRODUCT_ERROR = "RKA-001";

	public static final String OPTIMISE_GET_PRODUCT_ERROR = "OPT-001";

	public static final String CHINESEAN_GET_PRODUCT_ERROR = "CHI-001";

	public static final String PROMOTION_CHECK_MEMBERSHIP_PRICING_ONGOING = "PRO-001";

	public static final String MMS_STORE_API_GET_STORE_ERROR = "MSA-001";

	public static final String NOTIFICATION_API_ERROR = "NOT-001";

	public static final String MMS_SETTING_API_ERROR = "SET-001";

    private ErrorMessageTypeCode() {}
}
