package com.shoalter.mms_product_api.config.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Getter
@AllArgsConstructor
public enum ThirdPartySourceEnum {
	IMPACT("IMPACT"),
	TMALL("TMALL"),
	ALIBABA("ALIBABA"),
	RAKUTEN("RAKUTEN"),
	OPTIMISE("OPTIMISE"),
	CHINESE_AN("CHINESE_AN"),
	AWIN("AWIN"),
	PARTNERIZE("PARTNERIZE"),
	SHAREASALE("SHAREASALE"),
	TOONIES("TOONIES");

	private final String value;
}

@Converter(autoApply = true)
class AffiliateTypeEnumConverter implements AttributeConverter<ThirdPartySourceEnum, String> {
	@Override
	public String convertToDatabaseColumn(ThirdPartySourceEnum attribute) {
		if (attribute == null) {
			return "";
		}
		return attribute.name();
	}

	public ThirdPartySourceEnum convertToEntityAttribute(String dbData) {
		if (dbData == null || dbData.isEmpty()) {
			return null;
		}
		return ThirdPartySourceEnum.valueOf(dbData);
	}
}
