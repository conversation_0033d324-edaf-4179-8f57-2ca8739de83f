package com.shoalter.mms_product_api.config.product;

import lombok.Getter;

import java.util.Set;

@Getter
public enum SysParmCodeEnum {

	// PRODUCT_FIELD_VALUE
	MAINLAND_FIELD_1("mainland_field_1"),
	MAINLAND_FIELD_2("mainland_field_2"),
	MAINLAND_FIELD_3("mainland_field_3"),
	// SUPPLIER_STORE_WHITELIST
	TOONIES("TOONIES");

	private final String code;

	SysParmCodeEnum(String code) {
		this.code = code;
	}

	public static final Set<String> PRODUCT_FIELD_MAINLAND_CODE_SET =
		Set.of(
			SysParmCodeEnum.MAINLAND_FIELD_1.getCode(),
			SysParmCodeEnum.MAINLAND_FIELD_2.getCode(),
			SysParmCodeEnum.MAINLAND_FIELD_3.getCode()
		);

}
