package com.shoalter.mms_product_api.config.product;

import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import java.util.Collection;
import java.util.EnumSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HybrisMqHandlingEnum {
	OPEN_API_BATCH_EDIT_PRICE(HybrisAction.PRODUCT_SYNC_MODE_PRICE),
	BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB(HybrisAction.PRODUCT_SYNC_MODE_PRICE),
	OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS(HybrisAction.PRODUCT_SYNC_MODE_PICK_UP_DAYS),
	BATCH_EDIT_PRODUCT_ONLINE_STATUS(HybrisAction.PRODUCT_SYNC_MODE_ONOFFLINE),
	;


	private final String action;

	private static final Set<HybrisMqHandlingEnum> UPDATE_PRICE_ACTIONS = EnumSet.of(
		OPEN_API_BATCH_EDIT_PRICE);
	private static final Set<HybrisMqHandlingEnum> UPDATE_PRODUCT_READY_DAYS_ACTIONS = EnumSet.of(
		OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS);
	private static final Set<HybrisMqHandlingEnum> UPDATE_ONLINE_STATUS_ACTIONS = EnumSet.of(
		BATCH_EDIT_PRODUCT_ONLINE_STATUS);

	public static HybrisMqHandlingEnum getEnum(SaveProductRecordDo record) {
		if (record.getSource().equals(SaveProductSource.OPEN_API)) {
			switch (record.getUploadType()) {
				case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
					return OPEN_API_BATCH_EDIT_PRICE;
				case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
					return OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS;
				default:
					return null;
			}
		} else {
			switch (record.getUploadType()) {

				case SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB:
					return BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB;

				case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				case SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS:
				case SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE:
					return BATCH_EDIT_PRODUCT_ONLINE_STATUS;

				default:
					return null;
			}
		}
	}

	private static boolean isActionIn(SaveProductRecordDo saveProductRecordDo,
		Collection<HybrisMqHandlingEnum> actionCollection) {
		return actionCollection.contains(getEnum(saveProductRecordDo));
	}

	public static boolean isUpdatePrice(SaveProductRecordDo saveProductRecordDo) {
		return isActionIn(saveProductRecordDo, UPDATE_PRICE_ACTIONS);
	}

	public static boolean isUpdateProductReadyDays(SaveProductRecordDo saveProductRecordDo) {
		return isActionIn(saveProductRecordDo, UPDATE_PRODUCT_READY_DAYS_ACTIONS);
	}

	public static boolean isUpdateOnlineStatus(SaveProductRecordDo saveProductRecordDo) {
		return isActionIn(saveProductRecordDo, UPDATE_ONLINE_STATUS_ACTIONS);
	}
}
