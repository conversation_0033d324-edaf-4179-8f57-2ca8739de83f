package com.shoalter.mms_product_api.config.product.edit_column;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum PackingDimensionTemplateColumnEnum implements TemplateInterface<PackingDimensionTemplateColumnEnum> {
    STORE_ID(0, "Store Id", null, null, null),
    SKU_ID(1, "Sku ID", null, null, null),
	PACKING_SPEC_ENG(2, "Packing Spec (Eng)", null, null, null),
	PACKING_SPEC_CHI(3, "Packing Spec (Chi)", null, null, null),
	PACKING_SPEC_SC(4, "Packing Spec (SC)", null, null, null),
	PACKING_HEIGHT(5, "Packing Height", null, null, null),
	PACKING_LENGTH(6, "Packing Length", null, null, null),
	PACKING_DEPTH(7, "Packing Depth", null, null, null),
	PACKING_DIMENSION_UNIT(8, "Packing Dimension Unit", ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, null, null),
	WEIGHT(9, "Weight", null, null, null),
	WEIGHT_UNIT(10, "Weight Unit", ExcelValidationName.VALIDATION_WEIGHT_UNIT, null, null),
	PACKING_BOX_TYPE(11, "Packing Box Type", ExcelValidationName.VALIDATION_PACK_BOX_TYPE, null, null),
	CARTON_HEIGHT(12, "Carton Height(mm)", null, null, null),
	CARTON_DEPTH(13, "Carton Depth(mm)", null, null, null),
	CARTON_LENGTH(14, "Carton Length(mm)", null, null, null);

    private final Integer columnNumber;
    private final String columnName;
    private final String validationName;
    private final PackingDimensionTemplateColumnEnum parent;
    private final String parentSegment;

    PackingDimensionTemplateColumnEnum(Integer colNum, String columnName, String validationName, PackingDimensionTemplateColumnEnum parent, String parentSegment) {
        this.columnNumber = colNum;
        this.columnName = columnName;
        this.validationName = validationName;
        this.parent = parent;
        this.parentSegment = parentSegment;
    }

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}
}
