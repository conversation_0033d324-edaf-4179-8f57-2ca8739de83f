package com.shoalter.mms_product_api.config.product;

public enum SystemUserEnum {
	SYSTEM(0, "SYST<PERSON>", "SYST<PERSON>", "SYSTEM"),
	MMS_PRODUCT_SYSTEM(-1, "MMS_PRODUCT_SYSTEM", "MMS_Product_System", "SYSTEM");

	private final Integer systemId;
	private final String systemCode;
	private final String systemName;
	private final String systemRoleCode;

	SystemUserEnum(int systemId, String systemCode, String systemName, String systemRoleCode) {
		this.systemId = systemId;
		this.systemCode = systemCode;
		this.systemName = systemName;
		this.systemRoleCode = systemRoleCode;
	}

	public Integer getSystemId() {
		return systemId;
	}

	public String getSystemCode() {
		return systemCode;
	}

	public String getSystemName() {
		return systemName;
	}
	public String getSystemRoleCode(){
		return systemRoleCode;
	}
}
