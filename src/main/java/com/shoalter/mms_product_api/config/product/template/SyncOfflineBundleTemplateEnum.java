package com.shoalter.mms_product_api.config.product.template;

import lombok.Getter;

import static com.shoalter.mms_product_api.config.product.ExcelValidationName.VALIDATION_PRODUCT_STATUS;
import static com.shoalter.mms_product_api.config.product.ExcelValidationName.VALIDATION_VISIBLE;

@Getter
public enum SyncOfflineBundleTemplateEnum {
    SKU_CODE(0,"SKU ID"),
    SKU_STATUS(1,"SKU Status"),
	ERROR_REASON(2,"Error Reason");
    private final Integer columnNumber;
    private final String colName;

    SyncOfflineBundleTemplateEnum(Integer colNum, String colName){
        this.columnNumber = colNum;
        this.colName = colName;
    }
}
