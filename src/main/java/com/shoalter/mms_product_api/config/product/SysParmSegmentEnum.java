package com.shoalter.mms_product_api.config.product;

import java.util.List;

public enum SysParmSegmentEnum {
	CATEGORY_FOR_REMOVAL_SERVICE,
	PRODUCT_READY_METHOD,
	PRODUCT_READY_METHOD_WAREHOUSE,
	PACK_BOX_TYPE,
	<PERSON><PERSON><PERSON><PERSON>_DAYS,
	PRODUCT_READY_DAYS,
	PIC<PERSON>UP_TIMESLOT,
	RETURN_DAYS,
	SIZE_SYSTEM,
	SIZE,
	COLOR_FAMILIES,
	COLOR,
	COUNTRY_OF_ORIGIN,
	CURRENCY,
	VOUCHER_TYPE,
	VOUCHER_DISPLAY_TYPE,
	VOUCHER_TEMPLATE_TYPE,
	PRODUCT_FIELD,
	PRODUCT_FIELD_VALUE,
	OVERSEA_DELIVERY,
	OVERSEA_METHOD,
	BUYSELL_MERCHANT,
	PRODUCT_MANAGEMENT_AFFILIATE,
	ADMIN_ALLOW_OVERSEA,
	WECHAT_PRODUCT_READY_METHOD_EX,
	WECHAT_CATEGORY_EXCLUSION,
	CTM_CATEGORY_EXCLUSION,
	RMB_SKU_WHITELIST,
	SUPPLIER_STORE_WHITELIST;

	public static final List<SysParmSegmentEnum> SALES_CHANNEL_SEGMENTS = List.of(
		WECHAT_PRODUCT_READY_METHOD_EX,
		WECHAT_CATEGORY_EXCLUSION,
		CTM_CATEGORY_EXCLUSION
	);
}
