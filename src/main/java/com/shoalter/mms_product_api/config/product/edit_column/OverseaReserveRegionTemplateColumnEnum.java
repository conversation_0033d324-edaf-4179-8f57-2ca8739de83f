package com.shoalter.mms_product_api.config.product.edit_column;

import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum OverseaReserveRegionTemplateColumnEnum implements TemplateInterface<OverseaReserveRegionTemplateColumnEnum> {
    STORE_ID(0, "Store Id", null, null, null),
    SKU_ID(1, "Sku ID", null, null, null),
	RESERVE_REGION(2, "Reserve region", null, null, null);

    private final Integer columnNumber;
    private final String columnName;
    private final String validationName;
    private final OverseaReserveRegionTemplateColumnEnum parent;
    private final String parentSegment;

	OverseaReserveRegionTemplateColumnEnum(Integer columnNumber, String columnName, String validationName, OverseaReserveRegionTemplateColumnEnum parent, String parentSegment) {
        this.columnNumber = columnNumber;
        this.columnName = columnName;
        this.validationName = validationName;
        this.parent = parent;
        this.parentSegment = parentSegment;
    }

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}
}
