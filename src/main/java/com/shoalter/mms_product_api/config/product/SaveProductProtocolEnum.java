package com.shoalter.mms_product_api.config.product;

import java.util.HashMap;
import java.util.Map;

import static com.shoalter.mms_product_api.config.product.SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE;
import static com.shoalter.mms_product_api.config.product.SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS;
import static com.shoalter.mms_product_api.config.product.SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS;

public enum SaveProductProtocolEnum {
	HTTP, QUEUE;

	private static final Map<Integer, SaveProductProtocolEnum> PROTOCOL_MAP = new HashMap<>();

	static {
		PROTOCOL_MAP.put(BATCH_EDIT_PRODUCT_ONLINE_STATUS, QUEUE);
		PROTOCOL_MAP.put(BATCH_EDIT_STORE_ONLINE_STATUS, QUEUE);
		PROTOCOL_MAP.put(BATCH_EDIT_PRODUCT_FORCE_OFFLINE, QUEUE);
	}

	/**
	 * Retrieves the corresponding SaveProductProtocolEnum value based on the given save product type
	 * integer.
	 *
	 * @param saveProductTypeInteger The integer value representing the save product type
	 * @return Returns the corresponding protocol enum value if found in PROTOCOL_MAP, otherwise
	 * returns HTTP as default
	 * @see SaveProductProtocolEnum
	 */
	public static SaveProductProtocolEnum fromSaveProductTypeInt(Integer saveProductTypeInteger) {
		SaveProductProtocolEnum protocol = PROTOCOL_MAP.get(saveProductTypeInteger);
		if (protocol == null) {
			return HTTP;
		}
		return protocol;
	}
}
