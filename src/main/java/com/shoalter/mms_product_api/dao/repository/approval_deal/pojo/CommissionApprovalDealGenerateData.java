package com.shoalter.mms_product_api.dao.repository.approval_deal.pojo;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class CommissionApprovalDealGenerateData {

	private String userCode;
	private Long recordRowId;
	private Integer merchantId;
	private BuCodeEnum buCode;
	private ProductMasterDto editProduct;
	private ProductMasterResultDto originalProduct;
	private BigDecimal newCommissionRate;
	private Integer newContractProdTermsId;
	private ProductStoreStatusDo originalProductStoreStatus;

}
