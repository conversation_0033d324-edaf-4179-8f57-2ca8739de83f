package com.shoalter.mms_product_api.dao.repository.api;

import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.service.product.FindRecordHistoryDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchUploadDto;
import com.shoalter.mms_product_api.service.product.pojo.projection.CheckSaveProductRecordsStatusProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SaveProductRecordRepository extends JpaRepository<SaveProductRecordDo, Long> {
    @Query(value = "select count(1) as count " +
            "from SAVE_PRODUCT_RECORD SPR " +
            "where SPR.UPLOAD_TYPE not in (1, 2) and SPR.STATUS not in (1, -1) and SPR.MERCHANT_ID = :merchantId", nativeQuery = true)
    int countBatchProcess(Integer merchantId);

	@Query(value = "select count(1) as count " +
			"from SAVE_PRODUCT_RECORD SPR " +
			"where SPR.STATUS not in (1, -1) and SPR.MERCHANT_ID = :merchantId", nativeQuery = true)
	int countMerchantInProcess(Integer merchantId);

	@Query(value = "select count(1) as count " +
			"from SAVE_PRODUCT_RECORD SPR " +
			"where SPR.STATUS not in (1, -1) and SPR.MERCHANT_ID = :merchantId and SPR.UPLOAD_TYPE = :uploadType", nativeQuery = true)
	int countRecordInProcess(Integer merchantId, Integer uploadType);

    @Transactional
    @Modifying
    @Query(value = "UPDATE SAVE_PRODUCT_RECORD SET CHECK_TIME = NOW(), CHECK_ID = :checkId " +
            "WHERE STATUS = :status AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) AND ID IN :recordId", nativeQuery = true)
    void updateCheckId(String checkId, Integer status, Integer checkSecond, List<Long> recordId);

    List<SaveProductRecordDo> findByCheckId(String checkId);

    @Query(value = "select * from SAVE_PRODUCT_RECORD spr " +
            "where spr.ID = :id " +
            "and spr.UPLOAD_TYPE = :uploadType ",nativeQuery = true)
    Optional<SaveProductRecordDo> findByIdAndUploadType(Long id, Integer uploadType);

    @Query(value = "select * from SAVE_PRODUCT_RECORD spr " +
            "where spr.ID IN :id " +
            "and spr.UPLOAD_TYPE = :uploadType " +
			"and spr.STATUS = :status ",nativeQuery = true)
    List<SaveProductRecordDo> findByIdsAndUploadTypeAndStatus(List<Long> id, Integer uploadType, Integer status);

	@Transactional
	@Modifying
	@Query(value = "UPDATE SAVE_PRODUCT_RECORD SET CHECK_TIME = NOW(), CHECK_ID = :checkId " +
			"WHERE STATUS = 0 AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) " +
			"AND MERCHANT_ID NOT IN ( " +
			"SELECT SPR.MERCHANT_ID FROM ( " +
			"SELECT MERCHANT_ID FROM SAVE_PRODUCT_RECORD WHERE STATUS >= 2 GROUP BY MERCHANT_ID HAVING COUNT(*) >= 1 " +
			") AS SPR " +
			") ORDER BY ID limit 1 ", nativeQuery = true)
	void updateProcessingRecordCheckIdByMerchantProcessCount(String checkId, Integer checkSecond);

    @Query(value = "select * from SAVE_PRODUCT_RECORD spr where GROUP_ID = :groupId ", nativeQuery = true)
    List<SaveProductRecordDo> findByGroupId(Long groupId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE SAVE_PRODUCT_RECORD set STATUS = :status where ID in :recordIdList ", nativeQuery = true)
    void updateStatusByRecordIdList(List<Long> recordIdList, int status);

	@Query(value = "SELECT" +
			"    SPR.ID as recordId," +
			"    SPR.UPLOAD_TYPE as uploadType," +
			"    SPR.FILE_NAME as fileName," +
			"    SPR.MERCHANT_ID as merchantId," +
			"    SPR.STATUS as status," +
			"    SPR.UPLOAD_TIME as dateOfUpload," +
			"    SPR.UPLOAD_USER_ID as uploadUserId," +
			"    SPR.SOURCE as source," +
			"    SPR.SOURCE_IDENTIFIER as sourceIdentifier" +
			" FROM" +
			"    SAVE_PRODUCT_RECORD SPR" +
			" JOIN SAVE_PRODUCT_RECORD_ROW SPRR ON SPRR.RECORD_ID = SPR.ID" +
			" LEFT JOIN MERCHANT M ON SPR.MERCHANT_ID = M.ID" +
			"    WHERE SPR.STATUS IN (:#{#findRecordHistoryDto.statusList}) " +
			"		AND SPR.UPLOAD_TYPE IN (:#{#findRecordHistoryDto.uploadTypeList}) " +
			"       AND (COALESCE(:#{#findRecordHistoryDto.filterOutUploadTypeList}) is null OR UPLOAD_TYPE not in :#{#findRecordHistoryDto.filterOutUploadTypeList}) " +
			"    	AND (SPR.MERCHANT_ID IN :#{#findRecordHistoryDto.merchantIdList} OR (SPR.MERCHANT_ID = -1 AND SPR.UPLOAD_USER_ID = :#{#findRecordHistoryDto.userId}))" +
			"    	AND (:#{#findRecordHistoryDto.skuId} is null OR (SPRR.SKU = :#{#findRecordHistoryDto.skuId}))" +
			"    	AND (:#{#findRecordHistoryDto.merchantName} is null OR (M.MERCHANT_NAME = :#{#findRecordHistoryDto.merchantName} OR M.MERCHANT_NAME_TCHI = :#{#findRecordHistoryDto.merchantName} OR M.MERCHANT_NAME_SCHI = :#{#findRecordHistoryDto.merchantName}))" +
			"    	AND (:#{#findRecordHistoryDto.startDate} is null OR SPR.UPLOAD_TIME >= :#{#findRecordHistoryDto.startDate})" +
			"    	AND (:#{#findRecordHistoryDto.endDate} is null OR SPR.UPLOAD_TIME <= :#{#findRecordHistoryDto.endDate})" +
			"	 GROUP BY SPR.ID" ,
			countQuery = "    SELECT count(record.recordId) FROM (" +
					"    SELECT" +
					"        SPR.ID as recordId" +
					"    FROM" +
					"        SAVE_PRODUCT_RECORD SPR" +
					" 	 JOIN SAVE_PRODUCT_RECORD_ROW SPRR ON SPRR.RECORD_ID = SPR.ID" +
					" 	 LEFT JOIN MERCHANT M ON SPR.MERCHANT_ID = M.ID" +
				"    WHERE SPR.STATUS IN (:#{#findRecordHistoryDto.statusList}) " +
				"		AND SPR.UPLOAD_TYPE IN (:#{#findRecordHistoryDto.uploadTypeList}) " +
				"       AND (COALESCE(:#{#findRecordHistoryDto.filterOutUploadTypeList}) is null OR UPLOAD_TYPE not in :#{#findRecordHistoryDto.filterOutUploadTypeList}) " +
				"    	AND (SPR.MERCHANT_ID IN :#{#findRecordHistoryDto.merchantIdList} OR (SPR.MERCHANT_ID = -1 AND SPR.UPLOAD_USER_ID = :#{#findRecordHistoryDto.userId}))" +
				"    	AND (:#{#findRecordHistoryDto.skuId} is null OR (SPRR.SKU = :#{#findRecordHistoryDto.skuId}))" +
				"    	AND (:#{#findRecordHistoryDto.merchantName} is null OR (M.MERCHANT_NAME = :#{#findRecordHistoryDto.merchantName} OR M.MERCHANT_NAME_TCHI = :#{#findRecordHistoryDto.merchantName} OR M.MERCHANT_NAME_SCHI = :#{#findRecordHistoryDto.merchantName}))" +
				"    	AND (:#{#findRecordHistoryDto.startDate} is null OR SPR.UPLOAD_TIME >= :#{#findRecordHistoryDto.startDate})" +
				"    	AND (:#{#findRecordHistoryDto.endDate} is null OR SPR.UPLOAD_TIME <= :#{#findRecordHistoryDto.endDate})" +
				"	 GROUP BY SPR.ID) as record",
			nativeQuery = true)
	Page<BatchUploadDto> findBatchUploadRecords(@Param("findRecordHistoryDto") FindRecordHistoryDto findRecordHistoryDto, Pageable pageable);

	@Query(value = "select spr.* from SAVE_PRODUCT_RECORD spr " +
		"join SAVE_PRODUCT_RECORD_ROW sprr on spr.ID = sprr.RECORD_ID " +
		"where sprr.ID = :recordRowId", nativeQuery = true)
	Optional<SaveProductRecordDo> findByRecordRowId(Long recordRowId);

	@Query(value = "SELECT " +
			"spr.ID AS recordId, " +
			"spr.STATUS AS recordStatus, " +
			"sprr.UUID AS uuid, " +
			"sprr.SKU AS sku, " +
			"sprr.ERROR_MESSAGE AS errorMessage, " +
			"sprr.STATUS AS rowStatus " +
		"FROM SAVE_PRODUCT_RECORD spr " +
		"LEFT JOIN SAVE_PRODUCT_RECORD_ROW sprr on spr.ID = sprr.RECORD_ID " +
		"WHERE spr.ID IN :recordIds " +
		"ORDER BY spr.ID", nativeQuery = true)
	List<CheckSaveProductRecordsStatusProjection> findByRecordIds(List<Long> recordIds);

	@Query(value = "SELECT ID FROM SAVE_PRODUCT_RECORD " +
		"WHERE STATUS = :status " +
		"AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) " +
		"ORDER BY ID limit :queryTaskCount", nativeQuery = true)
	List<Long> findRecordIdByStatusAndCheckTime(Integer status, Integer checkSecond, Integer queryTaskCount);

	@Query(value = "SELECT ID FROM SAVE_PRODUCT_RECORD " +
		"WHERE STATUS = :status " +
		"AND PROTOCOL = :protocol " +
		"AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) " +
		"ORDER BY ID limit :queryTaskCount", nativeQuery = true)
	List<Long> findRecordIdByStatusAndProtocolAndCheckTime(Integer status, String protocol, Integer checkSecond, Integer queryTaskCount);

	@Query(value = "SELECT ID FROM SAVE_PRODUCT_RECORD " +
		"WHERE STATUS = :status " +
		"AND PROTOCOL = :protocol " +
		"AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) " +
		"AND UPLOAD_TYPE in :uploadTypes " +
		"ORDER BY ID limit :queryTaskCount", nativeQuery = true)
	List<Long> findRecordIdByStatusAndProtocolAndCheckTimeAndUploadType(Integer status, String protocol, Integer checkSecond, Integer queryTaskCount, Collection<Integer> uploadTypes);

	@Transactional
	@Modifying
	@Query(value = "UPDATE SAVE_PRODUCT_RECORD set CHECK_TIME = NOW() where ID in :recordIdList ", nativeQuery = true)
	void updateCheckTimeByRecordIds(Set<Long> recordIdList);

	List<SaveProductRecordDo> findAllByIdIn(Collection<Long> ids);

	@Query(value = "SELECT ID FROM SAVE_PRODUCT_RECORD WHERE UPLOAD_TIME < :localDateTime order by ID desc", nativeQuery = true)
	List<Long> findIdsByCreatedDateBefore(LocalDateTime localDateTime);

	@Modifying
	@Transactional
	@Query(value = "DELETE FROM SAVE_PRODUCT_RECORD WHERE ID <= :id limit :limitCount", nativeQuery = true)
	void deleteByIdAndLimitCount(Long id, int limitCount);
}
