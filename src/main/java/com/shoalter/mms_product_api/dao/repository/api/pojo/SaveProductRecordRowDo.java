package com.shoalter.mms_product_api.dao.repository.api.pojo;

import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "SAVE_PRODUCT_RECORD_ROW")
@Data
public class SaveProductRecordRowDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Long id;
    @Column(name = "RECORD_ID")
    private Long recordId;
    @Column(name = "SKU")
    private String sku;
    @Column(name = "UUID")
    private String uuid;
    @Column(name = "STATUS")
    private Integer status;
    @Column(name = "CONTENT")
    private String content;
    @Column(name = "NOT_SYNC_HYBRIS")
    private boolean notSyncHybris;
    @Column(name = "ERROR_MESSAGE")
    private String errorMessage;
    @Column(name = "PARENT_PRODUCT_ROW_ID")
    private Long parentProductRowId;
}
