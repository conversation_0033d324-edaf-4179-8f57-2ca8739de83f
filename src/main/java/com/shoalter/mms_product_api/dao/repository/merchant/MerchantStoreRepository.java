package com.shoalter.mms_product_api.dao.repository.merchant;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantStoreDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface MerchantStoreRepository extends JpaRepository<MerchantStoreDo, Integer> {

    @Query(value =
            "SELECT * " +
            "FROM MERCHANT_STORE " +
            "WHERE STORE_ID = :storeId", nativeQuery = true)
    Optional<MerchantStoreDo> findByStoreId(Integer storeId);

    @Query(value = "select MERCHANT_id  from MERCHANT_STORE ms where Store_id = :storeId ", nativeQuery = true)
    Integer findMerchantIdByStoreId(Integer storeId);

	@Query(value = "select * from MERCHANT_STORE where Store_id IN :storeIds ", nativeQuery = true)
	List<MerchantStoreDo> findMerchantStoresByStoreIds(List<Integer> storeIds);

	@Query(value = "SELECT ms.* " +
		"FROM MERCHANT_STORE ms " +
		" JOIN STORE s ON ms.STORE_ID = s.ID" +
		" JOIN BUS_UNIT bu ON s.BUS_UNIT_ID = bu.ID AND bu.CODE = :buCode " +
		" WHERE s.STOREFRONT_STORE_CODE = :storefrontStoreCode",
		nativeQuery = true)
	List<MerchantStoreDo> findByStorefrontStoreCode(String storefrontStoreCode, String buCode);
}
