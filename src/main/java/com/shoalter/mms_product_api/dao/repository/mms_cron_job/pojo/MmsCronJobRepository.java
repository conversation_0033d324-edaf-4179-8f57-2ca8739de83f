package com.shoalter.mms_product_api.dao.repository.mms_cron_job.pojo;

import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobNameEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsServiceNameEnum;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface MmsCronJobRepository extends JpaRepository<MmsCronJobDo, Long> {

	Optional<MmsCronJobDo> findByServiceNameAndJobName(MmsServiceNameEnum mmsServiceNameEnum, MmsCronJobNameEnum cronJobNameEnum);
}
