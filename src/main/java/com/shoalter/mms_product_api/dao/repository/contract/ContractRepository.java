package com.shoalter.mms_product_api.dao.repository.contract;

import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.service.product.pojo.IContractStoreDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ContractRepository extends JpaRepository<ContractDo, Integer> {
    @Query(value =
            "SELECT c.* " +
            "FROM CONTRACT c " +
            "INNER JOIN CONTRACT_TYPE ct " +
            "ON c.CONTRACT_TYPE_ID = ct.ID " +
            "WHERE ct.CODE = 'SC' AND c.STATUS = 'Active' " +
            "AND c.MASTER_CONTRACT_ID = :masterContractId AND :date >= c.START_DATE " +
            "ORDER BY c.START_DATE DESC, c.ID DESC ", nativeQuery = true)
    List<ContractDo> findSupplementaryContractList(Integer masterContractId, Date date);

    @Query(value=
            "SELECT ( CASE WHEN (c.MASTER_CONTRACT_ID IS NULL) "+
            "THEN ct.CODE ELSE mct.CODE END ) CONTRACT_TYPE " +
            "FROM CONTRACT c "+
            "LEFT JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
            "LEFT JOIN CONTRACT mc ON c.MASTER_CONTRACT_ID = mc.ID "+
            "LEFT JOIN CONTRACT_TYPE mct ON mc.CONTRACT_TYPE_ID = mct.ID " +
            "WHERE c.ID = :contractId LIMIT 1 ",nativeQuery = true)
    String findMainContractTypeInContract(Integer contractId);

    @Query(value = "select count(1) from CONTRACT c  " +
            "join CONTRACT_TYPE ct on c.CONTRACT_TYPE_ID =ct.id " +
            "LEFT JOIN CONTRACT mc ON c.MASTER_CONTRACT_ID = mc.ID  " +
            "LEFT JOIN CONTRACT_TYPE mct ON mc.CONTRACT_TYPE_ID = mct.ID  " +
            "where (ct.CODE ='BC' or mct.CODE ='BC') " +
            "and c.id= :contractId ",nativeQuery = true)
    int countIsBCContract(Integer contractId);

	@Query(value = "SELECT " +
			"    c.* " +
			"FROM " +
			"    CONTRACT c " +
			"        JOIN " +
			"    PRODUCT p ON p.CONTRACT_ID = c.ID " +
			"WHERE " +
			"    uuid = :uuid ", nativeQuery = true)
	ContractDo findByProductUuid(String uuid);

	@Query(value = "SELECT DISTINCT" +
			"    c.id " +
			" FROM CONTRACT c" +
			"        INNER JOIN CONTRACT_PROD_TERMS cpt ON c.ID = cpt.CONTRACT_ID" +
			"        INNER JOIN STORE s ON cpt.STORE_ID = s.Id" +
			" WHERE s.id = :storeId" +
			"      AND c.STATUS = 'Active'" +
			"      AND c.MASTER_CONTRACT_ID IS NULL" +
			"      AND DATE_FORMAT(NOW(), '%Y-%m-%d') >= DATE_FORMAT(c.START_DATE, '%Y-%m-%d')" +
			"      AND DATE_FORMAT(c.END_DATE, '%Y-%m-%d') >= DATE_FORMAT(NOW(), '%Y-%m-%d')", nativeQuery = true)
	int findContractIdByStoreId(Integer storeId);

	@Query(value = "SELECT " +
		"       MAX(c.ID) as contractId, " +
		"       s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"       s.Id as storeId " +
		"FROM CONTRACT c " +
		"         INNER JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
		"         INNER JOIN BUS_UNIT bu ON ct.BUS_UNIT_ID = bu.ID " +
		"         INNER JOIN CONTRACT_PROD_TERMS cpt on c.ID = cpt.CONTRACT_ID " +
		"         INNER JOIN STORE s ON cpt.STORE_ID = s.Id and s.STOREFRONT_STORE_CODE in :storefrontStoreCodes " +
		"WHERE bu.CODE = :buCode " +
		"  AND c.MASTER_CONTRACT_ID IS NULL " +
		"GROUP BY s.STOREFRONT_STORE_CODE, s.Id",nativeQuery = true)
	List<IContractStoreDto> findLastMainContractByBuCodeAndStorefrontStoreCodes(String buCode, Set<String> storefrontStoreCodes);

	@Query(value = "SELECT c.MERCHANT_ID FROM CONTRACT c WHERE c.ID IN :contractIds ", nativeQuery = true)
	List<Integer> findMerchantIdsByContractIds(List<Integer> contractIds);
}
