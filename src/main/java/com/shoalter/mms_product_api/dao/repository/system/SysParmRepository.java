package com.shoalter.mms_product_api.dao.repository.system;

import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SysParmRepository extends JpaRepository<SysParmDo, Integer> {

    List<SysParmDo> findBySegmentAndCode(String segment, String code);

    @Query(value = "SELECT * FROM SYS_PARM WHERE SEGMENT = :segment and CODE = :code and (PLATFORM_ID is null or PLATFORM_ID = :platformId) ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndCodeAndPlatformId(String segment, String code, Integer platformId);

    @Query(value = "SELECT * FROM SYS_PARM WHERE SEGMENT = :segment and (PLATFORM_ID is null or PLATFORM_ID = :platformId) ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndPlatformId(String segment, Integer platformId);

    @Query(value = "SELECT * FROM SYS_PARM WHERE SEGMENT IN (:segments) and CODE is not null and SHORT_DESC is not null", nativeQuery = true)
    List<SysParmDo> findBySegments(List<String> segments);

	/**
	 * Use this query when you need to perform groupBy operations with PARENT_CODE.
	 * Since PARENT_CODE is nullable in the SYS_PARM table, this method ensures only records with non-null PARENT_CODE are retrieved to prevent grouping issues(NPE).
	 * Note: If grouping is not required, consider using the standard findBySegments method instead.
	 */
	@Query(value = "SELECT * FROM SYS_PARM WHERE SEGMENT IN (:segments) and CODE is not null and PARENT_CODE is not null and SHORT_DESC is not null", nativeQuery = true)
	List<SysParmDo> findBySegmentsHavingParentCode(List<String> segments);

    @Query(value = "SELECT * FROM SYS_PARM WHERE PARENT_SEGMENT = :parentSegment and PARENT_CODE = :parentCode and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findByParentSegmentAndParentCodeAndBuCode(String parentSegment, String parentCode, String buCode);

    @Query(value = "SELECT * FROM SYS_PARM WHERE SEGMENT = :segment and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndBuCode(String segment, String buCode);

	@Query(value = "SELECT sp.PARM_VALUE FROM SYS_PARM sp WHERE SEGMENT = :segment and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
	List<String> findParmValueBySegmentAndBuCode(String segment, String buCode);

	@Query(value = "SELECT sp.PARM_VALUE FROM SYS_PARM sp WHERE SEGMENT = :segment and CODE = :code and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ ", nativeQuery = true)
	List<String> findParmValueBySegmentAndCodeAndPlatformId(String segment, String code, String buCode);

    @Query(value =
            "select * " +
                    "from SYS_PARM sp " +
                    "inner join STORE_PICKUP_DAYS spd " +
                    "on sp.CODE = spd.PICKUP_DAYS " +
                    "where SEGMENT = 'PICKUP_DAYS' " +
                    "and spd.STORE_ID = :storeId " +
                    "and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findPickupDaysByBuCodeAndStoreId(String buCode, Integer storeId);

    @Query(value =
            "SELECT * " +
                    "FROM SYS_PARM " +
                    "WHERE SEGMENT = :segment " +
                    "and (:code is null or (CODE = :code)) " +
                    "and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndBuCodeAndCode(String segment, String buCode, String code);

    @Query(value =
            "SELECT * " +
                    "FROM SYS_PARM " +
                    "WHERE SEGMENT = :segment " +
                    "and (:code is null or (CODE = :code)) " +
                    "and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) " +
                    "and (:shortDesc is null or (SHORT_DESC = :shortDesc))" +
                    "order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndBuCodeAndCodeAndShortDesc(String segment, String buCode, String code, String shortDesc);

	@Query(value =
			"SELECT * " +
					"FROM SYS_PARM " +
					"WHERE SEGMENT = :segment " +
					"and SHORT_DESC in :shortDescList " +
					"and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
	List<SysParmDo> findBySegmentAndBuCodeAndShortDescList(String segment, String buCode, List<String> shortDescList);

    @Query(value =
            "SELECT * " +
                    "FROM SYS_PARM " +
                    "WHERE SEGMENT = :segment " +
                    "and (COALESCE(:codeList) is null or CODE in (:codeList)) " +
                    "and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) " +
                    "order by DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findBySegmentAndBuCodeAndCodeList(String segment, String buCode, List<String> codeList);

	@Query(value =
		"SELECT * " +
			"FROM SYS_PARM " +
			"WHERE SEGMENT = :segment " +
			"and (COALESCE(:codeList) is null or PARENT_CODE in (:codeList)) " +
			"order by DISP_SEQ", nativeQuery = true)
	List<SysParmDo> findBySegmentAndParentCodeList(String segment, List<String> codeList);

	@Query(value =
		"SELECT * " +
			"FROM SYS_PARM " +
			"WHERE SEGMENT = :segment " +
			"and (COALESCE(:codeSet) is null or PARENT_CODE in (:codeSet)) " +
			"order by DISP_SEQ", nativeQuery = true)
	List<SysParmDo> findBySegmentAndParentCodeList(String segment, Set<String> codeSet);

    @Query(value =
            "SELECT count(1) " +
                    "FROM SYS_PARM " +
                    "WHERE SEGMENT = :segment " +
                    "and (:parmValue is null or (PARM_VALUE = :parmValue)) " +
                    "and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
    int countBySegmentAndBuCodeAndParmValue(String segment, String buCode, String parmValue);


    List<SysParmDo> findBySegment(String segment);


    @Query(value =
            "select * from SYS_PARM sp " +
                    "where Segment = :segment and code  = :code " +
                    "order by Segment ,DISP_SEQ " +
                    "Limit 1 ", nativeQuery = true)
    SysParmDo findTopBySegmentAndCodeOrderBySegmentAscDispSeqAsc(String segment, String code);

    @Query(value = "SELECT T2.* FROM SYS_PARM T1 " +
            "JOIN SYS_PARM T2 ON T2.PARENT_SEGMENT = T1.SEGMENT AND T2.PARENT_CODE = T1.CODE " +
            "WHERE T1.SEGMENT = :segment AND (T1.PLATFORM_ID is null or T1.PLATFORM_ID = :platformId) " +
            "ORDER BY T2.SEGMENT, T2.DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findChildBySegmentAndPlatformId(String segment, Integer platformId);

    @Query(value = "SELECT T2.* FROM SYS_PARM T1 " +
            "JOIN SYS_PARM T2 ON T2.PARENT_SEGMENT = T1.SEGMENT AND T2.PARENT_CODE = T1.CODE " +
            "WHERE T1.SEGMENT IN (:segments) AND (T1.PLATFORM_ID is null or T1.PLATFORM_ID = :platformId) " +
            "ORDER BY T2.SEGMENT, T2.DISP_SEQ", nativeQuery = true)
    List<SysParmDo> findChildBySegmentsAndPlatformId(List<String> segments, Integer platformId);

    @Query(value = "select count(*) from  SYS_PARM p " +
            "join STORE_PICKUP_DAYS spd on spd.PICKUP_DAYS = p.code " +
            "where PICKUP_DAYS = :pickupDays and STORE_ID = :storeId " +
            "and p.segment = :segment ", nativeQuery = true)
    int countStorePickupDay(String pickupDays, Integer storeId, String segment);

	@Query(value =
			"SELECT * " +
			"FROM SYS_PARM " +
			"WHERE SEGMENT = :segment " +
			"AND PARM_VALUE = :parmValue " +
			"and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) order by DISP_SEQ", nativeQuery = true)
	List<SysParmDo> findBySegmentAndParmValue(String segment, String parmValue, String buCode);


	@Query(value = "SELECT " +
			"    CONCAT(sp.CODE, ' : ', sp.SHORT_DESC) packingBoxType " +
			"FROM SYS_PARM sp " +
			"WHERE SEGMENT IN (:segments) " +
			"        AND (PLATFORM_ID IS NULL " +
			"        OR (:buCode IS NULL " +
			"        OR (PLATFORM_ID = (SELECT b.PLATFORM_ID FROM BUS_UNIT b " +
			"        WHERE b.CODE = :buCode)))) " +
			"ORDER BY DISP_SEQ", nativeQuery = true)
	List<String> findBySegmentsAndBuCode(List<String> segments, String buCode);

	Optional<SysParmDo> findBySegmentAndCodeAndParmValue(String segment, String code, String parmValue);

	@Query(value = "SELECT * FROM SYS_PARM " +
		"WHERE PARENT_SEGMENT = :parentSegment " +
		"and PARENT_CODE = :parentCode " +
		"and CODE = :code " +
		"and (PLATFORM_ID is null or (:buCode is null or (PLATFORM_ID =  (select b.PLATFORM_ID from BUS_UNIT b where b.CODE = :buCode)))) " +
		"order by DISP_SEQ", nativeQuery = true)
	List<SysParmDo> findByParentSegmentAndParentCodeAndCodeAndBuCode(String parentSegment, String parentCode, String code, String buCode);

	List<SysParmDo> findBySegmentAndCodeIn(String segment, Set<String> codeSet);

	List<SysParmDo> findBySegmentAndCodeNotIn(String segment, Set<String> codeSet);
}
