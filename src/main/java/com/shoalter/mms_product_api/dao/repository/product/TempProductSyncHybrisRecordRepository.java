package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.TempProductSyncHybrisRecordDo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface TempProductSyncHybrisRecordRepository extends JpaRepository<TempProductSyncHybrisRecordDo, Integer> {

	Optional<TempProductSyncHybrisRecordDo> findByTraceId(String traceId);
}
