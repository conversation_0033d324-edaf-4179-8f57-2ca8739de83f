package com.shoalter.mms_product_api.dao.repository.product.pojo;

import java.math.BigDecimal;

public interface ProductImportViewDo {
	String getSkuId();
	String getProductCode();
	Integer getId();
	String getSkuName();
	String getColor();
	String getSize();
	String getIsPrimarySku();
	BigDecimal getOriginalPrice();
	BigDecimal getSellingPrice();
	String getCategoryCode();
	String getLongDescription();
	String getShortDescription();
	String getBrandName();
}
