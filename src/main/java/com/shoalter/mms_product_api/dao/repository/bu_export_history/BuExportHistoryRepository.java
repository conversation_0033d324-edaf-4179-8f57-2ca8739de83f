package com.shoalter.mms_product_api.dao.repository.bu_export_history;

import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.BuExportHistoryDo;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.AffiliateProductDataProjection;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.BuPartyHistoryOverviewProjection;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallExportInfoProjection;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallProductDataProjection;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface BuExportHistoryRepository extends JpaRepository<BuExportHistoryDo, Integer> {

	@Query(value = "SELECT " +
		"    beh.ID AS historyRecordId," +
		"    beh.BU AS buCode," +
		"    beh.STATUS AS recordStatus," +
		"    beh.CREATED_DATE AS createTime," +
		"    beh.CREATED_BY AS createBy," +
		"    beh.SKU_COUNT AS productCounts," +
		"    beh.IS_LATEST_DATA AS isLatestData," +
		"    beh.EXPORT_TYPE AS exportType," +
		"    beh.ERROR_REASON AS failReason," +
		"    tei.INFORM_PRODUCT_TO_SYNC_PROGRESS AS informProductToSyncProgress" +
		" FROM" +
		"    BU_EXPORT_HISTORY beh LEFT JOIN TMALL_EXPORT_INFO tei on beh.id = tei.id" +
		" WHERE beh.BU IN (:queryCreateByBuCodes) AND beh.CREATED_BY = :userCode " +
		"    AND (:searchStartDate IS NULL OR beh.CREATED_DATE BETWEEN :searchStartDate AND :searchEndDate)"
		, nativeQuery = true,
		countQuery = "SELECT " +
			"    COUNT(exportHistory.id)" +
			" FROM" +
			"    (SELECT " +
			"        beh.ID" +
			"    FROM" +
			"        BU_EXPORT_HISTORY beh" +
			" WHERE beh.BU IN :queryCreateByBuCodes AND beh.CREATED_BY = :userCode " +
			"            AND (:searchStartDate is null or beh.CREATED_DATE BETWEEN :searchStartDate AND :searchEndDate)" +
			"    ) exportHistory")
	Page<BuPartyHistoryOverviewProjection> findBuHistoryOverviewByUserCodeOrBuOrCreateTime(String userCode, List<String> queryCreateByBuCodes, Date searchStartDate, Date searchEndDate, Pageable pageable);

	@Query(value = "SELECT " +
		"    beh.ID AS historyRecordId," +
		"    beh.BU AS buCode," +
		"    beh.STATUS AS recordStatus," +
		"    beh.CREATED_DATE AS createTime," +
		"    beh.CREATED_BY AS createBy," +
		"    beh.SKU_COUNT AS productCounts," +
		"    beh.IS_LATEST_DATA AS isLatestData," +
		"    beh.EXPORT_TYPE AS exportType," +
		"    beh.ERROR_REASON AS failReason," +
		"    tei.INFORM_PRODUCT_TO_SYNC_PROGRESS AS informProductToSyncProgress" +
		" FROM" +
		"    BU_EXPORT_HISTORY beh LEFT JOIN TMALL_EXPORT_INFO tei on beh.id = tei.id " +
		" WHERE ((beh.BU IN (:queryCreateByBuCodes) AND beh.CREATED_BY = :userCode) OR (beh.BU IN (:queryRmBuCodes))) " +
		"    AND (:searchStartDate IS NULL OR beh.CREATED_DATE BETWEEN :searchStartDate AND :searchEndDate)"
		, nativeQuery = true,
		countQuery = "SELECT " +
			"    COUNT(exportHistory.id)" +
			" FROM" +
			"    (SELECT " +
			"        beh.ID" +
			"    FROM" +
			"        BU_EXPORT_HISTORY beh" +
			" WHERE ((beh.BU IN (:queryCreateByBuCodes) AND beh.CREATED_BY = :userCode) OR (beh.BU IN (:queryRmBuCodes))) " +
			"            AND (:searchStartDate is null or beh.CREATED_DATE BETWEEN :searchStartDate AND :searchEndDate)" +
			"    ) exportHistory")
	Page<BuPartyHistoryOverviewProjection> findRmRoleBuHistoryOverviewByUserCodeOrBuOrCreateTime(String userCode, List<String> queryCreateByBuCodes, List<String> queryRmBuCodes, Date searchStartDate, Date searchEndDate, Pageable pageable);

	@Query(value = "SELECT " +
		"     beh.EXPORT_TYPE as exportType, ts.SKU_DATA as skuData, ts.shop_title as shopTitle, tei.STOREFRONT_STORE_CODE as storefrontStoreCode, ts.NUM_IID as numIid" +
		" FROM BU_EXPORT_HISTORY beh" +
		"  JOIN TMALL_EXPORT_INFO tei ON tei.id = beh.id" +
		"  JOIN TMALL_SKU ts ON ts.TMALL_EXPORT_INFO_ID = tei.id" +
		" WHERE" +
		"    beh.id = :id" +
		"    AND beh.STATUS = :buExportHistoryStatus", nativeQuery = true)
	List<TmallProductDataProjection> findTmallProductDataByIdAndStatus(Integer id, String buExportHistoryStatus);

	@Transactional
	@Modifying
	@Query(value = "UPDATE BU_EXPORT_HISTORY SET CHECK_TIME = NOW(), CHECK_ID = :checkId " +
		"WHERE BU = :buCode AND STATUS = :status AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) and ID in :ids ORDER BY ID limit :queryTaskCount ", nativeQuery = true)
	void updateCheckId(String buCode, String checkId, String status, Integer checkSecond, Integer queryTaskCount, List<Integer> ids);

	@Query(value = "select *,  0 AS clazz_ FROM BU_EXPORT_HISTORY " +
		"WHERE BU = :buCode AND STATUS = :status AND (CHECK_TIME is null or TIMESTAMPDIFF(SECOND, CHECK_TIME, NOW()) > :checkSecond) ORDER BY ID limit :queryTaskCount ", nativeQuery = true)
	List<BuExportHistoryDo> queryCheckId(String buCode, String status, Integer checkSecond, Integer queryTaskCount);

	@Query(value = "SELECT BEH.EXPORT_TYPE as exportType, AFS.SKU_DATA as skuData " +
		"FROM BU_EXPORT_HISTORY BEH " +
		" JOIN AFFILIATE_EXPORT_INFO AEI ON BEH.ID = AEI.ID " +
		" JOIN AFFILIATE_SKU AFS ON AFS.AFFILIATE_EXPORT_INFO_ID = AEI.ID " +
		"WHERE BEH.ID = :id " +
		" AND BEH.`STATUS` = :buExportHistoryStatus " +
		" ORDER BY AFS.CATEGORY" , nativeQuery = true)
	List<AffiliateProductDataProjection> findAffiliateProductDataByIdAndStatus(Integer id, String buExportHistoryStatus);

	@Transactional
	@Modifying
	@Query(value = "UPDATE BU_EXPORT_HISTORY SET CHECK_TIME = :checkTime " +
		"WHERE CHECK_ID = :checkId AND ID = :recordId ", nativeQuery = true)
	int updateCheckTimeByCheckIdAndRecordId(String checkId, Integer recordId, Date checkTime);

	@Query(value = "select * , 0 AS clazz_ " +
		"from BU_EXPORT_HISTORY where BU = 'Tmall' " +
		"                       and IS_LATEST_DATA = 1 " +
		"                       and EXPORT_TYPE = :exportType", nativeQuery = true)
	Optional<BuExportHistoryDo> findTmallHasDataExportHistoryByExportType(String exportType);

	Optional<BuExportHistoryDo> findByExportTypeAndStatus(String exportType, ExportStatusEnum status);

	@Transactional
	@Modifying
	@Query(value = "UPDATE BU_EXPORT_HISTORY set CHECK_TIME = NOW() where ID in :idList ", nativeQuery = true)
	void updateCheckTimeByIds(Set<Integer> idList);

	@Query(value =
		"SELECT tei.id AS id, tei.INFORM_PRODUCT_TO_SYNC_PROGRESS AS infromProductToSyncProgress "
			+ "FROM TMALL_EXPORT_INFO tei "
			+ "WHERE tei.INFORM_PRODUCT_TO_SYNC_PROGRESS = :informProductToSyncProgress", nativeQuery = true)
	List<TmallExportInfoProjection> countByInformProductToSyncProgress(String informProductToSyncProgress);

	@Query(value =
		"SELECT " +
			"beh.id " +
			"FROM BU_EXPORT_HISTORY beh " +
			"JOIN TMALL_EXPORT_INFO tei ON tei.ID = beh.ID " +
			"WHERE beh.STATUS = 'SUCCESS' " +
			"AND beh.IS_LATEST_DATA = 1 " +
			"AND tei.INFORM_PRODUCT_TO_SYNC_PROGRESS = :informProductToSyncProgress " +
			"LIMIT :limitCount",
		nativeQuery = true)
	Set<Integer> findTmallProductDataByInformProductToSyncProgressAndLimit(
		String informProductToSyncProgress, int limitCount);

	@Transactional
	@Modifying
	@Query(value =
		"UPDATE TMALL_EXPORT_INFO tei "
		+ "SET tei.INFORM_PRODUCT_TO_SYNC_PROGRESS = :informProductToSyncProgress "
		+ "WHERE tei.ID IN (:ids)", nativeQuery = true)
	void updateInformProductToSyncProgressByIds(String informProductToSyncProgress, Collection<Integer> ids);
}

