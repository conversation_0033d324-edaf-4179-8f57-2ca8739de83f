package com.shoalter.mms_product_api.dao.repository.brand;

import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandDo;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameTcViewDo;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandNameEnViewDo;
import com.shoalter.mms_product_api.service.product.pojo.BrandIdMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.BrandMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.BrandNameMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.response.BrandDto;
import com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface BrandRepository extends JpaRepository<BrandDo, Integer> {
    @Query(value = "select B.* " +
            "from BRAND B " +
            "inner join BUS_UNIT BU on B.BUS_UNIT_ID = BU.ID " +
            "where BU.CODE = :buCode and B.ID in :ids ", nativeQuery = true)
    List<BrandDo> findByBuCodeAndIds(String buCode, List<Integer> ids);

    @Query(value = "SELECT B.BRAND_NAME_EN " +
            "FROM BRAND B " +
            "WHERE B.STATUS = :status " +
            "AND B.BUS_UNIT_ID = :busUnitId " +
            "AND B.BRAND_NAME_EN IS NOT NULL AND B.BRAND_NAME_EN <> '' " +// is not empty
            "ORDER by B.BRAND_NAME_EN ",nativeQuery = true)
    List<String> findListByStatusAndBuId(String status, Integer busUnitId);

    @Query(value = "SELECT B.BRAND_NAME_EN " +
            "FROM BRAND B " +
            "WHERE B.ID IN :brandIdList " +
            "AND B.BRAND_NAME_EN IS NOT NULL AND B.BRAND_NAME_EN <> '' " +// is not empty
            "ORDER by B.BRAND_NAME_EN ",nativeQuery = true)
    List<String> findBrandNameById(List<Integer> brandIdList);

    List<BrandNameEnViewDo> findBrandNameEnByIdIn(List<Integer> idList);

	List<BrandNameTcViewDo> findBrandNameTcByIdIn(List<Integer> idList);

    /**
     *  only batch use.
     *  with the same brand name then get the first row.
     */
    @Query(value ="SELECT min(ID) as ID , B.BRAND_NAME_EN as BrandNameEn " +
            "FROM BRAND B  " +
            "where B.BUS_UNIT_ID = 85 " +
            "AND B.BRAND_NAME_EN in :brandNameEn " +
            "group by B.BRAND_NAME_EN  " +
            "ORDER by B.BRAND_NAME_EN ",nativeQuery = true) // 目前以HKTV的BRAND為主要查詢
    List<BrandNameEnViewDo> findIdByBrandNameEnIn(List<String> brandNameEn);

	@Query(value = "SELECT ID FROM BRAND WHERE BRAND_NAME_EN = :brandName OR BRAND_NAME_TC = :brandName", nativeQuery = true)
	List<Integer> findIdByBrandNameEnOrBrandNameTc(String brandName);

	@Query(value = "SELECT b.ID FROM BRAND b WHERE b.BRAND_CODE = :brandCode", nativeQuery = true)
	List<Integer> findIdByBrandCode(String brandCode);


	@Query(value =
		"select new com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto(b.id,b.brandNameEn, b.brandNameTc,b.brandNameSc) "
		+ "from BrandDo as b where b.id = :id")
	FindBrandDto findBrandById(Integer id);

	@Query(value = "select B.ID as id, B.BRAND_CODE as brandCode, B.BRAND_NAME_EN as brandNameEn, " +
		"B.BRAND_NAME_TC as brandNameTc, B.BRAND_NAME_SC as brandNameSc " +
		"from BRAND B " +
		"inner join BUS_UNIT BU on B.BUS_UNIT_ID = BU.ID " +
		"where BU.CODE = :#{#request.buCode} and B.STATUS = 'A' " +
		"and (:#{#request.brandId} is null or B.ID = :#{#request.brandId}) "  +
		"and ((:#{#request.brand} is null or B.BRAND_CODE like %:#{#request.brand}%) or " +
		"(:#{#request.brand} is null or B.BRAND_NAME_EN like %:#{#request.brand}%) or " +
		"(:#{#request.brand} is null or B.BRAND_NAME_TC like %:#{#request.brand}%) or " +
		"(:#{#request.brand} is null or B.BRAND_NAME_SC like %:#{#request.brand}%)) ",
		countQuery = "select count(*) " +
		"from BRAND B " +
		"inner join BUS_UNIT BU on B.BUS_UNIT_ID = BU.ID " +
		"where BU.CODE = :#{#request.buCode} and B.STATUS = 'A' " +
		"and (:#{#request.brandId} is null or B.ID = :#{#request.brandId}) "  +
		"and ((:#{#request.brand} is null or B.BRAND_CODE like %:#{#request.brand}%) or "  +
		"(:#{#request.brand} is null or B.BRAND_NAME_EN like %:#{#request.brand}%) or " +
		"(:#{#request.brand} is null or B.BRAND_NAME_TC like %:#{#request.brand}%) or " +
		"(:#{#request.brand} is null or B.BRAND_NAME_SC like %:#{#request.brand}%)) ",nativeQuery = true)
	Page<BrandDto> findBrandPage(BrandMainRequestData request, Pageable pageable);

	@Query(value = "select B.ID as id, B.BRAND_CODE as brandCode, B.BRAND_NAME_EN as brandNameEn, " +
		"B.BRAND_NAME_TC as brandNameTc, B.BRAND_NAME_SC as brandNameSc from BRAND B " +
		"inner join BUS_UNIT BU on B.BUS_UNIT_ID = BU.ID " +
		"where BU.CODE = :#{#request.buCode} and B.BRAND_NAME_EN in :#{#request.brandNameEns} ", nativeQuery = true)
	List<BrandDto> findBrandByNames(BrandNameMainRequestData request);

	@Query(value = "select B.ID as id, B.BRAND_CODE as brandCode, B.BRAND_NAME_EN as brandNameEn, " +
		"B.BRAND_NAME_TC as brandNameTc, B.BRAND_NAME_SC as brandNameSc from BRAND B " +
		"where B.ID in :#{#request.brandIds} ", nativeQuery = true)
	List<BrandDto> findBrandByIds(BrandIdMainRequestData request);
}
