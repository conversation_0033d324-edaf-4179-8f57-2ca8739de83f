package com.shoalter.mms_product_api.dao.repository.convert;


import com.shoalter.mms_product_api.util.StringUtil;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Converter
public class StringListConverter implements AttributeConverter<List<String>, String> {

	@Override
	public String convertToDatabaseColumn(List<String> attribute) {
		return attribute != null ? String.join(StringUtil.COMMA, attribute) : null;
	}

	@Override
	public List<String> convertToEntityAttribute(String dbData) {
		return dbData != null ? Arrays.stream(dbData.split(StringUtil.COMMA))
			.map(String::trim)
			.collect(Collectors.toList()) : null;
	}
}
