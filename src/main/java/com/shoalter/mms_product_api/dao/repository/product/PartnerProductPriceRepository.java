package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface PartnerProductPriceRepository extends JpaRepository<PartnerProductPriceDo, Long> {

	Optional<PartnerProductPriceDo> findByBusUnitIdAndStoreSkuId(Integer busUnitId, String storeSkuId);

	List<PartnerProductPriceDo> findByLastUpdateDateBetween(LocalDateTime startTime, LocalDateTime endTime);

	List<PartnerProductPriceDo> findByBusUnitIdAndStorefrontStoreCodeInAndStatus(int busUnitId, Set<String> storefrontStoreCodes, int status);
}
