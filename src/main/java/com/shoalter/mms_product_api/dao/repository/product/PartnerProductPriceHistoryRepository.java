package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PartnerProductPriceHistoryRepository extends JpaRepository<PartnerProductPriceHistoryDo, Long> {
	List<PartnerProductPriceHistoryDo> findByEndDateBetween(LocalDateTime startTime, LocalDateTime endTime);
}
