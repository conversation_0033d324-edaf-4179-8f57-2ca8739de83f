package com.shoalter.mms_product_api.dao.repository.mms_cron_job.pojo;

import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobStatusEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobNameEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsServiceNameEnum;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Table(name = "MMS_CRON_JOB")
@Setter
@Getter
public class MmsCronJobDo {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;
	@Column(name = "ACTIVE_IND")
	private int activeInd; // 0 DISABLE, 1 ENABLE
	@Column(name = "SERVICE_NAME")
	@Enumerated(EnumType.STRING)
	private MmsServiceNameEnum serviceName;
	@Column(name = "JOB_NAME")
	@Enumerated(EnumType.STRING)
	private MmsCronJobNameEnum jobName;
	@Column(name = "STATUS")
	@Enumerated(EnumType.STRING)
	private MmsCronJobStatusEnum status;
	@Column(name = "TIME_OUT")
	private Integer timeOut;
	@Column(name = "CREATE_DATE")
	@CreationTimestamp
	private LocalDateTime createDate;
	@Column(name = "LAST_UPDATE_DATE")
	@UpdateTimestamp
	private LocalDateTime lastUpdateDate;
}
