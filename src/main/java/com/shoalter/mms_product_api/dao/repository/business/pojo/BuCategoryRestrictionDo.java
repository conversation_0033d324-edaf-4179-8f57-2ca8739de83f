package com.shoalter.mms_product_api.dao.repository.business.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "BU_CATEGORY_RESTRICTION")
@Data
public class BuCategoryRestrictionDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "PRODUCT_READY_METHOD")
    private String productReadyMethod;
    @Column(name = "PRODUCT_CAT_CODE")
    private String productCatCode;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "BUS_UNIT_ID")
    private Integer busUnitId;
}
