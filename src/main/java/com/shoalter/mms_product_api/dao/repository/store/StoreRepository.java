package com.shoalter.mms_product_api.dao.repository.store;

import com.shoalter.mms_product_api.dao.repository.product.pojo.LittleMallFlattenStoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreBuViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StorefrontStoreCodeMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreAffiliateDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreCodesGroupByMerchantIdDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractTypeDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface StoreRepository extends JpaRepository<StoreDo, Integer> {
    @Query(value = "select * from STORE s where BUS_UNIT_ID = 85 and Store_code = :storeCode "
            , nativeQuery = true)
    Optional<StoreDo> findHktvStoreByStoreCode(String storeCode);

	@Query(value = "SELECT s.* " +
			"FROM STORE s " +
			"JOIN BUS_UNIT bu ON s.BUS_UNIT_ID = bu.ID " +
			"WHERE Store_code = :storeCode AND bu.CODE = :buCode ", nativeQuery = true)
	Optional<StoreDo> findByStoreCodeAndBuCode(String storeCode, String buCode);

    @Query(value = "select distinct s.* from STORE s " +
            "inner join MERCHANT_STORE ms on s.ID = ms.Store_id " +
            "where ms.MERCHANT_id = :merchantId and s.ONLINE_STATUS = 'ONLINE'", nativeQuery = true)
    List<StoreDo> findByMerchantId(Integer merchantId);

    @Query(value = "select * from STORE s where s.STORE_CODE in :storeCodeList ", nativeQuery = true)
    List<StoreDo> findByStoreCodeIn(Set<String> storeCodeList);


    @Query(value = "select distinct s.* from STORE s " +
            "inner join MERCHANT_STORE ms on s.Id = ms.Store_id " +
            "inner join CONTRACT_PROD_TERMS cpt on cpt.STORE_ID = s.Id " +
            "inner join CONTRACT c on c.ID = cpt.CONTRACT_ID " +
            "where ms.MERCHANT_id = :merchantId " +
            "and c.ID = :contractId " +
            "and c.STATUS = 'Active' " +
            "and c.MASTER_CONTRACT_ID IS NULL " +
            "and s.ONLINE_STATUS = 'ONLINE'"
            , nativeQuery = true)
    List<StoreDo> findByMerchantIdAndContractId(Integer merchantId, Integer contractId);


    @Query(value = "select count(1) from STORE s " +
            "inner join MERCHANT_STORE ms on s.Id = ms.Store_id " +
            "inner join CONTRACT_PROD_TERMS cpt on cpt.STORE_ID = s.Id " +
            "inner join CONTRACT c on c.ID = cpt.CONTRACT_ID " +
            "where ms.MERCHANT_id = :merchantId " +
            "and c.ID = :contractId " +
            "and c.STATUS = 'Active' " +
            "and c.MASTER_CONTRACT_ID IS NULL " +
            "and s.Store_code = :storeCode " +
            "and s.ONLINE_STATUS = 'ONLINE'"
            , nativeQuery = true)
    int countByStoreCode(Integer merchantId, Integer contractId, String storeCode);

	@Query(value = "select Store_code from STORE s where s.Id IN :ids "
		, nativeQuery = true)
	List<String> findStoreCodesByIds(List<Integer> ids);

	@Query(value = "select STOREFRONT_STORE_CODE from STORE s where s.Id IN :ids "
		, nativeQuery = true)
	List<String> findStorefrontStoreCodesByIds(List<Integer> ids);

	@Query(value = "select STOREFRONT_STORE_CODE from STORE s where s.STORE_CODE = :storeCode "
		, nativeQuery = true)
	String findStorefrontStoreCodeByStoreCode(String storeCode);


	@Query(value = "SELECT " +
			"    ms.MERCHANT_id" +
			" FROM" +
			"    STORE s" +
			"        JOIN" +
			"    MERCHANT_STORE ms ON ms.Store_id = s.id" +
			"        AND s.Store_code = :storeCode", nativeQuery = true)
	Integer findMerchantIdByStoreCode(String storeCode);

	@Query(value = "SELECT ms.MERCHANT_id as merchantId, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"s.Store_code as storeCode " +
		"FROM MERCHANT_STORE ms " +
		"JOIN STORE s on s.Id = ms.Store_id " +
		"WHERE s.STOREFRONT_STORE_CODE IN :storefrontStoreCodes", nativeQuery = true)
	List<StoreMerchantViewDo> findStoreMerchantViewDoByStorefrontStoreCodes(Set<String> storefrontStoreCodes);

	@Query(value = "SELECT ms.MERCHANT_id as merchantId, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"m.MERCHANT_NAME as merchantName, " +
		"s.STORE_NAME as storeName, " +
		"IF(s.Active_ind = 'Y', 'Active', 'Inactive') as storeStatus " +
		"FROM MERCHANT_STORE ms " +
		"JOIN STORE s on s.Id = ms.Store_id " +
		"JOIN MERCHANT m on m.Id = ms.MERCHANT_id " +
		"WHERE s.STOREFRONT_STORE_CODE IN :storefrontStoreCodes", nativeQuery = true)
	List<LittleMallFlattenStoreMerchantViewDo> findLittleMallFlattenStoreInfoByStorefrontStoreCodes(Set<String> storefrontStoreCodes);

	@Query(value = "SELECT ms.MERCHANT_id as merchantId, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"s.Store_code as storeCode " +
		"FROM MERCHANT_STORE ms " +
		"JOIN STORE s on s.Id = ms.Store_id " +
		"JOIN BUS_UNIT bu on bu.ID = s.BUS_UNIT_ID " +
		"WHERE s.STOREFRONT_STORE_CODE IN :storefrontStoreCodes " +
		"AND bu.CODE = :buCode", nativeQuery = true)
	List<StoreMerchantViewDo> findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(String buCode, List<String> storefrontStoreCodes);

	@Query(value = "SELECT ms.MERCHANT_id as merchantId, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"s.Store_code as storeCode " +
		"FROM MERCHANT_STORE ms " +
		"JOIN STORE s on s.Id = ms.Store_id " +
		"WHERE s.Id IN :storeIds", nativeQuery = true)
	List<StoreMerchantViewDo> findStoreMerchantViewDoByStoreIds(List<Integer> storeIds);

	@Query(value = "select bu.CODE as buCode, " +
		"s.Store_code as storeCode, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"s.Active_ind as activeInd " +
		"from STORE s " +
		"join BUS_UNIT bu on bu.ID = s.BUS_UNIT_ID " +
		"where s.STOREFRONT_STORE_CODE in :storefrontStoreCodeList " +
		"and bu.CODE in :buCodeList", nativeQuery = true)
	List<StoreBuViewDo> findStoreBuViewDoByStorefrontStoreCodes(Set<String> storefrontStoreCodeList, Set<String> buCodeList);

	@Query(value = "select * " +
		"from STORE " +
		"where Store_code in :storeCodes"
		, nativeQuery = true)
	List<StoreDo> findByStoreCodes(Set<String> storeCodes);

	@Query(value = "select * " +
		"from STORE s " +
		"join BUS_UNIT bu on s.BUS_UNIT_ID = bu.ID and bu.CODE = :buCode " +
		"where s.Store_code IN :storeCodes "
		, nativeQuery = true)
	List<StoreDo> findByBuCodeAmdStoreCodes(String buCode, Set<String> storeCodes);

	@Query(value = "select s " +
		"from StoreDo s " +
		"where s.storefrontStoreCode in :storefrontStoreCodes")
	List<StoreDo> findByStorefrontStoreCode(Set<String> storefrontStoreCodes);

	@Query(value= "SELECT ID " +
            "FROM STORE " +
            "WHERE STOREFRONT_STORE_CODE = :storefrontStoreCode ", nativeQuery = true)
	Optional<Integer> findByStoreFrontStoreCode(String storefrontStoreCode);

	@Query(value= "SELECT SPD.PICKUP_DAYS " +
            "FROM STORE_PICKUP_DAYS SPD " +
            "WHERE SPD.STORE_ID = :storeId ", nativeQuery = true)
	List<String> findPickupDaysByStoreId(Integer storeId);

	@Query(value = "SELECT s.* " +
			"FROM STORE s " +
			"JOIN BUS_UNIT bu ON s.BUS_UNIT_ID = bu.ID " +
			"WHERE STOREFRONT_STORE_CODE = :storefrontStoreCode AND bu.CODE = :buCode ", nativeQuery = true)
	Optional<StoreDo> findByStorefrontStoreCodeAndBuCode(String storefrontStoreCode, String buCode);

	@Query(value = "select distinct s.Store_code from STORE s" +
			" inner join MERCHANT_STORE ms on s.ID = ms.Store_id" +
			" where ms.MERCHANT_id in :merchantIds", nativeQuery = true)
	List<String> findByMerchantId(List<Integer> merchantIds);

	@Query(value = "SELECT " +
			"    GROUP_CONCAT(s.Store_code) AS storeCodes, ms.MERCHANT_id AS merchantId" +
			" FROM STORE s" +
			" INNER JOIN MERCHANT_STORE ms ON s.ID = ms.Store_id" +
			" WHERE s.ID IN :storeIds" +
			" GROUP BY ms.MERCHANT_id"
			, nativeQuery = true)
	List<StoreCodesGroupByMerchantIdDo> findStoreCodesAndMerchantIdByIds(List<Long> storeIds);

	@Query(value = "SELECT m.ID merchantId, " +
			"	m.MERCHANT_NAME merchantName, " +
			"	s.Id storeId, " +
			"	s.Store_code storeCode, " +
			"	s.STOREFRONT_STORE_CODE storefrontCode, " +
			"	c.ID contractId, " +
			"	ct.CODE as contractType " +
			"FROM STORE s " +
			"JOIN BUS_UNIT bu ON bu.ID = s.BUS_UNIT_ID AND bu.TYPE = :businessType " +
			"JOIN PLATFORM p ON p.ID = bu.PLATFORM_ID AND p.PLATFORM_CODE = :platformCode " +
			"JOIN CONTRACT c ON s.ID = c.STORE_ID " +
			"JOIN CONTRACT_TYPE ct ON ct.ID = c.CONTRACT_TYPE_ID " +
			"JOIN MERCHANT m ON c.MERCHANT_ID = m.ID " +
			"WHERE s.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
			"AND c.STATUS = 'Active' " +
			"AND c.MASTER_CONTRACT_ID IS NULL " +
			"AND DATE_FORMAT(NOW(),'%Y-%m-%d') >= DATE_FORMAT(c.START_DATE,'%Y-%m-%d') " +
			"AND DATE_FORMAT(c.END_DATE,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') " +
			"AND (c.STATUS='Active' OR " +
			"(c.STATUS='Terminated' AND DATE_FORMAT(c.TERMINATE_DATE,'%Y-%m-%d') > DATE_FORMAT(NOW(),'%Y-%m-%d')))"
			,nativeQuery = true)
	List<StoreContractMerchantDo> findStoreContractMerchantByStorefrontStoreCode(String businessType, String platformCode, String storefrontStoreCode);

@Query(value = "select s.STOREFRONT_STORE_CODE as storefrontStoreCode,  " +
	"			       ct.CODE as contractType " +
	"			from STORE s " +
	"			         join CONTRACT c on s.Id = c.STORE_ID " +
	"			         join CONTRACT_TYPE ct on ct.ID = c.CONTRACT_TYPE_ID " +
	"			         join BUS_UNIT bu on s.BUS_UNIT_ID = bu.ID " +
	"			where bu.CODE = :buCode " +
	"			  and (s.STOREFRONT_STORE_CODE in :storeCodesOrStorefrontStoreCodes or " +
	"			       s.Store_code in :storeCodesOrStorefrontStoreCodes) " +
	"			  and c.ID in (SELECT MAX(cc.ID) " +
	"			               FROM CONTRACT cc " +
	"			                        INNER JOIN STORE s ON s.Id = cc.STORE_ID " +
	"			               WHERE cc.MASTER_CONTRACT_ID is null " +
	"			               group by s.Id);"
			,nativeQuery = true)
	List<StoreContractTypeDo> findContractTypeByBuCodesAndStoreCodesOrStorefrontStoreCodes(String buCode, Collection<String> storeCodesOrStorefrontStoreCodes);

	@Query(value = "SELECT " +
			"s.ID AS storeId, " +
			"s.Store_code AS storeCode, " +
			"s.STOREFRONT_STORE_CODE AS storefrontStoreCode, " +
			"s.BUS_UNIT_ID AS busUnitId, " +
			"sa.PLATFORM_CODE AS platformCode " +
			"FROM STORE_AFFILIATE sa " +
			"JOIN STORE s ON sa.STORE_ID = s.ID " +
			"JOIN BUS_UNIT bu ON s.BUS_UNIT_ID = bu.ID " +
			"WHERE sa.PLATFORM_CODE = :platformCode " +
			"AND s.Store_code = :storeCode " +
			"AND bu.CODE = :buCode"
			, nativeQuery = true)
	List<StoreAffiliateDo> findStoreAffiliateByPlatformCodeAndStoreCode(String platformCode, String storeCode, String buCode);

	List<StoreDo> findAllByIdIn(List<Integer> storeIds);

	@Query(value = "SELECT ct.CODE " +
		"FROM STORE s " +
		"         JOIN BUS_UNIT bu on s.BUS_UNIT_ID = bu.ID and bu.CODE = :buCode " +
		"         JOIN CONTRACT c ON s.ID = c.STORE_ID and c.MASTER_CONTRACT_ID is null " +
		"         JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
		"WHERE s.STOREFRONT_STORE_CODE = :storefrontStoreCode order by c.CREATED_DATE desc limit 1", nativeQuery = true)
	Optional<String> findStoreLatestContractTypeByBuAndStorefrontStoreCode(String buCode, String storefrontStoreCode);


	@Query(value = "select ms.MERCHANT_id as merchantId, " +
		"s.STOREFRONT_STORE_CODE as storefrontStoreCode " +
		"from STORE s " +
		"join MERCHANT_STORE ms on ms.Store_id = s.Id " +
		"join BUS_UNIT bu on bu.ID = s.BUS_UNIT_ID " +
		"where bu.CODE = :buCode and ms.MERCHANT_id in :merchantIds and s.STOREFRONT_STORE_CODE is not null", nativeQuery = true)
	List<StorefrontStoreCodeMerchantViewDo>  findStorefrontStoreCodeByBuAndMerchants(String buCode, Set<Integer> merchantIds);


	@Query(value = "SELECT DISTINCT s.STOREFRONT_STORE_CODE " +
		"FROM CONTRACT c " +
		"JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
		"    JOIN STORE s on s.Id = c.STORE_ID " +
		"    JOIN BUS_UNIT bu on bu.ID = s.BUS_UNIT_ID and bu.CODE = 'HKTV' " +
		"WHERE ct.CODE in (:contractTypeCodes) AND c.STATUS = 'Active'", nativeQuery = true)
	List<String> findActiveStorefrontStoreCodeByContractTypeCodeIn(Set<String> contractTypeCodes);
}
