package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductCartonSizeDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ProductCartonSizeRepository extends JpaRepository<ProductCartonSizeDo, Integer> {

	@Modifying
	@Query(value = "delete from PRODUCT_CARTON_SIZE where PRODUCT_ID = :productId",nativeQuery = true)
	void deleteByProductId(Integer productId);
}
