package com.shoalter.mms_product_api.dao.repository.store;

import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;


public interface StoreWarehouseRepository extends JpaRepository<StoreWarehouseDo, Integer> {
    List<StoreWarehouseDo> findStoreWarehouseByStoreIdAndSeqNoInOrderBySeqNo(Integer storeId, List<Integer> seqNoList);

    @Query(value = "SELECT CONCAT(s.STOREFRONT_STORE_CODE, sw.SEQ_NO)   " +
            "FROM STORE_WAREHOUSE sw " +
            "JOIN STORE  s on sw.STORE_ID = s.id " +
            "WHERE s.Id = :storeId " +
            "and sw.LAST_SYNCHONIZE_DATE  is not null " +
            "and sw.WAREHOUSE_ADDRESS1  is not null " +
            "and sw.WAREHOUSE_ADDRESS2  is not null " +
            "and sw.WAREHOUSE_ADDRESS3  is not null " +
            "and sw.WAREHOUSE_ADDRESS4  is not null " +
            "and sw.WAREHOUSE_CONTACT_NAME is not null " +
            "and sw.WAREHOUSE_CONTACT_PHONE_NO  is not null", nativeQuery = true)
    List<String> findDropListByStoreId(Integer storeId);

	@Query(value = "SELECT CONCAT(s.STOREFRONT_STORE_CODE,'-' , sw.SEQ_NO)   " +
			"FROM STORE_WAREHOUSE sw " +
			"JOIN STORE  s on sw.STORE_ID = s.id " +
			"WHERE s.Id = :storeId " +
			"and sw.LAST_SYNCHONIZE_DATE  is not null " +
			"and sw.WAREHOUSE_ADDRESS1  is not null " +
			"and sw.WAREHOUSE_ADDRESS2  is not null " +
			"and sw.WAREHOUSE_ADDRESS3  is not null " +
			"and sw.WAREHOUSE_ADDRESS4  is not null " +
			"and sw.WAREHOUSE_CONTACT_NAME is not null " +
			"and sw.WAREHOUSE_CONTACT_PHONE_NO  is not null", nativeQuery = true)
	List<String> findOldDropListByStoreId(Integer storeId);

	@Query(value = "select CONCAT(s.STOREFRONT_STORE_CODE, sw.SEQ_NO) " +
			"from STORE_WAREHOUSE sw " +
			"join STORE s on sw.STORE_ID =s.Id " +
			"where sw.ID =:warehouseId ",nativeQuery = true)
	String findWarehouseId(Integer warehouseId);

	@Query(value = "select CONCAT(s.STOREFRONT_STORE_CODE, '-', sw.SEQ_NO) " +
		"from STORE_WAREHOUSE sw " +
		"join STORE s on sw.STORE_ID =s.Id " +
		"where sw.ID =:warehouseId ",nativeQuery = true)
	String findWarehouseIdWithDash(Integer warehouseId);

	@Query(value = "select SW.* " +
			"from STORE_WAREHOUSE SW " +
			"join STORE S on SW.STORE_ID =S.Id " +
			"where S.Store_code = :storeCode ", nativeQuery = true)
	List<StoreWarehouseDo> findWarehouseIdByStoreCode(String storeCode);

	@Query(value = "SELECT" +
			"    CASE" +
			"        WHEN" +
			"            (SELECT SEQ_NO FROM mms.STORE_WAREHOUSE WHERE id = :editWarehouseId)" +
			"            <>" +
			"            (SELECT SEQ_NO FROM mms.STORE_WAREHOUSE WHERE id = :beforeWarehouseId) " +
			"        THEN 'true'" +
			"        ELSE 'false'" +
			"    END AS isSeqNoDifferent", nativeQuery = true)
	boolean isWarehouseSeqNoDifferent(Integer editWarehouseId, Integer beforeWarehouseId);

	@Query(value = "select sw.id " +
		"from STORE_WAREHOUSE sw " +
		"         join STORE s on sw.STORE_ID = s.Id and s.STOREFRONT_STORE_CODE = :storefrontStoreCode and s.BUS_UNIT_ID = 85 " +
		"where sw.SEQ_NO = :warehouseSeqNo",nativeQuery = true)
	Optional<Integer> findIdByStorefrontStoreCodeAndHktvBusUnitAndWarehouseSeqNo(String storefrontStoreCode, Integer warehouseSeqNo);

	@Query(value = "SELECT CONCAT(s.STOREFRONT_STORE_CODE,'-' , sw.SEQ_NO)   " +
		"FROM STORE_WAREHOUSE sw " +
		"JOIN STORE  s on sw.STORE_ID = s.id " +
		"WHERE s.Id = :storeId " +
		"and sw.LAST_SYNCHONIZE_DATE  is not null " +
		"and sw.WAREHOUSE_ADDRESS1  is not null " +
		"and sw.WAREHOUSE_ADDRESS2  is not null " +
		"and sw.WAREHOUSE_ADDRESS3  is not null " +
		"and sw.WAREHOUSE_ADDRESS4  is not null " +
		"and sw.WAREHOUSE_CONTACT_NAME is not null " +
		"and sw.WAREHOUSE_CONTACT_PHONE_NO  is not null " +
		"and sw.SEQ_NO = :seqNo ", nativeQuery = true)
	String findWarehouseByStoreIdAndSeqNo(Integer storeId, Integer seqNo);

}
