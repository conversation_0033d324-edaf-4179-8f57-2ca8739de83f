package com.shoalter.mms_product_api.dao.repository.bu_export_history;

import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.LittleMallExportInfoDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LittleMallExportInfoRepository extends JpaRepository<LittleMallExportInfoDo, Integer> {

	@Query(value = "select * " +
		"from LITTLE_MALL_EXPORT_INFO lmei " +
		"         join BU_EXPORT_HISTORY beh on lmei.ID = beh.ID " +
		"where beh.CHECK_ID = :checkId", nativeQuery = true)
	List<LittleMallExportInfoDo> findByCheckId(String checkId);
}
