package com.shoalter.mms_product_api.dao.repository.product.pojo;

import com.shoalter.mms_product_api.service.hybris.enums.SyncHybrisStatusEnum;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Table(name = "TEMP_PRODUCT_SYNC_HYBRIS_RECORD")
@Data
public class TempProductSyncHybrisRecordDo {
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private Integer id;

	@Column(name = "TRACE_ID")
	private String traceId;

	@Column(name = "STORE_SKU_ID")
	private String storeSkuId;

	@Column(name = "STATUS")
	@Enumerated(EnumType.STRING)
	private SyncHybrisStatusEnum status;

	@Column(name = "ACTION")
	private String action;

	@Column(name = "CREATE_TIME")
	@CreationTimestamp
	private LocalDateTime createTime;

	@Column(name = "LAST_UPDATED_TIME")
	@UpdateTimestamp
	private LocalDateTime lastUpdatedTime;

}
