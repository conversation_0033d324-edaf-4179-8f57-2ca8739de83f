package com.shoalter.mms_product_api.dao.repository.approval_deal;

import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealStatusHistoryDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApprovalDealStatusHistoryRepository extends JpaRepository<ApprovalDealStatusHistoryDo, Long> {
	@Query(value = "SELECT * FROM APPROVAL_DEAL_STATUS_HISTORY adsh " +
		"JOIN APPROVAL_DEAL ad ON adsh.APPROVAL_DEAL_ID = ad.ID " +
		"WHERE ad.ID = :approvalDealId " + ApprovalDealRepository.permissionRmSql +
		"ORDER BY adsh.CREATED_DATE DESC ", nativeQuery = true)
	List<ApprovalDealStatusHistoryDo> findByApprovalDealId(UserDto userDto, Long approvalDealId);
}
