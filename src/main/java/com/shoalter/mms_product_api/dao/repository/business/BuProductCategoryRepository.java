package com.shoalter.mms_product_api.dao.repository.business;

import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.service.product.pojo.projection.OapiProductCategoryCodeProjection;
import com.shoalter.mms_product_api.service.product.pojo.projection.ProductCategoryCodeProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface BuProductCategoryRepository extends JpaRepository<BuProductCategoryDo, Integer> {
    @Query(value =
            "select bpc.* " +
            "from BU_PRODUCT_CATEGORY bpc " +
            "join PRODUCT_CATEGORIES pc " +
            "on bpc.id = pc.BU_PRODUCT_CATEGORY_ID " +
            "join BUS_UNIT bu " +
            "on pc.BUS_UNIT_ID = bu.ID " +
            "where bu.ID = :businessUnitId and pc.PRODUCT_ID = :productId and pc.PRIMARY_IND = :primaryInd", nativeQuery = true)
    List<BuProductCategoryDo> findByBusUnitIdAndProductIdAndPrimaryInd(Integer businessUnitId, Integer productId, String primaryInd);

	@Query(value = "select bpc.Product_cat_code as productCatCode, " +
		    "pc.PRIMARY_IND as primaryInd " +
			"from BU_PRODUCT_CATEGORY bpc " +
			"join PRODUCT_CATEGORIES pc " +
			"on bpc.id = pc.BU_PRODUCT_CATEGORY_ID " +
			"join BUS_UNIT bu " +
			"on pc.BUS_UNIT_ID = bu.ID " +
			"where bu.CODE = :buCode and pc.PRODUCT_ID = :productId ", nativeQuery = true)
	List<ProductCategoryCodeProjection> findProductCatCodeByBUCodeAndProductIdAndPrimaryInd(String buCode, Integer productId);

    /**
     * Find business unit product categories with category restrictions.
     * Categories will be excluded if they are restricted by the specified product ready methods.
     * The restriction setting is defined in the BU_CATEGORY_RESTRICTION table.
     */
    @Query(value = "select bpc.*, bpc.Product_cat_code " +
            "from BU_PRODUCT_CATEGORY bpc  " +
            "inner join BUS_UNIT bu on bpc.Bus_Unit_id = bu.ID " +
            "where bpc.Product_cat_type = 'MMS' " +
            "and bpc.Active_ind = 'Y' " +
            "and bu.CODE = :buCode " +
            "and ( :isSetEmpty = true or " +
            "    bpc.Product_cat_code not in ( " +
            "    select bcr.PRODUCT_CAT_CODE " +
            "    from BU_CATEGORY_RESTRICTION bcr " +
            "    where bcr.PRODUCT_READY_METHOD in (:restrictedProductReadyMethods) )) ", nativeQuery = true)
    List<BuProductCategoryDo> findByBuCode(String buCode, boolean isSetEmpty, Set<String> restrictedProductReadyMethods);

    default List<BuProductCategoryDo> findByBuCode(String buCode, Set<String> restrictedProductReadyMethods) {
        return findByBuCode(buCode, restrictedProductReadyMethods == null || restrictedProductReadyMethods.isEmpty(), restrictedProductReadyMethods);
    }

    @Query(value = "select bpc.id from BU_PRODUCT_CATEGORY bpc " +
            "inner join STORE_CATEGORY sc " +
            "on sc.CATEGORY_ID = bpc.id " +
            "where bpc.Product_cat_type = 'MMS' and bpc.Active_ind = 'Y' and sc.STORE_ID = :storeId ", nativeQuery = true)
    List<Integer> findIdByStoreId(Integer storeId);

    @Query(value = "select bpc.Product_cat_code from BU_PRODUCT_CATEGORY bpc " +
            "inner join CONTRACT_PROD_TERMS cpt on bpc.id = cpt.PRIMARY_CATEGORY_ID " +
            "inner join BUS_UNIT bu on bpc.Bus_Unit_id = bu.ID " +
            "inner join CONTRACT c on cpt.CONTRACT_ID = c.ID " +
            "inner join STORE s on c.STORE_ID = s.Id " +
            "where bpc.Product_cat_type = 'MMS' and bpc.Active_ind = 'Y' and s.ID = :storeId and bu.CODE = :buCode",
            nativeQuery = true)
    List<String> findCategoryCodeByContractProdTermStoreId(String buCode, Integer storeId);

    @Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
            "inner join BUS_UNIT bu " +
            "on bpc.Bus_Unit_id = bu.ID " +
            "where bpc.Product_cat_type = 'MMS' and bpc.Active_ind = 'Y' " +
            "and bpc.PRODUCT_CAT_CODE = :productCatCode " +
            "and bu.CODE = :buCode", nativeQuery = true)
    BuProductCategoryDo findByBuCodeAndProductCatCode(String buCode, String productCatCode);

    @Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
            "inner join BUS_UNIT bu " +
            "on bpc.Bus_Unit_id = bu.ID " +
            "where bpc.id = :id " +
            "and bu.CODE = :buCode", nativeQuery = true)
    BuProductCategoryDo findByBuCodeAndIndex(String buCode, Integer id);

    @Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
            "inner join BUS_UNIT bu " +
            "on bpc.Bus_Unit_id = bu.ID " +
            "where bpc.Product_cat_type = 'STORE' " +
			"and bpc.Active_ind = 'Y' " +
            "and bu.CODE = :buCode " +
            "and bpc.Product_cat_code = :primaryCategoryCode", nativeQuery = true)
    Optional<BuProductCategoryDo> findByProductCatCode(String buCode, String primaryCategoryCode);

	@Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
			"inner join BUS_UNIT bu " +
			"on bpc.Bus_Unit_id = bu.ID " +
			"where bpc.Product_cat_type = 'STORE' " +
			"and bpc.Active_ind = 'Y' " +
			"and bu.CODE = :buCode " +
			"and bpc.Product_cat_code in :primaryCategoryCodeList", nativeQuery = true)
	List<BuProductCategoryDo> findActiveCatogoriesByProductCatCodeList(String buCode, List<String> primaryCategoryCodeList);

	@Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
			"inner join BUS_UNIT bu " +
			"on bpc.Bus_Unit_id = bu.ID " +
			"where bpc.Product_cat_type = 'STORE' " +
			"and bu.CODE = :buCode " +
			"and bpc.Product_cat_code in :categoryCodeList", nativeQuery = true)
	List<BuProductCategoryDo> findByProductCatCodeList(String buCode, List<String> categoryCodeList);

	@Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
		"inner join BUS_UNIT bu " +
		"on bpc.Bus_Unit_id = bu.ID " +
		"where bpc.Product_cat_type = 'STORE' " +
		"and bu.CODE = :buCode " +
		"and bpc.Product_cat_code in :categoryCodeSet", nativeQuery = true)
	List<BuProductCategoryDo> findByProductCatCodeList(String buCode, Set<String> categoryCodeSet);

    @Query(value = "select bpc.id from BU_PRODUCT_CATEGORY bpc " +
            "inner join BUS_UNIT bu " +
            "on bpc.Bus_Unit_id = bu.ID " +
            "where bpc.Product_cat_type = :productCatType " +
            "and bu.CODE = :buCode " +
            "and bpc.Product_cat_code = :productCategoryCode", nativeQuery = true)
	Optional<Integer> findCategoryIdByProductCatCode(String productCatType, String buCode, String productCategoryCode);

	@Query(value = "select bpc.* from BU_PRODUCT_CATEGORY bpc " +
		"inner join BUS_UNIT bu " +
		"on bpc.Bus_Unit_id = bu.ID " +
		"where bpc.Product_cat_type = :productCatType " +
		"and bu.CODE = :buCode " +
		"and bpc.Product_cat_code = :productCategoryCode", nativeQuery = true)
	Optional<BuProductCategoryDo> findByProductCatCode(String productCatType, String buCode, String productCategoryCode);

	@Query(value = "SELECT " +
		"    P.UUID productUuid, " +
		"    BPC.PRODUCT_CAT_CODE productCatCode, " +
		"	 BPC.PRODUCT_CAT_NAME productCatName, " +
		"    CONCAT(BPC.PRODUCT_CAT_CODE, ' - ', BPC.PRODUCT_CAT_NAME) name " +
		"FROM BU_PRODUCT_CATEGORY BPC " +
		"         JOIN PRODUCT_CATEGORIES PC ON PC.BU_PRODUCT_CATEGORY_ID = BPC.ID " +
		"         JOIN PRODUCT P ON P.ID = PC.PRODUCT_ID " +
		"WHERE " +
		"    P.UUID in :skuUuids " +
		"  AND PC.PRIMARY_IND = 'N' ", nativeQuery = true)
	List<OapiProductCategoryCodeProjection> findOapiProductCatCodeByProductUuid(List<String> skuUuids);

	@Query(value = "select * from BU_PRODUCT_CATEGORY bpc " +
		"join BUS_UNIT bu " +
		"on bpc.Bus_Unit_id = bu.ID " +
		"where bpc.Product_cat_type = :productCatType " +
		"and bu.CODE = :buCode " +
		"and bpc.Product_cat_code in :productCategoryCodes", nativeQuery = true)
	List<BuProductCategoryDo> findByProductCatCodes(String productCatType, String buCode, List<String> productCategoryCodes);
}
