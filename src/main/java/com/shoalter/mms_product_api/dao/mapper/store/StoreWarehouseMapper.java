package com.shoalter.mms_product_api.dao.mapper.store;

import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import com.shoalter.mms_product_api.service.product.pojo.StoreWarehouseIdViewDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

@Mapper
public interface StoreWarehouseMapper {
	@Select("<script>" +
			"select CONCAT(s.STOREFRONT_STORE_CODE, sw.SEQ_NO) as warehouseId,sw.ID " +
			"from STORE_WAREHOUSE sw " +
			"join STORE s on sw.STORE_ID =s.Id " +
			"where sw.ID in "+
			"<foreach collection='warehouseId' item='item' open='(' separator=',' close=') '> " +
			" #{item}" +
			"</foreach> "+
			"</script>")
	List<StoreWarehouseIdViewDto> findWarehouseId(List<Integer> warehouseId);

	@Select("<script>" +
			"select CONCAT(s.STOREFRONT_STORE_CODE, '-', sw.SEQ_NO) as warehouseId,sw.ID " +
			"from STORE_WAREHOUSE sw " +
			"join STORE s on sw.STORE_ID =s.Id " +
			"where sw.ID in " +
			"<foreach collection='warehouseId' item='item' open='(' separator=',' close=') '> " +
			" #{item}" +
			"</foreach> " +
			"</script>")
	List<StoreWarehouseIdViewDto> findWarehouseIdHaveDelimiter(List<Integer> warehouseId);

	@Select(
			"select CONCAT(s.STOREFRONT_STORE_CODE, sw.SEQ_NO) as warehouseId,sw.ID " +
			"from STORE_WAREHOUSE sw " +
			"join STORE s on sw.STORE_ID =s.Id " +
			"where STORE_ID = #{storeId}")
	List<StoreWarehouseIdViewDto> findWarehouseIdByStoreId(Integer storeId);
}
