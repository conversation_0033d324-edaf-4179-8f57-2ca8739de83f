package com.shoalter.mms_product_api.dao.mapper.product;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ProductHistoryMapper {
    @Insert("INSERT INTO PRODUCT_HISTORY(" +
            "VER<PERSON>ON, VERSION_CREATE_DATED, PRODUCT_ID, MERCHANT_ID, " +
            "PRODUCT_CODE, CONTRACT_ID, SKU_CODE, IS_PRIMARY_SKU, " +
            "SKU_NAME, SKU_NAME_TCHI, SKU_NAME_SCHI, SKU_S_TITLE_HKTV_EN, SKU_S_TITLE_HKTV_CH, " +
            "SKU_S_DESC_HKTV_EN, SKU_S_DESC_HKTV_CH, SKU_S_DESC_HKTV_SC, SKU_L_TITLE_HKTV_EN, SKU_L_TITLE_HKTV_CH, " +
            "SKU_L_DESC_HKTV_EN, SKU_L_DESC_HKTV_CH, SKU_L_DESC_HKTV_SC, SKU_S_TITLE_HKB_EN, SKU_S_TITLE_HKB_CH, " +
            "SKU_S_DESC_HKB_EN, SKU_S_DESC_HKB_CH, SKU_L_TITLE_HKB_EN, SKU_L_TITLE_HKB_CH, " +
            "SKU_L_DESC_HKB_EN, SKU_L_DESC_HKB_CH, INVOICE_REMARKS_EN, INVOICE_REMARKS_CH, INVOICE_REMARKS_SC, " +
            "VIDEO_LINK, VIDEO_LINK_EN, VIDEO_LINK_CH, VIDEO_LINK_SC, BRAND_ID, " +
            "BARCODE, MANU_COUNTRY, WEIGHT, WEIGHT_UNIT, " +
            "PACK_HEIGHT, PACK_LENGTH, PACK_DEPTH, PACK_DIMENSION_UNIT, " +
            "PACK_BOX_TYPE, PACK_SPEC_EN, PACK_SPEC_CH, PACK_SPEC_SC, CURRENCY_CODE, " +
            "ORIGINAL_PRICE, SELLING_PRICE, MALL_DOLLAR, MALL_DOLLAR_VIP, " +
            "PRODUCT_READY_METHOD, WAREHOUSE_ID, DELIVERY_METHOD, DELIVERY_DETAILS_EN, DELIVERY_DETAILS_CH, " +
            "READY_PICKUP_DAYS, PICKUP_DAYS, DELIVERY_COMPLETION_DAYS, PICKUP_TIMESLOT," +
            "COLOR_EN, COLOR_CH, SIZE_SYSTEM, SIZE, " +
            "INVISIBLE_FLAG, FORCE_OUT_OF_STOCK, STATUS, COLOR_FAMILIAR, " +
            "CONSUMABLE, RECOM_SELLING_PRICE, RECOM_SELLING_PRICE_EN, RSP, FLASH_SALE, FIXED_DELIVERY_TIMESLOT, " +
			"RSP_FONT_COLOR, RSP_FONT_BAK_COLOR, PRIORITY, FEATURE_START_TIME, FEATURE_END_TIME, " +
            "VOUCHER_TYPE, VOUCHER_DISPLAY_TYPE, BUY_MAX, USER_MAX, " +
            "WEBSITE, REMARKS, REDEEM_ID, REDEEM_BUS_NAME, " +
            "REDEEM_LATITUDE, REDEEM_LONGITUDE, REDEEM_LOC_CITY, REDEEM_ADDRESS, " +
            "OPTION_DISPLAY_ORDER, REDEEM_START_DATE, REDEEM_END_DATE, URGENT, " +
            "REDEEM_TYPE, EXPIRY_TYPE, FIXED_REDEMPTION_END_DATE, NO_OF_DAY_AFTER_FEATURE_END, " +
            "UPON_PURCHASE_DATE, PAYMENT_TERMS, FINE_PRINT_TITLE_EN, FINE_PRINT_TITLE_CH, " +
            "FINE_PRINT_EN, FINE_PRINT_CH, FINE_PRINT_SC, DELIVERY_TITLE_EN, DELIVERY_TITLE_CH, " +
            "RETURN_DAYS, PRODUCT_READY_DAYS, COMMISSION_RATE, COST, CREATED_BY, " +
            "CREATED_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, STORAGE_FEE, " +
            "CONTRACT_PROD_TERMS_ID, FIELD1, VALUE1, FIELD2, " +
            "VALUE2, FIELD3, VALUE3, WEIGHT_LEVEL, " +
            "DISCOUNT_TEXT, DISCOUNT_TEXT_TCHI, DISCOUNT_TEXT_SCHI, STYLE, ATTRIBUTES_ID, " +
            "AFFILIATE_URL, EW_PERCENTAGE_SETTING, CLAIM_LINK_EN, CLAIM_LINK_CH, CLAIM_LINK_SC " +
            ") (SELECT " +
            "#{productVersion}, NOW(), ID, MERCHANT_ID, " +
            "PRODUCT_CODE, CONTRACT_ID, SKU_CODE, IS_PRIMARY_SKU, " +
            "SKU_NAME, SKU_NAME_TCHI, SKU_NAME_SCHI, SKU_S_TITLE_HKTV_EN, SKU_S_TITLE_HKTV_CH, " +
            "SKU_S_DESC_HKTV_EN, SKU_S_DESC_HKTV_CH, SKU_S_DESC_HKTV_SC, SKU_L_TITLE_HKTV_EN, SKU_L_TITLE_HKTV_CH, " +
            "SKU_L_DESC_HKTV_EN, SKU_L_DESC_HKTV_CH, SKU_L_DESC_HKTV_SC, SKU_S_TITLE_HKB_EN, SKU_S_TITLE_HKB_CH, " +
            "SKU_S_DESC_HKB_EN, SKU_S_DESC_HKB_CH, SKU_L_TITLE_HKB_EN, SKU_L_TITLE_HKB_CH, " +
            "SKU_L_DESC_HKB_EN, SKU_L_DESC_HKB_CH, INVOICE_REMARKS_EN, INVOICE_REMARKS_CH, INVOICE_REMARKS_SC, " +
            "VIDEO_LINK, VIDEO_LINK_EN, VIDEO_LINK_CH, VIDEO_LINK_SC, BRAND_ID, " +
            "BARCODE, MANU_COUNTRY, WEIGHT, WEIGHT_UNIT, " +
            "PACK_HEIGHT, PACK_LENGTH, PACK_DEPTH, PACK_DIMENSION_UNIT, " +
            "PACK_BOX_TYPE, PACK_SPEC_EN, PACK_SPEC_CH, PACK_SPEC_SC, CURRENCY_CODE," +
            "ORIGINAL_PRICE, SELLING_PRICE, MALL_DOLLAR, MALL_DOLLAR_VIP, " +
            "PRODUCT_READY_METHOD, WAREHOUSE_ID, DELIVERY_METHOD, DELIVERY_DETAILS_EN, DELIVERY_DETAILS_CH, " +
            "READY_PICKUP_DAYS, PICKUP_DAYS, DELIVERY_COMPLETION_DAYS, PICKUP_TIMESLOT, " +
            "COLOR_EN, COLOR_CH, SIZE_SYSTEM, SIZE, " +
            "INVISIBLE_FLAG, FORCE_OUT_OF_STOCK, STATUS, COLOR_FAMILIAR, " +
			"CONSUMABLE, RECOM_SELLING_PRICE, RECOM_SELLING_PRICE_EN, RSP, FLASH_SALE, FIXED_DELIVERY_TIMESLOT, " +
			"RSP_FONT_COLOR, RSP_FONT_BAK_COLOR, PRIORITY, FEATURE_START_TIME, FEATURE_END_TIME, " +
            "VOUCHER_TYPE, VOUCHER_DISPLAY_TYPE, BUY_MAX, USER_MAX, " +
            "WEBSITE, REMARKS, REDEEM_ID, REDEEM_BUS_NAME, " +
            "REDEEM_LATITUDE, REDEEM_LONGITUDE, REDEEM_LOC_CITY, REDEEM_ADDRESS," +
            "OPTION_DISPLAY_ORDER, REDEEM_START_DATE, REDEEM_END_DATE, URGENT," +
            "REDEEM_TYPE, EXPIRY_TYPE, FIXED_REDEMPTION_END_DATE, NO_OF_DAY_AFTER_FEATURE_END," +
            "UPON_PURCHASE_DATE, PAYMENT_TERMS, FINE_PRINT_TITLE_EN, FINE_PRINT_TITLE_CH, " +
            "FINE_PRINT_EN, FINE_PRINT_CH, FINE_PRINT_SC, DELIVERY_TITLE_EN, DELIVERY_TITLE_CH," +
            "RETURN_DAYS, PRODUCT_READY_DAYS, COMMISSION_RATE, COST, CREATED_BY, " +
            "CREATED_DATE, LAST_UPDATED_BY, LAST_UPDATED_DATE, STORAGE_FEE," +
            "CONTRACT_PROD_TERMS_ID, FIELD1, VALUE1, FIELD2, " +
            "VALUE2, FIELD3, VALUE3, WEIGHT_LEVEL," +
            "DISCOUNT_TEXT, DISCOUNT_TEXT_TCHI, DISCOUNT_TEXT_SCHI, STYLE, ATTRIBUTES_ID, " +
			"AFFILIATE_URL, EW_PERCENTAGE_SETTING, CLAIM_LINK_EN, CLAIM_LINK_CH, CLAIM_LINK_SC " +
            "FROM PRODUCT WHERE ID =  #{productId,jdbcType=INTEGER})")
    void add(Integer productId, Integer productVersion);
}
