package com.shoalter.mms_product_api.dao.mapper.product;

import com.shoalter.mms_product_api.service.product.pojo.BatchUploadDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SaveProductRecordMapper {
    @Select("<script>" +
            "SELECT SPR.ID as recordId, SPR.UPLOAD_TYPE as uploadType, SPR.FILE_NAME as fileName, " +
            "SPR.STATUS as status, SU.USER_CODE as uploadBy, SPR.UPLOAD_TIME as dateOfUpload " +
            "FROM SAVE_PRODUCT_RECORD SPR " +
            "INNER JOIN SYS_USER SU on SU.ID = SPR.UPLOAD_USER_ID " +
            "WHERE SPR.UPLOAD_TYPE IN " +
            "<foreach collection='uploadTypeList' item='item' open='(' separator=',' close=') '>" +
            "#{item}" +
            "</foreach>" +
            "AND SPR.MERCHANT_ID IN " +
            "<foreach collection='merchantIdList' item='item' open='(' separator=',' close=') '>" +
            "#{item}" +
            "</foreach>" +
            "AND SPR.STATUS IN " +
            "<foreach collection='statusList' item='item' open='(' separator=',' close=') '>" +
            "#{item}" +
            "</foreach>" +
            "ORDER BY SPR.UPLOAD_TIME desc limit #{limit} offset #{offset}" +
            "</script>")
    List<BatchUploadDto> findByMerchantIdAndUploadTypeListAndStatusList(List<Integer> merchantIdList, List<Integer> uploadTypeList, List<Integer> statusList, Integer limit, Integer offset);

    @Select("<script>" +
            "SELECT SPR.ID as recordId, SPR.UPLOAD_TYPE as uploadType, SPR.FILE_NAME as fileName, " +
            "SPR.STATUS as status, SU.USER_CODE as uploadBy, SPR.UPLOAD_TIME as dateOfUpload " +
            "FROM SAVE_PRODUCT_RECORD SPR " +
            "INNER JOIN SYS_USER SU on SU.ID = SPR.UPLOAD_USER_ID " +
            "WHERE SPR.UPLOAD_TYPE IN " +
            "<foreach collection='uploadTypeList' item='item' open='(' separator=',' close=') '>" +
            "#{item}" +
            "</foreach>" +
            "AND SPR.MERCHANT_ID IN " +
            "<foreach collection='merchantIdList' item='item' open='(' separator=',' close=') '>" +
            "#{item}" +
            "</foreach>" +
            " ORDER BY SPR.UPLOAD_TIME desc limit #{limit} offset #{offset}" +
            "</script>")
    List<BatchUploadDto> findByMerchantIdAndUploadTypeList(List<Integer> merchantIdList, List<Integer> uploadTypeList, Integer limit, Integer offset);

}
