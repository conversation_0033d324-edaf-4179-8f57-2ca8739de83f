package com.shoalter.mms_product_api.dao.mapper.product;

import com.shoalter.mms_product_api.service.product.pojo.ContractStoreDto;
import com.shoalter.mms_product_api.service.product.pojo.ContractTypeDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ContractMapper {
	@Select("SELECT distinct c.*, "+
			"ct.CODE as CONTRACT_TYPE_CODE " +
			"FROM CONTRACT c " +
			"INNER JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
			"INNER JOIN BUS_UNIT bu ON ct.BUS_UNIT_ID = bu.ID " +
			"INNER JOIN CONTRACT_PROD_TERMS cpt on c.ID = cpt.CONTRACT_ID " +
			"INNER JOIN STORE s ON cpt.STORE_ID = s.Id " +
			"WHERE bu.CODE = #{buCode} " +
			"AND s.ONLINE_STATUS = 'ONLINE' " +
			"AND c.STATUS = 'Active' " +
			"AND c.MERCHANT_ID = #{merchantId} " +
			"AND c.MASTER_CONTRACT_ID IS NULL " +
			"AND DATE_FORMAT(c.END_DATE,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') " +
			"AND (c.STATUS='Active' OR " +
			"(c.STATUS='Terminated' AND DATE_FORMAT(c.TERMINATE_DATE,'%Y-%m-%d') > DATE_FORMAT(NOW(),'%Y-%m-%d'))) " +
			"ORDER BY c.START_DATE DESC, c.ID DESC ")
	@Result(property = "contractTypeCode", column = "CONTRACT_TYPE_CODE")
	List<ContractTypeDto> findContractAndContractType(String buCode,Integer merchantId);

	@Select("SELECT distinct c.ID as CONTRACT_ID, c.OPPORTUNITY_NO as OPPORTUNITY_NO, " +
			"c.OPPORTUNITY_NAME as OPPORTUNITY_NAME, c.MERCHANT_ID as MERCHANT_ID, c.CONTRACT_NO as CONTRACT_NO, c.RM_ID as RM_ID, " +
			"c.START_DATE as START_DATE, c.END_DATE as END_DATE, c.DURATION_YEARS as DURATION_YEARS, c.CONTRACT_TYPE_ID as CONTRACT_TYPE_ID, " +
			"c.MASTER_CONTRACT_ID as MASTER_CONTRACT_ID, c.ANNUAL_FEE as ANNUAL_FEE, " +
			"c.STATUS as STATUS, c.TERMINATE_DATE as TERMINATE_DATE, c.INSURANCE as INSURANCE, " +
			"c.STORE_ID as STORE_ID, c.OVERSEAS_CURRENCY as OVERSEAS_CURRENCY, c.COMMISSION_RATE as COMMISSION_RATE, " +
			"ct.CODE as CONTRACT_TYPE_CODE, s.BUS_UNIT_ID as BUS_UNIT_ID, s.Store_code as STORE_CODE, s.Active_ind as ACTIVE_IND, " +
			"s.STOREFRONT_STORE_CODE as STOREFRONT_STORE_CODE, s.STORE_NAME as STORE_NAME, s.STORE_NAME_TC as STORE_NAME_TC,  " +
			"s.STOREFRONT_STORE_STATUS as STOREFRONT_STORE_STATUS, s.MARK_UP_RATE as MARK_UP_RATE, " +
			"s.ONLINE_STATUS as ONLINE_STATUS, s.DELIVERY_THERSHOLD as DELIVERY_THERSHOLD, s.DELIVERY_FEE as DELIVERY_FEE, " +
			"s.STORE_TYPE as STORE_TYPE, s.STORE_LANDMARK_FLAG as STORE_LANDMARK_FLAG, s.MERCHANT_TYPE as MERCHANT_TYPE " +
			"FROM CONTRACT c " +
			"INNER JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
			"INNER JOIN BUS_UNIT bu ON ct.BUS_UNIT_ID = bu.ID " +
			"INNER JOIN CONTRACT_PROD_TERMS cpt on c.ID = cpt.CONTRACT_ID " +
			"INNER JOIN STORE s ON c.STORE_ID = s.Id " +
			"WHERE bu.CODE = #{buCode} " +
			"AND c.STATUS = 'Active' " +
			"AND c.MERCHANT_ID = #{merchantId} " +
			"AND c.MASTER_CONTRACT_ID IS NULL " +
			"AND DATE_FORMAT(NOW(),'%Y-%m-%d') >= DATE_FORMAT(c.START_DATE,'%Y-%m-%d') " +
			"AND DATE_FORMAT(c.END_DATE,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') " +
			"AND (c.STATUS='Active' OR " +
			"(c.STATUS='Terminated' AND DATE_FORMAT(c.TERMINATE_DATE,'%Y-%m-%d') > DATE_FORMAT(NOW(),'%Y-%m-%d'))) " +
			"ORDER BY c.START_DATE DESC, c.ID DESC ")
	@Result(property = "contractId", column = "CONTRACT_ID")
	@Result(property = "opportunityNo", column = "OPPORTUNITY_NO")
	@Result(property = "opportunityName", column = "OPPORTUNITY_NAME")
	@Result(property = "merchantId", column = "MERCHANT_ID")
	@Result(property = "contractNo", column = "CONTRACT_NO")
	@Result(property = "rmId", column = "RM_ID")
	@Result(property = "startDate", column = "START_DATE")
	@Result(property = "endDate", column = "END_DATE")
	@Result(property = "durationYears", column = "DURATION_YEARS")
	@Result(property = "contractTypeId", column = "CONTRACT_TYPE_ID")
	@Result(property = "masterContractId", column = "MASTER_CONTRACT_ID")
	@Result(property = "annualFee", column = "ANNUAL_FEE")
	@Result(property = "status", column = "STATUS")
	@Result(property = "terminateDate", column = "TERMINATE_DATE")
	@Result(property = "insurance", column = "INSURANCE")
	@Result(property = "storeId", column = "STORE_ID")
	@Result(property = "overseasCurrency", column = "OVERSEAS_CURRENCY")
	@Result(property = "commissionRate", column = "COMMISSION_RATE")
	@Result(property = "contractTypeCode", column = "CONTRACT_TYPE_CODE")
	@Result(property = "busUnitId", column = "BUS_UNIT_ID")
	@Result(property = "storeCode", column = "STORE_CODE")
	@Result(property = "activeInd", column = "ACTIVE_IND")
	@Result(property = "storefrontStoreCode", column = "STOREFRONT_STORE_CODE")
	@Result(property = "storeName", column = "STORE_NAME")
	@Result(property = "storeNameTc", column = "STORE_NAME_TC")
	@Result(property = "storefrontStoreStatus", column = "STOREFRONT_STORE_STATUS")
	@Result(property = "markUpRate", column = "MARK_UP_RATE")
	@Result(property = "onlineStatus", column = "ONLINE_STATUS")
	@Result(property = "deliveryThershold", column = "DELIVERY_THERSHOLD")
	@Result(property = "deliveryFee", column = "DELIVERY_FEE")
	@Result(property = "storeType", column = "STORE_TYPE")
	@Result(property = "storeLandmarkFlag", column = "STORE_LANDMARK_FLAG")
	@Result(property = "merchantType", column = "MERCHANT_TYPE")
	@Result(property = "upgradeDate", column = "UPGRADE_DATE")
	List<ContractStoreDto> findContractAndStore(String buCode, Integer merchantId);

	@Select(value = "<script> SELECT distinct c.*, "+
		"ct.CODE as CONTRACT_TYPE_CODE " +
		"FROM CONTRACT c " +
		"INNER JOIN CONTRACT_TYPE ct ON c.CONTRACT_TYPE_ID = ct.ID " +
		"INNER JOIN CONTRACT_PROD_TERMS cpt on c.ID = cpt.CONTRACT_ID " +
		"WHERE c.ID in " +
		"<foreach collection='contractIds' item='id' open='(' separator=',' close=') '> #{id} </foreach> " +
		"AND c.STATUS = 'Active' " +
		"AND c.MASTER_CONTRACT_ID IS NULL " +
		"AND DATE_FORMAT(c.END_DATE,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') " +
		"AND (c.STATUS='Active' OR " +
		"(c.STATUS='Terminated' AND DATE_FORMAT(c.TERMINATE_DATE,'%Y-%m-%d') > DATE_FORMAT(NOW(),'%Y-%m-%d'))) " +
		"ORDER BY c.START_DATE DESC, c.ID DESC </script>")
	@Result(property = "contractTypeCode", column = "CONTRACT_TYPE_CODE")
	List<ContractTypeDto> findContractAndContractTypeByContractId(List<Integer> contractIds);
}
