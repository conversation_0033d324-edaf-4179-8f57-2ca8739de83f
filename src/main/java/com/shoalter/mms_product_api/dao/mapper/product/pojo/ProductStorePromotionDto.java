package com.shoalter.mms_product_api.dao.mapper.product.pojo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductStorePromotionDto {
    private Integer id;
    private Integer merchantId;
    private Integer storeId;
    private String storeSkuId;
    private Integer contractId;
    private String contractNo;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private BigDecimal commissionRate;
    private BigDecimal discount;
    private BigDecimal discountPrice;
    private BigDecimal vipDiscount;
    private BigDecimal vipDiscountPrice;
    private BigDecimal productCommissionRate;
    private BigDecimal productOriginalPrice;
    private BigDecimal productSellingPrice;
    private String status;
    private BigDecimal mallDollar;
    private BigDecimal mallDollarVip;
}
