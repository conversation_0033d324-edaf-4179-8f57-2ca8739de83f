package com.shoalter.mms_product_api.dao.mapper.product;

import com.shoalter.mms_product_api.service.product.pojo.EditProductCommonlyUsedInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.MppsProductDo;
import com.shoalter.mms_product_api.service.product.pojo.ProductUploadExcelDataDto;
import com.shoalter.mms_product_api.service.product.pojo.SkuMapUuidDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductMapper {

    @Select("SELECT " +
            "S.STOREFRONT_STORE_CODE, PSS.STORE_SKU_ID, P.SKU_NAME_TCHI, P.SKU_NAME, " +
            "(SELECT FILE_PATH FROM PRODUCT_IMAGES WHERE PRODUCT_ID = P.ID AND IMAGE_TYPE = 'main' LIMIT 1) FILE_PATH, " +
            "P.WEIGHT, P.WEIGHT_UNIT, P.PACK_HEIGHT, P.PACK_LENGTH, " +
            "P.PACK_DEPTH, P.PACK_DIMENSION_UNIT, (select GROUP_CONCAT(BARCODE)  from PRODUCT_BARCODE where PRODUCT_ID =P.ID) as BARCODE " +
            "FROM PRODUCT P " +
            "JOIN PRODUCT_STORE_STATUS PSS ON PSS.PRODUCT_ID = P.ID " +
            "JOIN STORE S ON S.ID = PSS.STORE_ID " +
            "WHERE P.UUID = #{uuid} " +
            "AND P.SKU_CODE = #{skuCode} " +
            "AND S.BUS_UNIT_ID = #{busUnitId}")
	@Result(property = "storeCode", column = "STOREFRONT_STORE_CODE")
	@Result(property = "skuId", column = "STORE_SKU_ID")
	@Result(property = "skuNameZh", column = "SKU_NAME_TCHI")
	@Result(property = "skuNameEn", column = "SKU_NAME")
	@Result(property = "mainPhoto", column = "FILE_PATH")
	@Result(property = "weight", column = "WEIGHT")
	@Result(property = "weightUnit", column = "WEIGHT_UNIT")
	@Result(property = "packingHeight", column = "PACK_HEIGHT")
	@Result(property = "packingLength", column = "PACK_LENGTH")
	@Result(property = "packingDepth", column = "PACK_DEPTH")
	@Result(property = "packingDimensionUnit", column = "PACK_DIMENSION_UNIT")
	@Result(property = "barcode", column = "BARCODE")
    MppsProductDo findMppsProduct(String uuid, String skuCode, Integer busUnitId);

	@Select("<script>" +
			"select p.SKU_CODE ,p.ORIGINAL_PRICE,p.INVISIBLE_FLAG,pss.ONLINE_STATUS  " +
			"from PRODUCT p " +
			"join PRODUCT_STORE_STATUS pss on p.ID =pss.PRODUCT_ID " +
			"where p.SKU_CODE in " +
			"<foreach collection='skuList' item='item' open='(' separator=',' close=') '> " +
			" #{item}" +
			"</foreach> "+
			"and p.MERCHANT_ID = #{merchantId} " +
			"and p.CONTRACT_ID = #{contractId} " +
			"</script>")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "price", column = "ORIGINAL_PRICE")
	@Result(property = "visible", column = "INVISIBLE_FLAG")
	@Result(property = "online", column = "ONLINE_STATUS")
	List<EditProductCommonlyUsedInfoDto> findCommonlyUsedTemplateData(List<String> skuList, Integer merchantId, Integer contractId);

	@Select("<script>" +
			"select p.SKU_CODE ,pss.SHARE_STOCK_UUID " +
			"from PRODUCT p " +
			"join PRODUCT_STORE_STATUS pss on p.id=pss.PRODUCT_ID " +
			"where pss.SHARE_STOCK_UUID in "+
			"<foreach collection='uuidList' item='uuid' open='(' separator=',' close=') '> " +
			" #{uuid}" +
			"</foreach> "+
			"</script>")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "uuid", column = "SHARE_STOCK_UUID")
	List<SkuMapUuidDto> getSkuCodeMapUuidByUuidList(List<String> uuidList);

	@Select("<script>" +
			"select p.SKU_CODE ,p.UUID " +
			"from PRODUCT p " +
			"where p.SKU_CODE in "+
			"<foreach collection='skuCodeList' item='uuid' open='(' separator=',' close=') '> " +
			" #{uuid}" +
			"</foreach> "+
			"</script>")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "uuid", column = "UUID")
	List<SkuMapUuidDto> getSkuCodeMapUuidBySkuList(List<String> skuCodeList);

	@Select("<script>" +
			"select p.SKU_CODE ,p.UUID " +
			"from PRODUCT p " +
			"where p.MERCHANT_ID = #{merchantId} " +
			"AND p.SKU_CODE in "+
			"<foreach collection='skuCodeList' item='uuid' open='(' separator=',' close=') '> " +
			" #{uuid}" +
			"</foreach> "+
			"</script>")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "uuid", column = "UUID")
	List<SkuMapUuidDto> getUuidByMerchantIdAndSkuList(Integer merchantId, List<String> skuCodeList);

	@Select("<script>" +
			"select p.UUID ,p.ID ,p.SKU_S_TITLE_HKTV_EN ,p.SKU_S_TITLE_HKTV_CH ,p.SKU_L_TITLE_HKTV_EN ,p.SKU_L_TITLE_HKTV_CH ," +
			"pa.VIDEO_LINK2 ,pa.VIDEO_LINK_EN2 ,pa.VIDEO_LINK_CH2 ,pa.VIDEO_LINK_SC2 ," +
			"pa.VIDEO_LINK3 ,pa.VIDEO_LINK_EN3 ,pa.VIDEO_LINK_CH3 ,pa.VIDEO_LINK_SC3 ," +
			"pa.VIDEO_LINK4 ,pa.VIDEO_LINK_EN4 ,pa.VIDEO_LINK_CH4 ,pa.VIDEO_LINK_SC4 ," +
			"pa.VIDEO_LINK5 ,pa.VIDEO_LINK_EN5 ,pa.VIDEO_LINK_CH5 ,pa.VIDEO_LINK_SC5 ," +
			"p.CONSUMABLE ,p.PRIORITY ,p.FINE_PRINT_TITLE_EN ,p.FINE_PRINT_TITLE_CH ," +
			"p.DELIVERY_TITLE_EN ,p.DELIVERY_TITLE_CH ,p.DELIVERY_DETAILS_EN ,p.DELIVERY_DETAILS_CH ," +
			"p.DELIVERY_COMPLETION_DAYS ,pa.MINIMUM_SHELF_LIFE ," +
			"(select GROUP_CONCAT(REGION)  from PRODUCT_OVERSEA_DELIVERY pod where pod.PRODUCT_ID =p.ID) overseaDelivery " +
			"from PRODUCT p " +
			"left join PRODUCT_ATTRIBUTES pa on pa.ID = p.ATTRIBUTES_ID " +
			"where p.UUID in " +
			"<foreach collection='uuidList' item='uuid' open='(' separator=',' close=') '> " +
			" #{uuid}" +
			"</foreach> "+
			"</script>")
	@Result(property = "uuid", column = "UUID")
	@Result(property = "id", column = "ID")
	@Result(property = "skuShortTitleHktvEn", column = "SKU_S_TITLE_HKTV_EN")
	@Result(property = "skuShortTitleHktvCh", column = "SKU_S_TITLE_HKTV_CH")
	@Result(property = "skuLongTitleHktvEn", column = "SKU_L_TITLE_HKTV_EN")
	@Result(property = "skuLongTitleHktvCh", column = "SKU_L_TITLE_HKTV_CH")
	@Result(property = "videoLink2", column = "VIDEO_LINK2")
	@Result(property = "videoLinkEn2", column = "VIDEO_LINK_EN2")
	@Result(property = "videoLinkCh2", column = "VIDEO_LINK_CH2")
	@Result(property = "videoLinkSc2", column = "VIDEO_LINK_SC2")
	@Result(property = "videoLink3", column = "VIDEO_LINK3")
	@Result(property = "videoLinkEn3", column = "VIDEO_LINK_EN3")
	@Result(property = "videoLinkCh3", column = "VIDEO_LINK_CH3")
	@Result(property = "videoLinkSc3", column = "VIDEO_LINK_SC3")
	@Result(property = "videoLink4", column = "VIDEO_LINK4")
	@Result(property = "videoLinkEn4", column = "VIDEO_LINK_EN4")
	@Result(property = "videoLinkCh4", column = "VIDEO_LINK_CH4")
	@Result(property = "videoLinkSc4", column = "VIDEO_LINK_SC4")
	@Result(property = "videoLink5", column = "VIDEO_LINK5")
	@Result(property = "videoLinkEn5", column = "VIDEO_LINK_EN5")
	@Result(property = "videoLinkCh5", column = "VIDEO_LINK_CH5")
	@Result(property = "videoLinkSc5", column = "VIDEO_LINK_SC5")
	@Result(property = "consumable", column = "CONSUMABLE")
	@Result(property = "priority", column = "PRIORITY")
	@Result(property = "finePrintTitleEn", column = "FINE_PRINT_TITLE_EN")
	@Result(property = "finePrintTitleCh", column = "FINE_PRINT_TITLE_CH")
	@Result(property = "deliveryTitleEn", column = "DELIVERY_TITLE_EN")
	@Result(property = "deliveryTitleCh", column = "DELIVERY_TITLE_CH")
	@Result(property = "deliveryDetailsEn", column = "DELIVERY_DETAILS_EN")
	@Result(property = "deliveryDetailsCh", column = "DELIVERY_DETAILS_CH")
	@Result(property = "deliveryCompletionDays", column = "DELIVERY_COMPLETION_DAYS")
	@Result(property = "minimumShelfLife", column = "MINIMUM_SHELF_LIFE")
	List<ProductUploadExcelDataDto> findProductUploadExcelDataList(List<String> uuidList);

	@Select("<script>" +
			"select p.SKU_CODE ,p.UUID " +
			"from PRODUCT p " +
			"join PRODUCT_STORE_STATUS pss on p.ID =pss.PRODUCT_ID " +
			"where pss.STORE_ID =  #{storeId} " +
			"AND p.SKU_CODE in "+
			"<foreach collection='skuCodeList' item='uuid' open='(' separator=',' close=') '> " +
			" #{uuid}" +
			"</foreach> "+
			"</script>")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "uuid", column = "UUID")
	List<SkuMapUuidDto> getUuidByStoreIdAndSkuList(Integer storeId, List<String> skuCodeList);

	@Select("select p.SKU_CODE ,p.UUID " +
			"from PRODUCT p " +
			"join PRODUCT_STORE_STATUS pss on p.ID =pss.PRODUCT_ID " +
			"where pss.STORE_ID =  #{storeId} ")
	@Result(property = "skuCode", column = "SKU_CODE")
	@Result(property = "uuid", column = "UUID")
	List<SkuMapUuidDto> getUuidAndSkuByStoreId(Integer storeId);
}
