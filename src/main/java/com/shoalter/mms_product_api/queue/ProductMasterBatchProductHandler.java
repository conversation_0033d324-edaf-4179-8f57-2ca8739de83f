package com.shoalter.mms_product_api.queue;

import com.google.gson.Gson;
import com.rabbitmq.client.Channel;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.helper.RabbitTemplateHelper;
import com.shoalter.mms_product_api.service.base.pojo.ProductMasterMqMessageDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterMqDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterReturnDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class ProductMasterBatchProductHandler {
	private final RabbitTemplateHelper rabbitTemplateHelper;
	private final SaveProductHelper saveProductHelper;
	private final Gson gson;
	private final Executor productSyncExecutor;

	@SneakyThrows
	@RabbitListener(queues = "${mpps.rabbitmq.queue.name}", containerFactory = "productMasterContainerFactory", ackMode = "MANUAL")
	public void receive(@Payload ProductMasterMqMessageDto productMasterMqMessageDto, Message message, Channel channel) {
		try {
			long startTime = System.currentTimeMillis();
			String correlationId = message.getMessageProperties().getCorrelationId();
			Arrays.stream(gson.toJson(productMasterMqMessageDto).split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
				.forEach(str -> log.info("Consumer hktvmall_queue, correlationId: {}, productMasterMqMessageDto :{}", correlationId, str));

			UserDto userDto = getUserDto(productMasterMqMessageDto.getUserCode());
			switch (productMasterMqMessageDto.getAction()) {
				case "CREATE":
					// 新建一筆商品
				case "UPDATE":
					// 修改現存商品狀態
					saveProduct(userDto, productMasterMqMessageDto, correlationId);
					break;
				case "OFFLINE":
					// 關閉指定商品狀態，調整product_store_status
					saveProductOffline(userDto, productMasterMqMessageDto, correlationId);
					break;
				default:
					channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
					log.error("not correct action");
					return;
			}
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
			long endTime = System.currentTimeMillis();
			log.info("Time taken by ProductMasterBatchProductHandler receive method : {} milliseconds", endTime - startTime);
		} catch (Exception e) {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
			log.error("Consumer hktvmall_queue fail correlationId: {} , errorMessage: {}", message.getMessageProperties().getCorrelationId(), e.getMessage());
			log.error(e.getMessage(), e);
		}
	}

	private UserDto getUserDto(String userCode) {
		return UserDto.builder()
				.userCode(userCode)
				.build();
	}

	private void saveProduct(UserDto userDto, ProductMasterMqMessageDto productMasterMqMessageDto, String correlationId) {
		log.info("saveProduct productMasterMqMessageDto size is: {}", productMasterMqMessageDto.getProducts().size());
		long startTime = System.currentTimeMillis();

		try {
			//group product by same store and same product id (variant sku)
			Map<String, List<ProductMasterMqDto>> variantSkuMap = productMasterMqMessageDto.getProducts().stream()
				.collect(Collectors.groupingBy(data -> data.getStores() + StringUtil.UNDERLINE + data.getProductId()));

			//process product async
			List<ProductMasterReturnDto> productMasterReturnDto = variantSkuMap.entrySet().stream()
				.map((entry) -> CompletableFuture.supplyAsync(() -> {
					log.info("saveProduct with multithread to hybris store product {}", entry.getKey());
					return saveProductHelper.createOrUpdateProducts(userDto, entry.getValue());
				}, productSyncExecutor))
				.map(CompletableFuture::join)
				.collect(Collectors.toList())
				.stream()
				.flatMap(Collection::stream)
				.collect(Collectors.toList());

			// 回傳pm
			if (CollectionUtil.isNotEmpty(productMasterReturnDto)) {
				rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDto, correlationId);
			}
		}catch (Exception e){
			log.error(e.getMessage(), e);
			throw  e;
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by ProductMasterBatchProductHandler saveProduct method(create、update) : {} milliseconds", endTime - startTime);
	}

	private void saveProductOffline(UserDto userDto, ProductMasterMqMessageDto productMasterMqMessageDto, String correlationId) {
		log.info("saveProductOffline productMasterMqMessageDto size is: {}", productMasterMqMessageDto.getProducts().size());
		long startTime = System.currentTimeMillis();
		List<ProductMasterReturnDto> productMasterReturnDto;
		productMasterReturnDto = productMasterMqMessageDto.getProducts().stream().parallel()
			.map(product -> CompletableFuture.supplyAsync(() -> {
				log.info("saveProduct with multithread to hybris uuid: {}, version: {}, record row id: {}", product.getUuid(), product.getVersion(), product.getRecordRowId());
				return saveProductHelper.updateProductOffline(userDto, product);
			}, productSyncExecutor))
			.map(CompletableFuture::join)
			.collect(Collectors.toList());
		// 回傳pm
		if (CollectionUtil.isNotEmpty(productMasterReturnDto)) {
			rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDto, correlationId);
		}
		long endTime = System.currentTimeMillis();
		log.info("Time taken by ProductMasterBatchProductHandler saveProductOffline method : {} milliseconds", endTime - startTime);
	}
}
