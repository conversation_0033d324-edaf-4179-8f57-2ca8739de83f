package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

@Mapper(
	componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface LittleMallRelationDataMapper {


	LittleMallRelationDto toLittleMallRelationDto(ProductMasterRelationSettingResponseDto productMasterRelationSettingResponseDtoList);

	List<LittleMallRelationDto> toLittleMallRelationDto(List<ProductMasterRelationSettingResponseDto> productMasterRelationSettingResponseDtoList);

}
