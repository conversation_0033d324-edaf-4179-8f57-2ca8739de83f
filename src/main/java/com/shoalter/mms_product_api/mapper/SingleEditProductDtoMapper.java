package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantMatrixProductDto;
import com.shoalter.mms_product_api.util.StringUtil;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface SingleEditProductDtoMapper {
	SingleEditProductDtoMapper INSTANCE = Mappers.getMapper(SingleEditProductDtoMapper.class);

	// 共用欄位
	@Mapping(target = "product.merchantId", source = "primary.product.merchantId")
	@Mapping(target = "product.productId", source = "primary.product.productId")
	@Mapping(target = "product.skuNameCh", source = "primary.product.skuNameCh")
	@Mapping(target = "product.additional.littleMall.storeCode", source = "primary.product.additional.littleMall.storeCode")
	@Mapping(target = "product.additional.littleMall.productReadyMethod", source = "primary.product.additional.littleMall.productReadyMethod")
	@Mapping(target = "product.additional.littleMall.storeCategory", source = "primary.product.additional.littleMall.storeCategory")
	@Mapping(target = "product.additional.littleMall.displayInHktvmallCategory", source = "primary.product.additional.littleMall.displayInHktvmallCategory")
	@Mapping(target = "product.additional.littleMall.skuLongDescriptionCh", source = "primary.product.additional.littleMall.skuLongDescriptionCh")
	@Mapping(target = "product.additional.littleMall.otherPhoto", source = "primary.product.additional.littleMall.otherPhoto")
	// 非共用欄位
	@Mapping(target = "product.additional.littleMall.storeSkuId", source = "variant.storeSkuId")
	@Mapping(target = "product.skuId", source = "variant.storeSkuId", qualifiedByName = "toSkuId")
	@Mapping(target = "product.originalPrice", source = "variant.originalPrice")
	@Mapping(target = "product.additional.littleMall.sellingPrice", source = "variant.sellingPrice")
	@Mapping(target = "product.additional.littleMall.isPrimarySku", expression = "java(false)")
	@Mapping(target = "product.additional.littleMall.visibility", source = "variant.visibility")
	@Mapping(target = "product.additional.littleMall.onlineStatus", source = "variant.onlineStatus")
	@Mapping(target = "product.additional.littleMall.mainPhoto", source = "variant.mainPhoto")
	@Mapping(target = "product.additional.littleMall.productField1", source = "variant.productField1")
	@Mapping(target = "product.additional.littleMall.productFieldCategory1", source = "variant.productFieldCategory1")
	@Mapping(target = "product.additional.littleMall.productFieldOption1", source = "variant.productFieldOption1")
	@Mapping(target = "product.additional.littleMall.productField2", source = "variant.productField2")
	@Mapping(target = "product.additional.littleMall.productFieldCategory2", source = "variant.productFieldCategory2")
	@Mapping(target = "product.additional.littleMall.productFieldOption2", source = "variant.productFieldOption2")
	@Mapping(target = "product.additional.littleMall.productField3", source = "variant.productField3")
	@Mapping(target = "product.additional.littleMall.productFieldCategory3", source = "variant.productFieldCategory3")
	@Mapping(target = "product.additional.littleMall.productFieldOption3", source = "variant.productFieldOption3")
	@Mapping(target = "product.additional.littleMall.productField4", source = "variant.productField4")
	@Mapping(target = "product.additional.littleMall.productFieldCategory4", source = "variant.productFieldCategory4")
	@Mapping(target = "product.additional.littleMall.productFieldOption4", source = "variant.productFieldOption4")
	SingleEditProductDto toSingleEditProductDto(SingleEditProductDto primary, VariantMatrixProductDto variant);

	@Mapping(target = "product.productId", source = "hktvProductFieldDto.productId", qualifiedByName = "toTmallProductId")
	@Mapping(target = "product.skuId", source = "hktvProductFieldDto", qualifiedByName = "toTmallSkuIdFromHktvField")
	@Mapping(target = "product.skuNameEn", source = "hktvProductFieldDto.skuNameEn")
	@Mapping(target = "product.skuNameCh", source = "hktvProductFieldDto.skuNameEn")
	@Mapping(target = "product.skuNameSc", source = "hktvProductFieldDto.skuNameEn")
	@Mapping(target = "product.colourFamilies", source = "hktvProductFieldDto.colourFamilies")
	@Mapping(target = "product.color", source = "hktvProductFieldDto.colorEn")
	@Mapping(target = "product.sizeSystem", source = "hktvProductFieldDto.sizeSystem")
	@Mapping(target = "product.size", source = "hktvProductFieldDto.size")
	@Mapping(target = "product.originalPrice", source = "hktvProductFieldDto.originalPrice")
	@Mapping(target = "product.packingHeight", source = "hktvProductFieldDto.packingHeight")
	@Mapping(target = "product.packingLength", source = "hktvProductFieldDto.packingLength")
	@Mapping(target = "product.packingDepth", source = "hktvProductFieldDto.packingDepth")
	@Mapping(target = "product.packingDimensionUnit", source = "hktvProductFieldDto.packingDimensionUnit")
	@Mapping(target = "product.weight", source = "hktvProductFieldDto.weight")
	@Mapping(target = "product.weightUnit", source = "hktvProductFieldDto.weightUnit")
	@Mapping(target = "product.option1", source = "hktvProductFieldDto.field1")
	@Mapping(target = "product.option1Value", source = "hktvProductFieldDto.value1")
	@Mapping(target = "product.option2", source = "hktvProductFieldDto.field2")
	@Mapping(target = "product.option2Value", source = "hktvProductFieldDto.value2")
	@Mapping(target = "product.option3", source = "hktvProductFieldDto.field3")
	@Mapping(target = "product.option3Value", source = "hktvProductFieldDto.value3")
	@Mapping(target = "product.additional.hktv.storefrontStoreCode", source = "hktvProductFieldDto.storefrontStoreCode")
	@Mapping(target = "product.additional.hktv.stores", source = "hktvProductFieldDto.storefrontStoreCode")
	@Mapping(target = "product.additional.hktv.sellingPrice", source = "hktvProductFieldDto.sellingPrice")
	@Mapping(target = "product.additional.hktv.style", source = "hktvProductFieldDto.sellingPrice", qualifiedByName = "toStyle")
	@Mapping(target = "product.additional.hktv.discountTextEn", source = "hktvProductFieldDto.discountTextEn")
	@Mapping(target = "product.additional.hktv.discountTextCh", source = "hktvProductFieldDto.discountTextCh")
	@Mapping(target = "product.additional.hktv.skuShortDescriptionEn", source = "hktvProductFieldDto.skuShortDescriptionEn")
	@Mapping(target = "product.additional.hktv.skuShortDescriptionCh", source = "hktvProductFieldDto.skuShortDescriptionEn")
	@Mapping(target = "product.additional.hktv.skuShortDescriptionSc", source = "hktvProductFieldDto.skuShortDescriptionEn")
	@Mapping(target = "product.additional.hktv.mainPhoto", source = "hktvProductFieldDto.convertedMainPhoto")
	@Mapping(target = "product.additional.hktv.warehouseCode", source = "hktvProductFieldDto.storefrontStoreCode", qualifiedByName = "toWarehouseCode")
	SingleEditProductDto toSingleEditProductDtoFromTmallData(HktvProductFieldDto hktvProductFieldDto);

	default List<SingleEditProductDto> toSingleEditProductDto(SingleEditProductDto primary, List<VariantMatrixProductDto> variantList) {
		if (variantList == null || primary == null) {
			return Collections.emptyList();
		}
		return variantList.stream()
			.map(variant -> toSingleEditProductDto(primary, variant))
			.collect(Collectors.toList());
	}

	@Named("toSkuId")
	default String toSkuId(String storeSkuId) {
		if (storeSkuId == null || !storeSkuId.contains(StringUtil.PRODUCT_SEPARATOR)) {
			return null;
		}
		return storeSkuId.split(StringUtil.PRODUCT_SEPARATOR)[1];
	}

	@Named("toTmallProductId")
	default String toTmallProductId(String productId) {
		return String.format("%s_H", productId);
	}

	@Named("toTmallSkuId")
	default String toTmallSkuId(String skuId) {
		return String.format("%s_H", skuId);
	}

	@Named("toTmallSkuIdFromHktvField")
	default String toTmallSkuIdFromHktvField(HktvProductFieldDto source) {
		if (source == null) {
			return null;
		}
		return toTmallSkuId(source.getSkuId() == null ? source.getProductId() : source.getSkuId());
	}

	@Named("toStyle")
	default String toStyle(BigDecimal sellingPrice) {
		if (sellingPrice != null && sellingPrice.compareTo(BigDecimal.ZERO) > 0) {
			return "GREY";
		}
		return "";
	}

	@Named("toWarehouseCode")
	default String toWarehouseCode(String storefrontStoreCode) {
		return String.format("%s-%s", storefrontStoreCode, 9);
	}
}

