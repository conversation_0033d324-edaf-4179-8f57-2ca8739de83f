package com.shoalter.mms_product_api.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.product.pojo.MmsProductDo;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallQueryFlattenStoresResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallSearchFlattenStoresDataResponse;
import com.shoalter.mms_product_api.util.ConvertPackingBoxTypeUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Mapper(
	componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface ProductResponseDataMapper {

	ProductResponseDataMapper INSTANCE = Mappers.getMapper(ProductResponseDataMapper.class);

	ProductResponseDto toResponseDto(ProductMasterResultDto productMasterResultDto);
	FindStoreSkuIdProductResponseDto toFindStoreSkuIdProductResponseDto(ProductMasterResultDto productMasterResultDto);

	@Mapping(target = "additional.hktv.contractNo", source = "contractNo")
	@Mapping(target = "additional.hktv.stores", source = "storeCode")
	@Mapping(target = "additional.hktv.productReadyMethod", source = "productReadyMethod")
	@Mapping(target = "additional.hktv.deliveryMethod", source = "deliveryMethod")
	@Mapping(target = "additional.hktv.storeSkuId", source = "storeSkuId")
	@Mapping(target = "additional.hktv.productTypeCode", source = "productTypeCode", qualifiedByName = "convertCommaStringToList")
	@Mapping(target = "additional.hktv.primaryCategory.productCatCode", source = "primaryCategoryCode")
	@Mapping(target = "additional.hktv.isPrimarySku", source = "isPrimarySku")
	@Mapping(target = "additional.hktv.visibility", source = "invisibleFlag", qualifiedByName = "toVisibility")
	@Mapping(target = "additional.hktv.skuShortDescriptionEn", source = "skuShortDescriptionEn")
	@Mapping(target = "additional.hktv.skuShortDescriptionCh", source = "skuShortDescriptionCh")
	@Mapping(target = "additional.hktv.skuShortDescriptionSc", source = "skuShortDescriptionSc")
	@Mapping(target = "additional.hktv.skuLongDescriptionEn", source = "skuLongDescriptionEn")
	@Mapping(target = "additional.hktv.skuLongDescriptionCh", source = "skuLongDescriptionCh")
	@Mapping(target = "additional.hktv.skuLongDescriptionSc", source = "skuLongDescriptionSc")
	@Mapping(target = "additional.hktv.featureStartTime", source = "featureStartTime", qualifiedByName = "dateToLocalDateTime")
	@Mapping(target = "additional.hktv.featureEndTime", source = "featureEndTime", qualifiedByName = "dateToLocalDateTime")
	@Mapping(target = "additional.hktv.voucherType", source = "voucherType")
	@Mapping(target = "additional.hktv.voucherDisplayType", source = "voucherDisplayType")
	@Mapping(target = "additional.hktv.expiryType", source = "expiryType")
	@Mapping(target = "additional.hktv.redeemStartDate", source = "redeemStartDate", qualifiedByName = "dateToLocalDateTime")
	@Mapping(target = "additional.hktv.fixedRedemptionDate", source = "fixedRedemptionDate", qualifiedByName = "dateToLocalDateTime")
	@Mapping(target = "additional.hktv.uponPurchaseDate", source = "uponPurchaseDate")
	@Mapping(target = "additional.hktv.finePrintEn", source = "finePrintEn")
	@Mapping(target = "additional.hktv.finePrintCh", source = "finePrintCh")
	@Mapping(target = "additional.hktv.finePrintSc", source = "finePrintSc")
	@Mapping(target = "additional.hktv.termName", expression = "java(mmsProductToTermName(mmsProductDo))")
	@Mapping(target = "additional.hktv.currency", source = "currency")
	@Mapping(target = "additional.hktv.cost", source = "cost")
	@Mapping(target = "additional.hktv.sellingPrice", source = "sellingPrice")
	@Mapping(target = "additional.hktv.style", source = "style")
	@Mapping(target = "additional.hktv.discountTextEn", source = "discountTextEn")
	@Mapping(target = "additional.hktv.discountTextCh", source = "discountTextCh")
	@Mapping(target = "additional.hktv.discountTextSc", source = "discountTextSc")
	@Mapping(target = "additional.hktv.mallDollar", source = "mallDollar")
	@Mapping(target = "additional.hktv.vipMallDollar", source = "vipMallDollar")
	@Mapping(target = "additional.hktv.userMax", source = "userMax")
	@Mapping(target = "additional.hktv.mainPhoto", source = "mainPhoto")
	@Mapping(target = "additional.hktv.mainVideo", source = "mainVideo")
	@Mapping(target = "additional.hktv.variantProductPhoto", source = "otherProductPhoto", qualifiedByName = "convertCommaStringToList")
	@Mapping(target = "additional.hktv.otherPhoto", source = "otherPhoto", qualifiedByName = "convertCommaStringToList")
	@Mapping(target = "additional.hktv.advertisingPhoto", source = "advertisingPhoto")
	@Mapping(target = "additional.hktv.videoLink", source = "videoLink")
	@Mapping(target = "additional.hktv.videoLinkTextEn", source = "videoLinkTextEn")
	@Mapping(target = "additional.hktv.videoLinkTextCh", source = "videoLinkTextCh")
	@Mapping(target = "additional.hktv.videoLinkTextSc", source = "videoLinkTextSc")
	@Mapping(target = "additional.hktv.videoLink2", source = "videoLink2")
	@Mapping(target = "additional.hktv.videoLinkTextEn2", source = "videoLinkTextEn2")
	@Mapping(target = "additional.hktv.videoLinkTextCh2", source = "videoLinkTextCh2")
	@Mapping(target = "additional.hktv.videoLinkTextSc2", source = "videoLinkTextSc2")
	@Mapping(target = "additional.hktv.videoLink3", source = "videoLink3")
	@Mapping(target = "additional.hktv.videoLinkTextEn3", source = "videoLinkTextEn3")
	@Mapping(target = "additional.hktv.videoLinkTextCh3", source = "videoLinkTextCh3")
	@Mapping(target = "additional.hktv.videoLinkTextSc3", source = "videoLinkTextSc3")
	@Mapping(target = "additional.hktv.videoLink4", source = "videoLink4")
	@Mapping(target = "additional.hktv.videoLinkTextEn4", source = "videoLinkTextEn4")
	@Mapping(target = "additional.hktv.videoLinkTextCh4", source = "videoLinkTextCh4")
	@Mapping(target = "additional.hktv.videoLinkTextSc4", source = "videoLinkTextSc4")
	@Mapping(target = "additional.hktv.videoLink5", source = "videoLink5")
	@Mapping(target = "additional.hktv.videoLinkTextEn5", source = "videoLinkTextEn5")
	@Mapping(target = "additional.hktv.videoLinkTextCh5", source = "videoLinkTextCh5")
	@Mapping(target = "additional.hktv.videoLinkTextSc5", source = "videoLinkTextSc5")
	@Mapping(target = "additional.hktv.warehouseId", source = "warehouseId")
	@Mapping(target = "additional.hktv.packingSpecEn", source = "packingSpecEn")
	@Mapping(target = "additional.hktv.packingSpecCh", source = "packingSpecCh")
	@Mapping(target = "additional.hktv.packingSpecSc", source = "packingSpecSc")
	@Mapping(target = "additional.hktv.invoiceRemarksEn", source = "invoiceRemarksEn")
	@Mapping(target = "additional.hktv.invoiceRemarksCh", source = "invoiceRemarksCh")
	@Mapping(target = "additional.hktv.invoiceRemarksSc", source = "invoiceRemarksSc")
	@Mapping(target = "additional.hktv.returnDays", source = "returnDays")
	@Mapping(target = "additional.hktv.productReadyDays", source = "productReadyDays")
	@Mapping(target = "additional.hktv.pickupDays", source = "pickupDays")
	@Mapping(target = "additional.hktv.pickupTimeslot", source = "pickupTimeslot")
	@Mapping(target = "additional.hktv.urgent", source = "urgent")
	@Mapping(target = "additional.hktv.warranty", source = "warranty")
	@Mapping(target = "additional.hktv.needRemovalServices", source = "needRemovalServices")
	@Mapping(target = "additional.hktv.goodsType", source = "goodsType")
	@Mapping(target = "additional.hktv.warrantyPeriodUnit", source = "warrantyPeriodUnit")
	@Mapping(target = "additional.hktv.warrantyPeriod", source = "warrantyPeriod")
	@Mapping(target = "additional.hktv.warrantySupplierEn", source = "warrantySupplierEn")
	@Mapping(target = "additional.hktv.warrantySupplierCh", source = "warrantySupplierCh")
	@Mapping(target = "additional.hktv.warrantySupplierSc", source = "warrantySupplierSc")
	@Mapping(target = "additional.hktv.serviceCentreAddressEn", source = "serviceCentreAddressEn")
	@Mapping(target = "additional.hktv.serviceCentreAddressCh", source = "serviceCentreAddressCh")
	@Mapping(target = "additional.hktv.serviceCentreAddressSc", source = "serviceCentreAddressSc")
	@Mapping(target = "additional.hktv.serviceCentreEmail", source = "serviceCentreEmail")
	@Mapping(target = "additional.hktv.serviceCentreContact", source = "serviceCentreContact")
	@Mapping(target = "additional.hktv.warrantyRemarkEn", source = "warrantyRemarkEn")
	@Mapping(target = "additional.hktv.warrantyRemarkCh", source = "warrantyRemarkCh")
	@Mapping(target = "additional.hktv.warrantyRemarkSc", source = "warrantyRemarkSc")
	@Mapping(target = "additional.hktv.onlineStatus", source = "onlineStatus")
	@Mapping(target = "additional.hktv.rmCode", source = "rmCode")
	@Mapping(target = "additional.hktv.virtualStore", source = "virtualStore")
	@Mapping(target = "additional.hktv.voucherTemplateType", source = "voucherTemplateType")
	@Mapping(target = "additional.hktv.preSellFruit", source = "preSellFruit")
	@Mapping(target = "additional.hktv.physicalStore", source = "physicalStore")
	@Mapping(target = "additional.hktv.thumbnailVideo", source = "thumbnailVideo")
	@Mapping(target = "additional.hktv.storageType", source = "storageType")
	@Mapping(target = "storageTemperature", source = "packingBoxType", qualifiedByName = "toStorageTemperature")
	@Mapping(target = "additional.hktv.commissionRate", source = "commissionRate")
	@Mapping(target = "barcodes", source = "barcode", qualifiedByName = "toBarcodes")
	@Mapping(target = "weightUnit", source = "weightUnit", defaultValue = "")
	@Mapping(target = "packingDimensionUnit", source = "packingDimensionUnit", defaultValue = "")
	@Mapping(target = "additional.hktv.deliveryDistrict", source = "deliveryDistrict", qualifiedByName = "convertCommaStringToList")
	@Mapping(target = "cartonSizeList", source = "cartonSizeJsonArrayStr", qualifiedByName = "toCartonSizeDto")
	@Mapping(target = "additional.hktv.affiliateUrl", source = "affiliateUrl")
	@Mapping(target = "additional.hktv.ewPercentageSetting", source = "ewPercentageSetting")
	@Mapping(target = "additional.hktv.claimLinkEn", source = "claimLinkEn")
	@Mapping(target = "additional.hktv.claimLinkCh", source = "claimLinkCh")
	@Mapping(target = "additional.hktv.claimLinkSc", source = "claimLinkSc")
	@Mapping(target = "additional.hktv.storefrontStoreCode", source = "storefrontStoreCode")
	@Mapping(target = "version", source = "version", defaultValue = "1")
	FindStoreSkuIdProductResponseDto fromMmsProductDo(MmsProductDo source);

	List<FindStoreSkuIdProductResponseDto> fromMmsProductDo(List<MmsProductDo> source);

	LittleMallQueryFlattenStoresResponseDto toLittleMallQueryFlattenStoresResponseDto(LittleMallSearchFlattenStoresDataResponse dto);

	@Named("toStorageTemperature")
	default String toStorageTemperature(String packingBoxType) {
		return ConvertPackingBoxTypeUtil.generateStorageTemperature(packingBoxType);
	}

	@Named("toVisibility")
	default String toVisibility(String invisibleFlag) {
		if (!StringUtils.equalsAnyIgnoreCase(invisibleFlag, ConstantType.CONSTANT_YES, ConstantType.CONSTANT_NO)) {
			return null;
		}
		return StringUtil.toggleYN(invisibleFlag);
	}

	@Named("dateToLocalDateTime")
	default LocalDateTime dateToLocalDateTime(Date date) {
		LocalDateTime localDateTime = null;
		if (date != null) {
			localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		}
		return localDateTime;
	}

	@Named("mmsProductToTermName")
	default String mmsProductToTermName(MmsProductDo source) {
		BigDecimal termNameNumber;
		String contractTypeCode = Optional.ofNullable(source.getContractTypeCode()).orElse("");
		if (ContractType.FIXED_COST_CONTRACT.equalsIgnoreCase(contractTypeCode)) {
			termNameNumber = Optional.ofNullable(source.getFixedCost()).orElse(new BigDecimal(0));
		} else if (ContractType.ANNUAL_INSURANCE_CONTRACT.equalsIgnoreCase(contractTypeCode))
			termNameNumber = Optional.ofNullable(source.getCommissionRate()).orElse(new BigDecimal(0));
		else {
			return null;
		}
		return String.format("%s(%d)", source.getTermName(), termNameNumber.intValue());
	}

	@Named("convertCommaStringToList")
	default List<String> convertCommaStringToList(String commaString) {
		if (ObjectUtils.isEmpty(commaString)) {
			return Collections.emptyList();
		}
		return Arrays.asList(commaString.split(StringUtil.COMMA));
	}

	@Named("toCartonSizeDto")
	default List<CartonSizeDto> toCartonSizeDto(String cartonSizeJsonArrayStr)
		throws JsonProcessingException {

		if (org.apache.commons.lang3.StringUtils.isBlank(cartonSizeJsonArrayStr)) {
			return new ArrayList<>();
		}

		ObjectMapper objectMapper = new ObjectMapper();

		return objectMapper.readValue(cartonSizeJsonArrayStr,
			new TypeReference<>() {
			});
	}

	@Named("toBarcodes")
	default List<ProductBarcodeDto> toBarcodes(String barcode) {
		if (StringUtils.isNotBlank(barcode)) {
			return Arrays.stream(barcode.split(StringUtil.COMMA))
				.filter(StringUtils::isNotBlank)
				.map(s -> {
					Matcher matcher = Pattern.compile("(\\d+)\\[(.+)]").matcher(s);
					if (matcher.find()) {
						String sequenceNo = matcher.group(1);
						String ean = matcher.group(2);
						return ProductBarcodeDto.builder()
							.sequenceNo(Integer.parseInt(sequenceNo))
							.ean(ean)
							.lock(false)
							.build();
					}
					return null;
				})
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
		} else {
			return List.of();
		}
	}
}
