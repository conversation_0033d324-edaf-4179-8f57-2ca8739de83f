package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface PartnerProductPriceDataHistoryMapper {

	@Mapping(target = "startDate", source = "lastUpdateDate")
	@Mapping(target = "endDate", ignore = true)
	@Mapping(target = "id", ignore = true)
	PartnerProductPriceHistoryDo toCreatePartnerProductPriceHistoryDo(PartnerProductPriceDo partnerProductPriceDo);
}
