package com.shoalter.mms_product_api.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateProductMainMqMessage;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterReturnDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Component
public class RabbitTemplateHelper {

    private final Gson gson;

	private final MessagePostProcessor productMasterMessagePostProcessor;

	private final RabbitTemplate rabbitTemplate;

	@Value("${product.master.rabbitmq.product.result.exchange}")
	private String rabbitMqProductExchange;

	@Value("${product.master.rabbitmq.product.result.routing.key}")
	private String rabbitMqProductMasterProductResultRoutingKey;

	@Value("${hybris.rabbitmq.product.update.routing.key}")
	private String rabbitMqHybrisProductUpdateRoutingKey;

	public void sendProductResultToProductMaster(List<ProductMasterReturnDto> productMasterReturnDtoList, String correlationId) {
		CorrelationData correlationData = new CorrelationData();
		if (correlationId != null) {
			correlationData.setId(correlationId);
		}

		Arrays.stream(gson.toJson(productMasterReturnDtoList).split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
			.forEach(str -> log.info("Send product result queue to product master, correlationId: {}, message :{}", correlationId, str));
		this.rabbitTemplate.convertAndSend(rabbitMqProductExchange, rabbitMqProductMasterProductResultRoutingKey, productMasterReturnDtoList, this.productMasterMessagePostProcessor, correlationData);
	}

	public void sendProductUpdateToHybris(HybrisUpdateProductMainMqMessage hybrisMqMessage, String correlationId) {
		log.info("Send update product queue to hybris, correlationId: {}, message :{}" , correlationId, gson.toJson(hybrisMqMessage));
		this.rabbitTemplate.convertAndSend(rabbitMqProductExchange, rabbitMqHybrisProductUpdateRoutingKey, hybrisMqMessage);
	}
}
