package com.shoalter.mms_product_api.helper;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

@Slf4j
@RequiredArgsConstructor
@Component
public class HttpRequestHelper {

	private final Gson gson;
	private final RestTemplate restTemplate;

	/**
	 * Generalized method to make http request and return response body.
	 *
	 * @return result as type R or null if there is error, response is null, status code is not 200, or response does not have body.
	 */
	public <T, R> R requestForBody(HttpRequestDto<T, R> request) {
		long startTime = System.currentTimeMillis();
		HttpEntity<T> entity = generateEntity(request, request.customHeaders());

		String logMessage = String.format("Call %s Api [%s]%s: callBy: %s", request.serviceName(), request.method().name(),
			request.url(), request.user() != null ? request.user().getUserCode() : SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemCode());
		logRequest(logMessage, request.identifier(), request.body());

		ResponseEntity<R> response = null;
		try {
			response = request.resultClass() == null
				? restTemplate.exchange(request.url(), request.method(), entity, request.resultTypeReference())
				: restTemplate.exchange(request.url(), request.method(), entity, request.resultClass());
		} catch (RestClientException e) {
			log.error(e.getMessage(), e);
		}

		R result = null;
		if (response != null && (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) && response.hasBody()) {
			if (response.hasBody()) {
				Arrays.stream(gson.toJson(response.getBody()).split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
					.forEach(str -> log.info("{} Success. response body: {}", logMessage, str));
			} else {
				log.info("{} Success.", logMessage);
			}
			result = response.getBody();
		} else if (response != null) {
			log.info("{} Fail. ErrorCode :{}, HTTP Status: {}", logMessage, request.systemErrorCode(), response.getStatusCodeValue());
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by {} : {} milliseconds", logMessage, endTime - startTime);
		logRequestForAlert(request, endTime - startTime);

		return result;
	}

	public <T, R> R requestForBodyIncludeThrowHttpException(HttpRequestDto<T, R> request) {
		long startTime = System.currentTimeMillis();
		HttpEntity<T> entity = generateEntity(request, request.customHeaders());

		String logMessage = String.format("Call %s Api [%s]%s: callBy: %s", request.serviceName(), request.method().name(),
			request.url(), request.user() != null ? request.user().getUserCode() : SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemCode());
		logRequest(logMessage, request.identifier(), request.body());

		ResponseEntity<R> response = null;
		try {
			response = request.resultClass() == null
				? restTemplate.exchange(request.url(), request.method(), entity, request.resultTypeReference())
				: restTemplate.exchange(request.url(), request.method(), entity, request.resultClass());
		} catch (RestClientException e) {
			log.error(e.getMessage(), e);
			throw e;
		}

		R result = null;
		if (response != null && (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) && response.hasBody()) {
			if (response.hasBody()) {
				Arrays.stream(gson.toJson(response.getBody()).split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
					.forEach(str -> log.info("{} Success. response body: {}", logMessage, str));
			} else {
				log.info("{} Success.", logMessage);
			}
			result = response.getBody();
		} else if (response != null) {
			log.info("{} Fail. ErrorCode :{}, HTTP Status: {}", logMessage, request.systemErrorCode(), response.getStatusCodeValue());
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by {} : {} milliseconds", logMessage, endTime - startTime);
		logRequestForAlert(request, endTime - startTime);

		return result;
	}

	public <T> HttpStatus requestForStatus(HttpRequestDto<T, Void> request) {
		long startTime = System.currentTimeMillis();
		HttpEntity<T> entity = generateEntity(request, request.customHeaders());

		String logMessage = String.format("Call %s Api [%s]%s: callBy: %s", request.serviceName(), request.method().name(),
			request.url(), request.user().getUserCode());
		logRequest(logMessage, request.identifier(), request.body());

		ResponseEntity<String> response = null;
		try {
			response = restTemplate.exchange(request.url(), request.method(), entity, String.class);
		} catch (RestClientException e) {
			log.error(e.getMessage(), e);
		}

		HttpStatus status = null;
		if (response != null) {
			status = response.getStatusCode();
			if (response.hasBody()) {
				log.info("{} Success. response body: {}", logMessage, response.getBody());
			}
			if (response.getStatusCode() != HttpStatus.OK) {
				log.info("{} Fail. ErrorCode :{}, HTTP Status: {}", logMessage, request.systemErrorCode(), response.getStatusCodeValue());
			}
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by {} : {} milliseconds", logMessage, endTime - startTime);
		logRequestForAlert(request, endTime - startTime);

		return status;
	}

	public <T, R> R requestTemplate(HttpRequestDto<T, R> request) throws RestClientException {
		long startTime = System.currentTimeMillis();
		HttpEntity<T> entity = generateEntity(request, request.customHeaders());
		ResponseEntity<R> response = null;

		response = request.resultClass() == null
			? restTemplate.exchange(request.url(), request.method(), entity, request.resultTypeReference())
			: restTemplate.exchange(request.url(), request.method(), entity, request.resultClass());

		R result = null;
		if (response != null && (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) && response.hasBody()) {
			result = response.getBody();
			log.info("Call Api Success. identifier: {}, ResponseBody: {}", request.identifier(), gson.toJson(result));
		} else if (response != null) {
			log.info("Call Api Fail. ErrorCode :{}, HTTP Status: {}, identifier: {}", request.systemErrorCode(), response.getStatusCodeValue(), request.identifier());
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by call api {} {} {} : {} milliseconds", request.method().name(), request.url(), request.serviceName(), endTime - startTime);
		logRequestForAlert(request, endTime - startTime);

		return result;
	}

	private static <T, R> HttpEntity<T> generateEntity(HttpRequestDto<T, R> request, HttpHeaders headers) {
		return request.body() == null
			? new HttpEntity<>(headers)
			: new HttpEntity<>(request.body(), headers);
	}

	private <T> void logRequest(String logMessage, String identifier, T requestBody) {
		log.info(logMessage + " Request, identifier: {}", identifier);

		if (requestBody != null) {
			Arrays.stream(gson.toJson(requestBody).split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
				.forEach(str -> log.info(logMessage + ", request body :{}", str));
		}
	}

	private <T, R> void logRequestForAlert(HttpRequestDto<T, R> request, long millisecond) {
		if (!HybrisHelper.SERVICE_NAME.equals(request.serviceName())) {
			return;
		}

		LogData logData = LogData.builder()
			.targetSystem(request.serviceName())
			.url(String.format("[%s]%s", request.method().name(), request.url()))
			.apiResponseTime(millisecond)
			.message(String.format("Time taken by Call %s Api [%s]%s: %d millisecond", request.serviceName(), request.method().name(),
				request.url(), millisecond))
			.log("alert log for NOC Monitoring")
			.build();
		log.info("{}{} ", StringUtils.LF, gson.toJson(logData));
	}

	@Data
	@Builder
	private static class LogData {
		@SerializedName("target_system")
		private String targetSystem;
		private String url;
		private String log;
		private String message;
		@SerializedName("api_response_time")
		private Long apiResponseTime;
	}
}
