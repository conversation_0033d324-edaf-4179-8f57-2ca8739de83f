package com.shoalter.mms_product_api.controller;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.BrandIdMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.BrandMainRequestData;
import com.shoalter.mms_product_api.service.product.FindBrandService;
import com.shoalter.mms_product_api.service.product.pojo.BrandNameMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.response.BrandDto;
import com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Brand")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("/brand")
public class BrandController {

	private final FindBrandService findBrandService;

	@GetMapping("/{brandId}")
	public ResponseDto<FindBrandDto> findBrandById(@PathVariable(required = true) Integer brandId) {

		return findBrandService.start(brandId);
	}

	@PostMapping("/page")
	public ResponseDto<Page<BrandDto>> findBrandPage(@Validated @RequestBody BrandMainRequestData request) {
		return findBrandService.start(request);
	}

	@PostMapping("/brandNames")
	public ResponseDto<List<BrandDto>> findBrandByNames(@Validated @RequestBody BrandNameMainRequestData request) {
		return findBrandService.start(request);
	}

	@PostMapping("/brandIds")
	public ResponseDto<List<BrandDto>> findBrandByIds(@Validated @RequestBody BrandIdMainRequestData request) {
		return findBrandService.start(request);
	}
}
