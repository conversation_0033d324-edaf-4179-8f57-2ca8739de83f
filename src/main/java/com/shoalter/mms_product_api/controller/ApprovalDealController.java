package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.service.approval_deal.ApprovalDealAuditService;
import com.shoalter.mms_product_api.service.approval_deal.ApprovalDealCancelService;
import com.shoalter.mms_product_api.service.approval_deal.ExportApprovalDealReportService;
import com.shoalter.mms_product_api.service.approval_deal.FindApprovalDealDetailService;
import com.shoalter.mms_product_api.service.approval_deal.FindApprovalDealHistoryService;
import com.shoalter.mms_product_api.service.approval_deal.FindApprovalDealListService;
import com.shoalter.mms_product_api.service.approval_deal.FindSkuCommissionRateService;
import com.shoalter.mms_product_api.service.approval_deal.FindWaitingApprovalDealService;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealDetailMainResponseData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealHistoryMainResponseData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ExportApprovalDealMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.SkuCommissionRateMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.SkuCommissionRateMainResponseData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.WaitingApprovalDealMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.request.CancelApprovalDealRequestData;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "ApprovalDeal")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("/approvalDeal")
public class ApprovalDealController {

	private final Gson gson;

	private final ApprovalDealAuditService approvalDealAuditService;

	@Operation(summary = "Approve commission rate change requests", description = "status 1代表成功-1代表失敗")
	@PostMapping("/{id}/approve")
	public ResponseDto<Void> approve(
		@AuthenticationPrincipal UserDto userDto,
		@PathVariable("id") Long id) {
		return approvalDealAuditService.start(userDto, id, ApprovalDealStatusEnum.APPROVED);
	}

	@Operation(summary = "Reject commission rate change requests", description = "status 1代表成功-1代表失敗")
	@PostMapping("/{id}/reject")
	public ResponseDto<Void> reject(
		@AuthenticationPrincipal UserDto userDto,
		@PathVariable("id") Long id) {
		return approvalDealAuditService.start(userDto, id, ApprovalDealStatusEnum.REJECTED);
	}

	private final ApprovalDealCancelService approvalDealCancelService;

	@Operation(summary = "Cancel deal by store", description = "status 1代表成功-1代表失敗")
	@PostMapping("/cancel")
	public ResponseDto<Void> cancel(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody CancelApprovalDealRequestData cancelDealMainRequest) {
		return approvalDealCancelService.start(userDto, cancelDealMainRequest);
	}

	private final FindApprovalDealHistoryService findApprovalDealHistoryService;

	@Operation(summary = "Query Approval Deal History by Approval ID - Status Change History", description = "status 1代表成功-1代表失敗")
	@GetMapping("/{id}/history")
	public ResponseDto<ApprovalDealHistoryMainResponseData> findApprovalDealHistory(
		@AuthenticationPrincipal UserDto userDto, @PathVariable Long id) {
		return findApprovalDealHistoryService.start(userDto, id);
	}

	private final FindApprovalDealListService findApprovalDealListService;

	@Operation(summary = "Query Approval Deal list with filter API", description = "status 1代表成功-1代表失敗")
	@PostMapping("/approvalDeals")
	public ResponseDto<PageDto<ApprovalDealListDto>> findApprovalDealList(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody ApprovalDealListMainRequestData requestData) {
		return findApprovalDealListService.start(userDto, requestData);
	}

	private final FindApprovalDealDetailService findApprovalDealDetailService;

	@Operation(summary = "Query Approval上的SKU History Detail + Approval Detail", description = "status 1代表成功-1代表失敗")
	@GetMapping("/{id}/detail")
	public ResponseDto<ApprovalDealDetailMainResponseData<?>> findApprovalDealDetail(
		@AuthenticationPrincipal UserDto userDto, @PathVariable Long id) {
		return findApprovalDealDetailService.start(userDto, id);
	}

	private final FindSkuCommissionRateService findSkuCommissionRateService;

	@Operation(summary = "Query Single SKU commission rate", description = "status 1代表成功-1代表失敗")
	@PostMapping("/getSkuCommissionRate")
	public ResponseDto<SkuCommissionRateMainResponseData> findSkuCommissionRate(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody SkuCommissionRateMainRequestData requestData) {
		return findSkuCommissionRateService.start(userDto, requestData);
	}

	private final FindWaitingApprovalDealService findWaitingApprovalDealService;

	@Operation(summary = "Query waiting approval info", description = "status 1代表成功-1代表失敗")
	@PostMapping("/getWaitingApproval")
	public ResponseDto<?> findWaitingApprovalDeal(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody WaitingApprovalDealMainRequestData requestData) {
		return findWaitingApprovalDealService.start(requestData);
	}

	private final ExportApprovalDealReportService exportApprovalDealReportService;

	@Operation(summary = "Export Approval Deal Report", description = "status 1代表成功-1代表失敗")
	@PostMapping("/export")
	public HttpEntity<ByteArrayResource> exportApprovalDealReport(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody ExportApprovalDealMainRequestData requestData) {
		return exportApprovalDealReportService.start(userDto, requestData);
	}
}
