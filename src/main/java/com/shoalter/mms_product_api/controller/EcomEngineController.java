package com.shoalter.mms_product_api.controller;


import static com.shoalter.mms_product_api.config.type.ConstantType.ECOM_ENGINE_PRODUCT_SERVICE;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.BatchEditInvisibleFlagService;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleRequestDto;
import java.util.List;
import java.util.Locale;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/s2s/ecom-engine")
public class EcomEngineController {

	private final MessageSource messageSource;
	private final BatchEditInvisibleFlagService batchEditInvisibleFlagService;

	@PatchMapping("/invisible")
	public ResponseDto<Void> editInvisible(
			@RequestHeader(value = "from-system") String fromSystem,
			@RequestBody @Valid EditInvisibleRequestDto request) {

		if (!ECOM_ENGINE_PRODUCT_SERVICE.equals(fromSystem)) {
			return ResponseDto.fail(
					List.of(messageSource.getMessage("message368", new String[]{fromSystem},
							Locale.getDefault())));
		}

		batchEditInvisibleFlagService.start(request);

		return ResponseDto.success();
	}
}
