package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.little_mall_product.LittleMallFineRelationSettingService;
import com.shoalter.mms_product_api.service.little_mall_product.LittleMallSingleEditService;
import com.shoalter.mms_product_api.service.little_mall_product.LittleMallSingleSaveService;
import com.shoalter.mms_product_api.service.little_mall_product.LittleMallSwitchPrimarySkuService;
import com.shoalter.mms_product_api.service.little_mall_product.LittleMallVariantSaveService;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.request.LittleMallFindRelationSettingMainRequestData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.LittleMallFindRelationSettingMainResponseData;
import com.shoalter.mms_product_api.service.product.CreateLittleMallProductTemplateService;
import com.shoalter.mms_product_api.service.product.DownloadLittleMallAllProductsService;
import com.shoalter.mms_product_api.service.product.DownloadLittleMallPartialProductsService;
import com.shoalter.mms_product_api.service.product.LittleMallBatchCheckRelationService;
import com.shoalter.mms_product_api.service.product.LittleMallBatchEditService;
import com.shoalter.mms_product_api.service.product.LittleMallBatchSaveService;
import com.shoalter.mms_product_api.service.product.LittleMallCheckExistProductIdsService;
import com.shoalter.mms_product_api.service.product.LittleMallImportProductsService;
import com.shoalter.mms_product_api.service.product.UpdateLittleMallRecordStatusService;
import com.shoalter.mms_product_api.service.product.*;
import com.shoalter.mms_product_api.service.product.CreateLittleMallProductTemplateService;
import com.shoalter.mms_product_api.service.product.DownloadLittleMallAllProductsService;
import com.shoalter.mms_product_api.service.product.DownloadLittleMallPartialProductsService;
import com.shoalter.mms_product_api.service.product.LittleMallBatchEditService;
import com.shoalter.mms_product_api.service.product.LittleMallBatchSaveService;
import com.shoalter.mms_product_api.service.product.LittleMallImportProductsService;
import com.shoalter.mms_product_api.service.product.UpdateLittleMallRecordStatusService;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallExistProductIdRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchProductImportRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallExistProductIdRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.BatchLittleMallProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordStatusRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.BatchLittleMallProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallBatchCheckRelationResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.DownloadLittleMallAllProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.DownloadLittleMallPartialProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallSwitchPrimarySkuRequestData;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallVariantSkuRequestData;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallBatchCheckRelationResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Little_Mall")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("little-mall")
public class LittleMallController {
    private final Gson gson;

    private final LittleMallBatchSaveService littleMallBatchSaveService;

    @Operation(summary = "批次多筆新增產品", description = "status 1代表成功-1代表失敗, source: SHOPLINE, EXCEL, LITTLE_MALL(default)")
    @PostMapping("/products/batch/create")
    public ResponseDto<Long> batchSave(@AuthenticationPrincipal UserDto userDto,
                                         @RequestBody BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
        return littleMallBatchSaveService.start(userDto, batchLittleMallProductRequestDto);
    }

	private final LittleMallImportProductsService littleMallImportProductsService;

	@Operation(summary = "從bu導入產品", description = "status 1代表成功-1代表失敗,")
	@PostMapping("/products/productImport")
	public ResponseDto<Void> importProducts(@AuthenticationPrincipal UserDto userDto,
											@RequestBody BatchProductImportRequestDto batchProductImportRequestDto) {
		return littleMallImportProductsService.start(userDto, batchProductImportRequestDto);
	}

	private final LittleMallSingleSaveService littleMallSingleSaveService;

	@Operation(summary = "單筆新增產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products")
	public ResponseDto<ProductRecordResponseDto> singleSave(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody SingleEditProductDto singleEditProductDto) {
		return littleMallSingleSaveService.start(userDto, singleEditProductDto);
	}

	private final LittleMallSingleEditService littleMallSingleEditService;

	@Operation(summary = "單筆更新產品", description = "status 1代表成功-1代表失敗")
	@PutMapping("/products")
	public ResponseDto<ProductRecordResponseDto> singleEdit(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody SingleEditProductDto singleEditProductDto) {
		return littleMallSingleEditService.start(userDto, singleEditProductDto);
	}

    private final CreateLittleMallProductTemplateService createLittleMallProductTemplateService;

	// currently useless
    @Operation(summary = "下載批次新增產品模板", description = "status -1代表失敗")
    @PostMapping("/createProductTemplate/{storefrontStoreCode}")
    public HttpEntity<ByteArrayResource> downloadCreateProductTemplate(
            @AuthenticationPrincipal UserDto userDto, @PathVariable("storefrontStoreCode") String storefrontStoreCode) {
        return createLittleMallProductTemplateService.start(userDto, storefrontStoreCode);
    }

    private final UpdateLittleMallRecordStatusService updateLittleMallRecordStatusService;

	@Operation(summary = "update record status", description = "status 1代表成功-1代表失敗, source: SHOPLINE")
    @PatchMapping("/product_records/status")
    public ResponseDto<String> updateShoplineImportRecordStatus(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody ProductRecordStatusRequestDto requestDto) {
        return updateLittleMallRecordStatusService.start(userDto, requestDto);
    }

	private final DownloadLittleMallAllProductsService downloadLittleMallAllProductsService;

	@Operation(summary = "根據storeIds下載批次編輯產品Excel", description = "status -1代表失敗")
	@PostMapping("/excel/allProducts/export")
	public ResponseDto<Void> exportProductsByStoreIds(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody DownloadLittleMallAllProductsRequestDto requestDto) {
		return downloadLittleMallAllProductsService.start(userDto, requestDto);
	}

	private final DownloadLittleMallPartialProductsService downloadLittleMallPartialProductsService;

	@Operation(summary = "根據sku uuids下載批次編輯產品Excel", description = "status -1代表失敗")
	@PostMapping("/excel/partialProducts/export")
	public ResponseDto<Void> exportProductsBySkuUuids(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody DownloadLittleMallPartialProductsRequestDto requestDto) {
		return downloadLittleMallPartialProductsService.start(userDto, requestDto);
	}

	private final LittleMallBatchEditService littleMallBatchEditService;

	@Operation(summary = "批次多筆更新產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products/batch/edit")
	public ResponseDto<Void> batchEdit(@AuthenticationPrincipal UserDto userDto,
									   @RequestBody BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return littleMallBatchEditService.start(userDto, batchLittleMallProductRequestDto, SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT);
	}

	private final LittleMallBatchCheckRelationService littleMallBatchCheckRelationService;

	@Operation(summary = "批次多筆新增檢查relation", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products/batch/check-relation")
	public ResponseDto<LittleMallBatchCheckRelationResponseDto> batchCheckRelation(@AuthenticationPrincipal UserDto userDto,
																				   @RequestBody BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		return littleMallBatchCheckRelationService.start(userDto, batchLittleMallProductRequestDto);
	}

	private final LittleMallVariantSaveService littleMallVariantSaveService;
	@Operation(summary = "新增Variant產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products/variantSku")
	public ResponseDto<ProductRecordResponseDto> variantSave(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody LittleMallVariantSkuRequestData littleMallVariantSkuRequestData) {
		return littleMallVariantSaveService.start(userDto, littleMallVariantSkuRequestData);
	}

	private final LittleMallSwitchPrimarySkuService littleMallSwitchPrimarySkuService;

	@Operation(summary = "Switching Y/N", description = "status 1代表成功-1代表失敗")
	@PostMapping("/switch/isPrimarySku")
	public ResponseDto<Void> switchPrimarySku(@AuthenticationPrincipal UserDto userDto,
											  @RequestBody LittleMallSwitchPrimarySkuRequestData littleMallSwitchPrimarySkuRequestData) {
		return littleMallSwitchPrimarySkuService.start(userDto, littleMallSwitchPrimarySkuRequestData);
	}
    private final LittleMallFineRelationSettingService littleMallFineRelationSettingService;

    @Operation(summary = "搜尋該store的product relation setting", description = "status 1代表成功 -1代表失敗")
    @PostMapping("/store/product/relationSetting")
    public ResponseDto<LittleMallFindRelationSettingMainResponseData> findRelationSetting(
        @AuthenticationPrincipal UserDto userDto,
        @RequestBody LittleMallFindRelationSettingMainRequestData littleMallFindRelationSettingMainRequestData
    ) {
        return littleMallFineRelationSettingService.start(userDto, littleMallFindRelationSettingMainRequestData);
    }

	private final LittleMallCheckExistProductIdsService littleMallCheckExistProductIdsService;

	@Operation(summary = "檢查store底下的product ids是否存在", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products/checkExistProductIds")
	public ResponseDto<List<String>> checkExistProductIds(@AuthenticationPrincipal UserDto userDto,
											   @RequestBody LittleMallExistProductIdRequestDto requestDto) {
		return littleMallCheckExistProductIdsService.start(userDto, requestDto);
	}
}
