package com.shoalter.mms_product_api.controller;

import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.TranslateProductService;
import com.shoalter.mms_product_api.service.product.pojo.BatchTranslateRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.security.core.annotation.AuthenticationPrincipal;

import java.util.List;

@Tag(name = "產品翻譯")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
public class ProductTranslateController {

    private final TranslateProductService translateProductService;

	@Operation(summary = "批次翻譯產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/translate")
	public ResponseDto<Void> batchTranslate(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchTranslateRequestDto requestDto) {
		List<String> errorMessageList = translateProductService.translate(userDto, requestDto);

		return ResponseDto.<Void>builder()
			.status(errorMessageList.isEmpty() ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode())
			.errorMessageList(errorMessageList)
			.build();
	}
}
