package com.shoalter.mms_product_api.controller;

import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.properties.TmallProperties;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.hybris.SyncHybrisService;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.partner_product_price.PartnerProductPriceService;
import com.shoalter.mms_product_api.service.price_alert.PriceAlertService;
import com.shoalter.mms_product_api.service.price_alert.PriceAlertsProcessDto;
import com.shoalter.mms_product_api.service.price_alert.ProductPriceMonitorCheckService;
import com.shoalter.mms_product_api.service.price_alert.ProductPriceMonitorService;
import com.shoalter.mms_product_api.service.product.BatchEditPriceExchangeRateService;
import com.shoalter.mms_product_api.service.product.CreateTmallVariantOptionValueService;
import com.shoalter.mms_product_api.service.product.DeprecatedProductImageService;
import com.shoalter.mms_product_api.service.product.TmallVariantOptionValueHelper;
import com.shoalter.mms_product_api.service.product.pojo.DeprecatedProductImageRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@Tag(name = "Internal Use Controller")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/s2s")
public class InternalController {

	private final DeprecatedProductImageService deprecatedProductImageService;
	private final BatchEditPriceExchangeRateService batchEditPriceExchangeRateService;
	private final PriceAlertService priceAlertService;
	private final ProductPriceMonitorService productPriceMonitorService;
	private final ProductPriceMonitorCheckService productPriceMonitorCheckService;
	private final TmallVariantOptionValueHelper tmallVariantOptionValueHelper;
	private final CreateTmallVariantOptionValueService createTmallVariantOptionValueService;

	private final TokenHelper tokenHelper;

	private final TmallProperties tmallProperties;

	@Operation(summary = "change product url for deprecated products")
	@PostMapping(path = "/deprecatedProduct/images")
	public ResponseDto<Void> updateDeprecatedProductImages(
		@RequestBody DeprecatedProductImageRequestDto requestDto) {
		return deprecatedProductImageService.start(requestDto);
	}

	private final SyncHybrisService syncHybrisService;

	@Operation(summary = "同步mainland contract的sku給hybris", description = "")
	@PostMapping("/temp/sync/mainland-contract-sku/to/hybris")
	public ResponseDto<Void> syncMainlandContractSkuToHybris() {
		return syncHybrisService.syncMainlandContractSkuToHybris();
	}

	@Operation(summary = "check rate exchange, batch edit currency rmb sku price to productMaster and hybris")
	@PostMapping("/batch/edit/currency/rmb/sku/price")
	public ResponseDto<Void> batchEditCurrencyRmbSkuPrice() {
		return batchEditPriceExchangeRateService.batchEditCurrencyRmbSkuPrice(
			UserDto.generateSystemUserDto());
	}

	@PostMapping("/cronjob/price-alert")
	public ResponseDto<Void> processPriceAlerts() {
		log.info("[cron-job][price-alert] start, time:{}", LocalDateTime.now());
		UserDto systemUser = tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM);

		// Step1. Process price alert checking
		PriceAlertsProcessDto processDto = priceAlertService.processPriceAlerts(systemUser);

		if (processDto.getProcessStatus() == PriceAlertsProcessDto.ProcessStatus.PRICE_CHECKED) {
			// Step2. Offline price alert product
			processDto = priceAlertService.offlinePriceAlertProduct(processDto.getCronJobUuid(),
				systemUser);

			// Step3. Notify user
			if (processDto.getProcessStatus() == PriceAlertsProcessDto.ProcessStatus.OFFLINE) {
				processDto = priceAlertService.notifyUser(processDto.getCronJobUuid());
				log.info("[cron-job][price-alert] notified user, processStatus:{}, time:{}",
					processDto.getProcessStatus(), LocalDateTime.now());
			}
		}

		if (PriceAlertsProcessDto.ProcessStatus.NOTIFY == processDto.getProcessStatus() ||
			PriceAlertsProcessDto.ProcessStatus.NO_RECORD == processDto.getProcessStatus()) {
			log.info("[cron-job][price-alert] finish, processStatus:{}, time:{}",
				processDto.getProcessStatus(), LocalDateTime.now());
			return ResponseDto.success(null);
		} else {
			log.error("[cron-job][price-alert] cron job failed, processStatus:{}, errorMessageList:{}",
				processDto.getProcessStatus(), processDto.getErrorMessageList());
			return ResponseDto.fail(null);
		}
	}

	@PostMapping("/cronjob/price-alert/offline-check")
	public ResponseDto<Void> priceAlertOfflineCheckProduct() {
		log.info("[cron-job][price-alert-offline-check] start, time:{}", LocalDateTime.now());
		UserDto systemUser = tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM);
		PriceAlertsProcessDto processDto = priceAlertService.offlineChecking(systemUser);

		if (PriceAlertsProcessDto.ProcessStatus.OFFLINE_CHECKED == processDto.getProcessStatus() ||
			PriceAlertsProcessDto.ProcessStatus.NO_RECORD == processDto.getProcessStatus()) {
			log.info("[cron-job][price-alert-offline-check] finish, processStatus:{}, time:{}",
				processDto.getProcessStatus(), LocalDateTime.now());
			return ResponseDto.success(null);
		} else {
			log.error(
				"[cron-job][price-alert-offline-check] failed, processStatus:{}, errorMessageList:{}",
				processDto.getProcessStatus(), processDto.getErrorMessageList());
			return ResponseDto.fail(null);
		}
	}

	@PostMapping("/cronjob/price-monitor/update-price")
	public ResponseDto<Void> priceMonitorUpdatePrice() {
		log.info("[cron-job][price-monitor/update-price] start, time:{}", LocalDateTime.now());
		productPriceMonitorService.updateMonitorProductPrice();
		return ResponseDto.success(null);
	}

	@PostMapping("/cronjob/price-monitor/check-price")
	public ResponseDto<Void> priceMonitorCheckPrice() {
		log.info("[cron-job][price-monitor/check-price] start, time:{}", LocalDateTime.now());
		productPriceMonitorCheckService.monitorProductPrice();
		return ResponseDto.success(null);
	}

	@PostMapping("/cronjob/price-monitor/check-offline")
	public ResponseDto<Void> priceMonitorCheckOffline() {
		log.info("[cron-job][price-monitor/check-offline] start, time:{}", LocalDateTime.now());
		productPriceMonitorCheckService.offlineChecking();
		return ResponseDto.success(null);
	}

	@PostMapping("/cronjob/variant-option-value/create")
	public ResponseDto<Void> variantOptionValueCreate() {
		log.info("[variant-option-value/create] start, time:{}", LocalDateTime.now());

		int remainingProcessingVariantQuota = tmallVariantOptionValueHelper.getRemainingProcessingVariantQuota();
		int processingVariantLimit = tmallProperties.getCronjob().getProcessingVariantLimit();

		if (remainingProcessingVariantQuota == 0) {
			log.info("Cronjob skipped: processing variant limit reached. limit = {}, remaining = {}",
				processingVariantLimit, remainingProcessingVariantQuota);
			return ResponseDto.success(null);
		}

		List<Integer> historyIdsForProcessing = tmallVariantOptionValueHelper.getTmallExportHistoryIdForProcessing();

		if (historyIdsForProcessing.isEmpty()) {
			log.info("Cronjob skipped: no PENDING records found.");
			return ResponseDto.success(null);
		}

		log.info("Found {} export record(s) for processing.", historyIdsForProcessing);
		tmallVariantOptionValueHelper.markTmallExportHistoryAsProcessingByIds(
			historyIdsForProcessing);

		historyIdsForProcessing.forEach(createTmallVariantOptionValueService::start);

		log.info("[variant-option-value/create] end, time:{}", LocalDateTime.now());

		return ResponseDto.success(null);
	}

	private final PartnerProductPriceService partnerProductPriceService;

	@PostMapping("/cronjob/partner-product-price/save-record")
	public ResponseDto<Void> partnerProductPriceSaveRecord() {
		log.info("[cron-job][partner-product-price/save-record] start, time:{}", LocalDateTime.now());
		partnerProductPriceService.savePartnerProductPriceRecord();
		log.info("[cron-job][partner-product-price/save-record] finish, time:{}", LocalDateTime.now());
		return ResponseDto.success(null);
	}

	@PostMapping("/cronjob/partner-product-price/daily-report")
	public ResponseDto<Void> partnerProductPriceChangeDailyReport() {
		log.info("[cron-job][partner-product-price/daily-report] start, time:{}", LocalDateTime.now());
		partnerProductPriceService.sendMailForPartnerProductPriceChangeDailyReport();
		log.info("[cron-job][partner-product-price/daily-report] finish, time:{}", LocalDateTime.now());
		return ResponseDto.success(null);
	}
}



