package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.mms_product.BatchEditAllService;
import com.shoalter.mms_product_api.service.mms_product.BatchExportAndSaveAllService;
import com.shoalter.mms_product_api.service.mms_product.BatchSaveAllService;
import com.shoalter.mms_product_api.service.mms_product.pojo.BatchExportAndSaveAllMainRequestData;
import com.shoalter.mms_product_api.service.mms_product.pojo.MmsBatchSaveProductDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "舊版過渡期產品API")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
public class MmsProductController {

	private final Gson gson;

	private final BatchSaveAllService batchSaveAllService;

	@Operation(summary = "多筆新增hktv產品，給舊版mms & mms2.0使用", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/saveAll")
	public ResponseDto<Void> batchSaveAll(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody MmsBatchSaveProductDto products) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchSaveAllService.start(userDto, products, clientIp);
	}

	private final BatchEditAllService batchEditAllService;

	@Operation(summary = "多筆更新hktv產品，給舊版mms & mms2.0使用", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editAll")
	public ResponseDto<Void> batchEditAll(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody MmsBatchSaveProductDto products) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditAllService.start(userDto, products, clientIp);
	}

	private final BatchExportAndSaveAllService batchExportAndSaveAllService;

	@Operation(summary = "auto create sku from tmall", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/{historyId}/exportAndSaveAll")
	public ResponseDto<Void> batchExportAndSaveAll(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchExportAndSaveAllMainRequestData batchExportAndSaveAllMainRequestData,
		@PathVariable Integer historyId
	) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchExportAndSaveAllService.start(userDto, historyId, batchExportAndSaveAllMainRequestData, clientIp);
	}
}
