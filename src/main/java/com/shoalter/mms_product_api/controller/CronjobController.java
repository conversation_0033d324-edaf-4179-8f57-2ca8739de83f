package com.shoalter.mms_product_api.controller;

import com.shoalter.mms_product_api.service.approval_deal.ApprovalDealDailyReportService;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.partner_product_price.PartnerProductPriceService;
import com.shoalter.mms_product_api.service.product.HouseKeepingSaveProductRecordService;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@Hidden
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/cronjob")
public class CronjobController {

	private final ApprovalDealDailyReportService approvalDealDailyReportService;
	private final HouseKeepingSaveProductRecordService houseKeepingSaveProductRecordService;

	@PostMapping("/approval/daily-report")
	public ResponseDto<Void> sendMailForApprovalDealDailyReport() {
		log.info("sendMailForApprovalDealDailyReport start, time:{}", LocalDateTime.now());
		ResponseDto<Void> result = approvalDealDailyReportService.sendMailForApprovalDealDailyReport();
		log.info("sendMailForApprovalDealDailyReport finish, time:{}", LocalDateTime.now());
		return result;
	}

	@PostMapping("/house-keeping/save-product-record")
	public ResponseDto<Void> houseKeepingSaveProductRecord() {
		log.info("[cron-job][house-keeping/save-product-record] start, time:{}", LocalDateTime.now());
		ResponseDto<Void> result = houseKeepingSaveProductRecordService.houseKeepingSaveProductRecord();
		log.info("[cron-job][house-keeping/save-product-record] finish, result status:{}, errorMessageList:{} time:{}", result.getStatus(), result.getErrorMessageList(), LocalDateTime.now());
		return result;
	}

}
