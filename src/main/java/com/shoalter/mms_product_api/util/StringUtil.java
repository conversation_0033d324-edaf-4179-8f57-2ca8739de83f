package com.shoalter.mms_product_api.util;

import org.apache.commons.lang3.StringUtils;
import java.nio.charset.Charset;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

	public static final String PRODUCT_SEPARATOR = "_S_";
	public static final String HTML_SRC = "src";
	public static final String HTML_IMG = "img";
	public static final String HTML_P = "p";
	public static final String COMMA = ",";
	public static final String UNDERLINE = "_";
	public static final String PERIOD = ".";
	public static final String FILE_EXTENSION_ZIP = "zip";
	public static final String FILE_EXTENSION_EXCEL = "xlsx";
	public static final String COLOR_SEPERATOR = "    ";
	public static final String HYBRIS_ONLINE_DATE = "01/01/2099 00:00:00";


	public static final Pattern FOUR_BYTE_EMOJI_PATTERN = Pattern.compile("[\\x{10000}-\\x{10FFFF}]");
	public static final Pattern ALL_EMOJI_PATTERN = Pattern.compile("(\\u00a9|\\u00ae|[\\u2000-\\u3300]|\\ud83c[\\ud000-\\udfff]|\\ud83d[\\ud000-\\udfff]|\\ud83e[\\ud000-\\udfff]|[\\x{10000}-\\x{10FFFF}])");
	public static final Pattern EMAIL_PATTERN = Pattern.compile("^(.+)@(.+)$");
	public static final Pattern HTML_PATTERN = Pattern.compile(".*\\<[^>]+>.*", Pattern.DOTALL);
	public static final Pattern URL_PATTERN = Pattern.compile("^(https?://)[a-zA-Z0-9.-]+(:[0-9]+)?(/.*)?$");

    public static boolean isEmpty(String value) {
        return value == null || value.length() == 0;
    }

    public static boolean isNotEmpty(String value) {
        return !isEmpty(value);
    }

	public static boolean isNotEquals(String value1, String value2) {
		return !StringUtils.equals(value1, value2);
	}

    public static String cutStringToTextMaximum(String string)
    {
        final int textSize = 21845; // 65535/3  UTF-8 Chinese char is 3 bytes
        if (isNotEmpty(string))
        {
            if (string.length() >= textSize)
            {
                return string.substring(0, textSize);
            }
        }
        return string;
    }

    public static boolean isNullOrBlank(String value) {
        return value == null || value.isBlank();
    }

    public static boolean isNotNullOrBlank(String value) {
        return !isNullOrBlank(value);
    }

    public static int countBytes(String value, Charset charset) {
        return value.getBytes(charset).length;
    }

    public static String generateDefaultStringValue(String value) {
    	if (isEmpty(value)) {
    		return "";
		}
    	return value;
	}

	public static String toggleYN(String value) {
		if ("Y".equalsIgnoreCase(value)) {
			return "N";
		}
		if ("N".equalsIgnoreCase(value)) {
			return "Y";
		}
		return value;
	}

	public static String getValidationValue(String value) {
		return isEmpty(value) ? StringUtils.EMPTY : value;
	}

	public static String replaceEmojiWithDecimalNCRs(String content) {
		Matcher matcher = FOUR_BYTE_EMOJI_PATTERN.matcher(content);
		StringBuilder result = new StringBuilder();
		while (matcher.find()) {
			String match = matcher.group();
			StringBuilder decimalNCR = new StringBuilder();
			for (int i = 0; i < match.length(); i++) {
				int codePoint = Character.codePointAt(match, i);
				decimalNCR.append("&#").append(codePoint).append(";");
				i += Character.charCount(codePoint) - 1;
			}
			matcher.appendReplacement(result, decimalNCR.toString());
		}
		matcher.appendTail(result);
		return result.toString();
	}

	public static String generateErrorMessage(List<String> errorMessageList) {
		return String.join(", \n", errorMessageList);
	}

	/**
	 * Compares two strings while treating null and empty strings as equivalent.
	 *
	 * @param str1 first string to compare
	 * @param str2 second string to compare
	 * @return true if both strings are equal (including null/empty equivalence), false otherwise
	 */
	public static boolean equalsIgnoreNullWithEmpty(String str1, String str2) {
		// Convert null to empty string to simplify comparison
		String s1 = str1 == null ? "" : str1;
		String s2 = str2 == null ? "" : str2;

		// Use String.equals() for comparison
		return s1.equals(s2);
	}
}
