package com.shoalter.mms_product_api.util;

import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;

import java.util.Arrays;

public class ResourceUtil {

    private ResourceUtil(){}

    public static boolean existsImageDomain(String filePath) {
        return StringUtil.isNotEmpty(filePath) &&
                Arrays.stream(ProductImageHelper.imageDomain.split(",")).anyMatch(filePath::contains);
    }

    public static boolean needModifiedImageDomain(String filePath) {
        return StringUtil.isNotEmpty(filePath) &&
                Arrays.stream(ProductImageHelper.imageDomainModify.split(",")).anyMatch(filePath::contains);
    }
}
