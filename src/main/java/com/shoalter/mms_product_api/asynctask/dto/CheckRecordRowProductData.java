package com.shoalter.mms_product_api.asynctask.dto;

import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealTempDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckRecordRowProductData {

	private UserDto userDto;
	private Map<String, ProductMasterResultDto> productFromPmMap = new HashMap<>();
	private Map<String, ProductDo> productFromDbMap = new HashMap<>();
	private Map<Long, ApprovalDealTempDo> waitingApprovalTempRowIdMap = new HashMap<>();
	private Map<String, MembershipPricingEventSetDto> membershipPricingEventMap = new HashMap<>();
	private Map<String, Boolean> ckeckLittleMallVariantMap = new HashMap<>();
	private BigDecimal rmbRate;

}
