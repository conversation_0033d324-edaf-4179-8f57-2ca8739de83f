package com.shoalter.mms_product_api.asynctask;

import static com.shoalter.mms_product_api.util.StringUtil.UNDERLINE;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductFileConfig;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductImagesRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductImageViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductImportViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.mms_product.pojo.BatchProductAsyncDto;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.SyncBaseProductInfoHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchProductImportRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchProductImportWrapper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class CreateSaveProductRecordTask {

	private final ProductImagesRepository productImagesRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreRepository storeRepository;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final SyncBaseProductInfoHelper syncBaseProductInfoHelper;
	private final SaveProductHelper saveProductHelper;
	private final Gson gson;
	private final ApprovalDealHelper approvalDealHelper;
	private final ExchangeRateHelper exchangeRateHelper;

	private final Set<String> imageTypes = Set.of(ProductFileConfig.IMAGE_TYPE_MAIN, ProductFileConfig.IMAGE_TYPE_OTHER_A, ProductFileConfig.IMAGE_TYPE_OTHER_B);

	private static final int MAX_RECORD__ROW_COUNT = 5000;

	/**
	 * for product import from HKTV to Little Mall
	 */
	@Async("createRecordExecutor")
	public void createRecordAsync(BatchProductImportWrapper batchProductImportWrapper) {
		UserDto userDto = batchProductImportWrapper.getUserDto();
		BatchProductImportRequestDto request = batchProductImportWrapper.getBatchProductImportRequestDto();
		List<ProductImportViewDo> skuViewsDoList = batchProductImportWrapper.getProductImportViewDoList();

		log.info("start create record for batch create product from hktv store {} to the place store {}", request.getFromStoreCode(), request.getToStoreCode());

		//prepare data
		List<Integer> allSkuProductIds = skuViewsDoList.stream().map(ProductImportViewDo::getId).collect(Collectors.toList());
		Map<Integer, List<ProductImageViewDo>> productIdToImageViewDoMap =
			productImagesRepository.findByProductIdsAndImageTypes(allSkuProductIds, imageTypes).stream()
				.collect(Collectors.groupingBy(ProductImageViewDo::getProductId));

		int recordCount = skuViewsDoList.size() / MAX_RECORD__ROW_COUNT + 1;
		int currentRecordCount = 1;
		String initFileName = String.format("import_products_from_%s_to_%s.xlsx", request.getFromStoreCode(), request.getToStoreCode());

		BigDecimal rmbRate;
		// If HKTV store last contract is mainland contract, the price is RMB value, need to calculate to HKD price to LittleMall
		Optional<String> storeContractType = storeRepository.findStoreLatestContractTypeByBuAndStorefrontStoreCode(BuCodeEnum.HKTV.name(), request.getFromStoreCode());
		boolean isHktvMainlandContract;
		if (storeContractType.isPresent() && ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(storeContractType.get())) {
			rmbRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);
			isHktvMainlandContract = true;
		} else {
			isHktvMainlandContract = false;
			rmbRate = null;
		}
		//create record (5000 skus per record)
		for (List<ProductImportViewDo> partitionSkus : ListUtils.partition(skuViewsDoList, MAX_RECORD__ROW_COUNT)) {
			String generateFileName = recordCount > 1 ?
				new StringBuilder(initFileName).insert(initFileName.lastIndexOf("."), "(" + currentRecordCount++ + "/" + recordCount + ")").toString() :
				initFileName;

			SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto, request.getMerchantId(),
				SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV, generateFileName, SaveProductStatus.WAIT_START);

			List<SaveProductRecordRowDo> recordRowDos = partitionSkus.stream()
				.map(productImportViewDo -> {
					LittleMallBatchDto littleMallBatchDto = productPreProcessingHelper.preProcessingLittleMallBatchDto(
						batchProductImportWrapper, request.getMerchantId(),
						productIdToImageViewDoMap.get(productImportViewDo.getId()), productImportViewDo,
						rmbRate, isHktvMainlandContract);
					littleMallBatchDto.setProductReadyMethod(ProductReadyMethodType.DEFAULT_LITTLE_MALL_PRODUCT_READY_METHOD);
					SingleEditProductDto singleEditProductDto = LittleMallBatchDto.convertToSingleEditProductDto(
						littleMallBatchDto);
					return saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(),
						singleEditProductDto, SaveProductStatus.CHECKING_PRODUCT, null);
				}).collect(Collectors.toList());

			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(recordRowDos);
			saveProductRecordHelper.updateRecordStatusToChecking(saveProductRecordDo);
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), recordRowDos.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		}

		log.info("end create record for batch create product hktv store {} to the place store {}", request.getFromStoreCode(), request.getToStoreCode());
	}

	@Async("createRecordExecutor")
	public void checkApprovalDealAndPrimarySkuAndAddVariantProduct(UserDto userDto, BatchProductAsyncDto batchProductAsyncDto) {
		approvalDealHelper.batchProcessAndCheckApproval(userDto, batchProductAsyncDto, ApprovalDealTypeEnum.COMMISSION_RATE);
		checkPrimarySkuAndAddVariantProductWithSameProductCode(userDto, batchProductAsyncDto.getSaveProductRecordDo());
	}


	/**
	 * for batch create product and batch edit product
	 */
	@Async("createRecordExecutor")
	public void checkPrimarySkuAndAddVariantProductWithSameProductCodeAsync(UserDto userDto, SaveProductRecordDo saveProductRecordDo) {
		checkPrimarySkuAndAddVariantProductWithSameProductCode(userDto, saveProductRecordDo);
	}

	private void checkPrimarySkuAndAddVariantProductWithSameProductCode(UserDto userDto, SaveProductRecordDo saveProductRecordDo) {
		long startTime = System.currentTimeMillis();

		List<SaveProductRecordRowDo> successRecordRow = saveProductRecordRowRepository.findByRecordId(saveProductRecordDo.getId());
		Map<String, List<SingleEditProductDto>> sameProductCodeProduct = successRecordRow.stream()
			.filter(row -> row.getStatus() != SaveProductStatus.FAIL)
			.map(row -> gson.fromJson(row.getContent(), SingleEditProductDto.class))
			.collect(Collectors.groupingBy(product -> product.getProduct().getAdditional().getHktv().getStores() + UNDERLINE + product.getProduct().getProductId()));

		int totalSuccessCount = sameProductCodeProduct.values().stream()
			.mapToInt(List::size)
			.sum();

		log.info("start checkPrimarySkuAndAddVariantProductWithSameProductCodeAsync, record id: {}, uploadType: {}, productList size: {}",
			saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), totalSuccessCount);

		//handle variant product and primary sku checking
		for (List<SingleEditProductDto> entry : sameProductCodeProduct.values()) {
			List<ProductMasterDto> baseProducts = entry.stream().map(SingleEditProductDto::getProduct).collect(Collectors.toList());
			Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair = syncBaseProductInfoHelper.findVariantProductsFromProductMaster(userDto, baseProducts);
			boolean passCheck = syncBaseProductInfoHelper.variantProductCheckAndUpdateRecordRows(productMasterResultPair, baseProducts, saveProductRecordDo).getLeft();
			if (passCheck) {
				syncBaseProductInfoHelper.handlePrimarySkuAndAddVariantProduct(productMasterResultPair, baseProducts, saveProductRecordDo);
			}
		}
		// async check finish update status WAIT_START -> PROCESSING
		saveProductRecordDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordRepository.save(saveProductRecordDo);

		long endTime = System.currentTimeMillis();
		log.info("end checkPrimarySkuAndAddVariantProductWithSameProductCodeAsync, {} milliseconds", endTime - startTime);
	}


	/**
	 * for generate matrix product
	 */
	@Async("createRecordExecutor")
	public void generateMatrixProduct(UserDto userDto, SingleEditProductDto product, String clientIp) {
		Map<SaveProductRecordDo, SaveProductRecordRowDo> matrixMap = saveProductHelper.createMatrixProduct(userDto, product, clientIp);
		if (!matrixMap.isEmpty()) {
			saveProductHelper.sendMatrixProductToProductMaster(userDto, matrixMap);
		}
	}

	@Async("createRecordExecutor")
	public void createBatchCurrencyRmbSkuPriceRecord(UserDto userDto) {
		log.info("[batchEditCurrencyRmbSkuPrice] start create record for batch edit RMB sku price");
		// find all sku from contract type is MC or MC3 and RMB_SKU_WHITELIST
		Map<Integer, List<SingleEditProductDto>> merchantProductMap = exchangeRateHelper.findCurrencyRmbMerchantProductMap(userDto);
		// save record and recordRows
		Map<Integer, Set<Long>> merchantRecordMap = exchangeRateHelper.createBatchEditRateExchangePriceRecord(userDto, merchantProductMap);
		int totalCount = merchantRecordMap.values().stream()
			.mapToInt(Set::size)
			.sum();
		log.info("[batchEditCurrencyRmbSkuPrice] end create record for batch edit RMB sku price, merchantId with recordId: {}, total record count: {}", merchantRecordMap, totalCount);
	}
}
