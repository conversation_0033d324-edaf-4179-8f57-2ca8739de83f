package com.shoalter.mms_product_api.asynctask;

import com.shoalter.mms_product_api.asynctask.dto.CheckRecordRowProductData;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealTempDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.schedule.CheckTimeSchedule;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.PromotionHelper;
import com.shoalter.mms_product_api.service.product.helper.TaskExceptionHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckRecordProductTask {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final ProductRepository productRepository;
	private final CheckRecordRowProductTask checkProductRecordRowTask;

	private final ProductMasterHelper productMasterHelper;
	private final TaskExceptionHelper taskExceptionHelper;

	private final UserHelper userHelper;
	private final ApprovalDealHelper approvalDealHelper;
	private final PromotionHelper promotionHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final ExchangeRateHelper exchangeRateHelper;

	@Async("checkRecordProductTaskExecutor")
	public void startWithCheckRecordProductTaskExecutor(SaveProductRecordDo record) {
		start(record, false);
	}

	@Async("checkQueueProtocolRecordProductTaskExecutor")
	public void startWithCheckQueueProtocolRecordProductTaskExecutor(SaveProductRecordDo record) {
		start(record, true);
	}

	private void start(SaveProductRecordDo record, boolean isQueue) {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		String status = SaveProductStatusEnum.getProductStatusName(SaveProductStatus.CHECKING_PRODUCT);
		final int recordRowWorkCountLimit = 100;
		List<SaveProductRecordRowDo> waitCheckRowList = saveProductRecordRowRepository.findByRecordIdAndStatusAndLimit(record.getId(), SaveProductStatus.CHECKING_PRODUCT, recordRowWorkCountLimit);
		int totalCount = saveProductRecordRowRepository.countByRecordId(record.getId());
		int finishCheckingCount = saveProductRecordRowRepository.countByRecordIdAndStatus(record.getId(), Set.of(SaveProductStatus.FAIL, SaveProductStatus.REQUESTING_PM));
		log.info("record id: {}, save product type: {}, total row count:{}, finish checking count: {}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()), totalCount, finishCheckingCount);

		if (CollectionUtil.isNotEmpty(waitCheckRowList)) {
			log.info("Task info run checking record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", record.getId(),
				SaveProductTypeEnum.getProductTypeName(record.getUploadType()), waitCheckRowList.size(), record.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.CHECKING_PRODUCT));
			handleCheckingProductFlow(record, waitCheckRowList, isQueue);
		} else {
			if (totalCount == finishCheckingCount) {
				record.setStatus(SaveProductStatus.REQUESTING_PM);
				record.setCheckTime(null);
				saveProductRecordRepository.save(record);
				log.info("Task info run checking record id: {}, save product type: {}, CHECKING_PRODUCT row size empty, update record status REQUESTING_PM",
					record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()));
			} else {
				log.info("Task info run checking record id: {}, save product type: {}, CHECKING_PRODUCT row size empty, but not all record row finish checking, do not update record status",
					record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()));
			}
		}

		stopWatch.stop();
		log.info("total time for processing record id {} in {} status : {} millisecond", record.getId(), status, stopWatch.getTotalTimeMillis());
	}

	private void handleCheckingProductFlow(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitCheckRowList,boolean isQueue) {
		try {
			CheckTimeSchedule.addProductRecordId(record.getId());

			UserDto userDto = userHelper.generateUserDtoByRecord(record);
			List<String> skuUuid = waitCheckRowList.stream()
				.map(SaveProductRecordRowDo::getUuid)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());

			Map<String, ProductDo> productFromDbMap = CollectionUtil.isEmpty(skuUuid) ? Map.of() :
				productRepository.findByUuidIn(skuUuid).stream()
					.collect(Collectors.toMap(ProductDo::getUuid, Function.identity()));

			List<ProductMasterResultDto> productFromPmList = productMasterHelper.requestProductsByUuid(userDto, ProductMasterSearchRequestDto.builder().uuids(skuUuid).build());
			Map<String, ProductMasterResultDto> productFromPmMap = CollectionUtil.isEmpty(skuUuid) ? Map.of() :
				productFromPmList.stream().collect(Collectors.toMap(ProductMasterResultDto::getUuid, Function.identity()));

			Map<Long, ApprovalDealTempDo> waitingApprovalTempRowIdMap = new HashMap<>();
			if (SaveProductType.CHECKING_APPROVAL_TYPE_SET.contains(record.getUploadType())) {
				waitingApprovalTempRowIdMap = approvalDealHelper.findWaitingCreateApprovalTempByRecordRows(waitCheckRowList, ApprovalDealTypeEnum.COMMISSION_RATE);
			}

			Map<String, MembershipPricingEventSetDto> membershipPricingEventMap = new HashMap<>();
			if (SaveProductType.HKTV_PROMOTION_MEMBERSHIP_PRICING_TYPE_SET.contains(record.getUploadType())) {
				membershipPricingEventMap = CollectionUtil.isEmpty(skuUuid) ? Map.of() : requestMembershipPricingEventSet(userDto, productFromPmList);
			}

			Map<String, Boolean> ckeckLittleMallVariantMap = new HashMap<>();
			if (SaveProductType.CHECK_LITTLE_MALL_VARIANT_SET.contains(record.getUploadType())) {
				ckeckLittleMallVariantMap = checkLittleMallProductHelper.generateCheckVariantProductMap(waitCheckRowList);
			}

			BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PRODUCT, CurrencyEnum.RMB);

			CheckRecordRowProductData checkRowData = CheckRecordRowProductData.builder()
				.userDto(userDto)
				.productFromPmMap(productFromPmMap)
				.productFromDbMap(productFromDbMap)
				.waitingApprovalTempRowIdMap(waitingApprovalTempRowIdMap)
				.membershipPricingEventMap(membershipPricingEventMap)
				.ckeckLittleMallVariantMap(ckeckLittleMallVariantMap)
				.rmbRate(rmbRate)
				.build();

			CompletableFuture.allOf(waitCheckRowList.stream()
					.map(row -> isQueue ? checkProductRecordRowTask.startWithCheckQueueProtocolRecordRowProductTaskExecutor(record, row, checkRowData) :
						checkProductRecordRowTask.startWithCheckRecordRowProductTaskExecutor(record, row, checkRowData))
					.toArray(CompletableFuture[]::new))
				.join();

			record.setCheckTime(null);
			saveProductRecordRepository.save(record);
		} catch (Exception e) {
			taskExceptionHelper.start(record, waitCheckRowList, e);
		} finally {
			CheckTimeSchedule.removeProductRecordId(record.getId());
		}
	}

	private Map<String, MembershipPricingEventSetDto> requestMembershipPricingEventSet(UserDto userDto, List<ProductMasterResultDto> productFromPmList) {

		Map<String, String> storeSkuToUuidMap = productFromPmList.stream()
			.filter(productMasterResult -> productMasterResult.getAdditional().getHktv() != null &&
				productMasterResult.getAdditional().getHktv().getStoreSkuId() != null)
			.collect(Collectors.toMap(data -> data.getAdditional().getHktv().getStoreSkuId(), ProductMasterProductDto::getUuid, (x, y) -> x));

		if (storeSkuToUuidMap.isEmpty()) {
			return Map.of();
		}

		List<MembershipPricingEventSetDto> checkPricingResults = promotionHelper.checkMembershipPricingEventSet(userDto, storeSkuToUuidMap.keySet());

		if (checkPricingResults == null) {
			return Map.of();
		}

		Map<String, MembershipPricingEventSetDto> storeSkuIdToMembershipResultMap = checkPricingResults.stream()
			.collect(Collectors.toMap(MembershipPricingEventSetDto::getStoreSkuId, Function.identity()));

		Map<String, MembershipPricingEventSetDto> uuidToMembershipResultMap = new HashMap<>();
		storeSkuToUuidMap.forEach((storeSkuId, productUuid) -> {
			if (storeSkuIdToMembershipResultMap.containsKey(storeSkuId)) {
				uuidToMembershipResultMap.put(productUuid, storeSkuIdToMembershipResultMap.get(storeSkuId));
			} else {
				log.info("storeSkuId: {} result not found in promotion api", storeSkuId);
			}
		});

		return uuidToMembershipResultMap;
	}
}
