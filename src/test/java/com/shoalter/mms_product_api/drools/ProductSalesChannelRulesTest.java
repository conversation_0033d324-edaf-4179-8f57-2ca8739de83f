package com.shoalter.mms_product_api.drools;

import com.shoalter.mms_product_api.config.drools.DroolsApplicationConfig;
import com.shoalter.mms_product_api.config.product.SalesChannelEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductDto;
import com.shoalter.mms_product_api.service.product.pojo.hybris.HybrisSalesChannelRuleDto;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = DroolsApplicationConfig.class)
class ProductSalesChannelRulesTest {

	@Autowired
	private KieContainer kieContainer;

	private static final Map<String, Set<String>> TEST_RULE_MAP = Map.of(
		SysParmSegmentEnum.CTM_CATEGORY_EXCLUSION.name(), Set.of("AA31253"),
		SysParmSegmentEnum.WECHAT_PRODUCT_READY_METHOD_EX.name(), Set.of("WECHAT_PRD_EXCLUSION"),
		SysParmSegmentEnum.WECHAT_CATEGORY_EXCLUSION.name(), Set.of("AA95")
	);

	private static final HybrisSalesChannelRuleDto TEST_RULE_DTO = new HybrisSalesChannelRuleDto(TEST_RULE_MAP);
	@ParameterizedTest(name = "[{index}] [{3}] with result [{2}]")
	@CsvSource({
		"MO, Category ,true, region is MO and not hit exclusion rule",
		"UK, Category ,false, region is not MO and not hit exclusion rule",
		"MO, AA31253001 ,false, region is MO and hit exclusion rule",
		", Category ,false, region is null",
		"MO,,true, region is MO and category is null",
	})
	public void salesChannelRule_ctmRules_test(String region, String categoryCode, Boolean isSuccess, String testCondition) {
		SaveHybrisProductDto saveHybrisProductDto = new SaveHybrisProductDto();
		saveHybrisProductDto.setDeliverableRegionCodes(region == null ? null : List.of(region));
		saveHybrisProductDto.setProductHktvCatList(categoryCode);

		List<String> results = executeRules(saveHybrisProductDto);

		if (isSuccess) {
			Assertions.assertThat(results).contains(SalesChannelEnum.CTM.name());
		} else {
			Assertions.assertThat(results).doesNotContain(SalesChannelEnum.CTM.name());
		}
	}

	@ParameterizedTest(name = "[{index}] [{2}] with result [{1}]")
	@CsvSource({
		"UK, false, region is not MO",
		", false, region is null",
		"MO, true , region is MO",
	})
	public void salesChannelRule_mfoodRules_test(String region, Boolean isSuccess, String testCondition) {
		SaveHybrisProductDto saveHybrisProductDto = new SaveHybrisProductDto();
		saveHybrisProductDto.setDeliverableRegionCodes(region == null ? null : List.of(region));

		List<String> results = executeRules(saveHybrisProductDto);

		if (isSuccess) {
			Assertions.assertThat(results).contains(SalesChannelEnum.MFOOD.name());
		} else {
			Assertions.assertThat(results).doesNotContain(SalesChannelEnum.MFOOD.name());
		}
	}

	@ParameterizedTest(name = "[{index}] [{3}] with result [{2}]")
	@CsvSource({
		"PRDA, AA91001 ,true, not hit any rule",
		"WECHAT_PRD_EXCLUSION, AA92001 ,false, hit product ready method exclusion rule",
		"PRDA, AA95001 ,false, hit category exclusion rule",
		"WECHAT_PRD_EXCLUSION, AA95001 ,false, hit both product ready method and category exclusion rule",
		"PRDA,,true, category is null",
		", AA91001,false, region is null",
	})
	public void salesChannelRule_wechatRules_test(String productReadyMethod, String categoryCode, Boolean isSuccess, String testCondition) {
		SaveHybrisProductDto saveHybrisProductDto = new SaveHybrisProductDto();
		saveHybrisProductDto.setProductReadyMethod(productReadyMethod);
		saveHybrisProductDto.setProductHktvCatList(categoryCode);

		List<String> results = executeRules(saveHybrisProductDto);

		if (isSuccess) {
			Assertions.assertThat(results).contains(SalesChannelEnum.WECHAT.name());
		} else {
			Assertions.assertThat(results).doesNotContain(SalesChannelEnum.WECHAT.name());
		}
	}

	private List<String> executeRules(SaveHybrisProductDto saveHybrisProductDto) {

		boolean wechatExclusive = false;
		boolean ctmExclusive = false;
		if (saveHybrisProductDto.getProductHktvCatList() != null) {
			for(String catCode : saveHybrisProductDto.getProductHktvCatList().split("\\|")) {
				if (!ctmExclusive && catCode.length() >= 7 && TEST_RULE_DTO.getCtmExcludedCategories().contains(catCode.substring(0, 7))) {
					ctmExclusive = true;
				}
				if (!wechatExclusive && catCode.length() >= 4 && TEST_RULE_DTO.getWechatExcludedCategories().contains(catCode.substring(0, 4))) {
					wechatExclusive = true;
				}
			}
		}

		List<String> results = new ArrayList<>();
		KieSession kieSession = kieContainer.newKieSession();
		try {
			kieSession.setGlobal("salesChannelRule", TEST_RULE_DTO); // Ensure global is set properly
			kieSession.setGlobal("results", results);
			kieSession.insert(saveHybrisProductDto);
			kieSession.setGlobal("wechatExclusive", wechatExclusive);
			kieSession.setGlobal("ctmExclusive", ctmExclusive);
			kieSession.fireAllRules();
		} finally {
			kieSession.dispose();
		}

		return results;
	}
}
