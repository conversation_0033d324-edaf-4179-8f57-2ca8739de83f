package com.shoalter.mms_product_api.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class ValidationCheckUtilTest {

	/**
	 * Tests for the ValidationCheckUtil class method areAllFieldsFilledOrEmpty. This method checks if
	 * all the provided fields are either filled (non-null and non-empty) or all are empty/null.
	 */

	@Test
	void test_allFieldsNull_returnsTrue() {
		// Arrange
		String field1 = null;
		String field2 = null;
		Integer field3 = null;

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(field1, field2, field3);

		// Assert
		assertTrue(result, "All fields are null, so the method should return true.");
	}

	@Test
	void test_allFieldsEmpty_returnsTrue() {
		// Arrange
		String field1 = "";
		String field2 = "";
		Integer field3 = null;

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(field1, field2, field3);

		// Assert
		assertTrue(result, "All fields are empty or null, so the method should return true.");
	}

	@Test
	void test_allFieldsFilled_returnsTrue() {
		// Arrange
		String field1 = "Value1";
		String field2 = "Value2";
		Integer field3 = 123;

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(field1, field2, field3);

		// Assert
		assertTrue(result, "All fields are filled, so the method should return true.");
	}

	@Test
	void test_mixedFieldsSomeNull_returnsFalse() {
		// Arrange
		String field1 = "Value1";
		String field2 = null;
		Integer field3 = 123;

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(field1, field2, field3);

		// Assert
		assertFalse(result,
			"Fields are mixed with filled and null values, so the method should return false.");
	}

	@Test
	void test_mixedFieldsSomeEmptyAndFilled_returnsFalse() {
		// Arrange
		String field1 = "Value1";
		String field2 = "";
		Integer field3 = 123;

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(field1, field2, field3);

		// Assert
		assertFalse(result,
			"Fields are mixed with filled and empty values, so the method should return false.");
	}

	@Test
	void test_emptyFieldSet_returnsTrue() {
		// Arrange
		Object[] fields = {};

		// Act
		boolean result = ValidationCheckUtil.areAllFieldsFilledOrEmpty(fields);

		// Assert
		assertTrue(result, "Empty fields array should return true as there is nothing to check.");
	}
}
