package com.shoalter.mms_product_api.service.force_offline;

import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.mapper.ProductMasterDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

import org.mockito.Spy;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link ForceOfflineService#processing(UserDto, ForceOfflineRequestDto)}
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ForceOfflineService Processing Tests")
class ForceOfflineServiceTest {

    @Mock
    private PermissionHelper permissionHelper;

    @Mock
    private SaveProductRecordHelper saveProductRecordHelper;

    @Mock
    private SaveProductRecordRowHelper saveProductRecordRowHelper;

    @Mock
    private StoreRepository storeRepository;

    @Mock
    private ProductMasterHelper productMasterHelper;

    @Mock
    private MessageSource messageSource;

    @Mock
    private ProductMasterDtoMapper productMasterDtoMapper;
    @Spy
    @InjectMocks
    private ForceOfflineService forceOfflineService;
    private UserDto userDto;
    private ForceOfflineRequestDto requestDto;
    private StoreDo storeDo;
    private ProductMasterResultDto productMasterResultDto;
    private BuProductDto buProductDto;
    private HktvProductDto hktvProductDto;
    private ProductMasterStoreSkuIdResponseDto productMasterResponseDto;
    private ProductMasterDto productMasterDto;

    @BeforeEach
    void setUp() {
        // Setup test data
        userDto = UserDto.builder()
                .userCode("testUser")
                .userId(123)
                .build();

        requestDto = new ForceOfflineRequestDto();
        requestDto.setStorefrontStoreCode("TEST_STORE");
        requestDto.setSkuCode("TEST_SKU");
        requestDto.setSkuStatus("Suspended");
        requestDto.setCaseNumber("CASE123");

        storeDo = new StoreDo();
        storeDo.setId(1);
        storeDo.setStoreCode("TEST_STORE");

        // Setup ProductMaster related DTOs
        hktvProductDto = new HktvProductDto();
        hktvProductDto.setStoreSkuId("TEST_STORE_S_TEST_SKU");
        hktvProductDto.setOnlineStatus(OnlineStatusEnum.ONLINE);
        hktvProductDto.setForceOffline(false);

        buProductDto = new BuProductDto();
        buProductDto.setHktv(hktvProductDto);

        productMasterResultDto = new ProductMasterResultDto();
        productMasterResultDto.setSkuId("TEST_SKU");
        productMasterResultDto.setAdditional(buProductDto);

        productMasterResponseDto = new ProductMasterStoreSkuIdResponseDto();
        productMasterResponseDto.setStatus("SUCCESS");
        productMasterResponseDto.setData(List.of(productMasterResultDto));

        productMasterDto = new ProductMasterDto();
        productMasterDto.setMerchantId(1);
        productMasterDto.setAdditional(buProductDto);
    }

    @Test
    @DisplayName("Should successfully process force offline request")
    void processing_ValidForceOfflineRequest_ShouldReturnSuccess() {
        // Arrange
        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));
        when(productMasterHelper.requestProductByStoreSkuId(eq(userDto), any(FindStoreSkuIdProductRequestDto.class)))
                .thenReturn(productMasterResponseDto);
        when(productMasterDtoMapper.toProductMasterDto(productMasterResultDto))
                .thenReturn(productMasterDto);
        doReturn(1L).when(forceOfflineService).createRecord(eq(userDto), any(SingleEditProductDto.class), anyList());

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.SUCCESS.getCode(), result.getStatus());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getRecordId());

        // Verify force offline was set to true
        assertTrue(hktvProductDto.getForceOffline());
        assertEquals("CASE123", hktvProductDto.getCaseNumber());
        assertEquals(OnlineStatusEnum.OFFLINE, hktvProductDto.getOnlineStatus());

        verify(permissionHelper).checkForceOfflinePermission(userDto);
        verify(storeRepository).findByStoreCodeAndBuCode("TEST_STORE", "HKTV");
        verify(productMasterHelper).requestProductByStoreSkuId(eq(userDto), any());
    }

    @Test
    @DisplayName("Should successfully process remove force offline request")
    void processing_ValidRemoveForceOfflineRequest_ShouldReturnSuccess() {
        // Arrange
        requestDto.setSkuStatus("Offline"); // Remove force offline
        hktvProductDto.setForceOffline(true); // Current state is force offline

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));
        when(productMasterHelper.requestProductByStoreSkuId(eq(userDto), any(FindStoreSkuIdProductRequestDto.class)))
                .thenReturn(productMasterResponseDto);
        when(productMasterDtoMapper.toProductMasterDto(productMasterResultDto))
                .thenReturn(productMasterDto);
        doReturn(2L).when(forceOfflineService).createRecord(eq(userDto), any(SingleEditProductDto.class), anyList());

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.SUCCESS.getCode(), result.getStatus());
        assertEquals(2L, result.getData().getRecordId());

        // Verify force offline was set to false (removed)
        assertFalse(hktvProductDto.getForceOffline());
        assertEquals("CASE123", hktvProductDto.getCaseNumber());
        // Online status should not be changed when removing force offline
        assertEquals(OnlineStatusEnum.ONLINE, hktvProductDto.getOnlineStatus());
    }

    @Test
    @DisplayName("Should fail when required fields are missing")
    void processing_MissingRequiredFields_ShouldReturnFail() {
        // Arrange
        requestDto.setStorefrontStoreCode(null);
        requestDto.setSkuCode(null);
        requestDto.setSkuStatus(null);

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(messageSource.getMessage("message187", null, Locale.getDefault()))
                .thenReturn("Store code is required");
        when(messageSource.getMessage("message170", null, Locale.getDefault()))
                .thenReturn("SKU code is required");
        when(messageSource.getMessage("message191", null, Locale.getDefault()))
                .thenReturn("SKU status is required");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(3, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("Store code is required"));
        assertTrue(result.getErrorMessageList().contains("SKU code is required"));
        assertTrue(result.getErrorMessageList().contains("SKU status is required"));
    }

    @Test
    @DisplayName("Should fail when store does not exist")
    void processing_StoreNotExists_ShouldReturnFail() {
        // Arrange
        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.empty());
        when(messageSource.getMessage("message134", null, Locale.getDefault()))
                .thenReturn("Store not found");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("Store not found"));
    }

    @Test
    @DisplayName("Should fail when product not found in ProductMaster")
    void processing_ProductNotFound_ShouldReturnFail() {
        // Arrange
        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));
        when(productMasterHelper.requestProductByStoreSkuId(eq(userDto), any(FindStoreSkuIdProductRequestDto.class)))
                .thenReturn(null); // Product not found
        when(messageSource.getMessage("message127", null, Locale.getDefault()))
                .thenReturn("Product not found");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("Product not found"));
    }

    @Test
    @DisplayName("Should fail when product has no HKTV data")
    void processing_ProductWithoutHktvData_ShouldReturnFail() {
        // Arrange
        productMasterResultDto.setAdditional(null); // No additional data

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));
        when(productMasterHelper.requestProductByStoreSkuId(eq(userDto), any(FindStoreSkuIdProductRequestDto.class)))
                .thenReturn(productMasterResponseDto);
        when(messageSource.getMessage("message227", null, Locale.getDefault()))
                .thenReturn("HKTV data not found");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("HKTV data not found"));
    }

    @Test
    @DisplayName("Should fail with invalid SKU status")
    void processing_InvalidSkuStatus_ShouldReturnFail() {
        // Arrange
        requestDto.setSkuStatus("INVALID_STATUS");

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().get(0).contains("Invalid skuStatus: INVALID_STATUS"));
    }

    @Test
    @DisplayName("Should fail with invalid case number format")
    void processing_InvalidCaseNumberFormat_ShouldReturnFail() {
        // Arrange
        requestDto.setCaseNumber("INVALID@CASE#123"); // Contains invalid characters

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(messageSource.getMessage("message372", null, Locale.getDefault()))
                .thenReturn("Case number contains invalid characters");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("Case number contains invalid characters"));
    }

    @Test
    @DisplayName("Should fail with case number too long")
    void processing_CaseNumberTooLong_ShouldReturnFail() {
        // Arrange
        requestDto.setCaseNumber("A".repeat(51)); // More than 50 characters

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(messageSource.getMessage("message371", null, Locale.getDefault()))
                .thenReturn("Case number too long");

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertEquals(1, result.getErrorMessageList().size());
        assertTrue(result.getErrorMessageList().contains("Case number too long"));
    }

    @Test
    @DisplayName("Should handle exception and return fail")
    void processing_ThrowsException_ShouldReturnFail() {
        // Arrange
        doThrow(new RuntimeException("Test exception")).when(permissionHelper).checkForceOfflinePermission(userDto);

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.FAIL.getCode(), result.getStatus());
        assertNull(result.getData());
    }

    @Test
    @DisplayName("Should process without case number")
    void processing_WithoutCaseNumber_ShouldReturnSuccess() {
        // Arrange
        requestDto.setCaseNumber(null); // No case number

        doNothing().when(permissionHelper).checkForceOfflinePermission(userDto);
        when(storeRepository.findByStoreCodeAndBuCode("TEST_STORE", "HKTV"))
                .thenReturn(Optional.of(storeDo));
        when(productMasterHelper.requestProductByStoreSkuId(eq(userDto), any(FindStoreSkuIdProductRequestDto.class)))
                .thenReturn(productMasterResponseDto);
        when(productMasterDtoMapper.toProductMasterDto(productMasterResultDto))
                .thenReturn(productMasterDto);
        doReturn(3L).when(forceOfflineService).createRecord(eq(userDto), any(SingleEditProductDto.class), anyList());

        // Act
        ResponseDto<ForceOfflineResponseDto> result = forceOfflineService.processing(userDto, requestDto);

        // Assert
        assertEquals(StatusCodeEnum.SUCCESS.getCode(), result.getStatus());
        assertEquals(3L, result.getData().getRecordId());

        // Verify force offline was set but case number was not updated
        assertTrue(hktvProductDto.getForceOffline());
        // Case number should not be set when not provided
        assertNull(hktvProductDto.getCaseNumber());
    }
}
