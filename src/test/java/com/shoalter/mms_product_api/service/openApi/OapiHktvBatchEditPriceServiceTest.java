package com.shoalter.mms_product_api.service.openApi;

import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OapiHktvBatchEditPriceServiceTest {

	@InjectMocks
	@Spy
	private OapiHktvBatchEditPriceService oapiHktvBatchEditPriceService;

	@Mock
	private MessageSource messageSource;

	@Mock
	private BatchEditHelper batchEditHelper;
	@Mock
	private OapiHelper oapiHelper;
	@Mock
	private UserHelper userHelper;


	private final static String TEST_STRING = "test";


	@Test
	void start_validateFail_returnFail() {
		//Given
		when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseDto.fail(null));
		when(oapiHelper.generateBatchEditResponseData(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(getMockOapiResponseDto(OapiStatusCodeEnum.FAIL, null, null));
		//When
		OapiResponseDto<OapiBatchEditMainResponseData> result = oapiHktvBatchEditPriceService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	void start_notEverutsContract_returnFail() {
		//Given
		when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(getStoreContractMerchantDo(ContractType.DISPLAY_STORE_CONTRACT)));
		when(oapiHelper.generateBatchEditResponseData(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(getMockOapiResponseDto(OapiStatusCodeEnum.FAIL, null, null));
		when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		OapiResponseDto<OapiBatchEditMainResponseData> result = oapiHktvBatchEditPriceService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	void start_process_returnSuccess() {
		//Given
		when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(getStoreContractMerchantDo(ContractType.EVERUTS)));
		when(oapiHelper.generateBatchEditResponseData(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(getMockOapiResponseDto(OapiStatusCodeEnum.SUCCESS, null, null));
		when(batchEditHelper.createOapiBatchEditRecord(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt()))
			.thenReturn(ResponseDto.success(1L));
		when(userHelper.generateOapiUserDto(Mockito.any()))
			.thenReturn(UserDto.generateSystemUserDto());

		//When
		OapiResponseDto<OapiBatchEditMainResponseData> result = oapiHktvBatchEditPriceService.start(TEST_STRING, TEST_STRING, TEST_STRING, Collections.emptyList());

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.SUCCESS.getCode());
	}

	private StoreContractMerchantDo getStoreContractMerchantDo(String contractType) {
		return new StoreContractMerchantDo() {
			@Override
			public Integer getMerchantId() {
				return 0;
			}

			@Override
			public String getMerchantName() {
				return "";
			}

			@Override
			public Integer getStoreId() {
				return 0;
			}

			@Override
			public String getStoreCode() {
				return "";
			}

			@Override
			public String getStorefrontCode() {
				return "";
			}

			@Override
			public Integer getContractId() {
				return 0;
			}

			@Override
			public String getContractType() {
				return contractType;
			}
		};
	}

	private OapiResponseDto<OapiBatchEditMainResponseData> getMockOapiResponseDto(OapiStatusCodeEnum statusEnum, Long recordId, List<String> message) {
		return OapiResponseDto.<OapiBatchEditMainResponseData>builder()
			.message(OapiStatusCodeEnum.SUCCESS == statusEnum ? "Open API batch edit successful" : "Open API batch edit failed")
			.code(statusEnum.getCode())
			.data(OapiBatchEditMainResponseData.builder()
				.recordId(recordId)
				.status(statusEnum.getCode())
				.message(message)
				.build())
			.build();
	}

}
