package com.shoalter.mms_product_api.service.product;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.service.product.helper.SysParamHelper;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class CheckProductMissingFieldServiceTest {

	@InjectMocks
	private CheckProductMissingFieldService checkProductMissingFieldService;
	@Mock
	private MessageSource messageSource;
	@Mock
	private SysParamHelper sysParamHelper;
	@Mock
	private StoreRepository storeRepository;

	@Test
	public void testCheckExternalPlatform_AllFieldsEmpty_NoErrors() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(null);
		externalPlatform.setSkuId(null);
		externalPlatform.setProductId(null);

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(errors.isEmpty(), "Should not add errors when all fields are empty.");
	}

	@Test
	public void testCheckExternalPlatform_OnlySourceAndProductId_NoSkuId_NoErrors() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(List.of("source1"));
		externalPlatform.setSkuId(null);
		externalPlatform.setProductId("productId123");

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(errors.isEmpty(),
			"Should not add errors when only source and product ID are present.");
	}

	@Test
	public void testCheckExternalPlatform_AllFieldsProvided_NoErrors() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(List.of("source1"));
		externalPlatform.setSkuId("skuId123");
		externalPlatform.setProductId("productId123");

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(errors.isEmpty(), "Should not add errors when all fields are properly provided.");
	}

	@Test
	public void testCheckExternalPlatform_SourceMissing_ErrorsAdded() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(null);
		externalPlatform.setSkuId("skuId123");
		externalPlatform.setProductId("productId123");

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(!errors.isEmpty(),
			"Should add errors when source is missing but other fields are present.");
	}

	@Test
	public void testCheckExternalPlatform_ProductIdMissing_ErrorsAdded() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(List.of("source1"));
		externalPlatform.setSkuId("skuId123");
		externalPlatform.setProductId(null);

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(!errors.isEmpty(),
			"Should add errors when product ID is missing but other fields are present.");
	}

	@Test
	public void testCheckExternalPlatform_OnlySkuIdProvided_ErrorsAdded() {
		Collection<String> errors = new ArrayList<>();
		ExternalPlatform externalPlatform = new ExternalPlatform();
		externalPlatform.setSource(null);
		externalPlatform.setSkuId("sku123");
		externalPlatform.setProductId(null);

		checkProductMissingFieldService.checkExternalPlatform(errors, externalPlatform);

		assertTrue(!errors.isEmpty(),
			"Should add errors when SKU ID is missing but other fields are present.");
	}
}
