package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreInfoDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.mapper.OapiDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditPriceMainRequestData;
import com.shoalter.mms_product_api.service.product.CheckProductMissingFieldService;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPriceDto;
import com.shoalter.mms_product_api.service.product.pojo.OapiBatchEditProductBaseDto;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BatchEditHelperTest {
	@InjectMocks
	private BatchEditHelper batchEditHelper;

	@Mock
	private MessageSource messageSource;

	@Mock
	private SaveProductRecordHelper saveProductRecordHelper;

	@Mock
	private ProductRepository productRepository;

	@Mock
	private SaveProductRecordRowHelper saveProductRecordRowHelper;

	@Mock
	private CheckProductMissingFieldService checkProductMissingFieldService;
	@Mock
	private OapiDataMapper oapiDataMapper;


	private final static String TEST_STRING = "test";
	private final static String TEST_SKU_CODE_A = "sku code a";
	private final static String TEST_SKU_CODE_B = "sku code b";

	@Test
	void createOapiBatchEditPriceRecord_noRequestData_throwNoDataException() {
		//Given
		Throwable thrown = Assertions.catchThrowable(() ->
			batchEditHelper.createOapiBatchEditRecord(null, null, Collections.emptyList(), 0));

		//Then
		Assertions.assertThat(thrown).isInstanceOf(NoDataException.class);
	}

	@Test
	void createOapiBatchEditPriceRecord_requestDataExceed_responseFail() {
		//Given
		List<OapiBatchEditProductBaseDto> mockList = Mockito.mock(List.class);
		when(mockList.size()).thenReturn(BatchCheckHelper.MAXIMUM_10000 + 1);
		when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		ResponseDto<Long> result = batchEditHelper.createOapiBatchEditRecord(null, null, mockList, 0);


		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.FAIL.getCode());
	}

	@Test
	void createOapiBatchEditPriceRecord_process_responseSuccess() {
		//Given
		SaveProductRecordDo saveProductRecordDo = generateCreateOapiBatchEditPriceRecordCondition();
		when(checkProductMissingFieldService.start(Mockito.any()))
			.thenReturn(ResponseDto.success(null));

		//When
		when(oapiDataMapper.toOapiBatchEditPriceMainRequestData(Mockito.any())).thenReturn(new BatchEditPriceDto());
		OapiBatchEditPriceMainRequestData requestData = new OapiBatchEditPriceMainRequestData();
		requestData.setSkuCode(TEST_SKU_CODE_A);
		ResponseDto<Long> result = batchEditHelper.createOapiBatchEditRecord(UserDto.builder().build(), getMockStoreContractMerchantDo(), List.of(requestData), SaveProductType.BATCH_EDIT_PRODUCT_PRICE);

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.SUCCESS.getCode());
		Assertions.assertThat(saveProductRecordDo.getStatus()).isEqualTo(SaveProductStatus.PROCESSING);

		ArgumentCaptor<List<SaveProductRecordRowDo>> saveProductRecordRowCap = ArgumentCaptor.forClass(List.class);
		Mockito.verify(saveProductRecordRowHelper, Mockito.times(1)).batchSaveSaveProductRecordRowDo(saveProductRecordRowCap.capture());
		List<SaveProductRecordRowDo> resultRecordRows = saveProductRecordRowCap.getValue();
		Assertions.assertThat(resultRecordRows.size()).isEqualTo(1);
		Assertions.assertThat(resultRecordRows.get(0).getStatus()).isEqualTo(SaveProductStatus.PROCESSING);
	}

	@Test
	void createOapiBatchEditPriceRecord_productStoreInfoNotFound_recordRowFail() {
		//Given
		SaveProductRecordDo saveProductRecordDo = generateCreateOapiBatchEditPriceRecordCondition();

		//When
		when(oapiDataMapper.toOapiBatchEditPriceMainRequestData(Mockito.any())).thenReturn(new BatchEditPriceDto());
		OapiBatchEditPriceMainRequestData requestData = new OapiBatchEditPriceMainRequestData();
		requestData.setSkuCode(TEST_SKU_CODE_B);
		ResponseDto<Long> result = batchEditHelper.createOapiBatchEditRecord(UserDto.builder().build(), getMockStoreContractMerchantDo(), List.of(requestData), SaveProductType.BATCH_EDIT_PRODUCT_PRICE);

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.SUCCESS.getCode());
		Assertions.assertThat(saveProductRecordDo.getStatus()).isEqualTo(SaveProductStatus.PROCESSING);

		ArgumentCaptor<List<SaveProductRecordRowDo>> saveProductRecordRowCap = ArgumentCaptor.forClass(List.class);
		Mockito.verify(saveProductRecordRowHelper, Mockito.times(1)).batchSaveSaveProductRecordRowDo(saveProductRecordRowCap.capture());
		List<SaveProductRecordRowDo> resultRecordRows = saveProductRecordRowCap.getValue();
		Assertions.assertThat(resultRecordRows.size()).isEqualTo(1);
		Assertions.assertThat(resultRecordRows.get(0).getStatus()).isEqualTo(SaveProductStatus.FAIL);
	}

	private SaveProductRecordDo generateCreateOapiBatchEditPriceRecordCondition() {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		when(saveProductRecordHelper.createOapiSaveProductRecordWithQueueProtocol(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt()))
			.thenAnswer(invocation -> {
				saveProductRecordDo.setUploadType(invocation.getArgument(2));
				saveProductRecordDo.setStatus(invocation.getArgument(4));
				return saveProductRecordDo;
			});
		when(productRepository.findProductStoreInfoByBuCodeAndStoreSkuIds(Mockito.any(), Mockito.any()))
			.thenReturn(List.of(getMockProductStoreInfoDo()));
		when(saveProductRecordRowHelper.generateProductRecordRowDo(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any()))
			.thenAnswer(invocation -> {
				SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
				saveProductRecordRowDo.setStatus(invocation.getArgument(2));
				return saveProductRecordRowDo;
			});
		return saveProductRecordDo;
	}

	private ProductStoreInfoDo getMockProductStoreInfoDo() {
		return new ProductStoreInfoDo() {
			@Override
			public String getUuid() {
				return "";
			}

			@Override
			public String getSkuCode() {
				return TEST_SKU_CODE_A;
			}

			@Override
			public String getStoreCode() {
				return "";
			}

			@Override
			public String getStorefrontStoreCode() {
				return "";
			}

			@Override
			public Integer getMerchantId() {
				return 0;
			}
		};
	}

	private StoreContractMerchantDo getMockStoreContractMerchantDo() {
		return new StoreContractMerchantDo() {
			@Override
			public Integer getMerchantId() {
				return 0;
			}

			@Override
			public String getMerchantName() {
				return "";
			}

			@Override
			public Integer getStoreId() {
				return 0;
			}

			@Override
			public String getStoreCode() {
				return "";
			}

			@Override
			public String getStorefrontCode() {
				return "";
			}

			@Override
			public Integer getContractId() {
				return 0;
			}

			@Override
			public String getContractType() {
				return "";
			}
		};
	}


}
