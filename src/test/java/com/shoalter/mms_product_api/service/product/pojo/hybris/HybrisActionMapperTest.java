package com.shoalter.mms_product_api.service.product.pojo.hybris;

import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

class HybrisActionMapperTest {

    @Test
    @DisplayName("Should return PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE mapper for simplified Chinese action")
    void getActionMapper_SimplifiedChineseAction_ReturnsCorrectMapper() {
        // Given
        String action = HybrisAction.PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE;

        // When
        HybrisActionMapper actualMapper = HybrisActionMapper.getActionMapper(action);

        // Then
        assertEquals(HybrisActionMapper.PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE, actualMapper);
    }

    @Test
    @DisplayName("Should return DEFAULT mapper for unknown action")
    void getActionMapper_UnknownAction_ReturnsDefaultMapper() {
        // Given
        String unknownAction = "unknownAction";

        // When
        HybrisActionMapper actualMapper = HybrisActionMapper.getActionMapper(unknownAction);

        // Then
        assertEquals(HybrisActionMapper.DEFAULT_SYNC_ALL, actualMapper);
    }

	@Test
	@DisplayName("Should return DEFAULT mapper for empty action")
	void getActionMapper_EmptyAction_ReturnsDefaultMapper() {
		// Given
		String action = "";

		// When
		HybrisActionMapper actualMapper = HybrisActionMapper.getActionMapper(action);

		// Then
		assertEquals(HybrisActionMapper.DEFAULT_SYNC_ALL, actualMapper);
	}

    @Test
    @DisplayName("Should return DEFAULT mapper for action is null")
    void getActionMapper_NullAction_ReturnsDefaultMapper() {
        // Given
        String action = null;

        // When
        HybrisActionMapper actualMapper = HybrisActionMapper.getActionMapper(action);

        // Then
        assertEquals(HybrisActionMapper.DEFAULT_SYNC_ALL, actualMapper);
    }

    @Test
    @DisplayName("Should map to SimplifiedChineseActionDto for PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE")
    void mapToActionObject_SimplifiedChineseAction_ReturnsSimplifiedChineseActionDto() {
        // Given
        SaveHybrisProductDto givenDto = new SaveHybrisProductDto();
        HybrisActionMapper mapper = HybrisActionMapper.PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE;

        // When
        Object actualResult = mapper.mapToSaveHybrisProductRequestDto(givenDto);

        // Then
        assertNotNull(actualResult);
    }

    @Test
    @DisplayName("Should return original DTO for DEFAULT mapper")
    void mapToSaveHybrisProductRequestDto_DefaultMapper_ReturnsOriginalDto() {
        // Given
        SaveHybrisProductDto givenDto = new SaveHybrisProductDto();
        HybrisActionMapper mapper = HybrisActionMapper.DEFAULT_SYNC_ALL;

        // When
        Object actualResult = mapper.mapToSaveHybrisProductRequestDto(givenDto);

        // Then
        assertNotNull(actualResult);
        assertSame(givenDto, actualResult, "Default mapper should return the original DTO");
    }

    @Test
    @DisplayName("Should return DEFAULT_SYNC_ALL mapper for update all action(PRODUCT_SYNC_MODE_ALL)")
    void getActionMapper_UpdateAllAction_ReturnsCorrectMapper() {
        // Given
        String action = HybrisAction.PRODUCT_SYNC_MODE_ALL;

        // When
        HybrisActionMapper actualMapper = HybrisActionMapper.getActionMapper(action);

        // Then
        assertEquals(HybrisActionMapper.DEFAULT_SYNC_ALL, actualMapper);
    }
}
