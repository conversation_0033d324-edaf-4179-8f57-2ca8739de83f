package com.shoalter.mms_product_api.service.product.mapper;

import com.shoalter.mms_product_api.service.product.pojo.BatchEditTranslateDto;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;

class BatchEditTranslateDtoMapperTest {

    private final BatchEditTranslateDtoMapper mapper = BatchEditTranslateDtoMapper.INSTANCE;

	@Test
	void fromProductMasterResultDtoWithOtherField_ShouldMapEmtpyFields() {
		// Given
		ProductMasterResultDto source = new ProductMasterResultDto();
		source.setVersion(1);
		source.setBrandId(1);
		BuProductDto additional = new BuProductDto();
		HktvProductDto hktv = new HktvProductDto();
		hktv.setVisibility("Y");
		additional.setHktv(hktv);
		source.setAdditional(additional);

		// When
		BatchEditTranslateDto result = mapper.fromProductMasterResultDto(source);

		// Then
		assertThat(result).isNotNull();
		assertThat(result.getSkuNameCh()).isNull();
		assertThat(result.getSkuNameSc()).isNull();
		assertThat(result.getHktvField()).isNotNull();
		assertThat(result.getHktvField().getSkuShortDescriptionCh()).isNull();
		assertThat(result.getHktvField().getSkuShortDescriptionSc()).isNull();
	}

	@Test
	void fromProductMasterResultDtoWithNoHktvOtherField_ShouldMapEmtpyFields() {
		// Given
		ProductMasterResultDto source = new ProductMasterResultDto();
		source.setVersion(1);
		source.setBrandId(1);
		BuProductDto additional = new BuProductDto();
		source.setAdditional(additional);

		// When
		BatchEditTranslateDto result = mapper.fromProductMasterResultDto(source);

		// Then
		assertThat(result).isNotNull();
		assertThat(result.getSkuNameCh()).isNull();
		assertThat(result.getSkuNameSc()).isNull();
		assertThat(result.getHktvField()).isNull();
	}



    @Test
    void fromProductMasterResultDto_ShouldMapAllFields() {
        // Given
        ProductMasterResultDto source = new ProductMasterResultDto();
        source.setSkuNameCh("測試商品");
        source.setSkuNameSc("测试商品");

		BuProductDto buProductDto = new BuProductDto();
		source.setAdditional(buProductDto);

        HktvProductDto hktvProductDto = new HktvProductDto();
        hktvProductDto.setSkuShortDescriptionCh("短描述測試");
        hktvProductDto.setSkuShortDescriptionSc("短描述测试");
        hktvProductDto.setSkuLongDescriptionCh("長描述測試");
        hktvProductDto.setSkuLongDescriptionSc("长描述测试");

        source.getAdditional().setHktv(hktvProductDto);

        // When
        BatchEditTranslateDto result = mapper.fromProductMasterResultDto(source);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSkuNameCh()).isEqualTo("測試商品");
        assertThat(result.getSkuNameSc()).isEqualTo("测试商品");

        // Verify HKTV fields
        BatchEditTranslateDto.HktvField hktvField = result.getHktvField();
        assertThat(hktvField).isNotNull();
        assertThat(hktvField.getSkuShortDescriptionCh()).isEqualTo("短描述測試");
        assertThat(hktvField.getSkuShortDescriptionSc()).isEqualTo("短描述测试");
        assertThat(hktvField.getSkuLongDescriptionCh()).isEqualTo("長描述測試");
        assertThat(hktvField.getSkuLongDescriptionSc()).isEqualTo("长描述测试");
    }

    @Test
    void fromProductMasterResultDto_WithNullHktv_ShouldMapBasicFields() {
        // Given
        ProductMasterResultDto source = new ProductMasterResultDto();
        source.setSkuNameCh("測試商品");
        source.setSkuNameSc("测试商品");
		BuProductDto buProductDto = new BuProductDto();
		source.setAdditional(buProductDto);
        source.getAdditional().setHktv(null);

        // When
        BatchEditTranslateDto result = mapper.fromProductMasterResultDto(source);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSkuNameCh()).isEqualTo("測試商品");
        assertThat(result.getSkuNameSc()).isEqualTo("测试商品");
        assertThat(result.getHktvField()).isNull();
    }

    @Test
    void fromHktvField_ShouldMapAllFields() {
        // Given
        HktvProductDto source = new HktvProductDto();
        source.setSkuShortDescriptionCh("短描述測試");
        source.setSkuShortDescriptionSc("短描述测试");
        source.setSkuLongDescriptionCh("長描述測試");
        source.setSkuLongDescriptionSc("长描述测试");

        // When
        BatchEditTranslateDto.HktvField result = mapper.fromHktvField(source);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSkuShortDescriptionCh()).isEqualTo("短描述測試");
        assertThat(result.getSkuShortDescriptionSc()).isEqualTo("短描述测试");
        assertThat(result.getSkuLongDescriptionCh()).isEqualTo("長描述測試");
        assertThat(result.getSkuLongDescriptionSc()).isEqualTo("长描述测试");
		assertNull(result.getFinePrintCh());
		assertNull(result.getFinePrintSc());

    }
}
