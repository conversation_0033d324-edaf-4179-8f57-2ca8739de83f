package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ActiveInd;
import com.shoalter.mms_product_api.config.product.MonitorPlatformGroupEnum;
import com.shoalter.mms_product_api.config.product.MonitorProductPriceStatus;
import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ProductPriceMonitorProductHelper
 */
@ExtendWith(MockitoExtension.class)
class ProductPriceMonitorProductHelperTest {

	@Mock
	private ProductPriceMonitorProductRepository monitorProductRepository;

	@Mock
	private StoreRepository storeRepository;

	@Mock
	private SysParmRepository sysParmRepository;

	// Don't mock Gson as it's a final class
	private Gson gson = new Gson();

	// Use a manually constructed helper instead of @InjectMocks since we're using a real Gson
	private ProductPriceMonitorProductHelper productPriceMonitorProductHelper;

	@Captor
	private ArgumentCaptor<ProductPriceMonitorProductDo> productDoCaptor;

	private static final String TMALL_SOURCE = "TMALL";
	private static final String PRODUCT_ID = "PROD001";
	private static final String SKU_ID = "SKU001";
	private static final String USER_CODE = "USER001";

	private ExternalPlatform validExternalPlatform;

	@BeforeEach
	void setUp() {
		// Initialize helper with real Gson and mocked repositories
		productPriceMonitorProductHelper = new ProductPriceMonitorProductHelper(monitorProductRepository, storeRepository, sysParmRepository, gson);

		// Initialize a valid external platform for reuse in tests
		validExternalPlatform = new ExternalPlatform();
		validExternalPlatform.setSource(Collections.singletonList(TMALL_SOURCE));
		validExternalPlatform.setProductId(PRODUCT_ID);
		validExternalPlatform.setSkuId(SKU_ID);
	}

	// =========================================================================
	// Tests for determinePriceMonitorAction method
	// =========================================================================

	@Test
	void whenNewPlatformNotNullAndOriginNull_thenShouldEnableMonitor() {
		// GIVEN: New platform exists but origin platform is null
		ExternalPlatform newPlatform = new ExternalPlatform();
		ExternalPlatform originPlatform = null;

		// WHEN: Determining the monitor action
		ProductPriceMonitorProductHelper.PriceMonitorAction result =
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform, originPlatform, MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB);

		// THEN: The action should be to enable monitoring
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_ENABLE_MONITOR, result);
	}

	@Test
	void whenNewPlatformNullAndOriginNotNull_thenShouldDisableMonitor() {
		// GIVEN: New platform is null but origin platform exists
		ExternalPlatform newPlatform = null;
		ExternalPlatform originPlatform = new ExternalPlatform();

		// WHEN: Determining the monitor action
		ProductPriceMonitorProductHelper.PriceMonitorAction result =
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform, originPlatform, MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB);

		// THEN: The action should be to disable monitoring
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_DISABLE_MONITOR, result);
	}

	@Test
	void whenBothPlatformsNullOrBothNotNull_thenNoMonitoringNeeded() {
		// GIVEN: Both platforms are null
		ExternalPlatform newPlatform1 = null;
		ExternalPlatform originPlatform1 = null;

		// WHEN: Determining the monitor action for both null
		// THEN: The action should indicate no monitoring needed
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NO_NEED_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform1, originPlatform1, MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB));

		// GIVEN: Both platforms are not null
		ExternalPlatform newPlatform2 = new ExternalPlatform();
		ExternalPlatform originPlatform2 = new ExternalPlatform();

		// WHEN: Determining the monitor action for both not null
		// THEN: The action should indicate no monitoring needed
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NO_NEED_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform2, originPlatform2, MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB));
	}

	@Test
	void whenMonitorSourceAddedToNewPlatform_thenShouldEnableMonitor() {
		// GIVEN: Both platforms exist, but monitor source is only in the new platform
		ExternalPlatform newPlatform = new ExternalPlatform();
		newPlatform.setSource(Collections.singletonList("TMALL")); // Contains the monitor source

		ExternalPlatform originPlatform = new ExternalPlatform();
		originPlatform.setSource(Collections.singletonList("OTHER_SOURCE")); // Does not contain the monitor source

		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		// WHEN: Determining the monitor action
		ProductPriceMonitorProductHelper.PriceMonitorAction result =
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform, originPlatform, groupEnum);

		// THEN: The action should be to enable monitoring
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_ENABLE_MONITOR, result);
	}

	@Test
	void whenMonitorSourceRemovedFromNewPlatform_thenShouldDisableMonitor() {
		// GIVEN: Both platforms exist, but monitor source is only in the original platform
		ExternalPlatform newPlatform = new ExternalPlatform();
		newPlatform.setSource(Collections.singletonList("OTHER_SOURCE")); // Does not contain the monitor source

		ExternalPlatform originPlatform = new ExternalPlatform();
		originPlatform.setSource(Collections.singletonList("TMALL")); // Contains the monitor source

		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		// WHEN: Determining the monitor action
		ProductPriceMonitorProductHelper.PriceMonitorAction result =
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform, originPlatform, groupEnum);

		// THEN: The action should be to disable monitoring
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_DISABLE_MONITOR, result);
	}

	@Test
	void whenBothPlatformsHaveSameSourceStatus_thenNoMonitoringNeeded() {
		// GIVEN: Both platforms have the same source status (both contain or both don't contain)
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		// Case 1: Both contain the monitor source
		ExternalPlatform newPlatform1 = new ExternalPlatform();
		newPlatform1.setSource(Collections.singletonList("TMALL"));

		ExternalPlatform originPlatform1 = new ExternalPlatform();
		originPlatform1.setSource(Collections.singletonList("TMALL"));

		// WHEN/THEN: Both contain the monitor source - no monitoring needed
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NO_NEED_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform1, originPlatform1, groupEnum));

		// Case 2: Neither contains the monitor source
		ExternalPlatform newPlatform2 = new ExternalPlatform();
		newPlatform2.setSource(Collections.singletonList("OTHER_SOURCE"));

		ExternalPlatform originPlatform2 = new ExternalPlatform();
		originPlatform2.setSource(Collections.singletonList("OTHER_SOURCE"));

		// WHEN/THEN: Neither contains the monitor source - no monitoring needed
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NO_NEED_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform2, originPlatform2, groupEnum));
	}

	@Test
	void whenSourceContainsMultipleValues_thenShouldDetectMonitorSourceCorrectly() {
		// GIVEN: Platforms with multiple sources
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		// Case 1: New platform adds the monitor source among other sources
		ExternalPlatform newPlatform1 = new ExternalPlatform();
		newPlatform1.setSource(Arrays.asList("OTHER_SOURCE", "TMALL", "ANOTHER_SOURCE"));

		ExternalPlatform originPlatform1 = new ExternalPlatform();
		originPlatform1.setSource(Arrays.asList("OTHER_SOURCE", "ANOTHER_SOURCE"));

		// WHEN/THEN: Should enable monitoring when source is added
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_ENABLE_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform1, originPlatform1, groupEnum));

		// Case 2: New platform removes the monitor source but keeps other sources
		ExternalPlatform newPlatform2 = new ExternalPlatform();
		newPlatform2.setSource(Arrays.asList("OTHER_SOURCE", "ANOTHER_SOURCE"));

		ExternalPlatform originPlatform2 = new ExternalPlatform();
		originPlatform2.setSource(Arrays.asList("OTHER_SOURCE", "TMALL", "ANOTHER_SOURCE"));

		// WHEN/THEN: Should disable monitoring when source is removed
		assertEquals(ProductPriceMonitorProductHelper.PriceMonitorAction.NEED_DISABLE_MONITOR,
			ProductPriceMonitorProductHelper.determinePriceMonitorAction(newPlatform2, originPlatform2, groupEnum));
	}

	// =========================================================================
	// Tests for meetMonitorCriteria method
	// =========================================================================

	@Test
	void whenExternalPlatformValid_thenMeetsCriteria() {
		// GIVEN: A valid external platform (initialized in setUp)

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			validExternalPlatform, ThirdPartySourceEnum.TMALL);

		// THEN: It should meet the criteria
		assertTrue(result);
	}

	@Test
	void whenExternalPlatformNull_thenDoesNotMeetCriteria() {
		// GIVEN: A null external platform

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(null, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertFalse(result);
	}

	@Test
	void whenSourceNull_thenDoesNotMeetCriteria() {
		// GIVEN: An external platform with null source
		ExternalPlatform extNullSource = new ExternalPlatform();
		extNullSource.setSource(null);
		extNullSource.setProductId(PRODUCT_ID);
		extNullSource.setSkuId(SKU_ID);

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extNullSource, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertFalse(result);
	}

	@Test
	void whenSourceEmpty_thenDoesNotMeetCriteria() {
		// GIVEN: An external platform with empty source list
		ExternalPlatform extEmptySource = new ExternalPlatform();
		extEmptySource.setSource(Collections.emptyList());
		extEmptySource.setProductId(PRODUCT_ID);
		extEmptySource.setSkuId(SKU_ID);

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extEmptySource, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertFalse(result);
	}

	@Test
	void whenSourceDoesNotContainRequiredValue_thenDoesNotMeetCriteria() {
		// GIVEN: An external platform with wrong source value
		ExternalPlatform extWrongSource = new ExternalPlatform();
		extWrongSource.setSource(Collections.singletonList("AMAZON"));
		extWrongSource.setProductId(PRODUCT_ID);
		extWrongSource.setSkuId(SKU_ID);

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extWrongSource, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertFalse(result);
	}

	@Test
	void whenSourceContainsRequiredValueAmongOthers_thenMeetsCriteria() {
		// GIVEN: An external platform with multiple sources including the required one
		ExternalPlatform extMultipleSources = new ExternalPlatform();
		extMultipleSources.setSource(Arrays.asList("AMAZON", TMALL_SOURCE, "EBAY"));
		extMultipleSources.setProductId(PRODUCT_ID);
		extMultipleSources.setSkuId(SKU_ID);

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extMultipleSources, ThirdPartySourceEnum.TMALL);

		// THEN: It should meet the criteria
		assertTrue(result);
	}

	@Test
	void whenProductIdBlank_thenDoesNotMeetCriteria() {
		// GIVEN: An external platform with blank product ID
		ExternalPlatform extEmptyProductId = new ExternalPlatform();
		extEmptyProductId.setSource(Collections.singletonList(TMALL_SOURCE));
		extEmptyProductId.setProductId("");
		extEmptyProductId.setSkuId(SKU_ID);

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extEmptyProductId, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertFalse(result);
	}

	@Test
	void whenSkuIdBlank_thenDoesNotMeetCriteria() {
		// GIVEN: An external platform with blank SKU ID
		ExternalPlatform extEmptySkuId = new ExternalPlatform();
		extEmptySkuId.setSource(Collections.singletonList(TMALL_SOURCE));
		extEmptySkuId.setProductId(PRODUCT_ID);
		extEmptySkuId.setSkuId("");

		// WHEN: Checking if it meets monitoring criteria
		boolean result = ProductPriceMonitorProductHelper.meetMonitorCriteria(
			extEmptySkuId, ThirdPartySourceEnum.TMALL);

		// THEN: It should not meet the criteria
		assertTrue(result);
	}

	// =========================================================================
	// Tests for disableMonitorRecord method
	// =========================================================================

	@Test
	void whenRecordExists_thenShouldDisableIt() {
		// GIVEN: An existing active monitor record
		String storeSkuId = "STORE_001#SKU_001";
		Integer busUnitId = 1;
		String targetPlatform = TMALL_SOURCE;

		ProductPriceMonitorProductDo existingRecord = new ProductPriceMonitorProductDo();
		existingRecord.setActiveInd(ActiveInd.ENABLE.getValue());

		when(monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatformAndActiveInd(
			storeSkuId, busUnitId, targetPlatform, ActiveInd.ENABLE.getValue()))
			.thenReturn(Optional.of(existingRecord));

		// WHEN: Disabling the monitor record
		productPriceMonitorProductHelper.disableMonitorRecord(storeSkuId, busUnitId, targetPlatform, USER_CODE);

		// THEN: The record should be updated and saved with disabled status
		verify(monitorProductRepository).save(productDoCaptor.capture());

		ProductPriceMonitorProductDo savedEntity = productDoCaptor.getValue();
		assertEquals(ActiveInd.DISABLE.getValue(), savedEntity.getActiveInd());
		assertEquals(MonitorProductPriceStatus.PENDING.getValue(), savedEntity.getPriceStatus());
		assertEquals(USER_CODE, savedEntity.getLastUpdatedBy());
		assertNotNull(savedEntity.getLastUpdatedDate());
	}

	@Test
	void whenRecordDoesNotExist_thenShouldDoNothing() {
		// GIVEN: No existing active monitor record
		String storeSkuId = "STORE_001_SKU_001";
		Integer busUnitId = 1;
		String targetPlatform = TMALL_SOURCE;

		when(monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatformAndActiveInd(
			storeSkuId, busUnitId, targetPlatform, ActiveInd.ENABLE.getValue()))
			.thenReturn(Optional.empty());

		// WHEN: Attempting to disable a non-existent record
		productPriceMonitorProductHelper.disableMonitorRecord(storeSkuId, busUnitId, targetPlatform, USER_CODE);

		// THEN: No save operation should be performed
		verify(monitorProductRepository, never()).save(any(ProductPriceMonitorProductDo.class));
	}

	// =========================================================================
	// Tests for createOrUpdateActiveMonitorRecord method
	// =========================================================================

	@Test
	void whenNoExistingRecord_thenShouldCreateNewOne() {
		// GIVEN: No existing monitor record in the repository
		String storeSkuId = "STORE_001_SKU_001";
		String storefrontStoreCode = "STORE_001";
		String skuId = "SKU_001";
		String skuName = "Test SKU";
		Integer busUnitId = 1;
		Integer merchantId = 1;
		Integer storeId = 1;
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		when(monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatform(
			storeSkuId, busUnitId, TMALL_SOURCE))
			.thenReturn(Optional.empty());

		// WHEN: Creating or updating an active monitor record
		boolean result = productPriceMonitorProductHelper.createOrUpdateActiveMonitorRecord(
			storeSkuId, storefrontStoreCode, skuId, skuName, busUnitId, merchantId, storeId, USER_CODE, validExternalPlatform, groupEnum);

		// THEN: A new record should be created and saved
		assertTrue(result, "Operation should succeed when creating a new record");
		verify(monitorProductRepository).save(any(ProductPriceMonitorProductDo.class));
	}

	@Test
	void whenDisabledRecordExists_thenShouldReactivateIt() {
		// GIVEN: An existing but disabled monitor record
		String storeSkuId = "STORE_001_SKU_001";
		String storefrontStoreCode = "STORE_001";
		String skuId = "SKU_001";
		String skuName = "Test SKU";
		Integer busUnitId = 1;
		Integer merchantId = 1;
		Integer storeId = 1;
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		ProductPriceMonitorProductDo existingRecord = new ProductPriceMonitorProductDo();
		existingRecord.setActiveInd(ActiveInd.DISABLE.getValue());

		when(monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatform(
			storeSkuId, busUnitId, TMALL_SOURCE))
			.thenReturn(Optional.of(existingRecord));

		// WHEN: Creating or updating an active monitor record
		boolean result = productPriceMonitorProductHelper.createOrUpdateActiveMonitorRecord(
			storeSkuId, storefrontStoreCode, skuId, skuName, busUnitId, merchantId, storeId, USER_CODE, validExternalPlatform, groupEnum);

		// THEN: The existing record should be reactivated with updated fields
		assertTrue(result, "Operation should succeed when reactivating a record");
		verify(monitorProductRepository).save(productDoCaptor.capture());

		ProductPriceMonitorProductDo savedRecord = productDoCaptor.getValue();
		assertEquals(ActiveInd.ENABLE.getValue(), savedRecord.getActiveInd(), "Record should be enabled");
		assertEquals(MonitorProductPriceStatus.PENDING.getValue(), savedRecord.getPriceStatus(), "Price status should be set to pending");
		assertEquals(USER_CODE, savedRecord.getLastUpdatedBy(), "Last updated by should be set");
	}

	@Test
	void whenActiveRecordExists_thenOnlyUpdateExternalPlatformDetail() {
		// GIVEN: An existing active monitor record
		String storeSkuId = "STORE_001_SKU_001";
		String storefrontStoreCode = "STORE_001";
		String skuId = "SKU_001";
		String skuName = "Test SKU";
		Integer busUnitId = 1;
		Integer merchantId = 1;
		Integer storeId = 1;
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		ProductPriceMonitorProductDo existingRecord = new ProductPriceMonitorProductDo();
		existingRecord.setActiveInd(ActiveInd.ENABLE.getValue());

		// Set initial values to verify they are updated
		existingRecord.setTargetProductCode("OLD_PROD");
		existingRecord.setTargetSkuCode("OLD_SKU");

		when(monitorProductRepository.findByStoreSkuIdAndBusUnitIdAndTargetPlatform(
			storeSkuId, busUnitId, TMALL_SOURCE))
			.thenReturn(Optional.of(existingRecord));

		// Capture the object being saved
		ArgumentCaptor<ProductPriceMonitorProductDo> recordCaptor = ArgumentCaptor.forClass(ProductPriceMonitorProductDo.class);

		// WHEN: Creating or updating an active monitor record
		boolean result = productPriceMonitorProductHelper.createOrUpdateActiveMonitorRecord(
			storeSkuId, storefrontStoreCode, skuId, skuName, busUnitId, merchantId, storeId, USER_CODE, validExternalPlatform, groupEnum);

		// THEN: Operation should succeed and the existing record should be updated with new external platform details
		assertTrue(result, "Operation should succeed when updating external platform details");

		// Verify save was called and capture the argument
		verify(monitorProductRepository).save(recordCaptor.capture());

		// Get the saved record and verify its properties
		ProductPriceMonitorProductDo savedRecord = recordCaptor.getValue();
		assertEquals(validExternalPlatform.getProductId(), savedRecord.getTargetProductCode(), "Target product code should be updated");
		assertEquals(validExternalPlatform.getSkuId(), savedRecord.getTargetSkuCode(), "Target SKU code should be updated");
		assertEquals(USER_CODE, savedRecord.getLastUpdatedBy(), "Last updated by should be set");
		assertNotNull(savedRecord.getLastUpdatedDate(), "Last updated date should not be null");
		assertEquals(ActiveInd.ENABLE.getValue(), savedRecord.getActiveInd(), "Active indicator should remain enabled");
	}

	@Test
	void whenValidationFails_thenShouldReturnFalseWithoutDbInteraction() {
		// GIVEN: An invalid external platform (null source)
		String storeSkuId = "STORE_001_SKU_001";
		String storefrontStoreCode = "STORE_001";
		String skuId = "SKU_001";
		String skuName = "Test SKU";
		Integer busUnitId = 1;
		Integer merchantId = 1;
		Integer storeId = 1;
		MonitorPlatformGroupEnum groupEnum = MonitorPlatformGroupEnum.TMALL_RMB_HKTV_RMB;

		ExternalPlatform invalidPlatform = new ExternalPlatform();
		invalidPlatform.setSource(null); // Will fail meetMonitorCriteria
		invalidPlatform.setProductId(PRODUCT_ID);
		invalidPlatform.setSkuId(SKU_ID);

		// WHEN: Attempting to create or update with invalid data
		boolean result = productPriceMonitorProductHelper.createOrUpdateActiveMonitorRecord(
			storeSkuId, storefrontStoreCode, skuId, skuName, busUnitId, merchantId, storeId, USER_CODE, invalidPlatform, groupEnum);

		// THEN: Operation should fail without any database interaction
		assertFalse(result, "Operation should fail when validation fails");
		verify(monitorProductRepository, never()).findByStoreSkuIdAndBusUnitIdAndTargetPlatform(anyString(), anyInt(), anyString());
		verify(monitorProductRepository, never()).save(any(ProductPriceMonitorProductDo.class));
	}
}
