package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.asynctask.CreateSaveProductRecordTask;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BatchEditPriceExchangeRateServiceTest {

	@InjectMocks
	private BatchEditPriceExchangeRateService batchEditPriceExchangeRateService;

	@Mock
	private CreateSaveProductRecordTask createSaveProductRecordTask;

	@Mock
	private ExchangeRateHelper exchangeRateHelper;

	private UserDto userDto;

	@BeforeEach
	void setUp() {
		userDto = UserDto.builder()
			.userId(1)
			.userCode("ADMIN")
			.merchantId(1)
			.roleCode("ADMIN")
			.build();
	}

	@Test
	void batchEditMainlandSkuPrice_Success() {
		when(exchangeRateHelper.getExchangeRateByCurrency(any(), any())).thenReturn(BigDecimal.ONE);
		// do test
		ResponseDto<Void> response = batchEditPriceExchangeRateService.batchEditCurrencyRmbSkuPrice(userDto);

		// assert result
		assertNotNull(response);
		assertEquals(StatusCodeEnum.SUCCESS.getCode(), response.getStatus());

		// verify call method
		verify(createSaveProductRecordTask).createBatchCurrencyRmbSkuPriceRecord(userDto);
	}

	@Test
	void batchEditMainlandSkuPrice_NoPermission() {
		// mock no permission roleCode
		userDto.setRoleCode(RoleCode.MERCHANT);

		// assert throw exception
		assertThrows(SystemI18nException.class, () ->
			batchEditPriceExchangeRateService.batchEditCurrencyRmbSkuPrice(userDto));
	}

}
