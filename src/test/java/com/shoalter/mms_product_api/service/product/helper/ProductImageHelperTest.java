package com.shoalter.mms_product_api.service.product.helper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.mock.web.MockMultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ProductImageHelperTest {

	@InjectMocks
	private ProductImageHelper productImageHelper;

	@Mock
	private MessageSource messageSource;

	private byte[] validImageBytes;
	private byte[] smallImageBytes;
	private byte[] invalidImageBytes;

	@BeforeEach
	void setup() throws IOException {
		// Create a valid test image (300x200 pixels)
		BufferedImage validImage = new BufferedImage(300, 200, BufferedImage.TYPE_INT_RGB);
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ImageIO.write(validImage, "jpg", baos);
		validImageBytes = baos.toByteArray();

		// Create a small test image (100x100 pixels)
		BufferedImage smallImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
		ByteArrayOutputStream smallBaos = new ByteArrayOutputStream();
		ImageIO.write(smallImage, "jpg", smallBaos);
		smallImageBytes = smallBaos.toByteArray();

		// Create invalid image bytes (not an image)
		invalidImageBytes = "This is not an image".getBytes();
	}

	@Test
	void checkImage_validImage_shouldReturnEmptyErrorList() throws IOException {
		// Arrange
		MockMultipartFile file = new MockMultipartFile(
			"test.jpg", "test.jpg", "image/jpeg", validImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors).isEmpty();
	}

	@Test
	void checkImage_invalidImageFormat_shouldReturnError() throws IOException {
		// Arrange
		MockMultipartFile file = new MockMultipartFile(
			"test.jpg", "test.jpg", "image/jpeg", invalidImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.containsExactly("Not Photo!");
	}

	@Test
	void checkImage_imageTooLarge_shouldReturnError() throws IOException {
		// Arrange - Create a byte array larger than 8MB but with valid image content at the beginning
		byte[] largeImageBytes = new byte[9 * 1024 * 1024]; // 9MB
		System.arraycopy(validImageBytes, 0, largeImageBytes, 0, validImageBytes.length);

		MockMultipartFile file = new MockMultipartFile(
			"test.jpg", "test.jpg", "image/jpeg", largeImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.allMatch(error -> error.contains("image size should not be larger than 8 MB"));
	}

	@ParameterizedTest
	@ValueSource(strings = {"test.bmp", "test.tiff", "test.webp"})
	void checkImage_unsupportedExtension_shouldReturnError(String filename) throws IOException {
		// Arrange
		MockMultipartFile file = new MockMultipartFile(
			filename, filename, "image/bmp", validImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.allMatch(error -> error.contains("Unable to upload more than image"));
	}

	@Test
	void checkImage_imageTooSmall_shouldReturnError() throws IOException {
		// Arrange
		MockMultipartFile file = new MockMultipartFile(
			"test.jpg", "test.jpg", "image/jpeg", smallImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.allMatch(error -> error.contains("minimum image dimension ratio should be (300 * 200)"));
	}

	@Test
	void checkImage_invalidFilename_shouldReturnError() throws IOException {
		// Arrange
		String invalidFilename = "test<>?*|.jpg"; // Contains invalid characters
		MockMultipartFile file = new MockMultipartFile(
			invalidFilename, invalidFilename, "image/jpeg", validImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.allMatch(error -> error.contains("Please ensure your file name contains only letters, numbers, and the following special characters"));
	}

	@Test
	void checkImage_filenameTooLong_shouldReturnError() throws IOException {
		// Arrange
		StringBuilder longFilename = new StringBuilder();
		for (int i = 0; i < 260; i++) {
			longFilename.append("a");
		}
		longFilename.append(".jpg");

		MockMultipartFile file = new MockMultipartFile(
			longFilename.toString(), longFilename.toString(), "image/jpeg", validImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(1)
			.extracting(error -> error)
			.allMatch(error -> error.contains("Please ensure your file name contains only letters, numbers, and the following special characters"));
	}

	@Test
	void checkImage_multipleErrors_shouldReturnMultipleErrors() throws IOException {
		// Arrange - Invalid filename and extension
		String invalidFilename = "test<>?*|.bmp";
		MockMultipartFile file = new MockMultipartFile(
			invalidFilename, invalidFilename, "image/bmp", smallImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		assertThat(errors)
			.hasSize(3)
			.anySatisfy(error -> assertThat(error).contains("Unable to upload more than image"))
			.anySatisfy(error -> assertThat(error).contains("minimum image dimension ratio should be (300 * 200)"))
			.anySatisfy(error -> assertThat(error).contains("Please ensure your file name contains only letters, numbers, and the following special characters"));
	}

	@Test
	void checkImage_gifExtension_shouldBeAccepted() throws IOException {
		// Arrange
		MockMultipartFile file = new MockMultipartFile(
			"test.gif", "test.gif", "image/gif", validImageBytes);

		// Act
		List<String> errors = productImageHelper.checkImage(file);

		// Assert
		// GIF is now supported, so it should not return an error
		assertThat(errors).isEmpty();
	}
}
