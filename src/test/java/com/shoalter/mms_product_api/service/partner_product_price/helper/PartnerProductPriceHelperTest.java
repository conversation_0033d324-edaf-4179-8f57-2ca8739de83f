package com.shoalter.mms_product_api.service.partner_product_price.helper;

import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataHistoryMapper;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataMapper;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesStatusEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartnerProductPriceHelperTest {

	@Mock
	private PartnerProductPriceRepository partnerProductPriceRepository;
	@Mock
	private PartnerProductPriceHistoryRepository partnerProductPriceHistoryRepository;
	@Spy
	private PartnerProductPriceDataMapper partnerProductPriceDataMapper = Mappers.getMapper(PartnerProductPriceDataMapper.class);
	@Spy
	private PartnerProductPriceDataHistoryMapper partnerProductPriceDataHistoryMapper = Mappers.getMapper(PartnerProductPriceDataHistoryMapper.class);

	@InjectMocks
	private PartnerProductPriceHelper helper;

	@Test
	void test_savePartnerProductPriceDo_createNewRecord() {
		// Arrange
		TooniesStatusEnum status = TooniesStatusEnum.SKU_FOUND;
		BigDecimal rmb = new BigDecimal("1.15");
		String storefrontStoreCode = "ST1";
		String productCode = "P100";
		String skuCode = "SKUCODE100";
		String storeSkuId = "ST1_S_SKU100";
		BigDecimal price = new BigDecimal("20");

		// Act
		helper.savePartnerProductPriceDo(status, rmb, storefrontStoreCode, productCode, skuCode, storeSkuId, price);

		// 驗證 save 有被呼叫
		ArgumentCaptor<PartnerProductPriceDo> entityCaptor = ArgumentCaptor.forClass(PartnerProductPriceDo.class);
		verify(partnerProductPriceRepository, times(1)).save(entityCaptor.capture());

		PartnerProductPriceDo entity = entityCaptor.getValue();

		// 驗證 mapper 實際 mapping 結果
		assertEquals(85, entity.getBusUnitId());
		assertEquals("RMB", entity.getChargeCurrency());
		assertEquals("HKD", entity.getChargeConvertedCurrency());
		assertEquals(new BigDecimal("20"), entity.getChargePrice());
		assertEquals(new BigDecimal("23.00"), entity.getChargeConverted());   // 20 * 1.15
		assertEquals(new BigDecimal("4"), entity.getServiceFee());             // <=20, fee = 4
		assertEquals(new BigDecimal("4.60"), entity.getServiceFeeConverted()); // 4 * 1.15
		assertEquals(storeSkuId, entity.getStoreSkuId());
		assertEquals(skuCode, entity.getSkuCode());
		assertEquals(productCode, entity.getProductCode());
		assertEquals(price, entity.getChargePrice());
		assertEquals(ThirdPartySourceEnum.TOONIES.getValue(), entity.getSource());
		assertEquals(status.getValue(), entity.getStatus());
		assertEquals(rmb, entity.getExchangeRate());
	}


	@Test
	void test_saveHistoryAndUpdatePartnerProductPriceRecord_updateRecord_TooniesPresent() {

		PartnerProductPriceDo originalPriceDo = generateMockPriceDo();

		// mock save return
		PartnerProductPriceDo updatedEntity = new PartnerProductPriceDo();
		updatedEntity.setLastUpdateDate(LocalDateTime.of(2025, 5, 20, 0, 0, 0)); // 或用 any
		when(partnerProductPriceRepository.save(any())).thenReturn(updatedEntity);

		// mock history mapper
		PartnerProductPriceHistoryDo mockHistory = partnerProductPriceDataHistoryMapper.toCreatePartnerProductPriceHistoryDo(originalPriceDo);
		when(partnerProductPriceDataHistoryMapper.toCreatePartnerProductPriceHistoryDo(any())).thenReturn(mockHistory);

		// Act
		helper.saveHistoryAndUpdatePartnerProductPriceRecord(originalPriceDo, new BigDecimal("25"), TooniesStatusEnum.SKU_FOUND, new BigDecimal("2"));

		// assert new PartnerProductPriceDo
		ArgumentCaptor<PartnerProductPriceDo> priceCaptor = ArgumentCaptor.forClass(PartnerProductPriceDo.class);
		verify(partnerProductPriceRepository, times(1)).save(priceCaptor.capture());
		PartnerProductPriceDo updatedPrice = priceCaptor.getValue();

		assertEquals("TOONIES", updatedPrice.getSource());
		assertEquals(TooniesStatusEnum.SKU_FOUND.getValue(), updatedPrice.getStatus());
		assertEquals(new BigDecimal("2"), updatedPrice.getExchangeRate());
		assertEquals(new BigDecimal("25"), updatedPrice.getChargePrice());
		assertEquals("RMB", updatedPrice.getChargeCurrency());
		assertEquals("HKD", updatedPrice.getChargeConvertedCurrency());
		assertEquals(new BigDecimal("50.00"), updatedPrice.getChargeConverted()); // 25*2
		assertEquals(new BigDecimal("8"), updatedPrice.getServiceFee());
		assertEquals(new BigDecimal("16.00"), updatedPrice.getServiceFeeConverted());

		// Assert history
		ArgumentCaptor<PartnerProductPriceHistoryDo> historyCaptor = ArgumentCaptor.forClass(PartnerProductPriceHistoryDo.class);
		verify(partnerProductPriceHistoryRepository, times(1)).save(historyCaptor.capture());
		PartnerProductPriceHistoryDo savedHistory = historyCaptor.getValue();
		assertEquals("skuCode", savedHistory.getSkuCode());
		assertEquals(ThirdPartySourceEnum.TOONIES.getValue(), savedHistory.getSource());
		assertEquals(new BigDecimal("20"), savedHistory.getChargePrice());
		assertEquals(originalPriceDo.getCreateDate(), savedHistory.getCreateDate());
		assertEquals(originalPriceDo.getLastUpdateDate(), savedHistory.getStartDate());
		assertEquals(updatedPrice.getLastUpdateDate(), savedHistory.getEndDate());
	}

	private PartnerProductPriceDo generateMockPriceDo() {
		PartnerProductPriceDo partnerProductPriceDo = new PartnerProductPriceDo();
		partnerProductPriceDo.setId(1L);
		partnerProductPriceDo.setBusUnitId(85);
		partnerProductPriceDo.setSource(ThirdPartySourceEnum.TOONIES.getValue());
		partnerProductPriceDo.setStatus(TooniesStatusEnum.SKU_FOUND.getValue());
		partnerProductPriceDo.setExchangeRate(new BigDecimal("1.15"));
		partnerProductPriceDo.setStorefrontStoreCode("storefrontStoreCode");
		partnerProductPriceDo.setProductCode("productCode");
		partnerProductPriceDo.setSkuCode("skuCode");
		partnerProductPriceDo.setStoreSkuId("storeSkuId");
		partnerProductPriceDo.setChargeCurrency("RMB");
		partnerProductPriceDo.setChargePrice(new BigDecimal("20"));
		partnerProductPriceDo.setChargeConvertedCurrency("HKD");
		partnerProductPriceDo.setChargeConverted(new BigDecimal("23"));
		partnerProductPriceDo.setServiceFeeCurrency("RMB");
		partnerProductPriceDo.setServiceFee(new BigDecimal("4"));
		partnerProductPriceDo.setServiceFeeConvertedCurrency("HKD");
		partnerProductPriceDo.setServiceFeeConverted(new BigDecimal("4.60"));
		partnerProductPriceDo.setCreateDate(LocalDateTime.of(2025, 5, 20, 0, 0, 0));
		partnerProductPriceDo.setLastUpdateDate(LocalDateTime.of(2025, 5, 20, 0, 0, 0));
		return partnerProductPriceDo;
	}

	@Test
	void test_saveHistoryAndUpdatePartnerProductPriceRecord_updateRecord_SkuNotFound() {
		PartnerProductPriceDo originalPriceDo = generateMockPriceDo(); // chargePrice = 20

		// mock save return
		PartnerProductPriceDo updatedEntity = new PartnerProductPriceDo();
		updatedEntity.setLastUpdateDate(LocalDateTime.of(2025, 5, 20, 0, 0, 0));
		when(partnerProductPriceRepository.save(any())).thenReturn(updatedEntity);

		// mock history mapper
		PartnerProductPriceHistoryDo mockHistory = partnerProductPriceDataHistoryMapper.toCreatePartnerProductPriceHistoryDo(originalPriceDo);
		when(partnerProductPriceDataHistoryMapper.toCreatePartnerProductPriceHistoryDo(any())).thenReturn(mockHistory);

		// Act
		helper.saveHistoryAndUpdatePartnerProductPriceRecord(
			originalPriceDo,
			null,
			TooniesStatusEnum.SKU_NOT_FOUND, // FOUND → NOT_FOUND
			new BigDecimal("2")
		);

		// Assert update new PartnerProductPriceDo
		ArgumentCaptor<PartnerProductPriceDo> priceCaptor = ArgumentCaptor.forClass(PartnerProductPriceDo.class);
		verify(partnerProductPriceRepository, times(1)).save(priceCaptor.capture());
		PartnerProductPriceDo updatedPrice = priceCaptor.getValue();

		assertEquals("TOONIES", updatedPrice.getSource());
		assertEquals(TooniesStatusEnum.SKU_NOT_FOUND.getValue(), updatedPrice.getStatus());
		assertEquals(new BigDecimal("2"), updatedPrice.getExchangeRate());
		assertNull(updatedPrice.getChargePrice());
		assertEquals("RMB", updatedPrice.getChargeCurrency());
		assertEquals("HKD", updatedPrice.getChargeConvertedCurrency());
		assertNull(updatedPrice.getChargeConverted());
		assertNull(updatedPrice.getServiceFee());
		assertNull(updatedPrice.getServiceFeeConverted());

		// Assert history record
		ArgumentCaptor<PartnerProductPriceHistoryDo> historyCaptor = ArgumentCaptor.forClass(PartnerProductPriceHistoryDo.class);
		verify(partnerProductPriceHistoryRepository, times(1)).save(historyCaptor.capture());
		PartnerProductPriceHistoryDo savedHistory = historyCaptor.getValue();
		assertEquals("skuCode", savedHistory.getSkuCode());
		assertEquals(ThirdPartySourceEnum.TOONIES.getValue(), savedHistory.getSource());
		assertEquals(TooniesStatusEnum.SKU_FOUND.getValue(), savedHistory.getStatus());
		assertEquals(new BigDecimal("20"), savedHistory.getChargePrice());
	}

	@Test
	void test_savePartnerProductPriceDo_skuNotFound_chargePriceNull() {
		// Arrange
		TooniesStatusEnum status = TooniesStatusEnum.SKU_NOT_FOUND;
		BigDecimal rmb = new BigDecimal("2");
		String storefrontStoreCode = "ST2";
		String productCode = "P200";
		String skuCode = "SKUCODE200";
		String storeSkuId = "ST2_S_SKU200";
		BigDecimal price = null; // 沒有價格

		// Act
		helper.savePartnerProductPriceDo(
			status, rmb, storefrontStoreCode, productCode, skuCode, storeSkuId, price
		);

		// Assert
		ArgumentCaptor<PartnerProductPriceDo> priceCaptor = ArgumentCaptor.forClass(PartnerProductPriceDo.class);
		verify(partnerProductPriceRepository, times(1)).save(priceCaptor.capture());
		PartnerProductPriceDo saved = priceCaptor.getValue();

		assertEquals("TOONIES", saved.getSource());
		assertEquals(TooniesStatusEnum.SKU_NOT_FOUND.getValue(), saved.getStatus());
		assertEquals(rmb, saved.getExchangeRate());
		assertEquals(storefrontStoreCode, saved.getStorefrontStoreCode());
		assertEquals(productCode, saved.getProductCode());
		assertEquals(skuCode, saved.getSkuCode());
		assertEquals(storeSkuId, saved.getStoreSkuId());
		assertNull(saved.getChargePrice());
		assertEquals("RMB", saved.getChargeCurrency());
		assertEquals("HKD", saved.getChargeConvertedCurrency());
		assertNull(saved.getChargeConverted());
		assertNull(saved.getServiceFee());
		assertNull(saved.getServiceFeeConverted());
	}
}
