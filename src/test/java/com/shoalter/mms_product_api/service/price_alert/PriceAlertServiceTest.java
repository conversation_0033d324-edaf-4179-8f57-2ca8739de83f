package com.shoalter.mms_product_api.service.price_alert;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceAlertRepository;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import java.math.BigDecimal;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PriceAlertServiceTest {

    @Mock
    private ProductPriceAlertRepository productPriceAlertRepository;

    @Mock
    private MmsThirdPartySkuHelper mmsThirdPartySkuHelper;

    @Mock
    private ProductMasterHelper productMasterHelper;

    @Mock
    private TokenHelper tokenHelper;

    @InjectMocks
    private PriceAlertService priceAlertService;

    @ParameterizedTest(name = "sourcePrice={0}, targetPrice={1}, threshold={2}, expectedResult={3}")
    @CsvSource({
        // Price exceeds the threshold cases
        "110.00, 100.00, 5, true",    // 5% threshold exceeded
        "110.00, 100.00, 7, true",    // 7% threshold exceeded
        // Price equals threshold cases
        "105.00, 100.00, 5, false",   // 5% threshold equal
        "107.00, 100.00, 7, false",   // 7% threshold equal
        // Price below the threshold case
        "103.00, 100.00, 5, false",   // Below 5% threshold
        "105.00, 100.00, 7, false",   // Below 7% threshold
        "107.00, 100.00, 10, false",   // Below 10% threshold
        // Special cases
        "10.00, 0, 5, true",          // Zero target prices
        "-95.00, -100.00, 5, true"    // Negative values
    })
    void isSourcePriceOverAlertLimitation_TestCases(
            String sourcePriceStr,
            String targetPriceStr,
            String thresholdStr,
            boolean expectedResult) {
        // Given
        BigDecimal sourcePrice = new BigDecimal(sourcePriceStr);
        BigDecimal targetPrice = new BigDecimal(targetPriceStr);
        BigDecimal threshold = new BigDecimal(thresholdStr);

        // When
        boolean result = PriceAlertService.isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, threshold);

        // Then
        assertEquals(expectedResult, result,
            String.format("Source price (%s) with target price (%s) and %s%% threshold should return %s",
                sourcePriceStr, targetPriceStr, thresholdStr, expectedResult));
    }
}
