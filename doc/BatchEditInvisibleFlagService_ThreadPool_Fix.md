# BatchEditInvisibleFlagService 線程池問題修復

## 問題描述

`BatchEditInvisibleFlagService` 中的 `start` 方法遇到沒有線程可用而無法正常執行的問題，懷疑是沒有正確釋放線程導致所有線程被佔用。

## 根本原因分析

經過代碼分析，發現以下問題：

### 1. 線程池配置不完整
- `ecomEngineSyncExecutor` 線程池缺少隊列容量設置
- 沒有設置拒絕策略，當線程池滿時可能導致任務被拒絕
- 缺少線程保持時間設置，可能導致線程資源浪費

### 2. 異步任務處理問題
- 使用 `CompletableFuture.allOf().join()` 可能導致無限等待
- 沒有超時機制，當某個任務卡住時會阻塞整個流程
- 缺少異常處理和任務取消機制

### 3. 缺少監控機制
- 沒有線程池狀態監控，難以診斷問題
- 無法及時發現線程池資源耗盡的情況

## 修復方案

### 1. 完善線程池配置

在 `AsyncTaskConfig.java` 中新增 `generateThreadPoolTaskExecutorWithQueue` 方法：

```java
private static ThreadPoolTaskExecutor generateThreadPoolTaskExecutorWithQueue(int corePoolSize, int maxPoolSize) {
    ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
    threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
    threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
    // 設置隊列容量，避免無限制的任務堆積
    threadPoolTaskExecutor.setQueueCapacity(100);
    // 設置線程保持時間
    threadPoolTaskExecutor.setKeepAliveSeconds(60);
    // 設置拒絕策略：當線程池和隊列都滿時，由調用者線程執行任務
    threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    // 設置線程名稱前綴，便於調試
    threadPoolTaskExecutor.setThreadNamePrefix("ecomEngine-");
    // 等待所有任務完成後再關閉線程池
    threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
    // 設置等待時間
    threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
    // 初始化線程池
    threadPoolTaskExecutor.initialize();
    return threadPoolTaskExecutor;
}
```

### 2. 優化異步任務處理

在 `BatchEditInvisibleFlagService.java` 中：

- 添加超時機制：使用 `CompletableFuture.get(30, TimeUnit.MINUTES)` 替代 `join()`
- 添加異常處理和任務取消機制
- 檢查任務是否被取消，避免處理已取消的任務

### 3. 添加線程池監控

新增 `logThreadPoolStatus` 方法來監控線程池狀態：

```java
private void logThreadPoolStatus(String phase) {
    try {
        if (ecomEngineSyncExecutor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) ecomEngineSyncExecutor;
            log.info("ThreadPool status at {}: corePoolSize={}, maxPoolSize={}, activeCount={}, poolSize={}, queueSize={}",
                    phase,
                    executor.getCorePoolSize(),
                    executor.getMaxPoolSize(),
                    executor.getActiveCount(),
                    executor.getPoolSize(),
                    executor.getThreadPoolExecutor().getQueue().size());
        }
    } catch (Exception e) {
        log.warn("Failed to log thread pool status", e);
    }
}
```

## 修復效果

### 1. 線程池資源管理
- 設置隊列容量限制，避免無限制的任務堆積
- 使用 `CallerRunsPolicy` 拒絕策略，確保任務不會被丟棄
- 設置線程保持時間，優化資源使用

### 2. 任務執行穩定性
- 添加超時機制，避免無限等待
- 提供任務取消機制，當出現問題時可以及時停止
- 改進異常處理，提高系統穩定性

### 3. 可觀測性
- 添加線程池狀態監控，便於問題診斷
- 提供詳細的日志信息，幫助運維人員了解系統狀態

## 建議的監控指標

### 1. 線程池指標
- `activeCount`: 當前活躍線程數
- `poolSize`: 當前線程池大小
- `queueSize`: 隊列中等待的任務數
- `completedTaskCount`: 已完成的任務數

### 2. 業務指標
- 批次處理時間
- 成功/失敗的任務數量
- 超時任務數量

## 部署建議

1. **測試環境驗證**：先在測試環境部署並驗證修復效果
2. **監控配置**：配置相關的監控告警，及時發現問題
3. **漸進式部署**：建議使用藍綠部署或滾動更新方式部署
4. **回滾準備**：準備回滾方案，以防出現意外問題

## 後續優化建議

1. **動態線程池配置**：考慮使用配置中心動態調整線程池參數
2. **更細粒度的監控**：添加更多的業務指標監控
3. **性能測試**：進行壓力測試，驗證修復後的性能表現
4. **文檔更新**：更新相關的運維文檔和故障排查指南
