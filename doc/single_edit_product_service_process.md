# SingleEditProductService 流程分析與技術文件

## 概述

`SingleEditProductService` 是負責處理單一產品編輯的核心服務類。此服務提供完整的產品編輯流程，包括權限檢查、數據驗證、變體產品處理、審批流程以及與 ProductMaster 系統的集成。

## 架構設計

### 主要組件
- **入口方法**: `start()` - 提供統一的服務入口
- **核心處理**: `processing()` - 處理產品編輯的主要邏輯
- **外部集成**: `callProductMasterAndUpdateRecordRow()` - 與 ProductMaster 系統交互
- **輔助方法**: 數據檢查、照片轉換等支援功能

### 依賴服務
服務類依賴多個 Helper 類和外部服務：
- **權限管理**: `PermissionHelper`
- **產品檢查**: `CheckProductHelper`
- **記錄管理**: `SaveProductRecordHelper`, `SaveProductRecordRowHelper`
- **變體產品**: `SyncBaseProductInfoHelper`
- **數據預處理**: `ProductPreProcessingHelper`
- **審批流程**: `ApprovalDealHelper`
- **價格監控**: `ProductPriceMonitorProductHelper`
- **匯率處理**: `ExchangeRateHelper`

## 詳細流程分析

### 1. 服務入口 - start() 方法

```java
public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, SingleEditProductDto product, int saveProductType, String clientIp) {
    ResponseDto<ProductRecordResponseDto> responseDto = SpringBeanProvider.getBean(SingleEditProductService.class).processing(userDto, product, saveProductType, clientIp);
    if (StatusCodeEnum.SUCCESS.getCode() == responseDto.getStatus()) {
        SpringBeanProvider.getBean(SingleEditProductService.class).callProductMasterAndUpdateRecordRow(responseDto.getData().getRecordId());
    }
    return responseDto;
}
```

**設計特點**:
- **兩階段處理**: 先處理業務邏輯，成功後再調用 ProductMaster
- **代理模式**: 使用 `SpringBeanProvider` 獲取代理對象，確保事務正確處理
- **錯誤隔離**: 只有第一階段成功才會執行第二階段

### 2. 核心處理邏輯 - processing() 方法

#### 2.1 權限檢查

```java
Integer merchantId = (product.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : product.getProduct().getMerchantId();
permissionHelper.checkPermission(userDto, merchantId);
```

**目的**: 確保用戶有權限編輯指定商戶的產品

#### 2.2 變體產品檢查

```java
boolean hasMatrix = CollectionUtil.isNotEmpty(product.getVariantSkuProductList());
if (hasMatrix) {
    List<String> skuList = product.getVariantSkuProductList().stream().map(VariantMatrixProductDto::getSkuId).collect(Collectors.toList());
    if (product.getProduct().getAdditional().getHktv() != null) {
        CheckProductResultDto checkSkuIsExistResult = checkProductHelper.checkProductSkuExistsInStore(userDto, product.getProduct().getAdditional().getHktv().getStores(), skuList);
        if (CollectionUtil.isNotEmpty(checkSkuIsExistResult.getErrorMessageList())) {
            return ResponseDto.<ProductRecordResponseDto>builder().status(StatusCodeEnum.FAIL.getCode()).errorMessageList(checkSkuIsExistResult.getErrorMessageList()).build();
        }
    }
}
```

**目的**: 
- 檢查是否為變體產品（Matrix 產品）
- 驗證變體 SKU 在店鋪中的存在性
- 提前終止不符合條件的請求

#### 2.3 照片格式轉換

```java
convertHktvProductPhoto(product);
```

**目的**: 標準化產品照片格式，移除特定的尺寸標識

#### 2.4 記錄創建

```java
SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, saveProductType, 
    String.format(SaveProductRecordHelper.EDIT_PRODUCT_FILE_NAME, product.getProduct().getSkuId(), System.currentTimeMillis()), 
    SaveProductStatus.WAIT_START, clientIp);
SaveProductRecordRowDo editeRow = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), product, SaveProductStatus.WAIT_START, null);
product.getProduct().setRecordRowId(editeRow.getId());
```

**目的**: 
- 創建編輯記錄用於追踪
- 初始狀態設為 `WAIT_START`
- 建立產品與記錄行的關聯

#### 2.5 變體產品處理

```java
if (hasMatrix) {
    createSaveProductRecordTask.generateMatrixProduct(userDto, product, clientIp);
}
```

**目的**: 為變體產品生成相關的矩陣數據

#### 2.6 產品數據檢查與生成

```java
boolean isCheckProductsSuccess = checkAndGenerateProductData(userDto, editeRow, saveProductRecordDo);
```

這是最核心的處理邏輯，包含多個子流程。

#### 2.7 變體產品同步處理

```java
if (isCheckProductsSuccess) {
    ProductMasterDto baseProduct = gson.fromJson(editeRow.getContent(), SingleEditProductDto.class).getProduct();
    // 處理變體產品關係
    Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair = syncBaseProductInfoHelper.findVariantProductsFromProductMaster(userDto, List.of(baseProduct));
    Pair<Boolean, String> passCheck = syncBaseProductInfoHelper.variantProductCheckAndUpdateRecordRows(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);
    
    if (!passCheck.getLeft()) {
        throw new BadRequestException(passCheck.getRight());
    }
    
    boolean createVariant = syncBaseProductInfoHelper.handlePrimarySkuAndAddVariantProduct(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);
    if (createVariant) {
        saveProductRecordDo.setFileName(String.format(SaveProductRecordHelper.VARIANT_EDIT_PRODUCT_FILE_NAME, baseProduct.getSkuId(), System.currentTimeMillis()));
    }
}
```

**目的**: 
- 處理變體產品之間的關係
- 檢查和更新主要 SKU 狀態
- 必要時創建新的變體產品

### 3. 產品數據檢查與生成 - checkAndGenerateProductData() 方法

這是整個編輯流程最複雜的部分，包含多個關鍵步驟：

#### 3.1 產品存在性驗證

```java
ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(row.getUuid())).build();
List<ProductMasterResultDto> productList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
ProductMasterResultDto beforeProduct = productList.get(0);

if (CollectionUtil.isEmpty(productList)) {
    setRecordAndRowFail(row, saveProductRecordDo, StringUtil.generateErrorMessage(List.of(messageSource.getMessage("message127", null, null))));
    isCheckProductsSuccess = false;
} else if (beforeProduct.getAdditional().getHktv() == null) {
    setRecordAndRowFail(row, saveProductRecordDo, StringUtil.generateErrorMessage(List.of(messageSource.getMessage("message227", null, null))));
    isCheckProductsSuccess = false;
}
```

**目的**: 
- 通過 UUID 查詢產品原始數據
- 確保產品存在且包含 HKTV 相關數據
- 失敗時設置錯誤狀態和訊息

#### 3.2 數據預處理

```java
CheckProductResultDto check3plResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, saveProductRecordDo, row, beforeProduct);
generateIIDSDataHelper.generateIIDSData(row);
```

**目的**: 
- 執行 HKTV 產品的預處理邏輯
- 生成 IIDS 相關數據

#### 3.3 促銷活動檢查

```java
String storeSkuId = beforeProduct.getAdditional().getHktv().getStoreSkuId();
List<MembershipPricingEventSetDto> checkPricingResults = promotionHelper.checkMembershipPricingEventSet(userDto, List.of(storeSkuId));
```

**目的**: 檢查產品是否參與會員價格活動

#### 3.4 業務規則檢查

```java
ResponseDto<Void> checkResult;
if (saveProductRecordDo.getUploadType() == SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT) {
    checkResult = checkEditProductFromPromotionContractHelper.checkEditProductHandler(userDto, row);
} else {
    ProductDo productDo = productRepository.findByUuid(row.getUuid()).get();
    MembershipPricingEventSetDto checkPricingResult = checkPricingResults == null ? null : checkPricingResults.get(0);
    BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
    checkResult = checkProductHelper.checkEditProductHandler(userDto, saveProductRecordDo, row, check3plResult, beforeProduct, productDo, checkPricingResult, rmbRate);
    
    // 價格監控處理
    productPriceMonitorProductHelper.priceMonitorUpdateProcess(row, beforeProduct, userDto);
    
    // 審批流程檢查
    checkApprovalDealResult = approvalDealHelper.processAndCheckApproval(userDto, row, beforeProduct, ApprovalDealTypeEnum.COMMISSION_RATE);
    if (CollectionUtil.isNotEmpty(checkApprovalDealResult.getErrorMessage())) {
        checkResult.getErrorMessageList().addAll(checkApprovalDealResult.getErrorMessage());
        checkResult = ResponseDto.fail(checkResult.getErrorMessageList());
    }
}
```

**特殊邏輯分支**:
- **促銷合同編輯**: 使用特殊的檢查邏輯
- **一般編輯**: 執行完整的業務規則檢查，包括：
  - 產品數據驗證
  - 價格監控處理
  - 佣金率審批檢查
  - 匯率計算

#### 3.5 審批處理

```java
if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
    if (checkApprovalDealResult != null && checkApprovalDealResult.isNeedToApproval()) {
        row = approvalDealHelper.convertWaitingApprovalFieldsFromOriginalValue(row, beforeProduct, ApprovalDealTypeEnum.COMMISSION_RATE);
    }
    prepareSendToPmData(row, saveProductRecordDo);
}
```

**目的**: 
- 檢查通過後處理審批需求
- 需要審批時轉換為等待審批的字段值
- 準備發送到 ProductMaster 的數據

### 4. ProductMaster 集成 - callProductMasterAndUpdateRecordRow() 方法

```java
@Transactional
public void callProductMasterAndUpdateRecordRow(Long recordId) {
    SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
    List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.REQUESTING_PM);
    
    if (rows.isEmpty()) {
        saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
        return;
    }

    ProductMasterResponseDto productMasterResponseDto = checkRequestPMRecordProductTask.requestSendProductToProductMaster(saveProductRecordDo, rows);
    saveProductRecordHelper.updateRecordByProductMasterResult(productMasterResponseDto, saveProductRecordDo, rows);
}
```

**目的**: 
- 查詢待處理的記錄行
- 調用 ProductMaster API 進行數據同步
- 根據返回結果更新記錄狀態

### 5. 輔助方法

#### 5.1 照片格式轉換

```java
private void convertHktvProductPhoto(SingleEditProductDto product) {
    HktvProductDto hktvProductDto = product.getProduct().getAdditional().getHktv();
    if (hktvProductDto != null) {
        // 處理主要照片
        if (StringUtil.isNotEmpty(hktvProductDto.getMainPhoto())) {
            hktvProductDto.setMainPhoto(convertPhoto(hktvProductDto.getMainPhoto()));
        }
        // 處理廣告照片
        if (StringUtil.isNotEmpty(hktvProductDto.getAdvertisingPhoto())) {
            hktvProductDto.setAdvertisingPhoto(convertPhoto(hktvProductDto.getAdvertisingPhoto()));
        }
        // 處理變體產品照片
        if (CollectionUtil.isNotEmpty(hktvProductDto.getVariantProductPhoto())) {
            hktvProductDto.setVariantProductPhoto(hktvProductDto.getVariantProductPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
        }
        // 處理其他照片
        if (CollectionUtil.isNotEmpty(hktvProductDto.getOtherPhoto())) {
            hktvProductDto.setOtherPhoto(hktvProductDto.getOtherPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
        }
    }
}

private String convertPhoto(String photo) {
    return ResourceUtil.existsImageDomain(photo) && photo.lastIndexOf("_1200.") != -1 ?
        photo.substring(0, photo.lastIndexOf("_1200.")) + photo.substring(photo.lastIndexOf(".")) :
        photo;
}
```

**目的**: 移除照片 URL 中的 "_1200" 尺寸標識，標準化照片格式

#### 5.2 失敗處理

```java
private void setRecordAndRowFail(SaveProductRecordRowDo row, SaveProductRecordDo saveProductRecordDo, String errorMessage) {
    row.setErrorMessage(errorMessage);
    row.setStatus(SaveProductStatus.FAIL);
    saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
}
```

**目的**: 統一設置記錄和記錄行的失敗狀態

#### 5.3 PM 數據準備

```java
private void prepareSendToPmData(SaveProductRecordRowDo row, SaveProductRecordDo saveProductRecordDo) {
    checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
    saveProductHelper.setFieldValueNullByRule(row);
    row.setStatus(SaveProductStatus.REQUESTING_PM);
}
```

**目的**: 
- 檢查和更新業務單元列表
- 根據規則設置字段為 null
- 更新狀態為請求 PM

## 狀態流轉

產品編輯過程中的狀態變化：

1. **WAIT_START** - 初始狀態，等待開始處理
2. **PROCESSING** - 處理中（變體產品相關）
3. **REQUESTING_PM** - 請求 ProductMaster 處理
4. **SUCCESS** - 成功完成
5. **FAIL** - 處理失敗

## 錯誤處理策略

### 1. 早期驗證
- 權限檢查失敗立即返回
- 變體產品 SKU 不存在立即返回

### 2. 階段性檢查
- 每個處理階段都有獨立的錯誤檢查
- 失敗時設置詳細的錯誤訊息

### 3. 事務回滾
- 使用 `@Transactional` 確保數據一致性
- 失敗時自動回滾所有變更

## 特殊處理場景

### 1. 促銷合同編輯
- 使用專門的檢查邏輯 `CheckEditProductFromPromotionContractHelper`
- 跳過一般的業務規則檢查

### 2. 變體產品處理
- 檢查變體 SKU 存在性
- 處理主要 SKU 和變體 SKU 的關係
- 必要時創建新的變體產品記錄

### 3. 審批流程
- 檢查是否需要審批（如佣金率變更）
- 需要審批時轉換為等待審批狀態的數據

### 4. 價格監控
- 自動觸發價格監控更新流程
- 記錄價格變化歷史

## 性能考量

### 1. 異步處理
- ProductMaster 調用採用異步模式
- 避免長時間阻塞用戶請求

### 2. 批量操作
- 變體產品批量處理
- 記錄行批量查詢和更新

### 3. 緩存利用
- 匯率信息可能有緩存
- 權限檢查結果可能有緩存

## 監控與日誌

### 1. 操作日誌
```java
log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", 
    saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 
    1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
```

### 2. 錯誤追踪
- 詳細的錯誤訊息記錄在記錄行中
- 支持多語言錯誤訊息

## 擴展性設計

### 1. 策略模式
- 不同的編輯類型使用不同的檢查策略
- 易於添加新的編輯類型

### 2. Helper 類分離
- 各個功能模塊獨立成 Helper 類
- 便於單元測試和維護

### 3. 配置驅動
- 業務規則通過配置控制
- 支持動態調整檢查邏輯

## 注意事項

1. **事務管理**: 方法使用適當的事務註解，確保數據一致性
2. **異步處理**: ProductMaster 調用是異步的，需要通過記錄狀態追踪結果
3. **權限安全**: 每次操作都會進行權限檢查
4. **數據完整性**: 多重驗證確保數據質量
5. **錯誤恢復**: 失敗記錄保留詳細信息供分析和重試

此服務是產品管理系統中的核心組件，提供了完整的單產品編輯解決方案，支持複雜的業務邏輯和多系統集成。
