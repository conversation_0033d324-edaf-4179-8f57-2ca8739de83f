# CheckProductHelper.checkEditProductHandler 方法技術分析文檔

## 概述

`checkEditProductHandler` 是產品編輯功能中的核心驗證方法，負責對編輯產品請求進行全面的業務邏輯檢查和數據驗證。本文檔詳細說明該方法的執行流程、驗證邏輯以及工程師在開發和問題追蹤時需要理解的關鍵細節。

## 方法簽名與參數

```java
public ResponseDto<Void> checkEditProductHandler(
    UserDto userDto,                               // 用戶信息
    SaveProductRecordDo productRecord,             // 產品記錄
    SaveProductRecordRowDo row,                    // 記錄行數據
    CheckProductResultDto check3PlResult,          // 第三方檢查結果
    ProductMasterResultDto beforeProductFromPm,    // ProductMaster 中的原始產品數據
    ProductDo beforeProductFromDb,                  // 數據庫中的原始產品數據
    MembershipPricingEventSetDto checkPricingResult, // 定價檢查結果
    BigDecimal rmbRate                             // 人民幣匯率
)
```

## 執行流程圖

```mermaid
graph TD
    A[開始 checkEditProductHandler] --> B[解析 JSON 數據]
    B --> C[檢查 HKTV 配置]
    C --> D{HKTV 配置存在?}
    D -->|否| E[返回成功 - 跳過驗證]
    D -->|是| F[驗證產品存在性]
    F --> G{原始產品存在?}
    G -->|否| H[返回錯誤 - 產品不存在]
    G -->|是| I[驗證 SKU/Product ID 一致性]
    I --> J{ID 一致性檢查}
    J -->|失敗| K[返回錯誤 - ID 不一致]
    J -->|成功| L[查找店鋪信息]
    L --> M{店鋪存在?}
    M -->|否| N[返回錯誤 - 店鋪不存在]
    M -->|是| O[查找主分類]
    O --> P{主分類存在?}
    P -->|否| Q[返回錯誤 - 分類不存在]
    P -->|是| R[生成 ProductCheckDto]
    R --> S[執行 checkEditProduct]
    S --> T[構建回應結果]
    T --> U[結束]
```

## 詳細執行步驟

### 第一步：數據解析與初始化檢查

```java
SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
ProductMasterDto productMasterDto = singleEditProductDto.getProduct();

if (productMasterDto.getAdditional().getHktv() == null) {
    return ResponseDto.success(null);
}
```

**目的**：
- 將 JSON 字符串反序列化為 Java 對象
- 檢查是否有 HKTV 相關配置
- 如果沒有 HKTV 配置，直接返回成功（表示該產品不需要 HKTV 相關驗證）

**開發注意事項**：
- 確保 JSON 格式正確，否則會拋出異常
- HKTV 配置為 null 是合法的業務場景

### 第二步：產品存在性與一致性驗證

```java
if (beforeProductFromDb != null) {
    boolean isSameMerchantId = beforeProductFromDb.getMerchantId().equals(productMasterDto.getMerchantId());
    if (StringUtil.isNotEquals(beforeProductFromDb.getSkuCode(), productMasterDto.getSkuId()) && isSameMerchantId) {
        return ResponseDto.fail(List.of(messageSource.getMessage("message105", null, null)));
    }
    if (StringUtil.isNotEquals(beforeProductFromDb.getProductCode(), productMasterDto.getProductId()) && isSameMerchantId) {
        return ResponseDto.fail(List.of(messageSource.getMessage("message222", null, null)));
    }
} else {
    return ResponseDto.fail(List.of(messageSource.getMessage("message51", null, null)));
}
```

**驗證邏輯**：
1. **產品存在性檢查**：`beforeProductFromDb` 不能為空
2. **SKU 一致性檢查**：相同商戶下，不允許修改 SKU Code
3. **Product ID 一致性檢查**：相同商戶下，不允許修改 Product ID

**錯誤碼對應**：
- `message105`：SKU Code 不一致錯誤
- `message222`：Product ID 不一致錯誤  
- `message51`：產品不存在錯誤

**問題追蹤點**：
- 如果出現一致性錯誤，檢查前端是否正確傳遞了產品 ID
- 確認數據庫中的產品記錄是否完整

### 第三步：店鋪信息驗證

```java
StoreDo store = storeRepository.findHktvStoreByStoreCode(
    productMasterDto.getAdditional().getHktv().getStores()
).orElse(null);

if (store == null) {
    return ResponseDto.fail(List.of(messageSource.getMessage("message69", null, null)));
}
```

**驗證邏輯**：
- 根據 Store Code 查找對應的 HKTV 店鋪
- 店鋪必須存在且為 HKTV 類型

**錯誤碼**：
- `message69`：店鋪不存在或非 HKTV 店鋪

**問題追蹤點**：
- 檢查 `productMasterDto.getAdditional().getHktv().getStores()` 的值
- 確認 `store` 表中是否有對應記錄且類型正確

### 第四步：主分類驗證

```java
Optional<BuProductCategoryDo> primaryCategory = buProductCategoryHelper.getBuProductCategoryDo(
    productMasterDto, 
    beforeProductFromPm.getAdditional().getHktv()
);

if (primaryCategory.isEmpty()) {
    return ResponseDto.fail(List.of(messageSource.getMessage("message49", 
        new String[]{productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode()}, null)));
}
```

**驗證邏輯**：
- 通過 Helper 查找主分類信息
- 主分類必須存在且有效

**錯誤碼**：
- `message49`：主分類不存在（會顯示具體的分類代碼）

**問題追蹤點**：
- 檢查分類代碼是否正確
- 確認 `bu_product_category` 表中是否有對應記錄

### 第五步：構建檢查對象

```java
boolean isNewProduct = false;
boolean isOfflineDueToRollback = productMasterDto.getAdditional().getHktv().getOfflineDueToRollback() != null 
    && productMasterDto.getAdditional().getHktv().getOfflineDueToRollback();

ProductCheckDto productCheckDo = ProductCheckDto.generateProductCheckDo(
    productMasterDto, 
    store, 
    primaryCategory.get(), 
    isNewProduct, 
    isOfflineDueToRollback,
    beforeProductFromPm.getAdditional().getHktv(), 
    check3PlResult, 
    checkPricingResult
);
```

**關鍵參數**：
- `isNewProduct = false`：編輯產品時為 false
- `isOfflineDueToRollback`：是否因回滾而下線
- 整合所有必要的檢查參數

### 第六步：執行詳細驗證

```java
CheckProductResultDto checkProductResultDto = checkEditProduct(
    userDto, 
    productCheckDo, 
    beforeProductFromDb, 
    beforeProductFromPm, 
    productRecord, 
    rmbRate
);
```

這裡會調用 `checkEditProduct` 方法，該方法根據不同的 `uploadType` 執行不同的驗證邏輯。

## checkEditProduct 方法的驗證分類

### 按編輯類型分類的驗證邏輯

#### 1. BATCH_EDIT_PRODUCT_VISIBILITY (可見性編輯)
```java
case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
    errorMessageList.addAll(checkVisibility(
        productCheckDto.getVisibility(), 
        productCheckDto.getReadyMethodCode(), 
        existProductDo.getStatus()
    ).getErrorMessageList());
    break;
```
**驗證內容**：產品可見性設置的合法性

#### 2. BATCH_EDIT_PRODUCT_ONLINE_STATUS (上線狀態編輯)
```java
case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
    errorMessageList.addAll(checkContractProdTerm(
        productCheckDto.getInsuranceContractProdTermName(), 
        productCheckDto.getContractId(),
        productCheckDto.getStore().getId(), 
        productCheckDto.getReadyMethodCode(), 
        productCheckDto.getSkuId(), 
        productCheckDto.getPrimaryCategory(), 
        productCheckDto.getBrandId()
    ).getErrorMessageList());
    break;
```
**驗證內容**：合約產品條款的有效性

#### 3. BATCH_EDIT_PRODUCT_PRICE (價格編輯)
```java
case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
    contractType = contractRepository.findMainContractTypeInContract(productCheckDto.getContractId());
    errorMessageList.addAll(checkPriceRelateFields(...).getErrorMessageList());
    errorMessageList.addAll(checkProductPriceUpdateDuringPromotion(...).getErrorMessageList());
    errorMessageList.addAll(checkOriginalPriceAndSellingPrice(...).getErrorMessageList());
    errorMessageList.addAll(checkPlusPrice(...).getErrorMessageList());
    // ...其他價格相關檢查
    break;
```
**驗證內容**：
- 價格相關欄位的合法性
- 促銷期間價格更新限制
- 原價與售價關係
- Plus 價格邏輯
- 折扣文字驗證

#### 4. BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION (包裝尺寸編輯)
```java
case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
    errorMessageList.addAll(checkPackingInformation(...).getErrorMessageList());
    errorMessageList.addAll(checkPackingBoxType(...).getErrorMessageList());
    errorMessageList.addAll(checkPackingSpec(...).getErrorMessageList());
    errorMessageList.addAll(checkCartonSize(...).getErrorMessageList());
    // ...其他包裝相關檢查
    break;
```
**驗證內容**：
- 包裝信息完整性
- 包裝盒類型適配性
- 包裝規格描述
- 紙箱尺寸合理性

#### 5. 完整產品編輯 (預設情況)
```java
default:
    // BATCH_EDIT_PRODUCT only mms1.0 call mms2.0 use
    HktvProductDto beforeHktvProductDto = beforeProduct.getAdditional().getHktv();
    errorMessageList.addAll(checkAllProduct(...).getErrorMessageList());
    errorMessageList.addAll(checkProductPackingAndBarcode(...).getErrorMessageList());
    errorMessageList.addAll(checkProductPriceUpdateDuringPromotion(...).getErrorMessageList());
    errorMessageList.addAll(checkPrimarySku(...).getErrorMessageList());
    errorMessageList.addAll(checkPackageConfirmed(...).getErrorMessageList());
    errorMessageList.addAll(checkBarcodeLock(...).getErrorMessageList());
    errorMessageList.addAll(checkContractProdTerm(...).getErrorMessageList());
    errorMessageList.addAll(checkSkuNameContainEmoji(...).getErrorMessageList());
    errorMessageList.addAll(checkEditMallDollar(...).getErrorMessageList());
    errorMessageList.addAll(checkEditEwSku(...).getErrorMessageList());
    errorMessageList.addAll(checkUpdateEverutsField(...).getErrorMessageList());
    errorMessageList.addAll(checkCannotChangeFields(...).getErrorMessageList());
    errorMessageList.addAll(checkMembershipPricingEventSet(...).getErrorMessageList());
    errorMessageList.addAll(checkPlusPrice(...).getErrorMessageList());
    errorMessageList.addAll(checkProductReadyMethodWithPrimaryCategoryOrBrandModified(...).getErrorMessageList());
    break;
```

**適用場景**：
- `BATCH_EDIT_PRODUCT`：MMS 1.0 調用 MMS 2.0 時使用
- 完整產品編輯功能
- 需要進行全面驗證的場景

## 關鍵驗證方法解析

### checkAllProduct 方法
執行產品的全面驗證，包括：
- 產品 ID 格式檢查
- 產品準備方法驗證
- SKU 名稱檢查
- 品牌審核狀態
- 商城金額驗證
- 倉庫信息檢查
- 影片連結驗證
- 主分類驗證
- 價格相關檢查

### 驗證方法的通用模式
```java
private CheckProductResultDto checkXXX(...) {
    List<String> errorMessage = new ArrayList<>();
    
    // 具體驗證邏輯
    if (validationFails) {
        errorMessage.add(messageSource.getMessage("messageXXX", params, null));
    }
    
    return CheckProductResultDto.generate(errorMessage);
}
```

## 預設檢查 (Default Case) 詳細內容

當 `uploadType` 不匹配任何特定類型時，系統會執行完整的產品編輯驗證，這是最全面的檢查模式。以下是所有檢查項目的詳細說明：

### 第一層：基礎產品驗證 (checkAllProduct)

`checkAllProduct` 方法包含以下 30+ 項基礎檢查：

#### 1. 產品標識檢查
- **產品 ID 檢查** (`checkProductId`)：驗證產品 ID 格式的合法性
- **SKU ID 檢查** (`checkSkuId`)：驗證 SKU ID 格式和唯一性

#### 2. 產品基本信息檢查
- **產品準備方法** (`checkProductReadyMethod`)：驗證準備方法與第三方檢查結果的一致性
- **SKU 名稱檢查** (`checkSkuName`)：驗證英文、繁體中文、簡體中文名稱的合法性
- **品牌審核狀態** (`checkBrandApproved`)：確認品牌已通過審核
- **商城金額檢查** (`checkMallDollar`)：驗證 Mall Dollar 和 VIP Mall Dollar 的設置

#### 3. 倉儲與物流檢查
- **倉庫檢查** (`checkWarehouse`)：驗證倉庫 ID 與業務代碼、準備方法的匹配性
- **配送方法檢查** (`checkDeliveryMethod`)：驗證配送方法與準備方法、合約類型的相容性
- **重量限制檢查** (`checkWeightLimitByDeliveryMethod`)：根據配送方法檢查重量限制
- **取貨時段檢查** (`checkTimeSlot`)：驗證取貨時段設置的合理性
- **取貨天數檢查** (`checkPickupDay`)：檢查取貨天數與店鋪、準備方法的匹配

#### 4. 分類與商品屬性檢查
- **主分類檢查** (`checkPrimaryCategory`)：驗證主分類與業務代碼、產品類型的匹配
- **地標分類檢查** (`checkProductLandMarkCat`)：檢查地標分類設置
- **產品類型檢查** (`checkProductType`)：驗證產品類型代碼列表
- **尺寸系統檢查** (`checkSizeSystem`)：驗證尺寸系統與尺寸的匹配
- **顏色家族檢查** (`checkColorFamily`)：驗證顏色家族與顏色的匹配

#### 5. 價格與財務檢查
- **價格相關欄位檢查** (`checkPriceRelateFields`)：驗證原價、成本、貨幣等價格信息
- **最小保質期檢查** (`checkMinimumShelfLife`)：驗證最小保質期設置

#### 6. 多媒體內容檢查
- **影片連結檢查** (`checkVideoLink`, `checkVideoLink2-5`)：驗證最多 5 個影片連結的格式
- **影片連結文字檢查** (`checkVideoLinkText`, `checkVideoLinkText2-5`)：驗證對應的影片連結文字
- **產品描述檢查** (`checkDescription`)：驗證短描述和長描述的內容
- **產品上傳數據檢查** (`checkProductDataForUpload`)：驗證主圖、其他圖片、變體圖片、廣告圖片

#### 7. 選項與變體檢查
- **選項欄位與值檢查** (`checkOptionFieldAndValue`)：驗證最多 3 組選項欄位和值的匹配

#### 8. 服務與合約檢查
- **移除服務檢查** (`checkRemovalService`)：驗證移除服務與產品類型的匹配
- **海外配送地區檢查** (`checkOverseaDeliveryDistrict`)：驗證海外配送地區設置
- **電子券與合約類型檢查** (`checkEvoucherAndContractType`)：驗證電子券產品的合約類型
- **保險與合約類型檢查** (`checkInsuranceAndContractType`)：驗證保險產品的合約類型

#### 9. 商戶與權限檢查
- **批次品牌檢查** (`checkBatchBrand`)：驗證批次操作中的品牌一致性
- **緊急標誌檢查** (`checkUrgentFlagUpdatedByMerchant`)：檢查商戶更新緊急標誌的權限
- **購買銷售店檢查** (`checkBuySellStore`)：驗證購買銷售店的設置
- **虛擬店檢查** (`checkVirtualStore`)：驗證虛擬店設置
- **用戶最大值檢查** (`checkUserMax`)：檢查用戶最大購買限制

#### 10. 儲存與準備檢查
- **儲存類型檢查** (`checkStorageType`)：驗證儲存類型設置
- **產品準備天數檢查** (`checkProductReadyDays`)：驗證產品準備天數
- **準備方法與包裝盒類型檢查** (`checkReadyMethodAndPackingBoxType`)：驗證兩者的匹配性

### 第二層：專項檢查

除了 `checkAllProduct` 的基礎檢查外，預設模式還包含以下專項檢查：

#### 1. 包裝與條碼檢查 (`checkProductPackingAndBarcode`)
```java
errorMessageList.addAll(checkProductPackingAndBarcode(
    productCheckDto.getBuCode(), 
    productCheckDto.getReadyMethodCode(), 
    productCheckDto.getPrimaryCategory(),
    productCheckDto.getPackingBoxTypeCode(), 
    productCheckDto.getWeight(), 
    productCheckDto.getHeight(), 
    productCheckDto.getDepth(), 
    productCheckDto.getLength(),
    productCheckDto.getStorageTemperature(), 
    productCheckDto.getBarcodeDtoList(), 
    productCheckDto.getWeightUnit(), 
    productCheckDto.getPackingDimensionUnit()
).getErrorMessageList());
```
**檢查內容**：
- 包裝信息完整性
- 條碼格式與唯一性
- 包裝尺寸合理性
- 重量單位與尺寸單位一致性
- 儲存溫度與包裝類型匹配

#### 2. 促銷期間價格更新檢查 (`checkProductPriceUpdateDuringPromotion`)
```java
errorMessageList.addAll(checkProductPriceUpdateDuringPromotion(
    existProductDo.getUuid(), 
    productCheckDto, 
    beforeProduct
).getErrorMessageList());
```
**檢查內容**：
- 檢查產品是否在促銷期間
- 限制促銷期間的價格修改
- 確保促銷價格邏輯正確

#### 3. 主 SKU 檢查 (`checkPrimarySku`)
```java
errorMessageList.addAll(checkPrimarySku(
    existProductDo, 
    productCheckDto.getIsPrimarySku(), 
    productRecord.getUploadType()
).getErrorMessageList());
```
**檢查內容**：
- 驗證主 SKU 標記的正確性
- 確保產品組中只有一個主 SKU
- 檢查主 SKU 變更的合法性

#### 4. 包裝確認檢查 (`checkPackageConfirmed`)
```java
errorMessageList.addAll(checkPackageConfirmed(
    beforeProduct, 
    productCheckDto.getDepth(), 
    productCheckDto.getHeight(),
    productCheckDto.getLength(), 
    productCheckDto.getWeight(), 
    productCheckDto.getPackingDimensionUnit(), 
    productCheckDto.getWeightUnit()
).getErrorMessageList());
```
**檢查內容**：
- 檢查包裝是否已確認
- 限制已確認包裝的修改
- 驗證包裝尺寸變更的合理性

#### 5. 條碼鎖定檢查 (`checkBarcodeLock`)
```java
errorMessageList.addAll(checkBarcodeLock(
    beforeProduct, 
    productCheckDto.getBarcodeDtoList()
).getErrorMessageList());
```
**檢查內容**：
- 檢查條碼是否被鎖定
- 限制鎖定條碼的修改
- 驗證條碼變更權限

#### 6. 合約產品條款檢查 (`checkContractProdTerm`)
```java
errorMessageList.addAll(checkContractProdTerm(
    productCheckDto.getInsuranceContractProdTermName(), 
    productCheckDto.getContractId(),
    productCheckDto.getStore().getId(), 
    productCheckDto.getReadyMethodCode(), 
    productCheckDto.getSkuId(), 
    productCheckDto.getPrimaryCategory(), 
    productCheckDto.getBrandId()
).getErrorMessageList());
```
**檢查內容**：
- 驗證保險合約產品條款
- 檢查合約與產品的匹配性
- 確保合約條款的有效性

#### 7. SKU 名稱表情符號檢查 (`checkSkuNameContainEmoji`)
```java
errorMessageList.addAll(checkSkuNameContainEmoji(
    productCheckDto.getSkuNameEn(), 
    productCheckDto.getSkuNameCh(), 
    productCheckDto.getSkuNameSc()
).getErrorMessageList());
```
**檢查內容**：
- 檢查 SKU 名稱中是否包含表情符號
- 驗證多語言名稱的一致性

#### 8. 商城金額編輯檢查 (`checkEditMallDollar`)
```java
errorMessageList.addAll(checkEditMallDollar(
    beforeHktvProductDto.getMallDollar(), 
    beforeHktvProductDto.getVipMallDollar(), 
    productCheckDto.getMallDollar(), 
    productCheckDto.getVipMallDollar()
).getErrorMessageList());
```
**檢查內容**：
- 限制 Mall Dollar 的修改
- 確保 VIP Mall Dollar 邏輯正確

#### 9. EW SKU 編輯檢查 (`checkEditEwSku`)
```java
errorMessageList.addAll(checkEditEwSku(
    productCheckDto.getPrimaryCategoryCode(), 
    beforeHktvProductDto.getPrimaryCategoryCode()
).getErrorMessageList());
```
**檢查內容**：
- 檢查 EW SKU 的編輯限制
- 驗證主分類變更對 EW SKU 的影響

#### 10. Everuts 欄位更新檢查 (`checkUpdateEverutsField`)
```java
errorMessageList.addAll(checkUpdateEverutsField(
    productCheckDto, 
    beforeProduct
).getErrorMessageList());
```
**檢查內容**：
- 驗證 Everuts 相關欄位的更新
- 檢查 Everuts 數據的一致性

#### 11. 不可變更欄位檢查 (`checkCannotChangeFields`)
```java
CheckProductResultDto checkCannotChangeFieldsResult = checkCannotChangeFields(
    productCheckDto, 
    existProductDo
);
errorMessageList.addAll(checkCannotChangeFieldsResult.getErrorMessageList());
```
**檢查內容**：
- 檢查系統定義的不可變更欄位
- 防止關鍵欄位的意外修改
- 確保數據完整性

#### 12. 會員定價事件檢查 (`checkMembershipPricingEventSet`)
```java
CheckProductResultDto checkMembershipPricingEventSetResultDto = checkMembershipPricingEventSet(
    userDto, 
    productCheckDto.getDeliveryDistrictList(), 
    productCheckDto.getStore().getStorefrontStoreCode(), 
    productCheckDto.getSkuId(), 
    productCheckDto.getCheckPricingResult()
);
errorMessageList.addAll(checkMembershipPricingEventSetResultDto.getErrorMessageList());
```
**檢查內容**：
- 驗證會員定價事件設置
- 檢查配送地區與定價的匹配
- 確保定價邏輯正確

#### 13. Plus 價格檢查 (`checkPlusPrice`)
```java
CheckProductResultDto checkPlusPriceResultDto = checkPlusPrice(
    productCheckDto.getOriginalPrice(), 
    productCheckDto.getSellingPrice(), 
    productCheckDto.getCheckPricingResult()
);
errorMessageList.addAll(checkPlusPriceResultDto.getErrorMessageList());
```
**檢查內容**：
- 驗證 Plus 會員價格邏輯
- 檢查原價與售價的關係
- 確保定價策略正確

#### 14. 產品準備方法與分類/品牌修改檢查 (`checkProductReadyMethodWithPrimaryCategoryOrBrandModified`)
```java
CheckProductResultDto checkProductReadyMethodWithPrimaryCategoryOrBrandModifiedResult = 
    checkProductReadyMethodWithPrimaryCategoryOrBrandModified(productCheckDto, beforeProduct);
errorMessageList.addAll(checkProductReadyMethodWithPrimaryCategoryOrBrandModifiedResult.getErrorMessageList());
```
**檢查內容**：
- 檢查主分類或品牌修改對準備方法的影響
- 驗證準備方法的適配性
- 確保業務邏輯一致性

### 檢查執行順序的重要性

預設檢查的執行順序經過精心設計：

1. **基礎檢查優先**：`checkAllProduct` 首先執行，確保基本數據正確
2. **依賴關係考慮**：後續檢查可能依賴前面檢查的結果
3. **性能優化**：將可能失敗率高的檢查放在前面，減少不必要的計算
4. **業務邏輯順序**：按照業務處理的自然順序進行檢查

### 檢查失敗的處理策略

所有檢查採用「累積錯誤」策略：
- 不會因為某個檢查失敗而停止後續檢查
- 將所有錯誤訊息收集到 `errorMessageList` 中
- 最終一次性返回所有錯誤，方便用戶一次性修正所有問題

### 監控與優化建議

1. **性能監控**：記錄每個檢查方法的執行時間
2. **錯誤統計**：統計各種錯誤的發生頻率
3. **優化策略**：對高頻錯誤項目進行前置檢查
4. **緩存策略**：對查詢頻繁的配置數據進行緩存

## 回應結果構建

```java
return ResponseDto.<Void>builder()
    .status(checkProductResultDto.getErrorMessageList().isEmpty() ? 1 : -1)
    .errorMessageList(checkProductResultDto.getErrorMessageList())
    .build();
```

**狀態碼**：
- `1`：驗證成功，無錯誤
- `-1`：驗證失敗，有錯誤訊息

## 工程師開發指南

### 1. 新增驗證邏輯
1. 在對應的 `switch case` 中添加新的檢查方法調用
2. 實現具體的檢查方法，遵循現有的模式
3. 在 `messages.properties` 中添加錯誤訊息
4. 編寫單元測試驗證邏輯

### 2. 問題追蹤步驟
1. **查看錯誤訊息**：根據 `errorMessageList` 定位具體錯誤
2. **檢查輸入數據**：驗證 `productMasterDto` 的各個欄位值
3. **確認數據庫狀態**：檢查相關表的數據完整性
4. **測試環境重現**：在測試環境重現問題進行調試

### 3. 性能優化建議
1. **數據庫查詢優化**：避免在循環中進行數據庫查詢
2. **快取機制**：對不常變動的配置數據使用快取
3. **批次處理**：對大量數據的處理採用批次方式
4. **異步處理**：對非關鍵路徑的檢查考慮異步處理

### 4. 常見問題與解決方案

#### 問題 1：JSON 解析失敗
**原因**：前端傳遞的 JSON 格式不正確
**解決**：檢查 `row.getContent()` 的格式，確保符合 `SingleEditProductDto` 的結構

#### 問題 2：店鋪查找失敗
**原因**：Store Code 不存在或店鋪類型不匹配
**解決**：確認 `store` 表中的數據，檢查 `store_type` 欄位

#### 問題 3：主分類驗證失敗
**原因**：分類代碼不存在或已停用
**解決**：檢查 `bu_product_category` 表，確認分類狀態

#### 問題 4：一致性檢查失敗
**原因**：前端傳遞的 ID 與數據庫記錄不符
**解決**：確認前端是否正確獲取並傳遞產品信息

## 日誌追蹤建議

### 關鍵日誌點
```java
log.info("開始編輯產品驗證，recordId: {}, skuId: {}", productRecord.getId(), productMasterDto.getSkuId());
log.debug("產品檢查數據：{}", gson.toJson(productCheckDo));
log.warn("驗證失敗，錯誤訊息：{}", checkProductResultDto.getErrorMessageList());
```

### 監控指標
- 驗證成功率
- 各類型錯誤的分布
- 驗證執行時間
- 頻繁失敗的驗證項目

## 總結

`checkEditProductHandler` 是產品編輯功能的關鍵守門員，確保所有編輯操作都符合業務規則和數據完整性要求。工程師在維護和擴展該功能時，需要：

1. **深入理解業務邏輯**：每個驗證都有其業務背景
2. **注意驗證順序**：某些驗證有前後依賴關係
3. **完善錯誤處理**：提供清晰的錯誤訊息幫助用戶解決問題
4. **保持代碼可維護性**：遵循現有的模式和約定

透過本文檔的指導，工程師應該能夠有效地開發新功能、追蹤問題並優化系統性能。
