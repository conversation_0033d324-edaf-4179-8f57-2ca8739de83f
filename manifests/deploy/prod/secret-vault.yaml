apiVersion: v1
kind: Secret
metadata:
  name: mms-product-secret
  namespace: mms
  annotations:
    avp.kubernetes.io/path: "app-cos/data/mms/mms-product-api"
type: Opaque
data:
  mms_product_db_password: <mms_product_db_password | base64encode>
  mms_product_db_username: <mms_product_db_username | base64encode>
  product_master_rabbit_mq_password: <product_master_rabbit_mq_password | base64encode>
  product_master_rabbit_mq_port: <product_master_rabbit_mq_port | base64encode>
  product_master_rabbit_mq_username: <product_master_rabbit_mq_username | base64encode>
  rabbitmq_hktv_username: <rabbitmq_hktv_username | base64encode>
  rabbitmq_hktv_password: <rabbitmq_hktv_password | base64encode>
  rabbitmq_hktv_queue_name: <rabbitmq_hktv_queue_name | base64encode>
  third_party_api_url: <third_party_api_url | base64encode>
  jwt_app_secret_key: <jwt_app_secret_key | base64encode>
  hybris_api_secret_key: <hybris_api_secret_key | base64encode>
  video_private_key: <video_private_key | base64encode>
  onebound_api_key: <onebound_api_key | base64encode>
  onebound_api_secret: <onebound_api_secret | base64encode>

