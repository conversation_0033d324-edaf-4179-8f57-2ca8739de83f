apiVersion: batch/v1
kind: CronJob
metadata:
  name: mms-product-cronjob-housekeeping-record
  namespace: mms
spec:
  schedule: "TZ=Asia/Hong_Kong 0 0 * * *"
  startingDeadlineSeconds: 600
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      activeDeadlineSeconds: 1800
      backoffLimit: 3
      parallelism: 1
      completions: 1
      template:
        spec:
          containers:
            - name: mms-product-cronjob
              image: alpine/curl:3.14
              imagePullPolicy: IfNotPresent
              volumeMounts:
                - name: command-config-volume
                  mountPath: /etc/config/command-config
              env:
                - name: JOB_NAME
                  value: "mms-product-cronjob-housekeeping-record"
                - name: JOB_URL
                  value: "http://mms-product-api-svc.mms.svc.cluster.local:8080/product/cronjob/house-keeping/save-product-record"
              command:
                - /bin/sh
                - -c
                - |
                  source /etc/config/command-config/job-command.sh
          volumes:
            - name: command-config-volume
              configMap:
                name: mms-product-configmap
          restartPolicy: Never
