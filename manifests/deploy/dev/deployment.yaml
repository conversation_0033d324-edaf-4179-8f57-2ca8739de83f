apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: mms
  name: mms-product-api
  labels:
    app: mms-product-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mms-product-api
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mms-product-api
    spec:
      containers:
        - name: mms-product-api
          image: <CI_REGISTRY>/<PROJECT_ID>/<CI_APPLICATION_REPOSITORY>:<TAGS>
          resources:
            limits:
              cpu: "4"
              memory: "8Gi"
            requests:
              cpu: "2"
              memory: "8Gi"
          ports:
            - containerPort: 8080
          envFrom:
            - prefix: mms_product_configmap_
              configMapRef:
                name: mms-product-configmap
            - prefix: mms_product_secret_
              secretRef:
                name: mms-product-secret
          livenessProbe:
            httpGet:
              path: /product/actuator/health
              port: 8080
            initialDelaySeconds: 180
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
            successThreshold: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /product/actuator/health
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 3
          volumeMounts:
            - mountPath: /rakuten
              name: mms-product-api-upload
      volumes:
        - name: mms-product-api-upload
          persistentVolumeClaim:
            claimName: mms-product-api-pvc
            readOnly: false
