# API Specification: Batch Edit Little Mall Product Commonly Used Fields

## Basic Information

- **API Name**: Batch Edit Little Mall Product Commonly Used Fields
- **HTTP Method**: POST
- **URL Path**: `/batchEditLittleMallProductCommonlyUsed`
- **Description**: This API is used to batch update commonly used fields of Little Mall products, specifically the store category. It supports updating by either SKU ID or product code (which will update all variant SKUs of the product).

## Request Headers

| Name | Required | Description |
|------|----------|-------------|
| Content-Type | Yes | Must be set to `application/json` |
| Authorization | Yes | Bearer token for authentication |

## Request Body

The request body is in JSON format and contains the following fields:

```json
{
  "merchant_id": 123,
  "file_name": "batch_edit_20230101.xlsx",
  "request_type": 1,
  "product_template": [
    {
      "sku_id": "SKU001",
      "uuid": "product-uuid-001",
      "store_category": ["Category1", "Category2"]
    },
    {
      "sku_id": "SKU002",
      "uuid": "product-uuid-002",
      "store_category": ["Category1", "Category3"]
    }
  ]
}
```

### Request Parameters

| Field Name | Type | Required | Description |
|------------|------|----------|-------------|
| merchant_id | Integer | Yes | Merchant ID |
| file_name | String | Yes | File name, typically used to track batch operations |
| request_type | Integer | No | Type of request: 1 (default) - update by SKU ID, 2 - update by product code (will update all variant SKUs) |
| product_template | Array | Yes | List of product templates containing information to be updated |
| product_template[].sku_id | String | Conditional | Product SKU code (required when request_type=1) |
| product_template[].product_code | String | Conditional | Product code (required when request_type=2) |
| product_template[].uuid | String | Yes | Product unique identifier |
| product_template[].store_category | Array | Yes | List of store categories, an array of category name strings |

## Response

The response is in JSON format and contains the following fields:

```json
{
  "status": 1,
  "message": null,
  "data": null
}
```

### Response Parameters

| Field Name | Type | Description |
|------------|------|-------------|
| status | Integer | Status code, 1 indicates success, -1 indicates failure |
| message | String | Error message, null when successful |
| data | Object | Response data, fixed as null for this API |

## Status Codes

| Status Code | Description |
|-------------|-------------|
| 1 | Success |
| -1 | Failure |

## Error Handling

When the API returns a failure status, the message field will contain error information. Common errors include:

- Missing required fields
- Merchant ID does not exist or no permission
- Product UUID does not exist
- Invalid request format

## Usage Examples

### Request Example (Using SKU ID)

```http
POST /batchEditLittleMallProductCommonlyUsed HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "merchant_id": 123,
  "file_name": "batch_edit_20230101.xlsx",
  "request_type": 1,
  "product_template": [
    {
      "sku_id": "SKU001",
      "uuid": "product-uuid-001",
      "store_category": ["Electronics", "Phone Accessories"]
    },
    {
      "sku_id": "SKU002",
      "uuid": "product-uuid-002",
      "store_category": ["Electronics", "Computer Accessories"]
    }
  ]
}
```

### Request Example (Using Product Code)

```http
POST /batchEditLittleMallProductCommonlyUsed HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "merchant_id": 123,
  "file_name": "batch_edit_20230101.xlsx",
  "request_type": 2,
  "product_template": [
    {
      "product_code": "PROD001",
      "uuid": "product-uuid-001",
      "store_category": ["Electronics", "Phone Accessories"]
    },
    {
      "product_code": "PROD002",
      "uuid": "product-uuid-002",
      "store_category": ["Electronics", "Computer Accessories"]
    }
  ]
}
```

### Success Response Example

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": 1,
  "message": null,
  "data": null
}
```

### Failure Response Example

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": -1,
  "message": "Missing required fields or invalid format",
  "data": null
}
```

## Notes

1. This API is processed asynchronously. A successful status return indicates that the request has been accepted and processing has begun, not that all products have been successfully updated.
2. The maximum number of products for batch processing is 10,000.
3. When request_type=1, each product must provide a valid sku_id, uuid, and store_category.
4. When request_type=2, each product must provide a valid product_code, uuid, and store_category. All variant SKUs associated with the product_code will be updated.
5. Users must have edit permissions for the corresponding merchant.
6. It is recommended to perform large batch updates during off-peak hours.

## Related APIs

- `/batchEditProductCommonlyUsed` - Used to edit commonly used fields of general products
- `/little-mall/products/batch/edit` - Used to batch edit all fields of Little Mall products
