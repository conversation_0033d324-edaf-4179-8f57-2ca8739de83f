#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 532676608 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=35040, tid=19580
#
# JRE version:  (21.0.6+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://ite-git01.hktv.com.hk': 

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Thu Jun  5 00:15:20 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.3912) elapsed time: 1.174840 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000028ac59d69c0):  JavaThread "Unknown thread" [_thread_in_vm, id=19580, stack(0x000000a2d7b00000,0x000000a2d7c00000) (1024K)]

Stack: [0x000000a2d7b00000,0x000000a2d7c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5cb9]
V  [jvm.dll+0x8c4113]
V  [jvm.dll+0x8c666e]
V  [jvm.dll+0x8c6d53]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0x6e2575]
V  [jvm.dll+0x6d602a]
V  [jvm.dll+0x3635db]
V  [jvm.dll+0x36b1a6]
V  [jvm.dll+0x3bd4f6]
V  [jvm.dll+0x3bd7c8]
V  [jvm.dll+0x335d2c]
V  [jvm.dll+0x336a1b]
V  [jvm.dll+0x88b569]
V  [jvm.dll+0x3ca6c8]
V  [jvm.dll+0x8745b8]
V  [jvm.dll+0x45f0de]
V  [jvm.dll+0x460dc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007fff761da148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x0000028ac76fe030 WorkerThread "GC Thread#0"                     [id=64992, stack(0x000000a2d7c00000,0x000000a2d7d00000) (1024K)]
  0x0000028ae5660080 ConcurrentGCThread "G1 Main Marker"            [id=3788, stack(0x000000a2d7d00000,0x000000a2d7e00000) (1024K)]
  0x0000028ae5663050 WorkerThread "G1 Conc#0"                       [id=71344, stack(0x000000a2d7e00000,0x000000a2d7f00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff758c8e07]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff7624eb30] Heap_lock - owner thread: 0x0000028ac59d69c0

Heap address: 0x0000000604000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x0000028adc6b0000,0x0000028add690000] _byte_map_base: 0x0000028ad9690000

Marking Bits: (CMBitMap*) 0x0000028ac76fe730
 Bits: [0x0000028add690000, 0x0000028ae5590000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.014 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff775a60000 - 0x00007ff775a6a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007fffde120000 - 0x00007fffde386000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffdd580000 - 0x00007fffdd649000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffdbb10000 - 0x00007fffdbedc000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffdb580000 - 0x00007fffdb6cb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fffca6a0000 - 0x00007fffca6bb000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007fffc9610000 - 0x00007fffc9628000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007fffdd300000 - 0x00007fffdd4ca000 	C:\WINDOWS\System32\USER32.dll
0x00007fffdbae0000 - 0x00007fffdbb07000 	C:\WINDOWS\System32\win32u.dll
0x00007fffc3030000 - 0x00007fffc32ca000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007fffdbee0000 - 0x00007fffdbf0b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffdd070000 - 0x00007fffdd119000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffdb910000 - 0x00007fffdba42000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffdb4d0000 - 0x00007fffdb573000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fffdc5c0000 - 0x00007fffdc5f0000 	C:\WINDOWS\System32\IMM32.DLL
0x0000028ac5d00000 - 0x0000028ac5d16000 	C:\WINDOWS\System32\umppc19607.dll
0x0000028ac5d50000 - 0x0000028ac5d63000 	C:\WINDOWS\System32\CsXumd64_19607.dll
0x00007fffd50d0000 - 0x00007fffd50dc000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007fffc1bf0000 - 0x00007fffc1c7d000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007fff75580000 - 0x00007fff76341000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007fffddf60000 - 0x00007fffde012000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffdd4d0000 - 0x00007fffdd576000 	C:\WINDOWS\System32\sechost.dll
0x00007fffdcdc0000 - 0x00007fffdced6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffdc0b0000 - 0x00007fffdc124000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fffdaf40000 - 0x00007fffdaf9e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fffc3510000 - 0x00007fffc3546000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffcbde0000 - 0x00007fffcbdeb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffdaf20000 - 0x00007fffdaf34000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fffda030000 - 0x00007fffda04a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fffc9fa0000 - 0x00007fffc9faa000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007fffc7f80000 - 0x00007fffc81c1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffdc230000 - 0x00007fffdc5b4000 	C:\WINDOWS\System32\combase.dll
0x00007fffdde70000 - 0x00007fffddf50000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fffc9fd0000 - 0x00007fffca009000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffdb430000 - 0x00007fffdb4c9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fffc9d30000 - 0x00007fffc9d50000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://ite-git01.hktv.com.hk': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8522825728                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8522825728                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\OpenLogic\jdk-*********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\apache\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Rancher Desktop\resources\resources\win32\bin\;C:\Program Files\Rancher Desktop\resources\resources\win32\docker-cli-plugins\;C:\Program Files\Rancher Desktop\resources\resources\linux\bin\;C:\Program Files\Rancher Desktop\resources\resources\linux\docker-cli-plugins\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\dotnet\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
USERNAME=dennis.liu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13648K (0% of 33281092K total physical memory with 1482204K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 19 days 18:56 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xec, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 16 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 32501M (1447M free)
TotalPageFile size 130805M (AvailPageFile size 374M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 71M, peak: 579M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for windows-amd64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
